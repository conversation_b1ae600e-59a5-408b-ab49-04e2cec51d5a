// 引入Http工具类
import httpUtils from "../../utils/HttpUtils";
// 引入工具类
import commonUtils from "../../utils/CommonUtils";
import { ReadHvConfig, WirelessSleepPeriod } from "../../protocol/nb-wireless/business/nb-valve/NbHvCmdConstants";
import { NbHvHistoryData } from "../../protocol/nb-wireless/business/nb-valve/process/bean/NbHvHistoryData";
import eventManager from "../../utils/EventUtils";
import {
    CHANGE_PRODUCTION_TEST_INFO,
    REFRESH_TEST_STATE,
    UPDATE_PRODUCTION_DEVICE_LIST,
    UPDATE_PRODUCTION_TEST_INFO
} from "../../protocol/EventConstants";
import { WirelessChannel } from "../../protocol/bluetooth/BlueCmdConstants";
import { NbHvConnectProvider } from '../../protocol/nb-wireless/business/nb-valve/NbHvConnectProvider';
import protocolUtils from '../../protocol/ProtocolUtils';
import { behavior, dispose } from '../../behaviors/behavior'
import {HvProtocolAutoTestProvider} from '../../protocol/nb-wireless/business/nb-valve/test/HvProtocolAutoTestProvider';
// 获取应用实例
const app = getApp();

/**
 * 本地小无线抄表功能页
 */
Component({
    // 继承公共行为
    behaviors: [behavior, dispose], 
 
    /**
     * 页面的初始数据
     */
    data: {
        // 蓝牙连接服务对象
        connectProvider: null,
        // 协议测试桩对象
        protocolAutoTestProvider: null,
        // 蓝牙设备信息
        deviceId: null, // 连接的蓝牙设备ID
        serviceId: null, // 连接的蓝牙服务uuid
        notifyId: null, // 传入特征uuid
        writeId: null, // 写入特征uuid
        deviceName: null, // 连接的设备名称
        // 当前操作设备基本信息
        deviceLocation: null, // 设备地址
        currentImei: null, // 当前设备对应的IMEI，用于发送命令时实际使用的设备地址参数
        deviceHistoryDataList: [], // 历史操作设备，最近10台
        deviceSearchDataList: [], // 后台搜索匹配到的设备
        deviceSearchDataTotal: 0, // 后台搜索匹配到的数量
        historyShow: '0',
        deviceSearchType: 'IMEI',// 搜索结果类型

        // 表号
        meterCode: null,

        wirelessChannelIndex: null, // 小无线信道

        // 设备数据
        valvePercent: null, // 阀门开度
        curInTemperature: null, // 进水温度
        curOutTemperature: null, // 回水温度
        totalControl: null, // 累计开关阀次数
        batteryVoltage: null, // 电池电压

        // NB-IoT信号
        CSQ: null,
        SINR: null,
        RSRP: null,
        RF_RSSI: null,
        ECL: null,
        PCI: null,
        CellId: null,
        nbExpand: false,

        // 设备信息
        versions: null, // 版本号
        IMEI: null, // imei
        IMSI: null, // imsi
        ICCID: null, // iccid
        deviceInfoExpand: false,

        // 读取/写入结果
        allRead: null, // 一键读取
        readMeterCode: null, // 读水表表号
        writeMeterCode: null, // 写水表表号
        readDeviceData: null, // 读设备数据
        valvePercentResult: null, // 阀门开度结果
        readMeterSignal: null, // 读水表信号
        readDeviceInfo: null, // 读设备信息
        resetNb: null, // 复位结果
        openNb: null, // 开启结果
        closeNb: null, // 关闭结果
        resetMCU: null, // 复位MCU结果
        workMode: null, // 工作模式
        testMode: null, //产测模式
        reportReadMeterData: null, //上报抄表结果
        valveCorrect: null, // 阀门开度校准结果
    
        //失败上报确认
        subFailShow: false,
        subFailButtons: [
            {text: '取消', extClass: ''},
            {text: '提交', extClass: 'text-danger'}
        ],
        failRemark: '',
        reportDeviceInfo: null, //上报信息结果
        reportSuccess: null,

        // 其它辅助功能字段
        fixeBtnExpand: false,//更多按钮
        logsShow: false, //日志显示
    },

    methods: {
        /**
         * 开启抄表事件监听
         */
        startEventListener() {
            let that = this;
            // 最近10台操作设备更新
            eventManager.onListener(UPDATE_PRODUCTION_DEVICE_LIST, (params: NbHvHistoryData[]) => {
                that.setData({deviceHistoryDataList: params});
            });
            // 设备操作结果信息更新
            eventManager.onListener(UPDATE_PRODUCTION_TEST_INFO + '_' + that.data.deviceId, (mapContentEventObj: Map<string, any>) => {
                // 更新操作结果信息
                mapContentEventObj.forEach((value, key) => {
                    that.setData({
                        // @ts-ignore
                        [key]: value
                    });
                });
            });
            // 切换操作设备
            eventManager.onListener(CHANGE_PRODUCTION_TEST_INFO + '_' + that.data.deviceId, (params: NbHvHistoryData) => {
                that.changeProductionDeviceInfo(params);
            });
            // 开始监听事件后，判断当前操作历史设备列表是否有值，如果没有值触发一次事件获取
            this.data.connectProvider.historyDataManager().emitLoadHistoryDataEvent();
            // 刷新上报结果
            eventManager.onListener(REFRESH_TEST_STATE + '_' + this.data.deviceId, (result: boolean) => {
                this.setData({reportSuccess: result});
            });
            // 如果当前为开发环境，则初始化协议栈测试桩
            if (commonUtils.isDevelop()) {
                that.data.protocolAutoTestProvider.initTestProvider();
            }
        },

        /**
         * 取消事件监听
         */
        clearEventListener() {
            eventManager.clearListener(UPDATE_PRODUCTION_DEVICE_LIST);
            eventManager.clearListener(UPDATE_PRODUCTION_TEST_INFO + '_' + this.data.deviceId);
            eventManager.clearListener(CHANGE_PRODUCTION_TEST_INFO + '_' + this.data.deviceId);
            eventManager.clearListener(REFRESH_TEST_STATE + '_' + this.data.deviceId);
        },

        /**
         * 切换操作设备信息
         * @param data
         */
        changeProductionDeviceInfo(data: NbHvHistoryData) {
            // 获取级联属性
            let deviceDataInfo = data.getDeviceDataInfo();
            let meterSignalInfo = data.getMeterSignalInfo();
            let deviceInfo = data.getDeviceInfo();
            // 统一设置其它属性
            this.setData({
                // 更新表号
                meterCode: data.getMeterCode(),
                // 更新设备数据
                valvePercent: deviceDataInfo.getValvePercent(), // 阀门开度
                curInTemperature: deviceDataInfo.getCurInTemperature(), // 进水温度
                curOutTemperature: deviceDataInfo.getCurOutTemperature(), // 回水温度
                totalControl: deviceDataInfo.getTotalControl(), // 累计开关阀次数
                batteryVoltage: deviceDataInfo.getBatteryVoltage(), // 电池电压
                // NB-IoT信号
                CSQ: meterSignalInfo.getRcvRssi(),
                SINR: meterSignalInfo.getSinr(),
                RSRP: protocolUtils.rsrpFormatter(meterSignalInfo.getRsrp()),
                RF_RSSI: meterSignalInfo.getRssi(),
                ECL: meterSignalInfo.getEcl(),
                PCI: meterSignalInfo.getPci(),
                CellId: meterSignalInfo.getCellId(),
                //@ts-ignore
                nbExpand: this.ifInputValue(meterSignalInfo.getCellId()),
                // 设备信息
                versions: deviceInfo.getMeterVersionInfo().getVersionInfoString(), // 版本号
                IMEI: deviceInfo.getImei(), // imei
                IMSI: deviceInfo.getImsi(), // imsi
                ICCID: deviceInfo.getIccid(), // iccid
                deviceInfoExpand: !!deviceInfo.getImei(),
                // 读取/写入结果
                allRead: data.getAllRead(), // 一键读取
                readMeterCode: data.getReadMeterCode(), // 读水表表号
                writeMeterCode: data.getWriteMeterCode(), // 写水表表号
                readDeviceData: data.getReadMeterData(), // 读设备数据
                valvePercentResult: data.getValvePercentResult(), // 阀门开度结果
                readMeterSignal: meterSignalInfo.getReadMeterSignal(), // 读水表信号
                readDeviceInfo: deviceInfo.getReadDeviceInfo(), // 读设备信息
                resetNb: data.getResetNb(), // 复位结果
                openNb: data.getOpenNb(), // 开启结果
                closeNb: data.getCloseNb(), // 关闭结果
                resetMCU: data.getResetMCU(), // 复位MCU结果
                reportReadMeterData: data.getReportReadMeterData(), // 上报抄表结果
                valveCorrect: data.getValveCorrect(), // 阀门开度校准结果
                reportDeviceInfo: data.getReportDeviceInfo() // 产测结果上报结果
            });
        },

        /**
         * 按照条件查询设备
         */
        queryDevice() {
            let that = this;
            let search = that.data.deviceLocation;
            if (!search) {
                return;
            }
            // 一旦有修改设备地址，则清空操作IMEI标志，否则有可能导致使用上一个设备的IMEI发送命令
            that.setData({
                currentImei: null,
                meterCode: null
            });
            // 根据输入内容查询设备
            httpUtils.requestFormUrl({
                url: 'auth/hv/query',
                params: {
                    search: search,
                    type: that.data.deviceSearchType
                },
                success: (res: any) => {
                    // 输入的设备地址如果匹配到了唯一的结果且已经输入了完成的设备地址，则直接填充并关闭搜索窗
                    if (res.totalElements == 1) {
                        let device = res.content[0];
                        let deviceLocation = that.data.deviceSearchType === 'IMEI' ? device.imei : device.meterCode;
                        if (that.data.deviceLocation == deviceLocation) {
                            that.setData({
                                deviceLocation: deviceLocation,
                                currentImei: device.imei,
                                meterCode: device.meterCode,
                                historyShow: '0',
                                deviceSearchDataList: [],
                                deviceSearchDataTotal: 0
                            });
                            // 更换操作设备
                            this.data.connectProvider.historyDataManager().changeProductionMeter(deviceLocation, device.imei, device.meterCode);
                            return;
                        }
                    }
                    that.setData({
                        deviceSearchDataList: res.content.map((item: any) => {
                            switch (that.data.deviceSearchType) {
                                case 'IMEI':
                                    item.text = item.imei.split(search).join(`<span class="highlight">${search}</span>`);
                                    break;
                                case 'METER_CODE':
                                    item.text = item.meterCode.split(search).join(`<span class="highlight">${search}</span>`);
                                    break;
                            }
                            return item;
                        }),
                        deviceSearchDataTotal: res.totalElements,
                        // 输入了值且有搜索结果时，显示搜索结果
                        historyShow: '2'
                    });
                },
                failure: () => {
                },
                method: 'GET'
            });
        },

        /**
         * 切换历史操作设备
         * @param e
         */
        historyTap(e: any) {
            // 切换了操作设备后，隐藏搜索和历史设备区域
            this.setData({
                deviceLocation: e.currentTarget.dataset.val,
                currentImei: e.currentTarget.dataset.imei,
                meterCode: e.currentTarget.dataset.metercode
            });
            // 更换操作设备
            this.data.connectProvider.historyDataManager().changeProductionMeter(this.data.deviceLocation, this.data.currentImei, this.data.meterCode);
        },

        /**
         * 判断当前是否输入设备地址以及是否存在对应设备
         */
        hasDevice() {
            //@ts-ignore
            if (!this.ifInputValue(this.data.deviceLocation)) {
                commonUtils.showToast('请输入设备地址!');
                return false;
            }
            //@ts-ignore
            if (!this.ifInputValue(this.data.currentImei)) {
                commonUtils.showToast('该地址未匹配到设备!');
                return false;
            }
            return true;
        },

        /**
         * 扫描设备地址
         * @param e
         */
        scanQRCode(e: any) {
            let that = this, type = e.currentTarget.dataset.type;
            // 将设备输入栏置空
            that.setData({
                [type]: ''
            });
            // 设备地址扫码时，将当前设备切换为空设备
            if (type === 'deviceLocation') {
                this.data.connectProvider.historyDataManager().changeEmptyDevice();
            }
            wx.scanCode({
                scanType:['barCode', 'qrCode', 'datamatrix'],
                success: (data) => {
                    this.setData({
                        historyShow: '0'
                    });
                    if (data && data.result) {
                        // 将扫码结果打印到日志上
                        this.data.connectProvider.historyDataManager().addLogs('扫码结果：' + data.result);
                        if(data.result.length <= 15 && /^[0-9]*$/.test(data.result)){
                            commonUtils.showToast('扫码成功', 'success');
                            that.setData({
                                [type]: data.result
                            });
                            // 当前为设备地址扫码，扫码成功后，去后台匹配验证对应设备
                            if (type === 'deviceLocation') {
                                let deviceLocation = that.data.deviceLocation;
                                that.validDeviceCode(deviceLocation);
                            }
                        }else{
                            commonUtils.showToast('扫码结果不合法！');
                        }
                    } else {
                        commonUtils.showToast(`扫码出错:${data.errMsg}`);
                    }
                }
            });
        },

        /**
         * 验证当前用户是否具有操作该设备的权限
         * @param deviceCode
         */
        validDeviceCode(deviceCode: string) {
            let that = this;
            httpUtils.requestFormUrl({
                url: 'auth/hv/scanCode',
                params: {
                    deviceCode: deviceCode
                },
                success: (res: any) => {
                    // 扫码并验证设备成功后，保存相关信息
                    that.setData({
                        currentImei: res.imei,
                        meterCode: res.meterCode
                    });
                    // 自动切换操作设备为当前扫码的设备
                    this.data.connectProvider.historyDataManager().changeProductionMeter(that.data.deviceLocation, that.data.currentImei, that.data.meterCode);
                },
                failure: (value: any) => {
                    // 如果是正式版本则必须要验证成功才能进行操作，如果是开发版本和体验版本则不需要验证
                    if (commonUtils.isRelease()) {
                        // 验证设备失败，则清空当前操作设备相关信息
                        that.setData({
                            currentImei: null,
                            meterCode: null
                        });
                        commonUtils.showToast(value);
                    } else {
                        // 扫码并验证设备成功后，保存相关信息
                        that.setData({
                            currentImei: deviceCode,
                            meterCode: deviceCode.length <= 14 && deviceCode.length != 10 ? deviceCode : null
                        });
                        // 自动切换操作设备为当前扫码的设备
                        this.data.connectProvider.historyDataManager().changeProductionMeter(that.data.deviceLocation, that.data.currentImei, that.data.meterCode);
                    }
                },
                method: 'GET'
            });
        },

        /**
         * 一键读取
         */
        oneKeyRead() {
            // 如果当前是非正式发布版本，允许在输入了完整的表号、IMEI的情况下点击一键读取开始操作
            if (commonUtils.isDevelop() || commonUtils.isTrial()) {
                let deviceLocation = this.data.deviceLocation;
                if (deviceLocation && !this.data.currentImei) {
                    let inputLen = deviceLocation.length;
                    if (inputLen == 14 || inputLen == 15) {
                        // 扫码并验证设备成功后，保存相关信息
                        this.setData({
                            currentImei: deviceLocation,
                            meterCode: deviceLocation.length == 14 ? deviceLocation : null
                        });
                        // 自动切换操作设备为当前扫码的设备
                        this.data.connectProvider.historyDataManager().changeProductionMeter(this.data.deviceLocation, this.data.currentImei, this.data.meterCode);
                    }
                }
            }
            if (this.hasDevice()) {
                this.data.connectProvider.protocolProvider().sendReadConfigCommand(this.data.currentImei, ReadHvConfig.READ_PRODUCTION_TEST_DATA);
            }
        },

        /**
         * 读表号
         */
        readMeterCodeOpt() {
            if (this.hasDevice()) {
                this.data.connectProvider.protocolProvider().sendReadConfigCommand(this.data.currentImei, ReadHvConfig.READ_METER_CODE);
            }
        },

        /**
         * 写暖通阀阀门号
         */
        writeMeterCodeOpt() {
            if (this.hasDevice()) {
                // 阀门ID不能为空
                //@ts-ignore
                if (!this.ifInputValue(this.data.meterCode)) {
                    commonUtils.showToast('请输入阀门ID');
                    return;
                }
                this.data.connectProvider.protocolProvider().sendWriteValveCodeCommand(this.data.currentImei, this.data.meterCode, () => {
                    this.setData({reportDeviceInfo: null});
                    this.data.connectProvider.historyDataManager().configTestStateInterval(this.data.deviceLocation);
                });
            }
        },

        /**
         * 读计量参数
         */
        readMeasuringParams() {
            if (this.hasDevice()) {
                this.data.connectProvider.protocolProvider().sendSingleReadMeterCommand(this.data.currentImei);
            }
        },

        /**
         * 开度调节
         * @param e
         */
        setValvePercent(e: any) {
            if (this.hasDevice()) {
                let valvePercent = parseInt(this.data.valvePercent);
                if ( (!valvePercent && valvePercent != 0) || valvePercent < 0 || valvePercent > 100) {
                    commonUtils.showToast('阀门开度必须是0-100');
                    return;
                }
                this.data.connectProvider.protocolProvider().sendValveCtrlCommand(this.data.currentImei, valvePercent);
            }
        },

        /**
         * 读NB信号
         */
        readNbSignal() {
            if (this.hasDevice()) {
                this.data.connectProvider.protocolProvider().sendReadConfigCommand(this.data.currentImei, ReadHvConfig.READ_SIGNAL);
            }
        },

        /**
         * 读取设备信息
         */
        readDeviceInfo() {
            if (this.hasDevice()) {
                this.data.connectProvider.protocolProvider().sendReadConfigCommand(this.data.currentImei, ReadHvConfig.READ_DEVICE_INFO);
            }
        },

        /**
         * 控制NB
         * @param e
         */
        ctrlNb(e: any) {
            let nbCtrlType = e.currentTarget.dataset.type;
            switch (nbCtrlType) {
                case 'RESET_NB': {
                    if (this.hasDevice()) {
                        this.data.connectProvider.protocolProvider().sendResetNbCommand(this.data.currentImei);
                    }
                    break;
                }
                case 'OPEN_NB': {
                    if (this.hasDevice()) {
                        this.data.connectProvider.protocolProvider().sendOpenNbCommand(this.data.currentImei);
                    }
                    break;
                }
                case 'CLOSE_NB': {
                    if (this.hasDevice()) {
                        this.data.connectProvider.protocolProvider().sendCloseNbCommand(this.data.currentImei);
                    }
                    break;
                }
            }
        },
    
        /**
         * 复位MCU
         */
        sendResetMCU(e: any) {
            if (this.hasDevice()) {
                this.data.connectProvider.protocolProvider().sendResetMCUCommand(this.data.currentImei);
            }
        },

        /**
         * 设置小无线运行模式
         */
        setWirelessMode(e: any) {
            let modeType = e.currentTarget.dataset.type;
            let wirelessSleepPeriod: WirelessSleepPeriod;
            let type = "";
            switch (modeType) {
                case "workMode": {
                    type = "工作";
                    wirelessSleepPeriod = WirelessSleepPeriod.FIVE_SECONDS;
                    break;
                }
                case "testMode": {
                    type = "产测";
                    wirelessSleepPeriod = WirelessSleepPeriod.ONE_SECOND;
                    break;
                }
                default: {
                    commonUtils.showToast("不支持的小无线模式：" + modeType);
                    return;
                }
            }
            commonUtils.showModal('提示', '该操作将会广播修改附近所有设备，是否确认切换至' + type + '模式？', () => {
                this.data.connectProvider.protocolProvider().sendSetWirelessParamsCommand(app.globalData.broadcastAddress, wirelessSleepPeriod);
            });
        },

        /**
         * 发送阀门校准指令
         */
        sendValveCorrectCommand() {
            if(this.hasDevice()) {
                this.data.connectProvider.protocolProvider().sendValveCorrectCommand(this.data.currentImei);
            }
        },
    
        /**
         * 上报信息
         */
        reportDeviceInfoOpt() {
            if (this.hasDevice()) {
                commonUtils.showModal('提示', '确认上报信息？', () => {
                    this.data.connectProvider.protocolProvider().sendReportDeviceInfoCommand(this.data.currentImei);
                });
            }
        },

        /**
         * 调整通信信道
         * @param e
         */
        wirelessChannelChange(e: any) {
            let wirelessChannel: WirelessChannel = parseInt(e.detail.value) + 1;
            // 将信道设置到缓存中
            this.data.connectProvider.nbSession().setWirelessChannel(wirelessChannel);
        },

        /**
         * 标记失败弹出框toggle
         */
        subFailToggle() {
            this.setData({
                subFailShow: !this.data.subFailShow
            });
        },
    
        /**
         * 标记失败弹出框按钮
         * @param e
         */
        subFailBtnTap(e: any) {
            // 等于1为提交
            if (e.detail.index === 1) {
                if (!this.data.currentImei) {
                    commonUtils.showToast('当前没有正在产测的设备');
                    return;
                }
                httpUtils.requestFormUrl({
                    url: 'auth/hv/reportFailMessage',
                    params: {
                        imei: this.data.currentImei,
                        failMessage: this.data.failRemark
                    },
                    success: () => {
                        commonUtils.showToast('标记失败完成');
                        this.data.connectProvider.historyDataManager().getCurrentDeviceData().setReportFailMessage(1);
                        this.data.connectProvider.historyDataManager().publishUpdateProductionInfo(protocolUtils.buildMapContentEventObj(['reportFailMessage'], [1]));
                    },
                    failure: (value: any) => {
                        commonUtils.showToast(value);
                        this.data.connectProvider.historyDataManager().getCurrentDeviceData().setReportFailMessage(0);
                        this.data.connectProvider.historyDataManager().publishUpdateProductionInfo(protocolUtils.buildMapContentEventObj(['reportFailMessage'], [0]));
                    },
                    method: 'POST'
                });
            }
            this.subFailToggle();
        },

    },

    lifetimes: {
        attached() {
            // 里面组件的取值是"0-3"，外面传入的是1-4，所以需要减1后设置
            this.setData({wirelessChannelIndex: this.data.wirelessChannelIndex - 1});
            // 初始化通道对象，协议中信道取值为1-4，所以可直接设置
            this.data.connectProvider = new NbHvConnectProvider(this.data.deviceId, this.data.serviceId, this.data.writeId,
                this.data.options.wirelessChannelIndex, this);
            // 初始化协议测试桩对象
            this.data.protocolAutoTestProvider = new HvProtocolAutoTestProvider(this.data.deviceId, this.data.connectProvider);
            // 开启产测事件监听
            this.startEventListener();
        }
    }
});
