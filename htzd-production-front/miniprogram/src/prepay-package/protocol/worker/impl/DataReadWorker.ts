import {AbstractWorker} from '../AbstractWorker';
import {CommandCode} from '../../Constants';
import {Meter} from '../../bean/Meter';

/**
 * 数据读取
 */
export class DataReadWorker extends AbstractWorker {

    constructor(meter: Meter, key: string) {
        super(meter, key);
    }

    getCommandCode(): number {
        return CommandCode.CODE_08;
    }

    getPayload(): Uint8Array {
        return new Uint8Array(0);
    }
}
