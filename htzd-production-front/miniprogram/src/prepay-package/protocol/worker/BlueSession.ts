import bluetoothUtils from '../../utils/BluetoothUtils';

/**
 * 蓝牙连接会话，用于封装向蓝牙设备发送信息的通道
 */
export class BlueSession {

    /**
     * 蓝牙设备ID
     */
    public deviceId: string;

    /**
     * 蓝牙设备服务ID
     */
    public serviceId: string;

    /**
     * 蓝牙设备写入信息通道标识ID
     */
    public writeId: string;

    /**
     * 设置或修改蓝牙通道信息，首次连接或者切换连接蓝牙时调用
     * @param deviceId
     * @param serviceId
     * @param writeId
     */
    constructor(deviceId: string, serviceId: string, writeId: string) {
        this.deviceId = deviceId;
        this.serviceId = serviceId;
        this.writeId = writeId;
    }

    /**
     * 断开蓝牙连接时调用，无法再发送信息
     */
    public disconnectBluetooth() {
        this.deviceId = null;
        this.serviceId = null;
        this.writeId = null;
    }

    /**
     * 判断当前会话中的蓝牙通道是否初始化好
     */
    private isInitSuccess(): boolean {
        return !!this.deviceId && !!this.serviceId && !!this.writeId;
    }

    /**
     * 向蓝牙设备发送信息
     * @param buffer
     * @param successCallback
     */
    public sendMessage(buffer: ArrayBuffer, successCallback: () => void) {
        // 判断当前蓝牙通道是否初始化
        if (!this.isInitSuccess()) {
            throw new Error('蓝牙通道尚未初始化，无法发送信息');
        }
        // 发送报文串
        bluetoothUtils.sendMessage(this.deviceId, this.serviceId, this.writeId, buffer, successCallback);
    }
}
