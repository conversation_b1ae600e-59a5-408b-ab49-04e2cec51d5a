import numberUtils from '../../../../utils/NumberUtils';
import { DeviceStatus } from './DeviceStatus';

/**
 * 水表基本信息，操作相关命令后返回的结果
 * @beforeMonths 是否返回往期用量
 */
export class MeterInfoRes {
    
    public static decode(buffer: Uint8Array, index: number = 0, beforeMonths: boolean = true): MeterInfoRes {
        // 存储解析结果
        let result: MeterInfoRes = new MeterInfoRes();
        // 价格版本
        result.priceVer = buffer[index];
        index += 1;
        //设备状态
        result.deviceStatus = DeviceStatus.decode(buffer.slice(index, index + 2))
        index += 2;
        // 电池电压，单位：mV
        result.batteryVoltage = numberUtils.bytesToNumber(buffer.slice(index, index + 2)) / 1000;
        index += 2;
        // 可用余额，单位0.01元，解析后换成元
        result.balance = numberUtils.bytesToNumber(buffer.slice(index, index + 4)) / 100;
        index += 4;
        // 总用金额，单位0.01元，解析后换成元
        result.totalUseAmount = numberUtils.bytesToNumber(buffer.slice(index, index + 4)) / 100;
        index += 4;
        // 总用量：单位0.1立方米，解析后换成1立方米
        result.totalUseConsumption = numberUtils.bytesToNumber(buffer.slice(index, index + 4)) / 10;
        index += 4;
        // 当月用量金额，单位：单位0.01元，解析后换成元
        result.curMonthUseConsumptionAmount = numberUtils.bytesToNumber(buffer.slice(index, index + 4)) / 100;
        index += 4;
        // 当月用量，单位：0.1立方米，解析后换成1立方米
        result.curMonthUseConsumption = numberUtils.bytesToNumber(buffer.slice(index, index + 2)) / 10;
        index += 2;
        // 透支金额
        result.overcharge = numberUtils.bytesToNumber(buffer.slice(index, index + 2)) / 100;
        index += 2;
        
        if (!beforeMonths) {
            return result;
        }
        
        // 上11个月分别用量（依次为：上1月、上2月、......、上11月）每月占2字节，单位：0.1立方米，解析后换成1立方米
        result.last1MonthUseConsumption = numberUtils.bytesToNumber(buffer.slice(index, index + 2)) / 10;
        index += 2;
        result.last2MonthUseConsumption = numberUtils.bytesToNumber(buffer.slice(index, index + 2)) / 10;
        index += 2;
        result.last3MonthUseConsumption = numberUtils.bytesToNumber(buffer.slice(index, index + 2)) / 10;
        index += 2;
        result.last4MonthUseConsumption = numberUtils.bytesToNumber(buffer.slice(index, index + 2)) / 10;
        index += 2;
        result.last5MonthUseConsumption = numberUtils.bytesToNumber(buffer.slice(index, index + 2)) / 10;
        index += 2;
        result.last6MonthUseConsumption = numberUtils.bytesToNumber(buffer.slice(index, index + 2)) / 10;
        index += 2;
        result.last7MonthUseConsumption = numberUtils.bytesToNumber(buffer.slice(index, index + 2)) / 10;
        index += 2;
        result.last8MonthUseConsumption = numberUtils.bytesToNumber(buffer.slice(index, index + 2)) / 10;
        index += 2;
        result.last9MonthUseConsumption = numberUtils.bytesToNumber(buffer.slice(index, index + 2)) / 10;
        index += 2;
        result.last10MonthUseConsumption = numberUtils.bytesToNumber(buffer.slice(index, index + 2)) / 10;
        index += 2;
        result.last11MonthUseConsumption = numberUtils.bytesToNumber(buffer.slice(index, index + 2)) / 10;
        return result;
    }
    
    /**
     * 返回阀门状态
     */
    get valveStatus() {
        let val = this.deviceStatus.valveState;
        if (val == 0) {
            return 'OPENED';
        } else if (val == 1) {
            return 'CLOSED';
        } else {
            return 'UNKNOWN';
        }
    }
    
    // 价格版本
    priceVer: number;
    //设备状态
    deviceStatus: DeviceStatus;
    // 电池电压，单位：mV
    batteryVoltage: number;
    // 可用余额，单位0.01元
    balance: number;
    // 总用金额，单位0.01元
    totalUseAmount: number;
    // 总用量：单位0.1立方米
    totalUseConsumption: number;
    // 当月用量，单位：0.1立方米
    curMonthUseConsumption: number;
    // 当月用量金额，单位：单位0.01元
    curMonthUseConsumptionAmount: number;
    // 透支金额，单位：单位0.01元
    overcharge: number;
    
    // 上11个月分别用量（依次为：上1月、上2月、......、上11月）每月占2字节，单位：0.1立方米
    last1MonthUseConsumption: number;
    last2MonthUseConsumption: number;
    last3MonthUseConsumption: number;
    last4MonthUseConsumption: number;
    last5MonthUseConsumption: number;
    last6MonthUseConsumption: number;
    last7MonthUseConsumption: number;
    last8MonthUseConsumption: number;
    last9MonthUseConsumption: number;
    last10MonthUseConsumption: number;
    last11MonthUseConsumption: number;
}
