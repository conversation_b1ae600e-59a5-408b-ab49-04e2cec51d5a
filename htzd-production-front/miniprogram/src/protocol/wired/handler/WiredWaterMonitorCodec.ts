import commonUtils from "../../../utils/CommonUtils";

/**
 * 有线水表协议公共包结构解析
 */
export class WiredWaterMonitorCodec {

    /**
     * 解析验证公共包结构
     * @param buffer
     * @return 验证结果
     */
    decode(buffer: ArrayBuffer): boolean {
        if (!buffer) {
            commonUtils.showToast('待解析的有线水表协议报文体格式不正确');
            return false;
        }
        let u8: Uint8Array = new Uint8Array(buffer);
        // 验证包头是否正确
        if (u8[0] != 0x68) {
            return false;
        }
        // 验证包尾是否正确
        if (u8[u8.byteLength - 1] != 0x16) {
            return false;
        }
        // 验证传输方向是否正确
        let ctrlCode: number = u8[9];
        if ((ctrlCode & 0x80) !== 0x80) {
            return false;
        }
        // 验证通讯是否正常
        if ((ctrlCode & 0x40) === 0x40) {
            return false;
        }
        // 取出数据域长度
        let dataLength: number = u8[10];
        // 取出数据域
        let data: Uint8Array = new Uint8Array(buffer, 11, u8.byteLength - 13);
        // 验证数据域长度是否正确
        if (dataLength !== data.length) {
            return false;
        }
        // 验证校验码
        let checkData: Uint8Array = new Uint8Array(buffer, 0, u8.byteLength - 2);
        let checkSum = checkData.reduce((a, b) => a + b) & 0xFF;
        if (checkSum != u8[u8.byteLength - 2]) {
            return false;
        }
        // 验证成功
        return true;
    }
}

export default new WiredWaterMonitorCodec();
