import {MeterWashValveInfo} from "./bean/MeterWashValveInfo";
import {NbAbstractMonitorProcess} from '../../../handler/NbAbstractMonitorProcess';
import {WaterCommandCode} from '../NbWaterCmdConstants';

/**
 * NB洗阀数据包解析
 */
export class NbWashValveProcess extends NbAbstractMonitorProcess {

    /**
     * 当前命令类型
     */
    private readonly commandCode: WaterCommandCode.CMD_16;

    /**
     * 洗阀命令响应内容
     */
    private meterWashValveInfo: MeterWashValveInfo;

    constructor(commandCode: WaterCommandCode.CMD_16) {
        super();
        this.commandCode = commandCode;
    }

    /**
     * 接收字节数组，完成NB协议解析
     * @param buffer NB协议报文体
     */
    process(buffer: ArrayBuffer): void {
        // 获取响应报文中的目标地址，用于验证是否是对应的设备返回
        let targetDeviceId = super.parseDeviceId(buffer, 12);
        super.setTargetDeviceId(targetDeviceId);
        this.meterWashValveInfo = new MeterWashValveInfo();
        let uint8Array: Uint8Array = new Uint8Array(buffer);
        // 判断报文中是否带有中继
        let isRelay: boolean = super.isRelay(buffer, 2);
        // 根据是否有中继调整读取数据域中的数据域的位置
        let dataIndex = isRelay ? 29 : 21;
        // 获取数据域
        let dataBuffer = new Uint8Array(buffer, dataIndex, 9);
        // 获取洗阀选项
        this.meterWashValveInfo.setWashValveOption(dataBuffer[0]);
        // 获取洗阀周期
        this.meterWashValveInfo.setWashValvePeriod((dataBuffer[4] << 24) | (dataBuffer[3] << 16) | (dataBuffer[2] << 8) | dataBuffer[1]);
        // 获取洗阀倒计时
        this.meterWashValveInfo.setWashValveCountDown((dataBuffer[8] << 24) | (dataBuffer[7] << 16) | (dataBuffer[6] << 8) | dataBuffer[5]);
        super.setResult(true);
    }

    getMeterWashValveInfo(): MeterWashValveInfo {
        return this.meterWashValveInfo;
    }

    public getCommandCode() {
        return this.commandCode;
    }
}
