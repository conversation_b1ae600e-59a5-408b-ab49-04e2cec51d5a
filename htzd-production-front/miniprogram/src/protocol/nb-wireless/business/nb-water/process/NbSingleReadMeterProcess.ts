import {NbAbstractMonitorProcess} from "../../../handler/NbAbstractMonitorProcess";
import {ReadingMeterDataInfo} from "./bean/ReadingMeterDataInfo";
import {WaterCommandCode} from "../NbWaterCmdConstants";
import protocolUtils from '../../../../ProtocolUtils';

/**
 * NB单播抄表帧数据包解析
 */
export class NbSingleReadMeterProcess extends NbAbstractMonitorProcess {

    /**
     * 当前命令类型
     */
    private readonly commandCode: WaterCommandCode.CMD_11;

    /**
     * 抄表数据信息
     */
    private readingMeterDataInfo: ReadingMeterDataInfo;

    constructor(commandCode: WaterCommandCode.CMD_11) {
        super();
        this.commandCode = commandCode;
    }

    /**
     * 接收字节数组，完成NB协议解析
     * @param buffer NB协议报文体
     */
    process(buffer: ArrayBuffer): void {
        // 获取响应报文中的目标地址，用于验证是否是对应的设备返回
        let targetDeviceId = super.parseDeviceId(buffer, 12);
        super.setTargetDeviceId(targetDeviceId);
        this.readingMeterDataInfo = new ReadingMeterDataInfo();
        let uint8Array: Uint8Array = new Uint8Array(buffer);
        // 判断报文中是否带有中继
        let isRelay: boolean = super.isRelay(buffer, 2);
        // 根据是否有中继调整读取数据域中的数据域的位置
        let dataIndex = isRelay ? 29 : 21;
        // 取出换算因子
        let factory = protocolUtils.getFactor(uint8Array[dataIndex]);
        // 设置换算因子即脉冲当量
        this.readingMeterDataInfo.setFactory(uint8Array[dataIndex]);
        // 取出表计数据
        let meterDataArray = buffer.slice(dataIndex + 1, dataIndex + 1 + 4);
        this.readingMeterDataInfo.setMeterData(protocolUtils.parseMeteringValueScaleNb(meterDataArray, factory, 4));
        // 取出反向流量
        let reverseFlowArray = buffer.slice(dataIndex + 1 + 4, dataIndex + 1 + 4 + 4);
        this.readingMeterDataInfo.setReverseFlow(protocolUtils.parseMeteringValueScaleNb(reverseFlowArray, factory, 4));
        // 取出阀门状态码
        let valveStateCode = uint8Array[dataIndex + 1 + 4 + 4];
        // 开关状态
        this.readingMeterDataInfo.setSwitchingState((valveStateCode & 0x01) != 0x01);
        // 阀门状态
        this.readingMeterDataInfo.setValveState((valveStateCode & 0x02) != 0x02);
        // 电池状态
        this.readingMeterDataInfo.setBatteryState((valveStateCode & 0x04) != 0x04);
        // 取出电池电压(转为V，保留两位小数)
        this.readingMeterDataInfo.setBatteryVoltage(Number((((uint8Array[dataIndex + 11] << 8)
            | uint8Array[dataIndex + 10]) / 1000).toFixed(2)));
        super.setResult(true);
    }

    getReadingMeterDataInfo(): ReadingMeterDataInfo {
        return this.readingMeterDataInfo;
    }

    public getCommandCode() {
        return this.commandCode;
    }
}
