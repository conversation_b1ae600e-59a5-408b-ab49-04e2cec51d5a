import { NbAbstractMonitorProcess } from "../../../handler/NbAbstractMonitorProcess";
import { HvCommandCode } from '../NbHvCmdConstants';

/**
 * NB阀门开度校准帧数据包解析
 */
export class NbValveCorrectProcess extends NbAbstractMonitorProcess {

    /**
     * 当前命令类型
     */
    private readonly commandCode: HvCommandCode.CMD_52;

    constructor(commandCode: HvCommandCode.CMD_52) {
        super();
        this.commandCode = commandCode;
    }

    /**
     * 接收字节数组，完成NB协议解析
     * @param buffer NB协议报文体
     */
    process(buffer: ArrayBuffer): void {
        // 获取响应报文中的目标地址，用于验证是否是对应的设备返回
        let targetDeviceId = super.parseDeviceId(buffer, 12);
        super.setTargetDeviceId(targetDeviceId);
        super.setResult(true);
    }

    public getCommandCode() {
        return this.commandCode;
    }
}
