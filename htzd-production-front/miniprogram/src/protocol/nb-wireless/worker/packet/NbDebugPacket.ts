import {NbAbstractPacket} from "../NbAbstractPacket";
import {CommandType, DeviceType, Dir} from "../../NbCmdConstants";

/**
 * NB水表无线协议报文结构体-调试帧结构
 */
export class NbDebugPacket extends NbAbstractPacket {

    /**
     * 控制字
     */
    ctrl: number = 0x00;

    /**
     * 命令码
     */
    commandCode: number;

    /**
     * 目标设备ID
     */
    targetDeviceId: string;

    /**
     * 发起设备ID
     */
    startDeviceId: string;

    /**
     * 调试帧数据域
     */
    debugData: Array<number>;

    /**
     * 设置命令码
     * @param commandCode
     */
    setCommandCode(commandCode: number) {
        this.commandCode = commandCode;
    }

    /**
     * 设置数据传输方向
     * @param dir
     */
    setDir(dir: Dir) {
        let value: number = dir.valueOf();
        this.ctrl = this.ctrl | ((value & 1) << 6);
    }

    /**
     * 设置设备类型
     * @param deviceType
     */
    setDeviceType(deviceType: DeviceType) {
        let value: number = deviceType.valueOf();
        this.ctrl = this.ctrl | ((value & 3) << 4);
    }

    /**
     * 设置帧类型
     * @param commandType
     */
    setCommandType(commandType: CommandType) {
        let value: number = commandType.valueOf();
        this.ctrl = this.ctrl | (value & 15);
    }

    /**
     * 设置目标设备ID
     * @param targetDeviceId
     */
    setTargetDeviceId(targetDeviceId: string) {
        this.targetDeviceId = targetDeviceId;
    }

    /**
     * 设置发起设备ID
     * @param startDeviceId
     */
    setStartDeviceId(startDeviceId: string) {
        this.startDeviceId = startDeviceId;
    }

    /**
     * 设置调试帧数据域
     * @param debugData
     */
    setDebugData(debugData: Array<number>) {
        this.debugData = debugData;
    }

    /**
     * 组装数据载荷
     * @return 数据载荷
     */
    protected buildPayload(): Array<number> {
        // 调试帧固定长度为19+数据域长度个字节
        let arrayBuffer: ArrayBuffer = new ArrayBuffer(19 + this.debugData.length);
        // 组装报文
        let uint8Array: Uint8Array = new Uint8Array(arrayBuffer);
        uint8Array[0] = this.ctrl;
        uint8Array[1] = this.commandCode;
        // 组装目标设备ID
        let targetDeviceIds: Array<number> = super.deviceIdToArrayAndReverse(this.targetDeviceId);
        targetDeviceIds.forEach((value, index) => {
            uint8Array[2 + index] = value;
        });
        // 组装发起设备ID
        let startDeviceIds: Array<number> = super.deviceIdToArrayAndReverse(this.startDeviceId);
        startDeviceIds.forEach((value, index) => {
            uint8Array[10 + index] = value;
        });
        // 设置数据域长度
        uint8Array[18] = this.debugData.length;
        // 组装数据域
        this.debugData.forEach((value, index) => {
           uint8Array[19 + index] = value;
        });
        // 将字节数组以Array<number>的形式返回，提供给父类作为调试帧数据载荷
        let results: Array<number> = [];
        uint8Array.forEach((value) => results.push(value));
        return results;
    }
}
