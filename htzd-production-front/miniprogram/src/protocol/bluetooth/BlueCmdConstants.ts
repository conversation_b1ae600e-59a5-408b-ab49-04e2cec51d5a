/**
 * 起始字符
 */
export const START_POS: number = 0x68;

/**
 * 结束字符
 */
export const END_POS: number = 0x16;

/**
 * 数据传输方向
 */
export enum Dir {

    /**
     * 下行方向
     */
    DOWN = 0x00,

    /**
     * 上行方向
     */
    UP = 0x01
}

/**
 * 中继标志
 */
export enum RelayMark {

    /**
     * 无中继
     */
    NO = 0x00,

    /**
     * 有中继
     */
    HAVE = 0x01
}

/**
 * 命令类型
 */
export enum CommandType {

    /**
     * 唤醒帧
     */
    WAKE = 0x01,

    /**
     * 命令帧
     */
    COMMAND = 0x02,

    /**
     * 上报帧
     */
    REPORT = 0x03,

    /**
     * 升级帧
     */
    UPGRADE = 0x04
}

/**
 * 命令码
 */
export enum CommandCode {

    /**
     * 数据透传下行命令码
     */
    TRANS_DOWN = 0x05,

    /**
     * 数据透传上行命令码
     */
    TRANS_UP = 0x06,

    /**
     * 唤醒应答命令码
     */
    WAKEUP = 0x07,

    /**
     * 无线调试下行命令码
     */
    DEBUG_DOWN = 0x08,

    /**
     * 无线调试上行命令码
     */
    DEBUG_UP = 0x88
}

/**
 * 设备类型
 */
export enum DeviceType {

    /**
     * 表节点
     */
    METER = 0x01,

    /**
     * 中继器
     */
    RELAY = 0x02,

    /**
     * 手抄机
     */
    MOBILE = 0x03,
}

/**
 无线信道取值-频率范围470.7MHz-473.1MHz
 信道索引    频点    说明
 0    470.7MHz    NB水表本地维护信道192kbps
 1    471.1MHz    普通工作信道192kbps/50kbps
 2    471.5MHz    无线打印调试使用 192kbps
 3    471.9MHz
 4    472.3MHz
 5    472.7MHz    唯一NB水表升级工作信道 50kbps
 6    473.1MHz    唯一NB电表工作信道50kbps
 */
export enum WirelessChannel {

    WC_1 = 0x01,
    WC_2 = 0x02,
    WC_3 = 0x03,
    WC_4 = 0x04
}

/**
 * 无线通信速率取值
 */
export enum WirelessRate {

    /**
     * 50k
     */
    WR_0,

    /**
     * 192k
     */
    WR_1,
}

/**
 * 调试状态
 */
export enum DebugState {

    /**
     * 开始调试
     */
    START,

    /**
     * 调试中
     */
    DEBUG,

    /**
     * 结束调试
     */
    END
}

/**
 * 红外波特率
 ZERO = 1200,
 ONE = 2400,
 TWO = 4800,
 THREE = 9600,
 FOUR = 19200,
 FIVE = 38400,
 SIX = 57600,
 SEVEN = 115200,
 */
export enum InfraredBaudRate {

    ZERO = 0x00,
    ONE = 0x01,
    TWO = 0x02,
    THREE = 0x03,
    FOUR = 0x04,
    FIVE = 0x05,
    SIX = 0x06,
    SEVEN = 0x07,
}
