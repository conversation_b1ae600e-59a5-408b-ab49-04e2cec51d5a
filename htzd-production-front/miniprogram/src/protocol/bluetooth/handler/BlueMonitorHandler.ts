import {CommandCode} from "../BlueCmdConstants";
import {TransUpProcess} from "./process/TransUpProcess";
import {WakeUpProcess} from "./process/WakeUpProcess";
import {DebugUpProcess} from "./process/DebugUpProcess";
import {BlueProcessResult} from "./BlueProcessResult";

/**
 * 解析蓝牙协议报文体，根据命令码调用不同的解析器
 */
export class BlueMonitorHandler {

    /**
     * 接收字节数组，完成蓝牙协议解析，返回数据载荷
     * @param arrayBuffer 蓝牙协议报文体
     * @return 剥离蓝牙协议后的报文中的内容载荷
     */
    handle(arrayBuffer: ArrayBuffer): BlueProcessResult {
        // 获取报文中的命令码
        let uint8Array: Uint8Array = new Uint8Array(arrayBuffer);
        let commandCode: number = uint8Array[2];
        // 根据命令码调用对应的协议解析器
        switch (commandCode) {
            // 数据透传上行命令码
            case CommandCode.TRANS_UP: {
                return new TransUpProcess().process(arrayBuffer);
            }
            // 唤醒应答命令码
            case CommandCode.WAKEUP: {
                return new WakeUpProcess().process(arrayBuffer);
            }
            // 无线调试上行命令码
            case CommandCode.DEBUG_UP: {
                return new DebugUpProcess().process(arrayBuffer);
            }
            default: {
                throw new Error('不支持的命令码类型');
            }
        }
    }
}

export default new BlueMonitorHandler();
