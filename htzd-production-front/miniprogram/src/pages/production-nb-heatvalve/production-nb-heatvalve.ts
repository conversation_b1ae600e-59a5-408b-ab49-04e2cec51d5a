import commonUtils from "../../utils/CommonUtils";
import {NbSession} from "../../protocol/nb-wireless/worker/NbSession";

import production from "../../mixins/production";
import channel from "../../mixins/channel"
/**
 * 产测tab页
 */
Page({
     /**
     * 混入
     */
    mixins: [production, channel],
    /**
     * 页面的初始数据
     */
    data: {
        settingValveTimeout: 0, // 阀控超时时间
        settingCommandTimeout: 0,
       
    },

    filter(name:string){
        return name.startsWith('HT');
    },

    onLoad() {
        // 初始化参数默认值
        this.setData({
            settingCommandTimeout: NbSession.getNormalTimeout() / 1000,
            settingValveTimeout: NbSession.getValveCtrlTimeout() / 1000
        });
    },

    /**
     * 设置弹窗按钮
     * @param e
     */
    settingBtnTap(e: any) {
        // 等于1为确定
        if (e.detail.index === 1) {
            //@ts-ignore
            if (!this.ifInputValue(this.data.settingValveTimeout) || !this.ifInputValue(this.data.settingCommandTimeout)) {
                commonUtils.showToast('值不能为空!');
                return;
            }
            NbSession.setNormalTimeout(this.data.settingCommandTimeout * 1000);
            NbSession.setValveCtrlTimeout(this.data.settingValveTimeout * 1000);
        } else {
            this.setData({
                settingCommandTimeout: NbSession.getNormalTimeout() / 1000,
                settingValveTimeout: NbSession.getValveCtrlTimeout() / 1000,
            });
        }
        //@ts-ignore
        this.settingToggle();
    },

});
