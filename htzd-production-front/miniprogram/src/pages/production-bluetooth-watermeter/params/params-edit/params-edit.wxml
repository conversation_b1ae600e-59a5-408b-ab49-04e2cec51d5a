<!--pages/production-bluetooth-watermeter/params/params-edit/params-edit.wxml-->
<view class="page-content">
    <mp-cell ext-class="my-cell item">
        <view class="input-wrap">
            <view class="label label-scan">模板名称</view>
            <input class="input" type="text" maxlength="32" model:value="{{name}}" />
        </view>
    </mp-cell>

    <mp-cell ext-class="my-cell item">
        <view class="input-wrap">
            <view class="label label-scan">是否启用</view>
            <view class="input" style="display: flex;align-items: center">
                <van-radio-group model:value="{{ enable }}" direction="horizontal">
                    <van-radio icon-size="40rpx" style="font-size: 32rpx;margin-right: 30rpx;" name="{{true}}" checked-color="#07c160">启用</van-radio>
                    <van-radio icon-size="40rpx" style="font-size: 32rpx" name="{{false}}" checked-color="#f44336">禁用</van-radio>
                </van-radio-group>
            </view>
        </view>
    </mp-cell>

    <mp-cell ext-class="my-cell item">
        <view class="input-wrap">
            <view class="label label-scan">自动产测</view>
            <view class="input" style="display: flex;align-items: center">
                <van-radio-group model:value="{{ autoTest }}" direction="horizontal">
                    <van-radio icon-size="40rpx" style="font-size: 32rpx;margin-right: 30rpx;" name="{{true}}" checked-color="#07c160">是</van-radio>
                    <van-radio icon-size="40rpx" style="font-size: 32rpx" name="{{false}}" checked-color="#f44336">否</van-radio>
                </van-radio-group>
            </view>
        </view>
    </mp-cell>

    <mp-cell ext-class="my-cell item">
        <view class="input-wrap">
            <view class="label label-scan">整数位数</view>
            <view class="input" style="display: flex;align-items: center">
                <picker bindchange="integerChange" model:value="{{integerIndex}}" range="{{integerNumber}}">
                    <view class="picker">
                        <view>{{integerIndex !== null ? integerNumber[integerIndex] : '请选择'}}</view>
                        <mp-icon class="icon icon-expand" icon="arrow" color="black" size="{{12}}"></mp-icon>
                    </view>
                </picker>
            </view>
        </view>
    </mp-cell>

    <mp-cell ext-class="my-cell item">
        <view class="input-wrap">
            <view class="label label-scan">脉冲当量</view>
            <view class="input" style="display: flex;align-items: center">
                <picker bindchange="pulseChange" model:value="{{pulseIndex}}" range="{{pulseArray}}">
                    <view class="picker">
                        <view>{{pulseIndex !== null ? pulseArray[pulseIndex] : '请选择'}}</view>
                        <mp-icon extClass="icon icon-expand" icon="arrow" color="black" size="{{12}}"></mp-icon>
                    </view>
                </picker>
            </view>
        </view>
    </mp-cell>

    <mp-cell ext-class="my-cell item" wx:if="{{!autoTest}}">
        <view class="input-wrap">
            <view class="label label-scan">自动写表号</view>
            <view class="input" style="display: flex;align-items: center">
                <van-radio-group model:value="{{ autoMeterCode }}" direction="horizontal">
                    <van-radio icon-size="40rpx" style="font-size: 32rpx;margin-right: 30rpx;" name="{{true}}" checked-color="#07c160">是</van-radio>
                    <van-radio icon-size="40rpx" style="font-size: 32rpx" name="{{false}}" checked-color="#f44336">否</van-radio>
                </van-radio-group>
            </view>
        </view>
    </mp-cell>

    <mp-cell ext-class="my-cell item" wx:if="{{!autoTest}}">
        <view class="input-wrap">
            <view class="label label-scan">自动开阀</view>
            <view class="input" style="display: flex;align-items: center">
                <van-radio-group model:value="{{ autoOpenValve }}" direction="horizontal">
                    <van-radio icon-size="40rpx" style="font-size: 32rpx;margin-right: 30rpx;" name="{{true}}" checked-color="#07c160">是</van-radio>
                    <van-radio icon-size="40rpx" style="font-size: 32rpx" name="{{false}}" checked-color="#f44336">否</van-radio>
                </van-radio-group>
            </view>
        </view>
    </mp-cell>

    <mp-cell ext-class="my-cell item">
        <view class="input-wrap">
            <view class="label label-scan">电压检测</view>
            <view class="input" style="display: flex;align-items: center">
                <van-radio-group model:value="{{ enableBatteryCheck }}" direction="horizontal">
                    <van-radio icon-size="40rpx" style="font-size: 32rpx;margin-right: 30rpx;" name="{{true}}" checked-color="#07c160">启用</van-radio>
                    <van-radio icon-size="40rpx" style="font-size: 32rpx" name="{{false}}" checked-color="#f44336">禁用</van-radio>
                </van-radio-group>
            </view>
        </view>
    </mp-cell>
    <mp-cell ext-class="my-cell item">
        <view class="input-wrap">
            <view class="label label-scan" style="width: 280rpx;">电压范围</view>
            <view class="range-wrap">
                <input class="input" type="digit" max="32" model:value="{{batteryLow}}" />
                ~
                <input class="input" type="digit" max="32" model:value="{{batteryHigh}}" />
            </view>
            
        </view>
    </mp-cell>

    <mp-cell ext-class="my-cell item">
        <view class="input-wrap">
            <view class="label label-scan">CSQ检测</view>
            <view class="input" style="display: flex;align-items: center">
                <van-radio-group model:value="{{ enableCsqCheck }}" direction="horizontal">
                    <van-radio icon-size="40rpx" style="font-size: 32rpx;margin-right: 30rpx;" name="{{true}}" checked-color="#07c160">启用</van-radio>
                    <van-radio icon-size="40rpx" style="font-size: 32rpx" name="{{false}}" checked-color="#f44336">禁用</van-radio>
                </van-radio-group>
            </view>
        </view>
    </mp-cell>
    <mp-cell ext-class="my-cell item">
        <view class="input-wrap">
            <view class="label label-scan" style="width: 280rpx;">CSQ范围</view>
            <view class="range-wrap">
                <input class="input" type="number" model:value="{{csqLow}}" />
                ~
                <input class="input" type="number" model:value="{{csqHigh}}" />
            </view>
        </view>
    </mp-cell>

    <mp-cell ext-class="my-cell item">
        <view class="input-wrap">
            <view class="label label-scan">启用功能</view>
            <view class="input" style="display: flex;align-items: center">
                <van-checkbox-group value="{{ enableFeaturesArr }}" direction="horizontal" bind:change="enableFeaturesChange">
                    <van-checkbox icon-size="40rpx" style="font-size: 32rpx;margin-right: 30rpx;" name="measuringConfig">计量配置</van-checkbox>
                </van-checkbox-group>
            </view>
        </view>
    </mp-cell>

    <button class="btn-primary" style="margin-top: 30rpx;" bindtap="save">保存</button>
</view>