var $table = $('#table');

function detailFormatter(index, row, element) {
  if (row.args) {
    return $.templates("#detailViewTmpl").render(row);
  }
  return null;
}

$(function () {
  $table.bootstrapTable({
    queryParams: function (params) {
      return $('#tableFilter').appendFilters(params);
    },
    onExpandRow: function (index, row, $detail) {
      if (!row.args) {
        $.get('args', {
          id: row.id
        }, function (data) {
          row.args = data.args;
          $($detail).html(detailFormatter(index, row));
        });
      }
    }
  });
});
