package com.acpiot.microservice.meterservice.client.onenet.sdk.dto.device;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BatchCreateDeviceReqDto {

    @Data
    public static class BatchCreateDeviceDto {
        /**
         * 设备名称，格式为英文字母、数字、短横线-和下划线_，长度不超过64位
         * 必填
         */
        @JsonProperty("device_name")
        private String deviceName;

        /**
         * LwM2M设备专有，当接入协议为LwM2M时必填，15位数字
         */
        private String imei;
        /**
         * LwM2M设备专有，必填，1-15位数字
         */
        private String imsi;

        /**
         * LwM2M设备专有，允许为空，当接入协议为LwM2M时该属性有效，格式为英文字母、数字，长度为8-16位，若不传/传空则平台自动生成一个随机字符串作为psk
         */
        private String psk;

        /**
         * LwM2M设备专有，允许为空，格式为英文字母、数字，长度不超过16位
         */
        @JsonProperty("auth_code")
        private String authCode;

        /**
         * 设备描述，长度不超过100位
         */
        private String desc;

        /**
         * 经度
         */
        private String lon;

        /**
         * 纬度
         */
        private String lat;
    }

    /**
     * 产品ID
     * 必填
     */
    @JsonProperty("product_id")
    private String productId;

    /**
     * LwM2M产品生效，若传入参数中imei有重复，是否对参数中imei不重复的剩余设备进行创建
     * 非必填
     */
    @JsonProperty("dup_continue")
    private boolean dupContinue;

    /**
     * 批量创建设备列表
     * 必填
     */
    @JsonProperty("device_list")
    private List<BatchCreateDeviceDto> devices;

}
