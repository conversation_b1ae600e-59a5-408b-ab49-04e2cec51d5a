package com.acpiot.microservice.meterservice.client.ctwing.watermeter.servicehandler.commandresponse;

import com.acpiot.microservice.meterservice.client.ctwing.handler.ServiceHandler;
import com.acpiot.microservice.meterservice.client.ctwing.watermeter.dto.WmValveCtrlResultDto;
import org.springframework.stereotype.Component;

import static java.lang.Byte.toUnsignedInt;

/**
 * 强制执行阀门控制响应解析
 * Created by moxin on 2022-05-25-0025
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Component
public class WmSID9009Handler implements ServiceHandler<WmValveCtrlResultDto> {
    @Override
    public WmValveCtrlResultDto resolve(byte[] payload) {
        return new WmValveCtrlResultDto(toUnsignedInt(payload[0]), toUnsignedInt(payload[1]));
    }
}
