package com.acpiot.microservice.meterservice.client.ctwing.msgreceiver;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.acpiot.microservice.meterservice.api.service.DeviceService;
import com.acpiot.microservice.meterservice.client.command.service.DeviceCommandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by moxin on 2023/8/25T10:31
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/push/ctwing")
public class MeterServiceMessageReceiver extends CtwingMessageHandler {

    public MeterServiceMessageReceiver(
            DeviceService deviceService,
            DeviceCommandService deviceCommandService,
            @Qualifier("ctwingMsgExecutor") ThreadPoolTaskExecutor ctwingMsgExecutor
    ) {
        super(deviceService, deviceCommandService, ctwingMsgExecutor);
    }

    @RequestMapping({"messageReceiver"})
    public void messageReceiver(@RequestBody String body) {
        handleAsync(body, s -> {
            TimeInterval timer = DateUtil.timer();
            try {
                log.info("收到HTTP推送消息：{}", s);
                handle(s);
            } catch (Exception e) {
                log.error("CTWing 消息处理异常", e);
            } finally {
                log.info("CTWing 消息处理耗时：{}", timer.intervalPretty());
            }
        });
    }

}
