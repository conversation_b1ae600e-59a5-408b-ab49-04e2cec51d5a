package com.acpiot.microservice.meterservice.client.onenet.wmmn316.resolver;

import com.acpiot.microservice.meterservice.client.onenet.CommandResolver;
import com.acpiot.microservice.meterservice.client.command.upgrade.dto.UpgradeResult;
import org.springframework.stereotype.Component;

import static com.acpiot.microservice.meterservice.client.onenet.util.OneNetProtocolUtil.resolve2UpgradeResult;

/**
 * 查询升级文件信息解析
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/27 21:12
 */
@Component
public class WmMn316C1DCmdResolver implements CommandResolver<UpgradeResult> {
    @Override
    public UpgradeResult resolve(byte[] bytes) {
        return resolve2UpgradeResult(bytes);
    }
}
