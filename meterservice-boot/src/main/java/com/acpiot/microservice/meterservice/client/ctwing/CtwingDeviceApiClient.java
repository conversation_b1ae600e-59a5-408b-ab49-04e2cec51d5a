package com.acpiot.microservice.meterservice.client.ctwing;

import com.acpiot.microservice.meterservice.api.dto.*;
import com.acpiot.microservice.meterservice.client.DeviceApiClient;
import com.acpiot.microservice.meterservice.client.ctwing.htwmudp.protocol.Packet;
import com.acpiot.microservice.meterservice.data.entity.Device;
import com.acpiot.microservice.meterservice.data.entity.DeviceCommand;
import com.acpiot.microservice.meterservice.data.enums.DeviceState;
import com.ctg.ag.sdk.biz.AepDeviceCommandClient;
import com.ctg.ag.sdk.biz.AepDeviceManagementClient;
import com.ctg.ag.sdk.biz.AepNbDeviceManagementClient;
import lombok.Getter;
import pers.mx.ctwing.sdk.client.DeviceCommandClient;
import pers.mx.ctwing.sdk.client.DeviceManagementClient;
import pers.mx.ctwing.sdk.dto.CtwingResult;
import pers.mx.ctwing.sdk.dto.device.*;
import pers.mx.ctwing.sdk.dto.devicecommand.CreateCommandReqDto;
import pers.mx.ctwing.sdk.dto.devicecommand.CreateCommandRspDto;
import pers.mx.ctwing.sdk.dto.devicecommand.TransparentCmdDto;
import pers.mx.ctwing.sdk.dto.product.ProductDto;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.util.HexUtil.encodeHexStr;
import static pers.mx.ctwing.sdk.dto.devicecommand.TransparentCmdDto.DATA_TYPE_HEX;

/**
 * Created by moxin on 2020-12-24-0024
 *
 * <AUTHOR> Email: <EMAIL>
 */
public class CtwingDeviceApiClient implements DeviceApiClient {

    private static final String OPERATOR_SYS_RESP = "sys_resp";

    private final DeviceManagementClient deviceManagementClient;
    @Getter
    private final DeviceCommandClient deviceCommandClient;

    /**
     * Instantiates a new Ctwing device api client.
     *
     * @param product                     the product
     * @param aepDeviceManagementClient   the aep device management client
     * @param aepNbDeviceManagementClient the aep nb device management client
     * @param aepDeviceCommandClient      the aep device command client
     */
    public CtwingDeviceApiClient(
            ProductDto product,
            AepDeviceManagementClient aepDeviceManagementClient,
            AepNbDeviceManagementClient aepNbDeviceManagementClient,
            AepDeviceCommandClient aepDeviceCommandClient
    ) {
        this.deviceManagementClient = new DeviceManagementClient(product, aepDeviceManagementClient, aepNbDeviceManagementClient);
        this.deviceCommandClient = new DeviceCommandClient(product, aepDeviceCommandClient);
    }

    private <T, R> Rest<R> toRest(CtwingResult<T> ctwingResult, Function<T, R> func) {
        if (ctwingResult.isSuccess()) {
            T t = ctwingResult.getResult();
            R r = null;
            if (func != null) {
                r = func.apply(t);
            }
            return Rest.success(r);
        }
        return Rest.error(ctwingResult.getCode(), ctwingResult.getMsg());
    }

    @Override
    public Rest<List<QueryDeviceStateApiOutDto>> queryDeviceStates(List<Device> devices) {
        ListDeviceInfoReqDto reqDto = new ListDeviceInfoReqDto();
        reqDto.setDeviceIdList(devices.stream().map(Device::getDeviceId).toList());
        CtwingResult<List<ListDeviceInfoRspDto>> ctwingResult = deviceManagementClient.listDeviceInfo(reqDto);
        return toRest(ctwingResult, list -> list.stream()
                .map(listDeviceInfoRspDto -> {
                    QueryDeviceStateApiOutDto outDto = new QueryDeviceStateApiOutDto();
                    outDto.setDeviceId(listDeviceInfoRspDto.getDeviceId());
                    outDto.setDeviceState(DeviceState.values()[listDeviceInfoRspDto.getDeviceStatus()]);
                    outDto.setOnline(listDeviceInfoRspDto.getNetStatus() == 1);
                    return outDto;
                })
                .collect(Collectors.toList()));
    }

    @Override
    public Rest<CreateDeviceApiOutDto> createDevice(DeviceDto deviceDto) {
        CreateDeviceReqDto reqDto = new CreateDeviceReqDto();
        reqDto.setDeviceName(deviceDto.getDeviceName());
        reqDto.setImei(deviceDto.getImei());
        reqDto.setOperator(deviceDto.getOperator());
        reqDto.setOther(new CreateDeviceReqDto.Other(0, deviceDto.getImsi(), null));
        CtwingResult<CreateDeviceRspDto> ctwingResult = deviceManagementClient.createDevice(reqDto);
        return toRest(ctwingResult, createDeviceRspDto -> {
            CreateDeviceApiOutDto outDto = new CreateDeviceApiOutDto();
            outDto.setDeviceId(createDeviceRspDto.getDeviceId());
            return outDto;
        });
    }

    @Override
    public Rest<List<BatchCreateDeviceApiOutDto>> batchCreateDevice(BatchCreateDeviceReqDto dto) {
        List<BatchCreateDeviceReqDto.DeviceInfo> deviceInfos = dto.getDeviceInfos();
        List<BatchCreateNBDeviceReqDto.Device> devices = deviceInfos.stream()
                .map(deviceInfo -> {
                    BatchCreateNBDeviceReqDto.Device device = new BatchCreateNBDeviceReqDto.Device();
                    device.setImei(deviceInfo.getImei());
                    device.setDeviceName(deviceInfo.getDeviceName());
                    device.setImsi(deviceInfo.getImsi());
                    return device;
                })
                .collect(Collectors.toList());
        BatchCreateNBDeviceReqDto reqDto = new BatchCreateNBDeviceReqDto();
        reqDto.setOperator(dto.getOperator());
        reqDto.setDevices(devices);
        CtwingResult<List<BatchCreateNBDeviceRspDto>> ctwingResult = deviceManagementClient.batchCreateDevices(reqDto);
        return toRest(ctwingResult, list -> list.stream()
                .map(rspDto -> {
                    BatchCreateDeviceApiOutDto outDto = new BatchCreateDeviceApiOutDto();
                    outDto.setResultCode(rspDto.getResultCode());
                    outDto.setDescription(rspDto.getDescription());
                    outDto.setDeviceId(rspDto.getDeviceId());
                    outDto.setImei(rspDto.getImei());
                    return outDto;
                })
                .collect(Collectors.toList()));
    }

    @Override
    public Rest<List<Device>> deleteDevices(List<Device> devices) {
        List<String> deviceIds = devices.stream().map(Device::getDeviceId).toList();
        CtwingResult<Void> ctwingResult = deviceManagementClient.deleteDeviceByPost(deviceIds);
        return toRest(ctwingResult, null);
    }

    @Override
    public Rest<Void> updateDevice(String deviceId, UpdateDeviceDto deviceDto) {
        UpdateDeviceReqDto reqDto = new UpdateDeviceReqDto();
        reqDto.setDeviceName(deviceDto.getDeviceName());
        reqDto.setOperator(deviceDto.getOperator());
        reqDto.setOther(new UpdateDeviceReqDto.Other(0, deviceDto.getImsi()));
        CtwingResult<Void> ctwingResult = deviceManagementClient.updateDevice(deviceId, reqDto);
        return toRest(ctwingResult, null);
    }

    /**
     * 创建命令.
     *
     * @param deviceId      the deviceId
     * @param deviceCommand the device command
     * @return the ctwing result
     */
    public CtwingResult<CreateCommandRspDto> createCommand(String deviceId, DeviceCommand deviceCommand) {
        CreateCommandReqDto reqDto = new CreateCommandReqDto();
        reqDto.setDeviceId(deviceId);
        reqDto.setOperator(deviceCommand.getOperator());
        reqDto.setContent(deviceCommand.getContent());
        reqDto.setTtl(deviceCommand.getTtl());
        return deviceCommandClient.createCommand(reqDto);
    }

    public CtwingResult<CreateCommandRspDto> createCommand(String deviceId, Packet packet) {
        CreateCommandReqDto reqDto = new CreateCommandReqDto();
        reqDto.setDeviceId(deviceId);
        reqDto.setOperator(OPERATOR_SYS_RESP);
        reqDto.setContent(new TransparentCmdDto(encodeHexStr(packet.toBytes(), false), DATA_TYPE_HEX));
        reqDto.setTtl(40);
        return deviceCommandClient.createCommand(reqDto);
    }

}
