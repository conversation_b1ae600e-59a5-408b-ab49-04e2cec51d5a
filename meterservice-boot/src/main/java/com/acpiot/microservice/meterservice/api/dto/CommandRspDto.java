package com.acpiot.microservice.meterservice.api.dto;

import cn.hutool.core.util.StrUtil;
import com.acpiot.microservice.meterservice.data.entity.DeviceCommand;
import com.acpiot.microservice.meterservice.data.enums.CommandStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by moxin on 2020-10-19-0019
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Schema(description = "命令响应参数模型")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommandRspDto {

    public static CommandRspDto of(DeviceCommand deviceCommand) {
        return new CommandRspDto(deviceCommand.getCacheId(),
                deviceCommand.getCommandStatus(), deviceCommand.getCommandDesc(), deviceCommand.getResultDetail());
    }

    /**
     * 命令ID
     */
    @Schema(description = "命令ID")
    private String commandId;

    /**
     * 命令状态
     */
    @Schema(description = "命令状态")
    private CommandStatus commandStatus;

    /**
     * 命令状态描述
     */
    @Schema(description = "命令状态描述")
    private String commandDesc;

    /**
     * 返回数据
     */
    @Schema(description = "响应数据，当命令状态为COMPLETED时该值才有意义")
    private Object data;

    /**
     * 命令状态描述
     */
    public String getCommandDesc() {
        if (StrUtil.isNotBlank(commandDesc)) {
            return commandDesc;
        }
        return commandStatus.getStatusDesc();
    }

}
