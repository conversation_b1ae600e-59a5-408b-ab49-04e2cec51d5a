package com.acpiot.microservice.meterservice.admin.service;

import com.acpiot.microservice.meterservice.api.dto.QueryDeviceStateApiOutDto;
import com.acpiot.microservice.meterservice.data.entity.Device;
import org.springframework.data.domain.Page;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.util.List;

/**
 * Created by moxin on 2021-07-22-0022
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface AdminDeviceService {
    Page<Device> pagedDevices(PagingQueryParams<Device> params);

    void deleteDevices(List<Device> devices);

    List<Device> getDevicesByIds(Long... ids);

    void syncDeviceStatus(List<QueryDeviceStateApiOutDto> outDtoList);
}
