package com.acpiot.microservice.meterservice.client.ctwing.htwmudp.command.resolver;

import com.acpiot.microservice.meterservice.client.ctwing.htwmudp.dto.data.C04Data;
import com.acpiot.microservice.meterservice.client.util.ByteBufferWrapper;
import org.springframework.stereotype.Component;

/**
 * 抄表指令应答解析
 * Created by moxin on 2023/7/28T17:16
 *
 * <AUTHOR>
 */
@Component
public class C04CommandResolver implements CommandResolver<C04Data> {
    @Override
    public C04Data resolver(ByteBufferWrapper buf) {
        C04Data data = new C04Data();
        buf.readMeterData(data);
        return data;
    }
}
