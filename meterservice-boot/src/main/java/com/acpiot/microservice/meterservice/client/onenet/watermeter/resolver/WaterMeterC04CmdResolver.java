package com.acpiot.microservice.meterservice.client.onenet.watermeter.resolver;

import com.acpiot.microservice.meterservice.client.onenet.CommandResolver;
import com.acpiot.microservice.meterservice.client.onenet.watermeter.dto.WmMeterData;
import com.acpiot.microservice.meterservice.client.util.ByteBufferWrapper;
import org.springframework.stereotype.Component;

import static com.acpiot.microservice.meterservice.client.onenet.watermeter.util.WmProtocolUtil.resolveMeterData;

/**
 * 抄表应答解析.
 *
 * <AUTHOR>
 */
@Component
public class WaterMeterC04CmdResolver implements CommandResolver<WmMeterData> {
    @Override
    public WmMeterData resolve(byte[] bytes) {
        WmMeterData meterData = new WmMeterData();
        resolveMeterData(ByteBufferWrapper.wrap(bytes), meterData);
        return meterData;
    }
}
