package com.acpiot.microservice.meterservice.client.ctwing.htwmudp.command.impl;

import com.acpiot.microservice.meterservice.client.ctwing.htwmudp.command.BaseCommand;
import com.acpiot.microservice.meterservice.client.ctwing.htwmudp.dto.param.C1CParam;
import lombok.RequiredArgsConstructor;

import static com.acpiot.microservice.meterservice.client.ctwing.htwmudp.protocol.ProtocolConstant.CMD_SET_PRE_METER_TEST;

/**
 * 设预置产测.
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class C1CCommand implements BaseCommand {

    private final C1CParam param;

    @Override
    public byte getCmd() {
        return CMD_SET_PRE_METER_TEST;
    }

    @Override
    public String getDesc() {
        return "设预置产测";
    }

    @Override
    public byte[] getData() {
        return param.toBytes();
    }

}
