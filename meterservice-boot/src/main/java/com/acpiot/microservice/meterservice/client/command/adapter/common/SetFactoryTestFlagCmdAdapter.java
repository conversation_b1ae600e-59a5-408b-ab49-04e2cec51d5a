package com.acpiot.microservice.meterservice.client.command.adapter.common;

import com.acpiot.microservice.meterservice.client.ctwing.AepService;
import com.acpiot.microservice.meterservice.client.command.adapter.CmdAdapter;
import com.acpiot.microservice.meterservice.client.command.adapter.CompactBinaryCmdStructure;
import com.acpiot.microservice.meterservice.client.command.adapter.ObjModelCmdStructure;
import com.acpiot.microservice.meterservice.client.dto.production.SetFactoryTestFlagParam;
import lombok.RequiredArgsConstructor;

import java.util.Map;

/**
 * 设置产测标志命令
 * Created by moxin on 2022-05-17-0017
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
public class SetFactoryTestFlagCmdAdapter extends CmdAdapter {

    private final AepService service;
    protected final SetFactoryTestFlagParam param;

    @Override
    protected ObjModelCmdStructure buildObjModelCmdStructure() {
        return new ObjModelCmdStructure() {
            @Override
            public String getServiceIdentifier() {
                return service.getServiceIdentifier();
            }

            @Override
            public Map<String, Object> getParams() {
                return Map.of("completionFlag", param.isCompleted() ? 1 : 0);
            }
        };
    }

    @Override
    protected CompactBinaryCmdStructure buildCompactBinaryCmdStructure() {
        return new CompactBinaryCmdStructure() {
            @Override
            public int getServiceId() {
                return service.getServiceId();
            }

            @Override
            public byte[] getPayload() {
                return new byte[]{(byte) (param.isCompleted() ? 1 : 0)};
            }
        };
    }
}
