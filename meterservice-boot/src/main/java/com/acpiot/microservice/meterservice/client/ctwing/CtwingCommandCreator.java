package com.acpiot.microservice.meterservice.client.ctwing;

import cn.hutool.core.lang.Assert;
import com.acpiot.microservice.meterservice.client.DeviceApiClientManager;
import com.acpiot.microservice.meterservice.client.command.AbstractCommandCreator;
import com.acpiot.microservice.meterservice.client.command.event.CommandChangedEvent;
import com.acpiot.microservice.meterservice.client.command.service.DeviceCommandService;
import com.acpiot.microservice.meterservice.data.entity.Device;
import com.acpiot.microservice.meterservice.data.entity.DeviceCommand;
import com.acpiot.microservice.meterservice.data.enums.IotPlatform;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import pers.mx.ctwing.sdk.dto.CtwingResult;
import pers.mx.ctwing.sdk.dto.devicecommand.CreateCommandRspDto;

import java.util.Objects;

@Slf4j
@RequiredArgsConstructor
@Component("ctwingCommandExecutor")
public class CtwingCommandCreator extends AbstractCommandCreator {

    private final DeviceApiClientManager deviceApiClientManager;
    private final DeviceCommandService deviceCommandService;
    @Qualifier("ctwingCmdExecutor")
    private final ThreadPoolTaskExecutor ctwingCmdExecutor;

    @Override
    protected IotPlatform getIotPlatform() {
        return IotPlatform.CTWING;
    }

    @Override
    protected ThreadPoolTaskExecutor getExecutor() {
        return ctwingCmdExecutor;
    }

    @Override
    protected void executeCmd(Device device, DeviceCommand deviceCommand) {
        CtwingDeviceApiClient deviceApiClient =
                (CtwingDeviceApiClient) deviceApiClientManager.getDeviceApiClient(device);
        Assert.notNull(deviceApiClient);

        CtwingResult<CreateCommandRspDto> result;
        try {
            result = deviceApiClient.createCommand(device.getDeviceId(), deviceCommand);
        } catch (Exception e) {
            log.error("CTWing创建命令异常", e);
            result = new CtwingResult<>(-1, "创建命令异常", null);
        }
        // 更新命令状态
        deviceCommandService.updateCommandStatus(Objects.requireNonNull(deviceCommand.getId()), result)
                .ifPresent(command -> applicationContext.publishEvent(
                        new CommandChangedEvent(IotPlatform.CTWING, CommandChangedEvent.EventData.of(device, command))));
    }
}
