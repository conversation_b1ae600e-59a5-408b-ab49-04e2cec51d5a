package com.acpiot.microservice.meterservice.admin.service;

import com.acpiot.microservice.meterservice.data.entity.Device;
import com.acpiot.microservice.meterservice.data.entity.ReportRecord;
import org.springframework.data.domain.Page;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.util.Date;

/**
 * Created by moxin on 2023/11/13T15:14
 *
 * <AUTHOR>
 */
public interface ReportRecordService {
    void addReportRecord(ReportRecord reportRecord, Device device);

    Page<ReportRecord> pagedReportRecord(PagingQueryParams<ReportRecord> params);

    void clearReportRecordBefore(Date date);

    boolean existsReportRecordBefore(Date date);

    void clearReportRecordBefore(Date date, int limit);
}
