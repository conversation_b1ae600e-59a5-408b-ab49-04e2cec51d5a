package com.acpiot.microservice.meterservice.client.onenet.watermeter.resolver;

import com.acpiot.microservice.meterservice.client.onenet.CommandResolver;
import com.acpiot.microservice.meterservice.client.onenet.watermeter.dto.WmMeterData;
import com.acpiot.microservice.meterservice.client.util.ByteBufferWrapper;
import org.springframework.stereotype.Component;

import static com.acpiot.microservice.meterservice.client.onenet.watermeter.util.WmProtocolUtil.resolveMeterData;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/27 21:00
 */
@Component
public class WaterMeterC07CmdResolver implements CommandResolver<WmMeterData> {

    @Override
    public WmMeterData resolve(byte[] bytes) {
        WmMeterData meterData = new WmMeterData();
        resolveMeterData(ByteBufferWrapper.wrap(bytes), meterData);
        return meterData;
    }
}
