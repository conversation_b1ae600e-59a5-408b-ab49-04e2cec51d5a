package com.acpiot.microservice.meterservice.client.command.adapter;

import com.acpiot.microservice.meterservice.client.ctwing.compactbinary.CmdType;
import com.acpiot.microservice.meterservice.client.ctwing.compactbinary.CompactBinaryPacket;

/**
 * 紧凑型二进制命令结构
 * Created by moxin on 2022-05-16-0016
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface CompactBinaryCmdStructure {

    byte[] USELESS_PAYLOAD = {0};

    int getServiceId();

    byte[] getPayload();

    default CompactBinaryPacket toCompactBinaryPacket(int taskId) {
        CompactBinaryPacket packet = new CompactBinaryPacket();
        packet.setCmdType(CmdType.CMD.val());
        packet.setServiceId(getServiceId());
        packet.setTaskId(taskId);
        packet.setPayload(getPayload());
        return packet;
    }

}
