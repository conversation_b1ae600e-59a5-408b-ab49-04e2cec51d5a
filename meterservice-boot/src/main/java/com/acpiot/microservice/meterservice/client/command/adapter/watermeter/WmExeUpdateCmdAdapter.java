package com.acpiot.microservice.meterservice.client.command.adapter.watermeter;

import com.acpiot.microservice.meterservice.client.command.upgrade.adapter.ExeUpdateCmdAdapter;
import com.acpiot.microservice.meterservice.client.command.upgrade.dto.ExeUpdateParam;
import com.acpiot.microservice.meterservice.client.command.adapter.OneNetCmdStructure;

import java.nio.ByteOrder;

import static cn.hutool.core.util.ByteUtil.intToBytes;
import static com.acpiot.microservice.meterservice.client.ctwing.watermeter.WaterMeterServiceCnst.Cmd.EXE_UPDATE;
import static com.acpiot.microservice.meterservice.client.onenet.watermeter.WaterMeterCmdConstant.CMD_EXEC_UPGRADE;

/**
 * 执行升级命令
 * Created by moxin on 2022-05-17-0017
 *
 * <AUTHOR> Email: <EMAIL>
 */
public class WmExeUpdateCmdAdapter extends ExeUpdateCmdAdapter {

    public WmExeUpdateCmdAdapter(ExeUpdateParam param) {
        super(EXE_UPDATE, param);
    }

    @Override
    protected OneNetCmdStructure buildOneNetCmdStructure() {
        return new OneNetCmdStructure() {
            @Override
            public byte getCmd() {
                return CMD_EXEC_UPGRADE;
            }

            @Override
            public byte[] getPayload() {
                return intToBytes(param.getSeq(), ByteOrder.LITTLE_ENDIAN);
            }
        };
    }
}
