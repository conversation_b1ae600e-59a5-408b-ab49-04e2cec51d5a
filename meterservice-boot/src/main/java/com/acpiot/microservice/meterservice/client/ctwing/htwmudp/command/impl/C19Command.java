package com.acpiot.microservice.meterservice.client.ctwing.htwmudp.command.impl;

import com.acpiot.microservice.meterservice.client.ctwing.htwmudp.command.BaseCommand;
import com.acpiot.microservice.meterservice.client.ctwing.htwmudp.dto.param.C19Param;
import lombok.RequiredArgsConstructor;

import static com.acpiot.microservice.meterservice.client.ctwing.htwmudp.protocol.ProtocolConstant.CMD_SET_REPORT_MODE;

/**
 * 设上报模式.
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class C19Command implements BaseCommand {

    private final C19Param param;

    @Override
    public byte getCmd() {
        return CMD_SET_REPORT_MODE;
    }

    @Override
    public String getDesc() {
        return "设上报模式";
    }

    @Override
    public byte[] getData() {
        return param.toBytes();
    }

}
