package com.acpiot.microservice.meterservice.client.command.adapter;

import pers.mx.ctwing.sdk.dto.devicecommand.CommandDto;

import java.util.Map;

/**
 * 物模型命令结构
 * Created by moxin on 2022-05-17-0017
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface ObjModelCmdStructure {

    Map<String, Object> USELESS_PARAM_DEFAULT = Map.of("DLflag", 0);

    Map<String, Object> USELESS_PARAM_CMD = Map.of("cmd", 0);

    String getServiceIdentifier();

    Map<String, Object> getParams();

    default CommandDto<Map<String, Object>> toCommandDto() {
        return new CommandDto<>(getServiceIdentifier(), getParams());
    }

}
