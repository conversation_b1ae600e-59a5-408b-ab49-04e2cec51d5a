package com.acpiot.microservice.meterservice.client.command.adapter.watermeter;

import com.acpiot.microservice.meterservice.client.onenet.protocol.Packet;
import com.acpiot.microservice.meterservice.client.command.adapter.CmdAdapter;
import com.acpiot.microservice.meterservice.client.command.adapter.CompactBinaryCmdStructure;
import com.acpiot.microservice.meterservice.client.command.adapter.ObjModelCmdStructure;
import com.acpiot.microservice.meterservice.client.command.adapter.OneNetCmdStructure;
import com.acpiot.microservice.meterservice.data.enums.CommandType;

import java.util.Map;

import static com.acpiot.microservice.meterservice.client.ctwing.watermeter.WaterMeterServiceCnst.Cmd.READ_MODE;
import static com.acpiot.microservice.meterservice.client.onenet.watermeter.WaterMeterCmdConstant.PKT_READ_NB_PARAMS;

/**
 * Created by moxin on 2022-05-20-0020
 *
 * <AUTHOR> Email: <EMAIL>
 */
public class ReadModeCmdAdapter extends CmdAdapter {
    @Override
    protected ObjModelCmdStructure buildObjModelCmdStructure() {
        return new ObjModelCmdStructure() {
            @Override
            public String getServiceIdentifier() {
                return READ_MODE.getServiceIdentifier();
            }

            @Override
            public Map<String, Object> getParams() {
                return USELESS_PARAM_DEFAULT;
            }
        };
    }

    @Override
    protected CompactBinaryCmdStructure buildCompactBinaryCmdStructure() {
        return new CompactBinaryCmdStructure() {
            @Override
            public int getServiceId() {
                return READ_MODE.getServiceId();
            }

            @Override
            public byte[] getPayload() {
                return USELESS_PAYLOAD;
            }
        };
    }

    @Override
    protected OneNetCmdStructure buildOneNetCmdStructure() {
        return new OneNetCmdStructure() {
            @Override
            public CommandType getCommandType() {
                return CommandType.WM_READ_MODE;
            }

            @Override
            public Packet toPacket() {
                return PKT_READ_NB_PARAMS;
            }
        };
    }
}
