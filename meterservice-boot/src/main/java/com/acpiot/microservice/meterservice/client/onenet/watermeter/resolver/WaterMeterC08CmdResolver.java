package com.acpiot.microservice.meterservice.client.onenet.watermeter.resolver;

import com.acpiot.microservice.meterservice.client.onenet.CommandResolver;
import com.acpiot.microservice.meterservice.client.onenet.watermeter.dto.WmCurveData;
import com.acpiot.microservice.meterservice.client.util.ByteBufferWrapper;
import org.springframework.stereotype.Component;

import static com.acpiot.microservice.meterservice.client.onenet.watermeter.util.WmProtocolUtil.resolveMeterData;

/**
 * 水表曲线数据解析
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/27 21:12
 */
@Component
public class WaterMeterC08CmdResolver implements CommandResolver<WmCurveData> {
    @Override
    public WmCurveData resolve(byte[] bytes) {
        WmCurveData curveData = new WmCurveData();
        ByteBufferWrapper buf = ByteBufferWrapper.wrap(bytes);
        resolveMeterData(buf, curveData);
        int len = buf.remaining();
        if (len > 0) {
            byte factor = buf.getByte();
            curveData.setFactor(factor);
            byte[] curveBytes = buf.getBytes(buf.remaining());
            curveData.setCurveBytes(curveBytes);
        }
        return curveData;
    }
}
