package com.acpiot.microservice.meterservice.aspect;

import cn.hutool.core.util.StrUtil;
import com.acpiot.microservice.meterservice.aspect.annotation.SystemLog;
import com.acpiot.microservice.meterservice.data.entity.Log;
import com.acpiot.microservice.meterservice.data.repository.LogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.dreamlu.mica.ip2region.core.Ip2regionSearcher;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;

/**
 * Created by moxin on 2020-12-30-0030
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class LogAspectService {

    private final LogRepository logRepository;
    private final Ip2regionSearcher ip2regionSearcher;

    /**
     * 记录操作日志
     *
     * @param joinPoint
     * @param log
     * @return
     */
    public void addLog(ProceedingJoinPoint joinPoint, Log log) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        SystemLog logAnnotation = method.getAnnotation(SystemLog.class);
        // 注解上的描述
        log.setDescription(logAnnotation.value());
        // 请求的方法名
        String methodName = String.format("%s.%s()", joinPoint.getTarget().getClass().getName(), signature.getName());
        log.setMethodName(methodName);

        StringBuilder argsBuilder = new StringBuilder("{");
        //参数值
        Object[] argValues = joinPoint.getArgs();
        //参数名称
        String[] argNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
        if (argValues != null) {
            for (int i = 0; i < argValues.length; i++) {
                argsBuilder.append(i > 0 ? ", " : " ")
                        .append(argNames[i]).append(": ")
                        .append(StrUtil.str(argValues[i], StandardCharsets.UTF_8));
            }
        }
        argsBuilder.append(" }");
        log.setArgs(argsBuilder.toString());

        try {
            String address = ip2regionSearcher.getAddressAndIsp(log.getIp());
            log.setAddress(address);
        } catch (Exception e) {
            LogAspectService.log.error("获取IP来源失败", e);
        }
        logRepository.save(log);
    }

}
