package com.acpiot.microservice.meterservice.client;

import com.acpiot.microservice.meterservice.client.exception.ClientException;
import com.acpiot.microservice.meterservice.client.onenet.OneNetDeviceApiInvoker;
import com.acpiot.microservice.meterservice.data.enums.CustomizedProduct;
import com.acpiot.microservice.meterservice.data.enums.DeviceType;
import com.acpiot.microservice.meterservice.data.enums.EdrxPeriod;
import com.acpiot.microservice.meterservice.data.enums.PowerSavingMode;
import com.acpiot.microservice.meterservice.data.model.DeviceModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by moxin on 2020-12-24-0024
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class DeviceApiClientManager {

    private final Map<String, DeviceApiClient> cached = new HashMap<>();
    private final OneNetDeviceApiInvoker oneNetDeviceApiInvoker;

    public DeviceApiClient getDeviceApiClient(DeviceModel deviceModel) {
        return switch (deviceModel.getIotPlatform()) {
            case CTWING -> cached.get(getDeviceModel(deviceModel));
            case ONENET -> oneNetDeviceApiInvoker;
        };
    }

    public void cachedApiClient(String name, DeviceApiClient deviceApiClient) {
        cached.put(name, deviceApiClient);
    }

    private String getDeviceModel(DeviceModel deviceModel) {
        if (deviceModel.getCustomizedProduct() != null) {
            return getCustomizedDeviceModel(deviceModel);
        }
        return getStandardDeviceModel(deviceModel);
    }

    /**
     * 获取标准产品型号
     *
     * @param deviceModel
     * @return
     */
    private String getStandardDeviceModel(DeviceModel deviceModel) {
        DeviceType deviceType = deviceModel.getDeviceType();
        PowerSavingMode powerSavingMode = deviceModel.getPowerSavingMode();
        EdrxPeriod edrxPeriod = deviceModel.getEdrxPeriod();
        boolean transparent = deviceModel.isTransparent();

        switch (deviceType) {
            case WATER_METER -> {
                if (powerSavingMode == PowerSavingMode.E_DRX
                        && edrxPeriod == EdrxPeriod.PERIOD_DEFAULT) {
                    if (transparent) {
                        throw new ClientException("水表不支持省电模式eDRX默认周期的透传方式");
                    }
                    return "HTNBWM001";
                }
                if (powerSavingMode == PowerSavingMode.PSM) {
                    if (transparent) {
                        throw new ClientException("水表不支持省电模式PSM下的透传方式");
                    }
                    return "HTNBWM002";
                }
                return transparent ? "HTNBWM005" : "HTNBWM003";
            }
            case ELECTRIC_METER -> {
                if (powerSavingMode == PowerSavingMode.DRX) {
                    if (transparent) {
                        throw new ClientException("电表不支持纯透传方式");
                    }
                    return "HTNBPM001";
                }
                throw new ClientException("电表不支持省电模式 {}", powerSavingMode);
            }
            case WM_COLLECTOR -> {
                if (powerSavingMode == PowerSavingMode.E_DRX
                        && edrxPeriod == EdrxPeriod.PERIOD_DEFAULT) {
                    throw new ClientException("水表采集器不支持省电模式eDRX下默认周期");
                }
                if (powerSavingMode == PowerSavingMode.PSM) {
                    throw new ClientException("水表采集器不支持省电模式PSM");
                }
                if (transparent) {
                    throw new ClientException("水表采集器不支持透传方式");
                }
                return "HTNBRPT001";
            }
            case HEAT_VALVE -> {
                if (powerSavingMode == PowerSavingMode.E_DRX
                        && edrxPeriod == EdrxPeriod.PERIOD_DEFAULT) {
                    throw new ClientException("水表采集器不支持省电模式eDRX下默认周期");
                }
                if (powerSavingMode == PowerSavingMode.PSM) {
                    throw new ClientException("水表采集器不支持省电模式PSM");
                }
                return transparent ? "HTNBHM002" : "HTNBHM001";
            }
            default -> throw new ClientException("不支持的设备类型：{}", deviceType);
        }
    }

    /**
     * 获取定制产品型号
     *
     * @param deviceModel
     * @return
     */
    private String getCustomizedDeviceModel(DeviceModel deviceModel) {
        DeviceType deviceType = deviceModel.getDeviceType();
        CustomizedProduct customizedProduct = deviceModel.getCustomizedProduct();
        throw new ClientException("设备类型 {} 定制产品 {} 不支持 CTWing 平台", deviceType, customizedProduct);
    }

}
