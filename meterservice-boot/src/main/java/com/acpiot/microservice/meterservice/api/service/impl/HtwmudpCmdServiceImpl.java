package com.acpiot.microservice.meterservice.api.service.impl;

import com.acpiot.microservice.meterservice.api.dto.CommandReqDto;
import com.acpiot.microservice.meterservice.api.dto.CommandRspDto;
import com.acpiot.microservice.meterservice.api.dto.Rest;
import com.acpiot.microservice.meterservice.api.service.HtwmudpCmdService;
import com.acpiot.microservice.meterservice.client.command.adapter.CmdAdapter;
import com.acpiot.microservice.meterservice.client.command.service.DeviceCommandService;
import com.acpiot.microservice.meterservice.client.command.upgrade.dto.ExeUpdateParam;
import com.acpiot.microservice.meterservice.client.command.upgrade.dto.ReadUpdateParam;
import com.acpiot.microservice.meterservice.client.command.upgrade.dto.UpdateDataParam;
import com.acpiot.microservice.meterservice.client.command.upgrade.dto.UpgradeInfo;
import com.acpiot.microservice.meterservice.client.ctwing.htwmudp.command.BaseCommand;
import com.acpiot.microservice.meterservice.client.ctwing.htwmudp.command.impl.*;
import com.acpiot.microservice.meterservice.client.ctwing.htwmudp.dto.param.*;
import com.acpiot.microservice.meterservice.client.exception.ClientException;
import com.acpiot.microservice.meterservice.data.enums.DeviceType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.function.Function;

/**
 * Created by moxin on 2024/1/9T16:54
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class HtwmudpCmdServiceImpl implements HtwmudpCmdService {

    private final DeviceCommandService deviceCommandService;

    private <T> Rest<CommandRspDto> createOrCacheCommand(CommandReqDto<T> reqDto, Function<T, BaseCommand> mapper) {
        return deviceCommandService.createOrCacheCommand(reqDto, new CmdAdapter() {
            @Override
            protected BaseCommand buildHtwmudpCmd() {
                return mapper.apply(reqDto.getContent());
            }
        }, DeviceType.WATER_METER, null);
    }

    @Override
    public Rest<CommandRspDto> readCat1SoftVer(CommandReqDto<Void> reqDto) {
        return createOrCacheCommand(reqDto, aVoid -> C01Command.INSTANCE);
    }

    @Override
    public Rest<CommandRspDto> readCat1Param(CommandReqDto<Void> reqDto) {
        return createOrCacheCommand(reqDto, aVoid -> C02Command.INSTANCE);
    }

    @Override
    public Rest<CommandRspDto> readMcuParam(CommandReqDto<Void> reqDto) {
        return createOrCacheCommand(reqDto, aVoid -> C03Command.INSTANCE);
    }

    @Override
    public Rest<CommandRspDto> metering(CommandReqDto<Void> reqDto) {
        return createOrCacheCommand(reqDto, aVoid -> C04Command.INSTANCE);
    }

    @Override
    public Rest<CommandRspDto> serviceValve(CommandReqDto<C05Param> reqDto) {
        return createOrCacheCommand(reqDto, C05Command::new);
    }

    @Override
    public Rest<CommandRspDto> forceValve(CommandReqDto<C06Param> reqDto) {
        return createOrCacheCommand(reqDto, C06Command::new);
    }

    @Override
    public Rest<CommandRspDto> readCurve(CommandReqDto<C09Param> reqDto) {
        return createOrCacheCommand(reqDto, C09Command::new);
    }

    @Override
    public Rest<CommandRspDto> clearCurve(CommandReqDto<Void> reqDto) {
        return createOrCacheCommand(reqDto, aVoid -> C0ACommand.INSTANCE);
    }

    @Override
    public Rest<CommandRspDto> setReportInterval(CommandReqDto<C10Param> reqDto) {
        return createOrCacheCommand(reqDto, C10Command::new);
    }

    @Override
    public Rest<CommandRspDto> setMeterCode(CommandReqDto<C11Param> reqDto) {
        return createOrCacheCommand(reqDto, C11Command::new);
    }

    @Override
    public Rest<CommandRspDto> setBaseValue(CommandReqDto<C12Param> reqDto) {
        return createOrCacheCommand(reqDto, C12Command::new);
    }

    @Override
    public Rest<CommandRspDto> setMonthlyInvoiceDay(CommandReqDto<C13Param> reqDto) {
        return createOrCacheCommand(reqDto, C13Command::new);
    }

    @Override
    public Rest<CommandRspDto> setFuncVal(CommandReqDto<C14Param> reqDto) {
        return createOrCacheCommand(reqDto, C14Command::new);
    }

    @Override
    public Rest<CommandRspDto> setDailyFlowThreshold(CommandReqDto<C15Param> reqDto) {
        return createOrCacheCommand(reqDto, C15Command::new);
    }

    @Override
    public Rest<CommandRspDto> setInstantFlowThreshold(CommandReqDto<C16Param> reqDto) {
        return createOrCacheCommand(reqDto, C16Command::new);
    }

    @Override
    public Rest<CommandRspDto> setMeteringParam(CommandReqDto<C17Param> reqDto) {
        return createOrCacheCommand(reqDto, C17Command::new);
    }

    @Override
    public Rest<CommandRspDto> setIp(CommandReqDto<C18Param> reqDto) {
        return createOrCacheCommand(reqDto, C18Command::new);
    }

    @Override
    public Rest<CommandRspDto> setReportMode(CommandReqDto<C19Param> reqDto) {
        return createOrCacheCommand(reqDto, C19Command::new);
    }

    @Override
    public Rest<CommandRspDto> setValveClosedReportParam(CommandReqDto<C1AParam> reqDto) {
        return createOrCacheCommand(reqDto, C1ACommand::new);
    }

    @Override
    public Rest<CommandRspDto> setBaseParam(CommandReqDto<C1BParam> reqDto) {
        return createOrCacheCommand(reqDto, C1BCommand::new);
    }

    @Override
    public Rest<CommandRspDto> preMeterTest(CommandReqDto<C1CParam> reqDto) {
        C1CParam param = reqDto.getContent();
        if (param.isCodeSetting()) {
            if (param.getCodeParam() == null) {
                throw new ClientException("表号参数不能为空");
            }
        }
        if (param.isBaseSetting()) {
            if (param.getBaseParam() == null) {
                throw new ClientException("基表参数不能为空");
            }
        }
        return createOrCacheCommand(reqDto, C1CCommand::new);
    }

    @Override
    public Rest<CommandRspDto> setEdrxPeriod(CommandReqDto<C1DParam> reqDto) {
        return createOrCacheCommand(reqDto, C1DCommand::new);
    }

    @Override
    public Rest<CommandRspDto> setInstantFlowParam(CommandReqDto<C1EParam> reqDto) {
        return createOrCacheCommand(reqDto, C1ECommand::new);
    }

    @Override
    public Rest<CommandRspDto> readInstantFlowParam(CommandReqDto<Void> reqDto) {
        return createOrCacheCommand(reqDto, aVoid -> C1FCommand.INSTANCE);
    }

    @Override
    public Rest<CommandRspDto> rebootCat1(CommandReqDto<Void> reqDto) {
        return createOrCacheCommand(reqDto, aVoid -> C25Command.INSTANCE);
    }

    @Override
    public Rest<CommandRspDto> enableUpgrade(CommandReqDto<Void> reqDto) {
        return createOrCacheCommand(reqDto, aVoid -> C50Command.INSTANCE);
    }

    @Override
    public Rest<CommandRspDto> disableUpgrade(CommandReqDto<Void> reqDto) {
        return createOrCacheCommand(reqDto, aVoid -> C51Command.INSTANCE);
    }

    @Override
    public Rest<CommandRspDto> readUpgradeChannel(CommandReqDto<Void> reqDto) {
        return createOrCacheCommand(reqDto, aVoid -> C52Command.INSTANCE);
    }

    @Override
    public Rest<CommandRspDto> setDiffValue(CommandReqDto<C43Param> reqDto) {
        return createOrCacheCommand(reqDto, C43Command::new);
    }

    //<editor-fold desc="升级命令">
    @Override
    public Rest<CommandRspDto> startUpdate(CommandReqDto<UpgradeInfo> reqDto) {
        return createOrCacheCommand(reqDto, C20Command::new);
    }

    @Override
    public Rest<CommandRspDto> updateData(CommandReqDto<UpdateDataParam> reqDto) {
        return createOrCacheCommand(reqDto, C21Command::new);
    }

    @Override
    public Rest<CommandRspDto> readUpdate(CommandReqDto<ReadUpdateParam> reqDto) {
        return createOrCacheCommand(reqDto, C22Command::new);
    }

    @Override
    public Rest<CommandRspDto> exeUpdate(CommandReqDto<ExeUpdateParam> reqDto) {
        return createOrCacheCommand(reqDto, C23Command::new);
    }

    @Override
    public Rest<CommandRspDto> readUpdateInfo(CommandReqDto<Void> reqDto) {
        return createOrCacheCommand(reqDto, aVoid -> C24Command.INSTANCE);
    }

    @Override
    public Rest<CommandRspDto> readVersion(CommandReqDto<Void> reqDto) {
        return createOrCacheCommand(reqDto, aVoid -> C04Command.INSTANCE);
    }
    //</editor-fold>
}
