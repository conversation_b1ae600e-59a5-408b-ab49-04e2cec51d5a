package com.acpiot.microservice.meterservice.client.command.cmdexecutor;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import com.acpiot.microservice.meterservice.client.command.event.DeviceCmdTimeoutEvent;
import com.acpiot.microservice.meterservice.data.entity.Device;
import com.acpiot.microservice.meterservice.data.entity.DeviceCommand;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * Created by moxin on 2022-05-06-0006
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Component
public class DeviceCmdManager {

    private static final int DEFAULT_CACHE_TIME = 2 * 60_000 + 10_000;

    private final TimedCache<String, DeviceCommand> cmdCache;

    public DeviceCmdManager(ApplicationContext applicationContext) {
        this.cmdCache = CacheUtil.newTimedCache(DEFAULT_CACHE_TIME);
        this.cmdCache.setListener((key, deviceCommand) -> {
            // 通知当前命令结束，执行下一条命令
            if (!deviceCommand.getCommandStatus().isFinished()) {
                applicationContext.publishEvent(new DeviceCmdTimeoutEvent(deviceCommand.getDevice()));
            }
        });
        this.cmdCache.schedulePrune(50);
    }

    public DeviceCommand getCmd(String imei) {
        return cmdCache.get(imei);
    }

    public void removeCmd(String imei) {
        cmdCache.remove(imei);
    }

    public boolean containsCmd(String imei) {
        return cmdCache.containsKey(imei);
    }

    public void updateCommand(Device device, DeviceCommand deviceCommand) {
        deviceCommand.setDevice(device);
        cmdCache.put(device.getImei(), deviceCommand);
    }

    public void putCmd(Device device, DeviceCommand deviceCommand) {
        deviceCommand.setDevice(device);
        cmdCache.put(device.getImei(), deviceCommand, calcCacheTime(deviceCommand.getTtl()));
    }

    private static long calcCacheTime(Integer ttl) {
        long timeout;
        if (ttl == null) {
            timeout = 7200_000L;
        } else {
            timeout = ttl * 1000L;
        }
        return timeout + 125_000L;
    }

}
