package com.acpiot.microservice.meterservice.data.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.data.annotation.CreatedBy;

/**
 * 系统日志
 * Created by moxin on 2019-09-29-0029
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(indexes = {
        @Index(columnList = "logType,createdDate"),
        @Index(columnList = "createdDate")
})
@SecondaryTable(name = "log_details", pkJoinColumns = @PrimaryKeyJoinColumn(name = "log_id"))
@DynamicInsert
@DynamicUpdate
public class Log extends BaseAuditingEntity {

    /**
     * 日志描述
     */
    @Column(nullable = false)
    private String description;

    /**
     * 日志类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private LogType logType;

    /**
     * 调用方法名称
     */
    @Column
    private String methodName;

    /**
     * 方法参数
     */
    @Lob
    @Column(columnDefinition = "longtext", table = "log_details")
    private String args;

    /**
     * 异常信息
     */
    @Lob
    @Column(columnDefinition = "longtext", table = "log_details")
    private String exceptionMsg;

    /**
     * 用户代理
     */
    @Column(length = 512)
    private String userAgent;

    /**
     * ip地址
     */
    @Column(length = 64)
    private String ip;

    /**
     * 地址
     */
    @Column
    private String address;

    /**
     * 耗时
     */
    @Column(nullable = false)
    private int costTime;

    /**
     * 创建者
     */
    @CreatedBy
    @Column(nullable = false, length = 64)
    private String createdBy;

    public Log(LogType logType, int costTime) {
        this.logType = logType;
        this.costTime = costTime;
    }
}
