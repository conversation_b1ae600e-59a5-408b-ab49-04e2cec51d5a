package com.acpiot.microservice.meterservice.client.ctwing.htwmudp.dto.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 7日冻结数据.
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class C0BData extends C04Data {

    /**
     * 7日冻结正向脉冲数
     */
    private Long[] forwardValArr;

    /**
     * 7日冻结反向脉冲数
     */
    private Long[] backwardValArr;

    /**
     * 获取7日冻结数据
     *
     * @return
     */
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    public BigDecimal[] getFrozenDataArr() {
        BigDecimal[] frozenDataArr = new BigDecimal[7];
        for (int i = 0; i < forwardValArr.length; i++) {
            Long forwardVal = forwardValArr[i];
            Long backwardVal = backwardValArr[i];
            if (forwardVal == null
                    || backwardVal == null) {
                continue;
            }
            frozenDataArr[i] = calcMeterVal(forwardVal - backwardVal);
        }
        return frozenDataArr;
    }

}
