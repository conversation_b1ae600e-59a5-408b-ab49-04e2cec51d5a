package com.acpiot.microservice.meterservice.admin.service;

import com.acpiot.microservice.meterservice.data.entity.Log;
import com.acpiot.microservice.meterservice.data.projections.LogProjections;
import org.springframework.data.domain.Page;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.util.Date;
import java.util.Optional;

/**
 * Created by moxin on 2021-09-02-0002
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface LogService {
    Page<LogProjections.Log> pagedInfoLog(PagingQueryParams<Log> params);

    Page<LogProjections.Log> pagedErrorLog(PagingQueryParams<Log> params);

    void clearLogsBefore(Date date);

    boolean existsLogBefore(Date date);

    void clearLogsBefore(Date date, int limit);

    Optional<LogProjections.Args> getLogArgs(Long id);

    Optional<LogProjections.Exception> getLogException(Long id);
}
