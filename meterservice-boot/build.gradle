plugins {
    id 'org.springframework.boot'
    id 'org.hibernate.orm'
    id 'org.hidetake.ssh'
}

jar {
    enabled = false
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    annotationProcessor('org.springframework.boot:spring-boot-configuration-processor')

    implementation 'org.thymeleaf.extras:thymeleaf-extras-springsecurity6'
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
    implementation 'org.webjars:webjars-locator-core'

    // 数据库操作相关依赖
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'io.hypersistence:hypersistence-utils-hibernate-63'
//    runtimeOnly('org.mariadb.jdbc:mariadb-java-client')
    runtimeOnly('com.mysql:mysql-connector-j')

    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-hibernate6'

    implementation 'com.github.gavlyukovskiy:p6spy-spring-boot-starter'

    // CtWing接口相关依赖
    implementation 'pers.mx.ctwing:ctwing-spring-boot-starter'

    // Forest
    implementation 'com.dtflys.forest:forest-spring-boot3-starter'

    // hutool工具类库
    implementation 'cn.hutool:hutool-core'
    implementation 'cn.hutool:hutool-cache'
    implementation 'cn.hutool:hutool-crypto'
    implementation 'cn.hutool:hutool-poi'
    implementation "org.apache.poi:poi-ooxml"
    implementation "cn.hutool:hutool-extra"

    implementation 'net.dreamlu:mica-ip2region'

    // MQTT
    implementation 'org.springframework.boot:spring-boot-starter-integration'
    implementation 'org.springframework.integration:spring-integration-mqtt'

    // API接口文档依赖
    implementation 'com.github.xiaoymin:knife4j-openapi3-jakarta-spring-boot-starter'

    // jjwt
    implementation "io.jsonwebtoken:jjwt-api"
    runtimeOnly "io.jsonwebtoken:jjwt-impl", "io.jsonwebtoken:jjwt-jackson"

    implementation 'pers.mx.jupiter:jupiter-jpa'
    implementation 'pers.mx.jupiter:jupiter-web'
    implementation 'pers.mx.jupiter:jupiter-common'
    // 定时任务
    implementation 'pers.mx.scheduler:scheduler-spring-boot-starter'
    // API版本化
    implementation 'pers.mx.version:version-spring-boot-starter'
    // 文件存储
    implementation 'pers.mx.fileserver:fileserver-spring-boot-starter'

    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.hibernate.orm:hibernate-jpamodelgen'

    // QueryDSL
    implementation 'com.querydsl:querydsl-jpa:5.1.0:jakarta'
    annotationProcessor 'com.querydsl:querydsl-apt:5.1.0:jakarta'
    annotationProcessor 'jakarta.persistence:jakarta.persistence-api'
    annotationProcessor 'jakarta.annotation:jakarta.annotation-api'

    runtimeOnly 'org.webjars.bowergithub.jquery:jquery-dist'
    runtimeOnly('org.webjars:bootstrap') {
        exclude group: 'org.webjars', module: 'jquery'
    }
    runtimeOnly 'org.webjars.npm:bootstrap-icons'
    runtimeOnly 'org.webjars:font-awesome'
    runtimeOnly 'org.webjars.bowergithub.wenzhixin:bootstrap-table'
    runtimeOnly 'org.webjars.bowergithub.hhurz:tableexport.jquery.plugin'
    runtimeOnly('org.webjars.bowergithub.craftpip:jquery-confirm') {
        exclude group: 'org.webjars.bowergithub.jquery', module: 'jquery-dist'
    }
    runtimeOnly('org.webjars.bowergithub.jquery-validation:jquery-validation') {
        transitive = false
    }
    runtimeOnly 'org.webjars.bowergithub.borismoore:jsrender'

    testImplementation 'cn.hutool:hutool-http'
    testCompileOnly('org.projectlombok:lombok')
    testAnnotationProcessor('org.projectlombok:lombok')
}

hibernate {
    enhancement
}

remotes {
    webServer {
        host = '************'
        user = 'moxin'
        password = 'YRum%&gt;0IXC[@W&amp;j}'
        knownHosts = allowAnyHosts
    }
}
tasks.register('deploy') {
    doLast {
        ssh.run {
            session(remotes.webServer) {
                def remoteDir = '/home/<USER>/app/meterservice/'
                def localFile = bootJar.archiveFile.get().asFile

                remove remoteDir + localFile.name
                put from: localFile, into: remoteDir
                execute 'sh ' + remoteDir + 'restart-admin.sh'
            }
        }
    }
}
