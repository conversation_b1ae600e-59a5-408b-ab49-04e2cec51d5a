import {Inject, Injectable} from '@angular/core';
import {HttpService} from "@lib";
import {HttpClient} from "@angular/common/http";
import {Observable} from "rxjs";

/**
 * 微信用户相关接口
 */
@Injectable({
    providedIn: 'root'
})
export class WxUserService extends HttpService {

    constructor(public http: HttpClient, @Inject('apiUrl') public apiUrl) {
        super(http, apiUrl);
    }

    /**
     * 根据authCode注册微信用户，如果已经注册则直接返回对应的openid
     * @param appid
     * @param authCode
     */
    registerWxUserByAuthCode(appid, authCode): Observable<any>  {
        return this.postBodyAndParams('green/registerWxUserByAuthCode', null, {appid, authCode});
    }
}
