import {Component, Inject, OnInit, ViewChild} from '@angular/core';
import {Day<PERSON><PERSON>zeData, Operator, PaginationComponent} from "@lib";
import {IonItem} from "@ionic/angular";
import {HttpClient} from "@angular/common/http";
import {NativeService} from "../../services/native.service";
import {BillService} from "../../services/bill.service";
import * as moment from "moment";

@Component({
    selector: 'app-day-freeze-data',
    templateUrl: './day-freeze-data.component.html',
    styleUrls: ['./day-freeze-data.component.scss'],
})
export class DayFreezeDataComponent extends PaginationComponent<DayFreezeData> implements OnInit {

    emptyShow: boolean = false;
    customPickerOptions: any;
    currentYear: string;
    billYear: any;
    totalAmount: number;
    @ViewChild(IonItem, {static: true}) ionItem: any;
    date: any;

    constructor(public http: HttpClient, @Inject('apiUrl') public apiUrl,
                public nativeService: NativeService, public billService: BillService) {
        super(http, apiUrl + 'dayFreezeData/pageDayFreezeData', 20);
    }

    ngOnInit() {
    }

    ionViewWillEnter() {
        this.nativeService.showLoading('加载中...').then(() => {
            this.pagination.reload().then(() => {
                this.emptyShow = this.pagination.content.length === 0;
                this.nativeService.hideLoading();
            });
        });
    }

    dateChange($event: CustomEvent) {
        this.nativeService.showLoading('加载中...').then(() => {
            if ($event.detail.value) {
                this.pagination.addFilter('freezeDate', moment($event.detail.value).format("YYYY-MM-DD"), Operator.eq, true);
            } else {
                this.pagination.removeFilter('freezeDate');
            }
            this.pagination.reload().then(() => {
                this.emptyShow = this.pagination.content.length === 0;
                this.nativeService.hideLoading();
            });
        });
    }
}
