import { Component, Inject, OnInit } from '@angular/core';
import { Operator, PaginationComponent } from "@lib";
import { HttpClient } from "@angular/common/http";
import { NavController } from "@ionic/angular";
import { NativeService } from "../../services/native.service";
import { PayIncomeService } from "../../services/payIncome.service";
import * as moment from "moment";

@Component({
    selector: 'app-pay-incomes',
    templateUrl: './pay-incomes.component.html',
    styleUrls: ['./pay-incomes.component.scss'],
})
export class PayIncomesComponent extends PaginationComponent<any> implements OnInit {

    emptyShow: boolean = false;
    customPickerOptions: any;
    currentMonth: any;
    totalAmount: number;

    constructor(public http: HttpClient, @Inject('apiUrl') public apiUrl,
                public nav: NavController, public nativeService: NativeService,
                public payIncomeService: PayIncomeService) {
        super(http, apiUrl + 'payIncome/pagePayIncome');
    }

    ngOnInit() {
        this.currentMonth = moment().format('YYYY-MM');
        this.customPickerOptions = {
            buttons: [{
                text: '取消',
                handler: () => {
                    return;
                }
            }, {
                text: '确定',
                handler: (value) => {
                    this.currentMonth = value.year.text + "-" + value.month.text;
                    this.findPayIncomeByMonth();
                }
            }]
        };
        const firstDay = moment(this.currentMonth).startOf('month').toDate();
        const endDay = moment(this.currentMonth).endOf('month').toDate();
        this.pagination.setSort('payTime', 'desc');
        this.pagination.addFilter('createdDate', firstDay, Operator.ge, false);
        this.pagination.addFilter('createdDate', endDay, Operator.le, false);
        this.nativeService.showLoading('加载中...').then(() => {
            this.payIncomeService.getTotalAmountByMonth(this.currentMonth).subscribe(result => {
                this.totalAmount = result;
            });
            this.pagination.reload().then(() => {
                this.emptyShow = this.pagination.content.length === 0;
                this.nativeService.hideLoading();
            });
        });
    }

    findPayIncomeByMonth() {
        this.nativeService.showLoading('加载中...').then(() => {
            this.payIncomeService.getTotalAmountByMonth(this.currentMonth).subscribe(result => {
                this.totalAmount = result;
            });
            const firstDay = moment(this.currentMonth).startOf('month').toDate();
            const endDay = moment(this.currentMonth).endOf('month').toDate();
            this.pagination.clearFilters();
            this.pagination.addFilter('payTime', firstDay, Operator.ge, false);
            this.pagination.addFilter('payTime', endDay, Operator.le, false);
            this.pagination.reload().then(() => {
                this.emptyShow = this.pagination.content.length === 0;
                this.nativeService.hideLoading();
            });
        });
    }

    goBack() {
        this.nav.back();
    }

}
