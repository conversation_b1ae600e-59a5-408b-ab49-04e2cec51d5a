import {Component, OnInit} from '@angular/core';
import {AccountNoDataInfo, PayCustomerInfo} from "@lib";
import {AccountService} from "../../services/account.service";
import {CustomerService} from "../../services/customer.service";

@Component({
    selector: 'app-balance-detail',
    templateUrl: './balance-detail.component.html',
    styleUrls: ['./balance-detail.component.scss'],
})
export class BalanceDetailComponent implements OnInit {
    /**
     * 当前登录的户号相关信息
     */
    payCustomerInfo: PayCustomerInfo;

    /**
     * 当前登录的户号下设备的数据
     */
    accountNoDataInfo: AccountNoDataInfo;

    constructor(private accountService: AccountService, private customerService: CustomerService) {
    }

    ionViewWillEnter() {
        this.accountService.getPayCustomerInfo().then(data => this.payCustomerInfo = data);
    }

    ngOnInit() {
        this.customerService.getWaterMeterDataInfo().subscribe(data => this.accountNoDataInfo = data);
    }
}
