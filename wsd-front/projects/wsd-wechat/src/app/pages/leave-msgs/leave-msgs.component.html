<ion-content (click)="closeReply()">
    <div class="message-list">
        <div *ngFor="let advisory of advisories" class="message-group">
            <!-- <div class="line"> <span>{{advisory.createdDate.split(' ')[0]}}</span></div> -->
            <div class="message-item message-item--issue">
                <img class="avatar" *ngIf="payCustomerInfo.headImgUrl" [src]="payCustomerInfo.headImgUrl" alt="">
                <img class="avatar" *ngIf="!payCustomerInfo.headImgUrl" src="assets/imgs/avatar.png" alt="">
                <div>
                    <span class="message-item-time">{{advisory.createdDate}}</span>
                    <div class="message-bubble">{{advisory.content}}</div>
                </div>
            </div>
            <div *ngIf="advisory.advisoryReplies && advisory.advisoryReplies.length>0" class="issue-reply">
                <div  *ngFor="let reply of advisory.advisoryReplies" class="message-item">
                    <img *ngIf="reply.wxUser" class="avatar" [src]="payCustomerInfo.headImgUrl" alt="">
                    <img *ngIf="!reply.wxUser" class="avatar" src="assets/imgs/<EMAIL>" alt="">
                    <div>
                        <span class="message-item-time">{{reply.createdDate}}</span>
                        <div class="message-bubble">{{reply.replyContent}}</div>
                    </div>
                </div>

            </div>
            <div class="add-reply-wrap">
                <div class="add-reply" (click)="replyRespondent(advisory)">回复</div>
            </div>
        </div>
    </div>

    <empty *ngIf="emptyShow && advisories.length == 0" [info]="'暂无咨询记录'"></empty>

    <ion-fab vertical="bottom" horizontal="right" slot="fixed" class="export-fab">
        <ion-fab-button color="primary" routerLink="/main/add-leave-msg">留言</ion-fab-button>
    </ion-fab>
</ion-content>

<ion-footer *ngIf="isShow" class="comment-ion-footer">
    <ion-item lines="none">
        <ion-textarea id="reply" maxlength="256" autoGrow="true" rows="1" placeholder="请输入回复内容" [(ngModel)]="replyContent"></ion-textarea>
        <ion-button (click)="sendReply()">提交</ion-button>

    </ion-item>
</ion-footer>

