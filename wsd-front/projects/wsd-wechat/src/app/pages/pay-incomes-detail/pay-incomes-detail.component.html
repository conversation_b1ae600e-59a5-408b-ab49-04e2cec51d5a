<ion-content>
    <ion-card>
        <ion-card-header>
            <div class="heade-pic"><img src="./assets/imgs/jiaofeixiang<PERSON>@3x.png" alt=""></div>

            <ion-card-title>+{{payIncome?.originAmount}}</ion-card-title>
            <ion-card-subtitle>
                <span>{{'PayIncomeStatus_' + payIncome?.incomeStatus | translate}}</span>
                <img *ngIf="payIncome?.incomeStatus ==='PAY_SUCCESS' " class="status-icon" src="./assets/imgs/<EMAIL>" alt="">
                <img *ngIf="payIncome?.incomeStatus ==='UNPAID' " class="status-icon" src="./assets/imgs/<EMAIL>" alt="">
                <img *ngIf="payIncome?.incomeStatus ==='PAY_FAILED' " class="status-icon" src="./assets/imgs/<EMAIL>" alt="">
            </ion-card-subtitle>
        </ion-card-header>
        <ion-card-content>
            <div class="gap gap-l"></div>
            <div class="gap gap-r"></div>
            <ion-list>
                <ion-item>
                    <ion-label>实际缴费金额</ion-label>
                    <ion-note slot="end">{{payIncome?.amount}}</ion-note>
                </ion-item>
                <ion-item>
                    <ion-label>已退款金额</ion-label>
                    <ion-note slot="end">{{payIncome?.refundAmount || 0}}</ion-note>
                </ion-item>
                <ion-item>
                    <ion-label>户号</ion-label>
                    <ion-note slot="end">{{payIncome?.measuringPoint.accountNo}}</ion-note>
                </ion-item>
                <ion-item>
                    <ion-label>户主</ion-label>
                    <ion-note slot="end">{{payIncome?.customer.name}}</ion-note>
                </ion-item>
                <ion-item>
                    <ion-label>小区名称</ion-label>
                    <ion-note slot="end">{{payIncome?.measuringPoint.community.name}}</ion-note>
                </ion-item>
                <ion-item>
                    <ion-label>缴费方式</ion-label>
                    <ion-note slot="end">{{'PayIncomeType_' + payIncome?.incomeType | translate}}</ion-note>
                </ion-item>
                <ion-item>
                    <ion-label>订单生成时间</ion-label>
                    <ion-note slot="end">{{payIncome?.createdDate}}</ion-note>
                </ion-item>
                <ion-item *ngIf="payIncome?.incomeStatus === 'PAY_SUCCESS'">
                    <ion-label>订单支付时间</ion-label>
                    <ion-note slot="end">{{payIncome?.payTime}}</ion-note>
                </ion-item>
                <ion-item>
                    <ion-label>商户订单号</ion-label>
                    <ion-note slot="end">{{payIncome?.payCode}}</ion-note>
                </ion-item>
                <ion-item *ngIf="payIncome?.incomeStatus === 'PAY_SUCCESS'">
                    <ion-label>微信交易单号</ion-label>
                    <ion-note slot="end">{{payIncome?.orderCode}}</ion-note>
                </ion-item>
                <ion-item *ngIf="payIncome?.tradeStateDesc">
                    <ion-label>交易状态</ion-label>
                    <ion-note slot="end">{{payIncome.tradeStateDesc}}</ion-note>
                </ion-item>
                <ion-item>
                    <ion-label>备注</ion-label>
                    <ion-note slot="end">{{payIncome?.remark}}</ion-note>
                </ion-item>
            </ion-list>

        </ion-card-content>
    </ion-card>

    <div *ngIf="payIncome?.incomeStatus === 'PAY_SUCCESS' &&
                payIncome?.payTimeValid && !payIncome?.refunding &&
                (payIncome?.incomeType.includes('WECHAT') || payIncome?.incomeType === 'SXY_RESERVE') && payIncome?.amount > 0" style="text-align: center;
            color: #2196F3;
            font-size: 0.15rem;
            padding-top: 10px;
            text-decoration: underline;" (click)="presentPopover()">
        申请退款
    </div>

    <ng-container *ngIf="refundList.length">
        <div style="font-size: 0.16rem;padding: 0.16rem 0.16rem 0.1rem;">退款申请记录</div>
        <ng-container *ngFor="let item of refundList">
            <ion-card style="margin-top: 0.0rem!important;margin-bottom: 0.1rem!important;" >
                <ion-card-content style="padding: 0.1rem 0.16rem;">
                    <ion-item>
                        <ion-label>申请时间</ion-label>
                        <ion-note slot="end">{{item.createdDate}}</ion-note>
                    </ion-item>
                    <ion-item>
                        <ion-label>退款金额</ion-label>
                        <ion-note slot="end">{{item.amount}}</ion-note>
                    </ion-item>
                    <ion-item>
                        <ion-label>审核状态</ion-label>
                        <ion-note slot="end">
                            <ng-container [ngSwitch]="item.applyStatus">
                                <span *ngSwitchCase="'AUDIT_SUCCESS'" class="ion-color-success">审核通过</span>
                                <span *ngSwitchCase="'AUDIT_FAIL'" class="ion-color-danger">审核不通过</span>
                                <span *ngSwitchCase="'REFUNDING'" class="ion-color-warning">退款中</span>
                                <span *ngSwitchDefault class="ion-color-warning">待审核</span>
                            </ng-container>
                        </ion-note>
                    </ion-item>
                    <ion-item *ngIf="item.applyStatus === 'AUDIT_FAIL'">
                        <ion-label>审核备注</ion-label>
                        <ion-note slot="end">{{item.failReason}}</ion-note>
                    </ion-item>
                </ion-card-content>
            </ion-card>
        </ng-container>
    </ng-container>
</ion-content>

