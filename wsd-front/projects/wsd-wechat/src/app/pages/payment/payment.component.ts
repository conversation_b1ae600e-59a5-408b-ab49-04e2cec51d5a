import {Component, OnDestroy, OnInit} from '@angular/core';
import {PaymentService} from "../../services/payment.service";
import {Router} from "@angular/router";
import {PayCustomerInfo} from "@lib";
import {AccountService} from "../../services/account.service";
import {AlertController, Events, PopoverController} from "@ionic/angular";
import {NativeService} from "../../services/native.service";
import {PayTestComponent} from '../../components/pay-test/pay-test.component';

@Component({
    selector: 'app-payment',
    templateUrl: './payment.component.html',
    styleUrls: ['./payment.component.scss'],
})
export class PaymentComponent implements OnInit {
    payTestFlag = 0;

    /**
     * 手工输入的充值金额
     */
    amount: any = '100';
    /**
     * 最小充值金额
     */
    min: number = 10;
    /**
     * 最大充值金额
     */
    max: number = 10000;

    amountList: number[] = [];

    /**
     * 当前登录的户号相关信息
     */
    payCustomerInfo: PayCustomerInfo;

    constructor(private paymentService: PaymentService, private router: Router,
                private accountService: AccountService, public alertController: AlertController,
                private nativeService: NativeService, private events: Events,
                private popoverController: PopoverController) {
    }

    ionViewWillEnter() {
        this.accountService.getPayCustomerInfo().then(data => {
            this.payCustomerInfo = data;
        });
    }

    ngOnInit() {
        // 获取起充金额
        this.paymentService.getPaymentMinMaxAmount().subscribe(data => {
            this.min = data.minAmount;
            this.max = data.maxAmount;
            let array = [10, 30, 50, 100, 200, 500, 800, 1000, 2000, 5000, 10000];
            this.amountList =  array.filter(item => {
                return item <= data.maxAmount && item >= data.minAmount;
            }).splice(0, 3);
            this.amount = `${this.min}`;
        });
    }
    async confirmPay() {
        let amount =  Number(this.amount) ;
        if ( amount % 10 !== 0) {
            this.nativeService.showToast("充值金额必须为¥10的整数倍");
            return;
        }
        if (amount > this.max) {
            this.nativeService.showToast("充值金额不能大于¥" + this.max);
            return;
        }
        if (amount < this.min) {
            this.nativeService.showToast("充值金额不能小于¥" + this.min);
            return;
        }
        const alert = await this.alertController.create({
            cssClass: 'my-custom-class',
            header: '确认充值',
            subHeader: '',
            message: '将为您提交支付请求并创建预支付订单，2小时内未完成支付预支付订单将失效，是否确认充值？',
            buttons: [
                {
                    text: '取消',
                    cssClass: '',
                    handler: (blah) => {
                        console.log('取消');
                    }
                },
                {
                    text: '确定',
                    cssClass: 'color-red',
                    handler: (blah) => {
                        this.pay(amount);
                    }
                },
            ]
        });
        await alert.present();
        await alert.onDidDismiss();
    }

    pay(amount) {
        this.paymentService.requestRechargePay(amount).subscribe(params => {
            this.paymentService.pay(params, () => {
                // 充值成功，重新计算当前户号的账户余额和可用余额，并更新缓存
                let data = {
                    accountNo: this.payCustomerInfo.accountNo
                };
                this.events.publish('balanceChange', data);
                this.router.navigate(['/main/pay-incomes']);
            }, () => {
                console.log('充值失败');
            });
        });
    }

    /**
     * 支付测试 0.01 用于验证微信支付配置是否成功
     * */
    async payTest() {
        this.payTestFlag++;
        if (this.payTestFlag === 10) {
            const popover = await this.popoverController.create({
                component: PayTestComponent,
                componentProps: {},
                backdropDismiss: false,
                cssClass: 'refund-popover',
                translucent: true
            });
            popover.onDidDismiss().then((res) => {
                if (res.data) {
                    this.paymentService.requestRechargePay(res.data.amount).subscribe(params => {
                        this.paymentService.pay(params, () => {
                            // 充值成功，重新计算当前户号的账户余额和可用余额，并更新缓存
                            let data = {
                                accountNo: this.payCustomerInfo.accountNo
                            };
                            this.events.publish('balanceChange', data);
                            this.router.navigate(['/main/pay-incomes']);
                        }, () => {
                            console.log('充值失败');
                        });
                    });
                }
            });
            return await popover.present();
        }
    }


}
