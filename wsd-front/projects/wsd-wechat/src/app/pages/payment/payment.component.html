<!--
  Generated template for the AdvisoriesPage page.

  See http://ionicframework.com/docs/components/#navigation for more info on
  Ionic pages and navigation.
-->

<ion-content class="payment-component">
    <ion-card class="info-card bg-full">
        <ion-row>
            <ion-col class="lable">缴费户名：</ion-col>
            <ion-col class="text">{{payCustomerInfo?.houseHolder}}</ion-col>
        </ion-row>
        <ion-row>
            <ion-col class="lable">缴费户号：</ion-col>
            <ion-col class="text">{{payCustomerInfo?.accountNo}}</ion-col>
        </ion-row>
        <ion-row>
            <ion-col class="lable">表号：</ion-col>
            <ion-col class="text">{{payCustomerInfo?.meterCode}}</ion-col>
        </ion-row>
        <ion-row>
            <ion-col class="lable">关联编号：</ion-col>
            <ion-col class="text">{{payCustomerInfo?.linkCode || ''}}</ion-col>
        </ion-row>
        <ion-row>
            <ion-col class="lable">手机号：</ion-col>
            <ion-col class="text">{{payCustomerInfo?.mobile || ''}}</ion-col>
        </ion-row>
        <ion-row>
            <ion-col class="lable">可用余额(元)：</ion-col>
            <ion-col class="text">{{payCustomerInfo?.realBalance}}</ion-col>
        </ion-row>
    </ion-card>
    <ion-card class="amount-card">
        <ng-container *ngIf="amountList.length">
            <ion-card-header (click)="payTest()">选择充值额度</ion-card-header>
            <ion-card-content  style="margin-bottom: 0.2rem">
                <ion-grid>
                    <ion-row>
                        <ion-col *ngFor="let a of amountList" (click)="amount = a">
                            <ion-button [ngClass]="{'active':amount == a }">{{a}}<span>元</span></ion-button>
                        </ion-col>
                    </ion-row>
                </ion-grid>
            </ion-card-content>
        </ng-container>
        <ion-card-header>输入金额</ion-card-header>
        <ion-card-content class="amount">
            <ion-input type="number" [(ngModel)]="amount" placeholder="请输入充值金额"></ion-input>
        </ion-card-content>
        <p class="msg">限制：充值金额{{min}}~{{max}}，且为10的整数倍！</p>
        <p *ngIf="payCustomerInfo?.ownerPayPoundage" class="msg">注意：包含微信支付服务费<span>{{(amount * payCustomerInfo?.chargeRate).toFixed(2)}}</span>元，
            费率为 {{payCustomerInfo?.chargeRate * 100}}%</p>
        <p class="msg">注意：请认真核对您的充值户号和充值金额，充值成功后若要退款将无法退还交易手续费</p>
    </ion-card>
    <ion-row class="button-wrap">
        <ion-button expand="block" (click)="confirmPay()">立即充值</ion-button>
        <span routerLink="/main/pay-incomes">缴费记录</span>
    </ion-row>
</ion-content>
