import {Pipe, PipeTransform} from '@angular/core';
import {ValidationErrors} from '@angular/forms';

@Pipe({
    name: 'errorMessage'
})
export class ErrorMessagePipe implements PipeTransform {

    transform(errors: ValidationErrors, args?: any): any {
        for (const key in errors) {
            return this.formatError(key, args);
        }
    }

    formatError(key, args?) {
        if (args) {
            const msg = args[key];
            if (msg) {
                return msg;
            }
        }
        switch (key) {
            case 'required':
                return '必填项';
            case 'maxlength':
                return '字数过长';
            case 'minlength':
                return '长度不够';
            case 'pattern':
                return '不符要求';
            default:
                return '';
        }
    }

}
