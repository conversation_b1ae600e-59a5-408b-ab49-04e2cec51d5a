package com.acpiot.wsd.pay.wxpay.properties;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 微信支付相关参数
 **/
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class WxPayProperties implements Serializable {

    /**
     * 是否启用微信支付能力
     */
    private boolean enable;

    /**
     * 公众号appid
     */
    private String appId;

    /**
     * 微信商户号
     */
    private String mchId;

    /**
     * 商户apiV3密钥
     */
    private String apiV3Key;

    /**
     * 商户私钥
     */
    private String privateKey;

    /**
     * 商户证书序列号
     */
    private String mchSerialNo;

    /**
     * 微信支付公钥ID
     */
    private String publicKeyId;

    /**
     * 微信支付公钥
     */
    private String publicKey;

    /**
     * 微信支付商户的创建配置时间，全新商户需采用新的公钥模式验签，过渡阶段商户采用平滑模式，判断机制：在特定时间之后创建的为全新商户
     */
    private Date createdDate;

    /**
     * 支付回调方法地址
     */
    private String notifyUrl = "https://www.acp-iot.com/wsd-ctwing/revenue/payment/wxCallback";

    /**
     * 退款回调方法地址
     */
    private String refundNotifyUrl = "https://www.acp-iot.com/wsd-ctwing/revenue/payment/wxRefundCallback";

    /*
     * 微信Native扫码支付统一下单url
     */
    private String scanCodeUnifiedOrderUrl = "https://api.mch.weixin.qq.com/v3/pay/transactions/native";

    /**
     * 微信jsapi支付统一申请退款url
     */
    private String jsapiUnifiedRefundUrl = "https://api.mch.weixin.qq.com/v3/refund/domestic/refunds";

    /**
     * 微信jsapi支付统一下单url
     */
    private String jsapiUnifiedOrderUrl = "https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi";

    /**
     * 微信支付订单号查询订单url
     */
    private String queryOrderUrlByTransactionId = "https://api.mch.weixin.qq.com/v3/pay/transactions/id/{transaction_id}";

    /**
     * 商户订单号查询订单url
     */
    private String queryOrderUrlByOutTradeNo = "https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/{out_trade_no}";
}
