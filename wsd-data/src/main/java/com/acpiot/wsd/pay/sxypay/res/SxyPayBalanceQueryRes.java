package com.acpiot.wsd.pay.sxypay.res;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 微信调起Jsapi支付所需参数结构体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SxyPayBalanceQueryRes {

    /**
     * 商户编号
     */
    private String merchantId;

    /**
     * 时间戳
     */
    private String dateTime;

    /**
     * 请求状态
     */
    private String status;

    /**
     * 余额信息
     */
    private AccountList accountList;

    /**
     * 余额信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccountList {

        // 账户全部余额，单位为该币种的最小单位（日元为元，其余为分）
        private String balance;
        // 账户可用余额，可结算、转账、退款、汇款等，单位为该币种的最小单位（日元为元，其余为分）
        private String availableBalance;
        // 不可用金额，单位为该币种的最小单位（日元为元，其余为分）
        private String transitFund;
        // 查询币种
        private String currency;
    }
}
