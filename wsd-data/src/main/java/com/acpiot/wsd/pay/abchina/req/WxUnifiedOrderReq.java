package com.acpiot.wsd.pay.abchina.req;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 农行-微信支付-统一下单请求参数结构体
 */
@Data
@NoArgsConstructor
public class WxUnifiedOrderReq {

    @NotBlank
    private String TrxType;            //必填，交易码，长度30
    @NotBlank
    private String CommodityType;      //必填，商品类型，详情参考表格下面类型说明，长度4
    @NotBlank
    private String PaymentType;        //必填，支付账户类型，8：微信支付，长度1
    @NotBlank
    private String PaymentLinkType;    //必填，支付交易渠道，1：internet网络接入 2：手机网络接入 3:数字电视网络接入 4:智能客户端 5：线下渠道，长度1
    @NotBlank
    private String NotifyType;         //必填，支付结果通知方式，0：仅URL页面通知 1：服务器通知和URL页面通知，长度1
    @NotBlank
    private String ResultNotifyURL;    //必填，商户接收支付结果通知地址，商户自己填写，长度512
    @NotNull
    private Order Order;               //必填，订单信息域
    /**
     * 以下字段选填
     **/
    private String MerchantRemarks;    //设定附言
    private String MerModelFlag;       //是否为大商户模式
    private String SubMerchantID;      //分行大商户模式的子商户号
    private String SubMerNo;           //大商户模式子商户号
    private String IsBreakAccount;     //设定交易是否支持向二级商户入账
    private String SplitAccTemplate;   //分账模版编号

    @Data
    @NoArgsConstructor
    public static class Order {
        @NotBlank
        private String PayTypeID;           //必填，设定微信支付类型
        @NotBlank
        private String OrderDate;           //必填，设定订单日期 （YYYY/MM/DD）
        @NotBlank
        private String OrderTime;           //必填，设定订单时间 （HH:MM:SS）
        @NotBlank
        private String OrderNo;             //必填，设定订单编号，长度60
        @NotBlank
        private String CurrencyCode;        //必填，设定交易币种
        @NotBlank
        private String OrderAmount;         //必填，设定交易金额，长度15
        @NotBlank
        private String AccountNo;           //必填，设定支付账户，刷卡支付时，请上送授权码；公众号、小程序、APP支付时，请上送应用的APPID
        @NotBlank
        private String OpenID;              //必填，设定用户在子商户公众号下面的ID，公众号、小程序支付时，请上送用户在微信的用户标示openid
        @NotBlank
        private String OrderDesc;           //必填，设定订单说明，长度100
        /**
         * 以下字段选填
         **/
        private String orderTimeoutDate;    //设定订单有效期
        private String SubsidyAmount;       //设定补贴金额
        private String ExpiredDate;         //设定过期时间
        private String ReceiverAddress;     //收货地址
        private String BuyIP;               //IP
        private String TerminalNo;          //设备终端号
        private String LimitPay;            //限制贷记卡
        private String SplitAccInfoItems;   //平台商户当前分账情况
    }
}
