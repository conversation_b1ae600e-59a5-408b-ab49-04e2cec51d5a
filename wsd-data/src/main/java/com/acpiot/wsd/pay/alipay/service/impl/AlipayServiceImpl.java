package com.acpiot.wsd.pay.alipay.service.impl;

import com.acpiot.wsd.pay.alipay.Factory;
import com.acpiot.wsd.pay.alipay.properties.AliPayProperties;
import com.acpiot.wsd.pay.alipay.req.FaceToFaceUnifiedOrderReq;
import com.acpiot.wsd.pay.alipay.service.AlipayService;
import com.acpiot.wsd.pay.alipay.utils.AliPayUtils;
import com.alipay.easysdk.payment.common.models.AlipayTradeQueryResponse;
import com.alipay.easysdk.payment.facetoface.models.AlipayTradePrecreateResponse;
import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 支付宝支付相关接口封装
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AlipayServiceImpl implements AlipayService {

    @Override
    public AlipayTradePrecreateResponse faceToFaceUnifiedOrder(AliPayProperties aliPayProperties, FaceToFaceUnifiedOrderReq faceToFaceUnifiedOrderReq) {
        try {
            log.info("支付宝当面付: 统一收单线下交易预创建（扫码支付）订单，开始执行，req={}", faceToFaceUnifiedOrderReq);
            // 重新配置支付宝参数
            AliPayUtils.configAlipayPlatformParams(aliPayProperties, faceToFaceUnifiedOrderReq.getCompanyId());
            // 调用当面付创建统一预支付订单方法
            String subject = faceToFaceUnifiedOrderReq.getSubject();
            String outTradeNo = faceToFaceUnifiedOrderReq.getOutTradeNo();
            String totalAmount = faceToFaceUnifiedOrderReq.getTotalAmount();
            AlipayTradePrecreateResponse response = Factory.Payment.FaceToFace(faceToFaceUnifiedOrderReq.getCompanyId()).preCreate(subject, outTradeNo, totalAmount);
            log.info("支付宝当面付: 统一收单线下交易预创建（扫码支付）订单，执行完成，res={}", response != null ? new Gson().toJson(response) : null);
            return response;
        } catch (Exception e) {
            log.error("支付宝当面付: 统一收单线下交易预创建（扫码支付）订单失败", e);
            return null;
        }
    }

    @Override
    public AlipayTradeQueryResponse queryPayOrderDetail(AliPayProperties aliPayProperties, String outTradeNo, String companyId) {
        try {
            log.info("支付宝: 订单详情查询，开始执行，outTradeNo={}", outTradeNo);
            // 重新配置支付宝参数
            AliPayUtils.configAlipayPlatformParams(aliPayProperties, companyId);
            // 调用订单详情查询方法
            AlipayTradeQueryResponse response = Factory.Payment.Common(companyId).query(outTradeNo);
            log.info("支付宝: 订单详情查询，执行完成，res={}", response != null ? new Gson().toJson(response) : null);
            return response;
        } catch (Exception e) {
            log.error("支付宝: 订单详情查询，执行失败", e);
            return null;
        }
    }
}
