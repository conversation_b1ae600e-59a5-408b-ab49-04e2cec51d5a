package com.acpiot.wsd.common.dto;

import com.acpiot.wsd.common.serializer.CompanyId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 公司公共统计结构
 * Created by zc on 2021/7/27 17:50
 *
 * <AUTHOR> Email: <EMAIL>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommonKeyValueAndMapAndCompanyId extends CommonKeyValueAndMap {

    /**
     * 公司ID
     */
    @CompanyId
    private String companyId;
}