package com.acpiot.wsd.data.sms.entity;

import com.acpiot.wsd.common.entity.BaseAuditingCompanyEntity;
import com.acpiot.wsd.core.sms.properties.SmsProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import javax.validation.constraints.*;

/**
 * 短信参数配置
 * Created by YoungLu on 2021-06-11-0001
 *
 * <AUTHOR>
 */
@Table(uniqueConstraints = @UniqueConstraint(columnNames = {"company_id"}))
@Getter
@Setter
@NoArgsConstructor
@Entity
@DynamicInsert
@DynamicUpdate
public class SmsConfig extends BaseAuditingCompanyEntity {

    /**
     * 是否启用短信功能
     */
    @Column(nullable = false)
    private boolean enable;

    /**
     * 短信平台
     */
    @NotNull
    @Column(length = 4)
    @Enumerated
    private SmsPlatform smsPlatform;

    /**
     * 短信API地址/用户鉴权地址
     */
    @Column(length = 64, nullable = false)
    @NotBlank
    @Size(max = 64, message = "最多可输入64个字符")
    private String domain;

    /**
     * 用户企业名称（移动MAS平台专用）
     */
    @Column(length = 64)
    @Size(max = 64, message = "用户企业名称最多可输入64个字符")
    private String ecName;

    /**
     * accessKeyId
     */
    @Column(length = 32, nullable = false)
    @NotBlank
    @Size(max = 32, message = "accessKeyId最多可输入32个字符")
    private String accessKeyId;

    /**
     * accessKeySecret
     */
    @Column(length = 32, nullable = false)
    @NotBlank
    @Size(max = 32, message = "accessKeySecret最多可输入32个字符")
    private String accessKeySecret;

    /**
     * 签名
     */
    @Column(length = 64, nullable = false)
    @Size(max = 64, message = "签名最多可输入64个字符")
    private String signName;

    /**
     * 充值成功通知短信模板编码
     */
    @Column(length = 32, nullable = false)
    @Size(max = 32, message = "充值成功通知短信模板编码最多可输入32个字符")
    private String rechargeSuccessCode;

    /**
     * 缴费成功通知短信模板编码
     */
    @Column(length = 32, nullable = false)
    @Size(max = 32, message = "缴费成功通知短信模板编码最多可输入32个字符")
    private String paySuccessCode;

    /**
     * 开户成功通知模板编码
     */
    @Column(length = 32, nullable = false)
    @Size(max = 32, message = "开户成功通知模板编码最多可输入32个字符")
    private String openAccountCode;

    /**
     * 销户成功通知模板编码
     */
    @Column(length = 32, nullable = false)
    @Size(max = 32, message = "销户成功通知模板编码最多可输入32个字符")
    private String closeAccountCode;

    /**
     * 登录验证码模板编码
     */
    @Column(length = 32, nullable = false)
    @Size(max = 32, message = "登录验证码模板编码最多可输入32个字符")
    private String loginValidateCode;

    /**
     * 修改密码验证码模板编码
     */
    @Column(length = 32, nullable = false)
    @Size(max = 32, message = "修改密码验证码模板编码最多可输入32个字符")
    private String updatePasswordCode;

    /**
     * 找回密码验证码模板编码
     */
    @Column(length = 32, nullable = false)
    @Size(max = 32, message = "找回密码验证码模板编码最多可输入32个字符")
    private String forgetPasswordCode;

    /**
     * 开阀通知模版编码
     */
    @Column(length = 32, nullable = false)
    @Size(max = 32, message = "开阀通知模版编码最多可输入32个字符")
    private String openValveCode;

    /**
     * 关阀通知模版编码
     */
    @Column(length = 32, nullable = false)
    @Size(max = 32, message = "关阀通知模版编码最多可输入32个字符")
    private String closeValveCode;

    /**
     * 余额预警通知
     */
    @Column(length = 32, nullable = false)
    @Size(max = 32, message = "余额预警通知编码最多可输入32个字符")
    private String balanceWarnNotifyCode;

    /**
     * 账单缴费提醒
     */
    @Column(length = 32, nullable = false)
    @Size(max = 32, message = "账单缴费提醒编码最多可输入32个字符")
    private String billPaymentRemindCode;

    /**
     * 发送短信开始时间
     */
    @Column
    @Min(0)
    @Max(23)
    private Integer sendStartHour;

    /**
     * 发送短信结束时间
     */
    @Column
    @Min(0)
    @Max(23)
    private Integer sendEndHour;

    /**
     * 转换成短信平台的参数配置
     *
     * @return
     */
    public SmsProperties toSmsProperties() {
        SmsProperties smsProperties = new SmsProperties();
        smsProperties.setEnable(this.enable);
        smsProperties.setSmsPlatform(this.smsPlatform);
        smsProperties.setDomain(this.domain);
        smsProperties.setEcName(this.ecName);
        smsProperties.setAccessKeyId(this.accessKeyId);
        smsProperties.setAccessKeySecret(this.accessKeySecret);
        smsProperties.setSignName(this.signName);
        smsProperties.setSmsRechargeSuccess(this.rechargeSuccessCode);
        smsProperties.setSmsPaySuccess(this.paySuccessCode);
        smsProperties.setOpenAccountCode(this.openAccountCode);
        smsProperties.setCloseAccountCode(this.closeAccountCode);
        smsProperties.setLoginValidateCode(this.loginValidateCode);
        smsProperties.setUpdatePasswordCode(this.updatePasswordCode);
        smsProperties.setForgetPasswordCode(this.forgetPasswordCode);
        smsProperties.setOpenValve(this.openValveCode);
        smsProperties.setCloseValve(this.closeValveCode);
        smsProperties.setBalanceWarnNotify(this.balanceWarnNotifyCode);
        smsProperties.setBillPaymentRemind(this.billPaymentRemindCode);
        smsProperties.setSendStartHour(this.sendStartHour);
        smsProperties.setSendEndHour(this.sendEndHour);
        return smsProperties;
    }

    /**
     * 短信平台
     */
    public enum SmsPlatform {

        /**
         * 阿里云平台
         */
        ALI_YUN,

        /**
         * 移动Mas云-普通短信
         */
        MAS,

        /**
         * 移动Mas云-模板短信
         */
        MAS_TEMP;
    }
}
