package com.acpiot.wsd.data.monitor.entity;

import com.acpiot.wsd.data.archive.entity.NbCollector;
import com.acpiot.wsd.common.entity.BaseEntity;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * NB采集器上报数据
 */
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Entity
@DynamicInsert
@DynamicUpdate
@EntityListeners(AuditingEntityListener.class)
public class NbCollectorData extends BaseEntity {

    /**
     * 所属NB水表采集器
     */
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    private NbCollector nbCollector;

    /**
     * 上报时间
     */
    @JsonDeserialize
    @Temporal(TemporalType.TIMESTAMP)
    @Column(nullable = false)
    private Date reportTime;

    /**
     * 电池电压
     */
    @Column(precision = 4, scale = 2)
    private BigDecimal batteryVoltage;

    /**
     * 版本信息
     */
    @Column(length = 32, columnDefinition = "char(32) not null")
    private String version;

    /**
     * NB信号数据
     */
    @Embedded
    private NbSignal nbSignal;

    /**
     * 上报数据
     */
    @Type(type = "json")
    @Column(columnDefinition = "json null")
    private Object dataList;

    /**
     * 创建时间
     */
    @CreatedDate
    @Temporal(TemporalType.TIMESTAMP)
    @Column(nullable = false)
    private Date createdDate;

}
