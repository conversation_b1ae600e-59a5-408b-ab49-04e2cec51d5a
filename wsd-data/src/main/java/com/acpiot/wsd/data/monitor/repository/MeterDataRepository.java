package com.acpiot.wsd.data.monitor.repository;

import com.acpiot.wsd.data.archive.entity.WaterMeter;
import com.acpiot.wsd.data.monitor.entity.MeterData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import pers.mx.jupiter.jpa.repository.BaseRepository;

import java.util.Date;
import java.util.Optional;

/**
 * Created by moxin on 2019-12-03-0003
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface MeterDataRepository extends JpaRepository<MeterData, Long>, JpaSpecificationExecutor<MeterData>, BaseRepository<MeterData> {

    @Modifying
    @Query("delete from MeterData md where md.waterMeter.id in ?1")
    void deleteByWaterMeterIds(Long... meterIds);

    Optional<MeterData> findFirstByWaterMeterAndNbSignalIsNotNullOrderByCreatedDateDesc(WaterMeter waterMeter);

    Optional<MeterData> findTopByWaterMeter_IdOrderByCreatedDateDesc(Long meterId);

    /**
     * 根据水表ID和抄表时间范围查询抄表数据条数
     *
     * @param meterId
     * @param startTime
     * @param endTime
     * @return
     */
    boolean existsByWaterMeter_IdAndMeteringTimeBetween(long meterId, Date startTime, Date endTime);

    Optional<MeterData> findTopByWaterMeter_IdAndMeteringTimeBeforeOrderByCreatedDateDesc(Long meterId, Date meteringTime);

    @Modifying
    @Query("delete from MeterData md where md.createdDate < ?1")
    void deleteByCreatedDateBefore(Date meteringTime);
}
