package com.acpiot.wsd.data.archive.entity;

import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.data.archive.enums.IotPlatform;
import com.acpiot.wsd.data.archive.enums.NbState;
import com.acpiot.wsd.common.entity.BaseAuditingEntity;
import com.acpiot.wsd.common.exception.BusinessException;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Objects;

/**
 * NB设备信息
 * Created by moxin on 2019-11-25-0025
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Getter
@Setter
@NoArgsConstructor
@Entity
@DynamicInsert
@DynamicUpdate
public class NbInfo extends BaseAuditingEntity {

    /**
     * IoT平台
     */
    @Enumerated
    @Column(nullable = false, columnDefinition = "tinyint not null")
    private IotPlatform iotPlatform;

    /**
     * NB模块唯一编号
     */
    @Column(nullable = false, unique = true, length = 15, columnDefinition = "char(15) not null")
    @NotBlank(message = "imei不能为空")
    @Size(max = 15, message = "imei最多可输入15个字符")
    private String imei;

    /**
     * 移动平台需要，和imei对应
     */
    @Column(nullable = false, unique = true, length = 15, columnDefinition = "char(15) not null")
    @NotBlank(message = "imsi不能为空")
    @Size(max = 15, message = "imsi最多可输入15个字符")
    private String imsi;

    /**
     * ICCID号
     */
    @Column(length = 20, columnDefinition = "char(20) null")
    @Size(max = 20, message = "iccid最多可输入20个字符")
    private String iccid;

    /**
     * 设备状态
     */
    @Enumerated
    @Column(nullable = false, columnDefinition = "tinyint not null")
    private NbState nbState = NbState.UNREGISTERED;

    /**
     * 是否在线
     */
    @Column(nullable = false)
    private boolean online;

    /**
     * 是否已注册
     *
     * @return
     */
    public boolean isRegistered() {
        return nbState != null && nbState != NbState.UNREGISTERED;
    }

    public static NbInfo example(long id, String imei, String imsi, String iccid) {
        NbInfo nbInfo = new NbInfo();
        nbInfo.setId(id);
        nbInfo.setImei(imei);
        nbInfo.setImsi(imsi);
        nbInfo.setIccid(iccid);
        return nbInfo;
    }

    public static NbInfo of(DeviceArchive deviceArchive) {
        NbInfo nbInfo = new NbInfo();
        nbInfo.imei = deviceArchive.getImei();
        nbInfo.imsi = deviceArchive.getImsi();
        nbInfo.iccid = deviceArchive.getIccid();
        nbInfo.iotPlatform = IotPlatform.CTWING;
        nbInfo.nbState = deviceArchive.isSync() ? NbState.INACTIVE : NbState.UNREGISTERED;
        return nbInfo;
    }

    public static NbInfo of(NbInfo nbInfo, IotPlatform iotPlatform) {
        NbInfo newNbInfo = new NbInfo();
        newNbInfo.imei = nbInfo.getImei();
        newNbInfo.imsi = nbInfo.getImsi();
        newNbInfo.iccid = nbInfo.getIccid();
        newNbInfo.iotPlatform = iotPlatform;
        return newNbInfo;
    }

    @JsonIgnore
    public boolean isInputThirdCode() {
        return StrUtil.isNotBlank(imei) && StrUtil.isNotBlank(imsi) && iotPlatform != null;
    }

    @JsonIgnore
    public boolean isMatchConfig(NbInfo input) {
        return Objects.equals(imei, input.getImei()) && Objects.equals(imsi, input.getImsi()) && Objects.equals(iccid, input.getIccid()) && iotPlatform == input.getIotPlatform();
    }

    public void checkInputValid() {
        boolean input = StrUtil.isNotBlank(imei) && StrUtil.isNotBlank(imsi) && iotPlatform != null;
        boolean noInput = StrUtil.isBlank(imei) && StrUtil.isBlank(imsi) && iotPlatform == null;
        if (!input && !noInput) {
            throw new BusinessException("IMEI相关信息必须全部输入或全部不输入，包括（IMEI/IMSI/IOT平台）");
        }
    }
}
