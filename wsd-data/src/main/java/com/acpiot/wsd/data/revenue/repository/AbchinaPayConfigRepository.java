package com.acpiot.wsd.data.revenue.repository;

import com.acpiot.wsd.pay.abchina.properties.AbchinaPayProperties;
import com.acpiot.wsd.data.revenue.entity.AbchinaPayConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import pers.mx.jupiter.jpa.repository.BaseRepository;

import java.util.Optional;

/**
 * 农行支付参数配置
 * Created by YoungLu on 2023/02/02
 *
 * <AUTHOR>
 */
public interface AbchinaPayConfigRepository extends JpaRepository<AbchinaPayConfig, Long>, JpaSpecificationExecutor<AbchinaPayConfig>,
        QuerydslPredicateExecutor<AbchinaPayConfig>, BaseRepository<AbchinaPayConfig> {

    /**
     * 查询已启用的农行支付参数配置
     *
     * @param companyId
     * @return
     */
    Optional<AbchinaPayConfig> findFirstByCompanyIdAndEnableIsTrue(String companyId);

    /**
     * 获取已启用的农行微信支付参数配置
     *
     * @param companyId
     * @return
     */
    default Optional<AbchinaPayProperties> getEnabledAbchinaPayProperties(String companyId) {
        return findFirstByCompanyIdAndEnableIsTrue(companyId).map(AbchinaPayConfig::toAbchinaPayProperties);
    }
}
