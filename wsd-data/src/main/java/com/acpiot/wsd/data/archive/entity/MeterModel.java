package com.acpiot.wsd.data.archive.entity;

import com.acpiot.wsd.data.archive.enums.ConcentratorMeterType;
import com.acpiot.wsd.data.archive.enums.MeterType;
import com.acpiot.wsd.common.entity.BaseAuditingEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 水表型号
 * Created by moxin on 2019-11-22-0022
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Table(uniqueConstraints = @UniqueConstraint(columnNames = {"name"}))
@Getter
@Setter
@NoArgsConstructor
@Entity
@DynamicInsert
@DynamicUpdate
public class MeterModel extends BaseAuditingEntity {

    /**
     * 型号名称
     */
    @Column(nullable = false, unique = true, length = 16)
    @NotBlank
    @Size(max = 16, message = "型号名称最多可输入16个字符")
    private String name;

    /**
     * 水表类型
     */
    @Enumerated
    @Column(nullable = false, columnDefinition = "tinyint not null")
    private MeterType meterType;

    /**
     * 集中器水表分类
     */
    @Enumerated
    @Column(columnDefinition = "tinyint null")
    private ConcentratorMeterType concentratorMeterType;

    /**
     * 基表型号
     */
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    private BaseMeter baseMeter;

    /**
     * NB水表运行程序
     */
    @ManyToOne(fetch = FetchType.EAGER)
    private NbWmProgram nbWmProgram;

    /**
     * 是否支持通过NB采集器进行控制
     */
    @Column(nullable = false, columnDefinition = "bit(1) not null")
    private boolean supportNbCollector;

    /**
     * 是否支持业务/强制开关阀
     */
    @Column(nullable = false, columnDefinition = "bit(1) not null")
    private boolean supportServiceOrForceValveCtrl;

    /**
     * 是否支持曲线数据上报
     */
    @Column
    private boolean supportCurveData;

    /**
     * 备注
     */
    @Column
    @Size(max = 255, message = "备注最多可输入255个字符")
    private String remark;

    /**
     * 判断当前水表是否支持UDP协议命令
     *
     * @return
     */
    public boolean isSupportedUdpCmd() {
        if (meterType != MeterType.CAT1 && meterType != MeterType.NB_IOT) {
            return false;
        }
        return meterType != MeterType.NB_IOT || nbWmProgram.isSupportedPreProduction();
    }
}
