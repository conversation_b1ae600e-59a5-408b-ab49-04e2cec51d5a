package com.acpiot.wsd.data.revenue.entity;

import com.acpiot.wsd.common.entity.BaseAuditingEntity;
import com.acpiot.wsd.common.serializer.CompanyId;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 信息打印模板表(ReceiptTemplate)实体类
 *
 * <AUTHOR>
 * @since 2021-04-20 17:30:14
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@FilterDefs({
        @FilterDef(name = "companyFilter", parameters = {@ParamDef(name = "companyIds", type = "string")})
})
@Filters({
        @Filter(name = "companyFilter", condition = "(company_id in (:companyIds) or company_id is null)")
})
public class ReceiptTemplate extends BaseAuditingEntity {

    /**
     * 模板类型
     */
    @NotNull
    @Enumerated
    @Column(nullable = false, length = 1)
    private TemplateType templateType;

    /**
     * 模板名称
     */
    @NotBlank
    @Size(max = 64, message = "模板名称最多只可输入64个字符")
    @Column(nullable = false, length = 64)
    private String receiptName;

    /**
     * 模板内容
     */
    @Column
    private String content;

    /**
     * 模板宽度
     */
    @Column(nullable = false)
    private double width;

    /**
     * 模板高度
     */
    @Column(nullable = false)
    private double height;

    /**
     * 是否启用
     */
    @Column(nullable = false, length = 1)
    private boolean enabled;

    /**
     * 备注
     */
    @Size(max = 255, message = "备注最多只可输入255个字符")
    @Column(nullable = false, length = 255)
    private String remark;

    /**
     * 公司ID
     */
    @Column
    @CompanyId
    private String companyId;

    /**
     * 模板类型
     */
    public enum TemplateType {

        /**
         * 收据
         */
        RECEIPT,

        /**
         * 账单
         */
        BILL
    }
}