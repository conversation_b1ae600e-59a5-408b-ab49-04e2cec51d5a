package com.acpiot.wsd.data.sys.entity;

import com.acpiot.wsd.common.entity.BaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * 小程序操作记录表
 * Created by moxin on 2019-09-29-0029
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Getter
@Setter
@NoArgsConstructor
@Entity
@DynamicInsert
@DynamicUpdate
public class MiniOperateLog extends BaseEntity {

    /**
     * imei
     */
    @Column(length = 15)
    private String imei;

    /**
     * 操作通道
     */
    @Column(nullable = false, length = 64)
    private String operateChannel;

    /**
     * 操作项目
     */
    @Column(nullable = false, length = 64)
    private String operateName;

    /**
     * 操作参数
     */
    @Column
    private String operateParams;

    /**
     * 操作人
     */
    @Column(nullable = false)
    private String operator;

    /**
     * 操作时间
     */
    @Temporal(TemporalType.TIMESTAMP)
    @Column(nullable = false)
    private Date operateTime;
}
