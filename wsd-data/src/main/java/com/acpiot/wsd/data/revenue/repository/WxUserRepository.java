package com.acpiot.wsd.data.revenue.repository;

import com.acpiot.wsd.data.revenue.entity.WxUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import pers.mx.jupiter.jpa.repository.BaseRepository;

import java.util.Optional;

/**
 * Created by hsq on 2021-04-24-0001
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface WxUserRepository extends JpaRepository<WxUser, Long>, JpaSpecificationExecutor<WxUser>,
        QuerydslPredicateExecutor<WxUser>, BaseRepository<WxUser> {

    /**
     * 根据openid查询微信用户
     *
     * @param openid
     * @return
     */
    Optional<WxUser> findTopByOpenid(String openid);
}
