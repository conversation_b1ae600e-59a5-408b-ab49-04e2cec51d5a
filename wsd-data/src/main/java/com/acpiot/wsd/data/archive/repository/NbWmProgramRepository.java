package com.acpiot.wsd.data.archive.repository;

import com.acpiot.wsd.data.archive.entity.NbWmProgram;
import com.acpiot.wsd.data.archive.enums.TransparentProtocol;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import pers.mx.jupiter.jpa.repository.BaseRepository;

import java.util.List;

/**
 * Created by YoungLu on 2024-04-17-0001
 *
 * <AUTHOR>
 */
public interface NbWmProgramRepository extends JpaRepository<NbWmProgram, Long>, JpaSpecificationExecutor<NbWmProgram>,
        BaseRepository<NbWmProgram> {

    List<NbWmProgram> findAllByProtocolEquals(TransparentProtocol protocol);
}
