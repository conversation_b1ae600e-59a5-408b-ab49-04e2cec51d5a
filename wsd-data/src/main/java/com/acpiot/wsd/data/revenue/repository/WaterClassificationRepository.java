package com.acpiot.wsd.data.revenue.repository;

import com.acpiot.wsd.data.revenue.entity.WaterClassification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import pers.mx.jupiter.jpa.repository.BaseRepository;

import java.util.List;

/**
 * Created by moxin on 2020-03-05-0005
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface WaterClassificationRepository extends JpaRepository<WaterClassification, Long>,
        JpaSpecificationExecutor<WaterClassification>, QuerydslPredicateExecutor<WaterClassification>,
        BaseRepository<WaterClassification> {

    List<WaterClassification> findByCompanyIdAndNameIn(String companyId, List<String> allWaterClassificationName);
}
