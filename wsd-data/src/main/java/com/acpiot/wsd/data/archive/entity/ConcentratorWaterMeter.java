package com.acpiot.wsd.data.archive.entity;

import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.core.utils.ProtocolPortUtils;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import javax.validation.constraints.Size;

/**
 * Created by moxin on 2020-07-15-0015
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Getter
@Setter
@NoArgsConstructor
@Entity
@DynamicInsert
@DynamicUpdate
@PrimaryKeyJoinColumn(name = "id")
public class ConcentratorWaterMeter extends WaterMeter {

    /**
     * 最大序号
     */
    public static final int MAX_SERIAL_NO = 1024;

    /**
     * 中继器编号长度
     */
    public static final int COLLECTOR_UID_LENGTH = 12;

    /**
     * 中继器，目前支持3级中继
     * 存储方向为：集中器 -> 中继器n -> 水表
     */
    @Column(length = 38)
    @Size(max = 38, message = "中继器最多可输入38个字符")
    private String collectorUid;

    /**
     * 表序号，从1开始，最大值为MAX_SERIAL_NO
     */
    @Column(nullable = false, columnDefinition = "smallint unsigned not null")
    private int serialNo;

    /**
     * 该档案是否已同步
     */
    @Column(nullable = false)
    private boolean sync;

    /**
     * 协议端口号，多功能集中器版本水表支持，原有水表默认填写0即可
     */
    @Column(nullable = false)
    private int protocolPort = 0;

    /**
     * 表波特率(仅捷先集中器使用)
     */
    @Column
    private Integer meterBaud;

    /**
     * 集中器连接端口(仅捷先集中器使用)
     */
    @Column
    private Integer concentratorPort;

    /**
     * 采集器端口号： 表通过采集器连接到集中器时候，此值为 1,2，分别对应采集器的 1,2 端口。(仅捷先集中器使用)
     */
    @Column
    private Integer collectorPort;

    /**
     * 获取中继器
     *
     * @return
     */
    public String[] getRepeaters() {
        if (StrUtil.isBlank(collectorUid)) {
            return null;
        }
        return collectorUid.split(",");
    }

    /**
     * （适用于有线多功能集中器水表）协议类型，临时字段，用于页面档案维护
     */
    @Transient
    private String protocol;

    /**
     * （适用于有线多功能集中器水表）端口，临时字段，用于页面档案维护
     */
    @Transient
    private String port;

    /**
     * 解析协议端口字段，转换为数据库存储格式，有线多功能集中器水表适用
     */
    public void parseProtocolPort() {
        this.protocolPort = ProtocolPortUtils.mergeProtocolPortByNo(protocol, port);
    }

    /**
     * 解析协议端口字段，转换为前端使用的数据
     */
    public void parseProtocolPortView() {
        ProtocolPortUtils.parseProtocolPort(protocolPort, (int protocol, int port) -> {
            this.protocol = String.valueOf(protocol);
            this.port = String.valueOf(port);
        });
    }


    public String getProtocolName() {
        return ProtocolPortUtils.parseProtocolName(this.protocolPort);
    }

    public String getPortName() {
        return ProtocolPortUtils.parsePortName(this.protocolPort);
    }
}
