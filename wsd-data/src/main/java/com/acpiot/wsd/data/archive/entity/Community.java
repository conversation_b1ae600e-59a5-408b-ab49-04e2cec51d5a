package com.acpiot.wsd.data.archive.entity;

import com.acpiot.wsd.common.entity.BaseAuditingCompanyEntity;
import com.acpiot.wsd.data.revenue.entity.WaterClassification;
import com.acpiot.wsd.data.sys.entity.Region;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.*;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 小区
 * Created by moxin on 2019-11-22-0022
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Table(uniqueConstraints = @UniqueConstraint(columnNames = {"name"}))
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Entity
@DynamicInsert
@DynamicUpdate
@FilterDef(name = "communityFilter", parameters = {@ParamDef(name = "communityIds", type = "long")})
@Filter(name = "communityFilter", condition = "id in (:communityIds)")
public class Community extends BaseAuditingCompanyEntity {

    /**
     * 所属管辖区域
     */
    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    private Region region;

    /**
     * 小区名称
     */
    @Column(nullable = false, unique = true, length = 24)
    @NotBlank
    @Size(max = 24, message = "小区名称最多可输入24个字符")
    private String name;

    /**
     * 所属上级小区(为空时代表顶级小区)
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private Community parent;

    /**
     * 层级编码
     */
    @Column(nullable = false, length = 64)
    private String layerCode;

    /**
     * 小区地址
     */
    @Column(length = 64)
    @Size(max = 64, message = "小区地址最多可输入64个字符")
    private String address;

    /**
     * 备注
     */
    @Column
    @Size(max = 255, message = "备注最多可输入255个字符")
    private String remark;

    /**
     * 小区用水类型
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private WaterClassification waterClassification;

    /**
     * 启用结算日期
     */
    @Temporal(TemporalType.DATE)
    @Column
    private Date enabledBillDate;

    /**
     * 结算类型
     */
    @Enumerated
    @Column(nullable = false)
    private SettlementType settlementType = SettlementType.POSTPAID;

    /**
     * 自动开关阀开关
     */
    @Column(nullable = false)
    private boolean autoSwitchValve = false;

    /**
     * 是否业主支付手续费
     */
    @Column(nullable = false)
    private boolean ownerPayPoundage = false;

    /**
     * 手续费率（收款单位向用户收取的手续费）
     */
    @Column
    private BigDecimal chargeRate;

    /**
     * 小流量阈值
     */
    @Column
    private BigDecimal minFlow;

    /**
     * 大流量阈值
     */
    @Column
    private BigDecimal maxFlow;

    /**
     * 最低消费限额
     */
    @Column
    private BigDecimal minChargeLimit;

    /**
     * 服务费
     */
    @Column
    private BigDecimal serviceFee;

    /**
     * 延迟关阀天数
     */
    @Column
    @Min(value = 1, message = "延迟关阀天数不能小于1")
    private Integer delayCloseValveDays;

    public enum SettlementType {
        //预付费
        PREPAID,
        //后付费
        POSTPAID
    }
}
