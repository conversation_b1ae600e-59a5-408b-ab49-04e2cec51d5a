package com.acpiot.wsd.data.productiontest.repository;

import com.acpiot.wsd.data.productiontest.entity.TestBatch;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import pers.mx.jupiter.jpa.repository.BaseRepository;

/**
 * Created by moxin on 2021-03-23-0023
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface TestBatchRepository extends JpaRepository<TestBatch, Long>, JpaSpecificationExecutor<TestBatch>,
        BaseRepository<TestBatch> {

    @Modifying
    @Query("delete from TestBatch tb where tb.id in (?1)")
    void deleteAllInBatch(long[] ids);
}
