package com.acpiot.wsd.core.sms.bean;

import lombok.Data;

/**
 * 封装短信发送结果
 */
@Data
public class SmsResult {

    private String code;
    private String msg;
    private String requestId;
    private boolean result;

    public SmsResult(String code, String msg, String requestId) {
        this.code = code;
        this.msg = msg;
        this.requestId = requestId;
        this.result = "OK".equals(this.code) && "OK".equals(this.msg);
    }

    public static SmsResult returnNotSupportError() {
        return new SmsResult("ERROR", "未识别的短信平台", null);
    }

    public static SmsResult returnNotEnableError() {
        return new SmsResult("ERROR", "系统未开启短信功能，请联系管理员开启", null);
    }

    public static SmsResult returnNotConfigError() {
        return new SmsResult("ERROR", "系统未配置相应短信模板编码，请联系管理员配置", null);
    }

    public static SmsResult returnMobileError(String mobile) {
        return new SmsResult("ERROR", "手机号码 " + mobile + " 格式错误，请检查", null);
    }

    public static SmsResult returnUnauthorizedError() {
        return new SmsResult("ERROR", "短信平台未授权，请联系管理员", null);
    }

    public static SmsResult returnNotHasWaterMeter() {
        return new SmsResult("ERROR", "未查询到对应水表", null);
    }

    public static SmsResult returnNotHasLatestData() {
        return new SmsResult("ERROR", "对应水表不存在最新读数", null);
    }

    public static SmsResult returnNotHasBill() {
        return new SmsResult("ERROR", "未查询到对应账单", null);
    }

    public static SmsResult returnNotConsumption() {
        return new SmsResult("ERROR", "账单本期无用量，不发送通知", null);
    }
}
