package com.acpiot.wsd.core.event;

import com.acpiot.wsd.data.archive.entity.WaterMeter;
import com.acpiot.wsd.data.monitor.entity.AbstractMeterData;
import lombok.Value;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * Created by moxin on 2021-08-03-0003
 *
 * <AUTHOR> Email: <EMAIL>
 */
public class MeterDataSavedEvent extends ApplicationEvent {

    @Value
    public static class EventData {
        WaterMeter waterMeter;
        AbstractMeterData meterData;
    }

    public MeterDataSavedEvent(List<EventData> dataList) {
        super(dataList);
    }

    @SuppressWarnings("unchecked")
    public List<EventData> getDataList() {
        return (List<EventData>) getSource();
    }
}
