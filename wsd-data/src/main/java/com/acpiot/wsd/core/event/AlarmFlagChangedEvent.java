package com.acpiot.wsd.core.event;

import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.data.archive.entity.WaterMeter;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 故障状态标识变化事件
 * Created by moxin on 2021-07-02-0002
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Getter
public class AlarmFlagChangedEvent extends ApplicationEvent {

    private final WaterMeter waterMeter;
    private final boolean batteryUnderVoltage;
    private final boolean magneticAttack;

    public AlarmFlagChangedEvent(Object source, WaterMeter waterMeter, boolean batteryUnderVoltage, boolean magneticAttack) {
        super(source);
        this.waterMeter = waterMeter;
        this.batteryUnderVoltage = batteryUnderVoltage;
        this.magneticAttack = magneticAttack;
    }

    @Override
    public String toString() {
        return StrUtil.format("公司 {} 水表 {} 报警标志变化 -> 电池低电压：{}，强磁攻击告警：{}",
                waterMeter.getCompanyId(), waterMeter.getCode(), batteryUnderVoltage, magneticAttack);
    }

}
