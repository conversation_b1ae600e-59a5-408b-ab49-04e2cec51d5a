package com.acpiot.wsd.core.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * 设备档案信息同步事件
 * Created by hsq on 2021/9/18 14:36
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Getter
@Setter
public class DeviceArchiveSyncEvent extends ApplicationEvent {

    /**
     * 水表imei
     */
    private List<String> imeis;

    /**
     * 是否被使用
     */
    private Boolean used;

    /**
     * 同步状态
     */
    private Boolean sync;

    public DeviceArchiveSyncEvent(Object source, String imei, Boolean used, Boolean sync) {
        super(source);
        this.imeis = List.of(imei);
        this.used = used;
        this.sync = sync;
    }

    public DeviceArchiveSyncEvent(Object source, List<String> imeis, Boolean used, Boolean sync) {
        super(source);
        this.imeis = imeis;
        this.used = used;
        this.sync = sync;
    }
}