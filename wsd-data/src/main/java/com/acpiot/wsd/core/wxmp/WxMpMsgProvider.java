package com.acpiot.wsd.core.wxmp;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.acpiot.wsd.core.service.ConfigService;
import com.acpiot.wsd.core.wxmp.bean.*;
import com.acpiot.wsd.data.revenue.entity.Bill;
import com.acpiot.wsd.data.revenue.entity.WxMsgRecord;
import com.acpiot.wsd.data.revenue.enums.ApplyStatus;
import com.acpiot.wsd.data.revenue.service.PayCustomerService;
import com.acpiot.wsd.data.revenue.service.WxMsgRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.function.Predicate;

import static com.acpiot.wsd.core.constant.CommonConstant.*;

/**
 * 微信模板消息服务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WxMpMsgProvider {

    private final WxMpProvider wxMpProvider;
    private final ConfigService configService;
    private final WxMsgRecordService wxMsgRecordService;
    private final PayCustomerService payCustomerService;
    private final List<Integer> ERROR_CODE = List.of(43004);

    /**
     * 发送充值成功通知
     *
     * @param openIds
     * @param rechargeSuccessMsg
     */
    public boolean rechargeSuccessMsg(List<String> openIds, RechargeSuccessMsg rechargeSuccessMsg) {
        // 判断当前是否开启公众号模板消息能力
        WxMpProperties wxMpProperties = configService.getWxMpProperties(rechargeSuccessMsg.getCompanyId());
        if (!wxMpProperties.isEnableMsg()) {
            return false;
        }
        if (StringUtils.isBlank(wxMpProperties.getRechargeSuccessMsg())) {
            return false;
        }
        // 检查openId参数
        if (CollectionUtil.isEmpty(openIds)) {
            throw new IllegalStateException("微信公众号模板消息推送：请提供要推送的目标用户openId参数");
        }
        // 检查必填参数
        if (!rechargeSuccessMsg.isValid()) {
            throw new IllegalStateException(String.format("微信公众号模板消息推送：请提供所有必填参数，rechargeSuccessMsg=%s", rechargeSuccessMsg));
        }
        // 获取公司对应的微信公众号服务接口
        WxMpService wxMpService = wxMpProvider.wxMpService(rechargeSuccessMsg.getCompanyId());

        WxMpTemplateMessage msg = new WxMpTemplateMessage();
        msg.setTemplateId(wxMpProperties.getRechargeSuccessMsg());
        // 获取请求参数
        String linkCode = rechargeSuccessMsg.getLinkCode();
        String installLocation = rechargeSuccessMsg.getInstallLocation();
        String rechargeTime = rechargeSuccessMsg.getRechargeTime();
        String amount = rechargeSuccessMsg.getAmount();
        String realBalance = rechargeSuccessMsg.getRealBalance();
        if (wxMpProperties.isTemplateV1()) {
            String balance = rechargeSuccessMsg.getBalance();
            // 组织模板消息参数
            String content = String.format("尊敬的客户您好，您当前户号%s（地址：%s）的充值已到账，充值后账户余额为%s元，实际可用余额为%s元，请知悉。",
                    linkCode, installLocation, balance, realBalance);
            WxMpTemplateData firstData = new WxMpTemplateData("first", content);
            WxMpTemplateData templateData1 = new WxMpTemplateData("keyword1", amount);
            WxMpTemplateData templateData2 = new WxMpTemplateData("keyword2", rechargeTime);

            msg.addData(firstData);
            msg.addData(templateData1);
            msg.addData(templateData2);
        } else {
            WxMpTemplateData thing8Data = new WxMpTemplateData("thing8", "表号(" + rechargeSuccessMsg.getMeterCode() + ")");
            WxMpTemplateData time6Data = new WxMpTemplateData("time6", rechargeTime);
            WxMpTemplateData amount5Data = new WxMpTemplateData("amount5", amount);
            WxMpTemplateData amount9Data = new WxMpTemplateData("amount9", realBalance);

            msg.addData(thing8Data);
            msg.addData(time6Data);
            msg.addData(amount5Data);
            msg.addData(amount9Data);
        }

        // 只要有一个openid成功推送，就视为成功
        boolean success = false;
        // 循环发送给所有指定的微信用户
        for (String openid : openIds) {
            try {
                msg.setToUser(openid);
                String response = wxMpService.getTemplateMsgService().sendTemplateMsg(msg);
                log.info("微信公众号模板消息推送: 充值成功通知发送结果，openid={}，response={}", openid, response);
                wxMsgRecordService.saveWxMsgRecord(msg, WxMsgRecord.TemplateName.RECHARGE_SUCCESS, response, rechargeSuccessMsg.getAccountNo(), null);
                success = true;
            } catch (WxErrorException e) {
                log.warn("微信公众号模板消息推送: 充值成功通知发送失败，openid={}\n{}", openid, e.getMessage());
                if (ERROR_CODE.contains(e.getError().getErrorCode())) {
                    payCustomerService.unBoundPayCustomer(rechargeSuccessMsg.getAccountNo(), null, openid);
                }
            }
        }
        return success;
    }

    /**
     * 发送缴费成功通知
     *
     * @param openIds
     * @param paySuccessMsg
     * @param predicate
     */
    public boolean paySuccessMsg(List<String> openIds, PaySuccessMsg paySuccessMsg, Predicate<WxMpProperties> predicate) {
        // 判断当前是否开启公众号模板消息能力
        WxMpProperties wxMpProperties = configService.getWxMpProperties(paySuccessMsg.getCompanyId());
        if (!wxMpProperties.isEnableMsg()) {
            return false;
        }
        if (StringUtils.isBlank(wxMpProperties.getPaySuccessMsg())) {
            return false;
        }
        // 检查openId参数
        if (CollectionUtil.isEmpty(openIds)) {
            throw new IllegalStateException("微信公众号模板消息推送：请提供要推送的目标用户openId参数");
        }
        // 检查必填参数
        if (!paySuccessMsg.isValid()) {
            throw new IllegalStateException(String.format("微信公众号模板消息推送：请提供所有必填参数，paySuccessMsg=%s", paySuccessMsg));
        }
        if (!predicate.test(wxMpProperties)) {
            return true;
        }
        // 获取公司对应的微信公众号服务接口
        WxMpService wxMpService = wxMpProvider.wxMpService(paySuccessMsg.getCompanyId());

        WxMpTemplateMessage msg = new WxMpTemplateMessage();
        msg.setTemplateId(wxMpProperties.getPaySuccessMsg());
        // 获取提交参数
        String linkCode = paySuccessMsg.getLinkCode();
        String installLocation = paySuccessMsg.getInstallLocation();

        String paymentType = paySuccessMsg.getPaymentType();
        String payTime = paySuccessMsg.getPayTime();
        String amount = paySuccessMsg.getAmount();

        if (wxMpProperties.isTemplateV1()) {
            String period = paySuccessMsg.getPeriod();
            String balance = paySuccessMsg.getBalance();
            // 组织模板消息参数
            String content = String.format("尊敬的客户您好，您当前户号%s（地址：%s）的账单缴费已到账，账单周期为%s，缴费方式为%s，请知悉。", linkCode, installLocation, period, paymentType);
            WxMpTemplateData firstData = new WxMpTemplateData("first", content);
            WxMpTemplateData templateData1 = new WxMpTemplateData("keyword1", payTime);
            WxMpTemplateData templateData2 = new WxMpTemplateData("keyword2", amount);
            WxMpTemplateData templateData3 = new WxMpTemplateData("keyword3", balance);

            msg.addData(firstData);
            msg.addData(templateData1);
            msg.addData(templateData2);
            msg.addData(templateData3);
        } else {
            WxMpTemplateData characterString7Data = new WxMpTemplateData("character_string7", linkCode);
            WxMpTemplateData thing17Data = new WxMpTemplateData("thing17", getInstallLocationLessThan20(installLocation));
            WxMpTemplateData time12Data = new WxMpTemplateData("time12", payTime);
            WxMpTemplateData amount3Data = new WxMpTemplateData("amount3", amount);
            WxMpTemplateData thing13Data = new WxMpTemplateData("thing13", paymentType);

            msg.addData(characterString7Data);
            msg.addData(thing17Data);
            msg.addData(time12Data);
            msg.addData(amount3Data);
            msg.addData(thing13Data);
        }

        // 只要有一个openid成功推送，就视为成功
        boolean success = false;
        // 循环发送给所有指定的微信用户
        for (String openid : openIds) {
            try {
                msg.setToUser(openid);
                String response = wxMpService.getTemplateMsgService().sendTemplateMsg(msg);
                log.info("微信公众号模板消息推送: 缴费成功通知发送结果，openid={}，response={}", openid, response);
                wxMsgRecordService.saveWxMsgRecord(msg, WxMsgRecord.TemplateName.PAY_SUCCESS_MSG, response, paySuccessMsg.getAccountNo(), null);
                success = true;
            } catch (WxErrorException e) {
                log.warn("微信公众号模板消息推送: 缴费成功通知发送失败，openid={}\n{}", openid, e.getMessage());
                if (ERROR_CODE.contains(e.getError().getErrorCode())) {
                    payCustomerService.unBoundPayCustomer(paySuccessMsg.getAccountNo(), null, openid);
                }
            }
        }
        return success;
    }

    /**
     * 发送账单缴费提醒
     *
     * @param openIds
     * @param billPaymentRemindMsg
     * @param predicate
     */
    public boolean billPaymentRemind(List<String> openIds, BillPaymentRemindMsg billPaymentRemindMsg, Predicate<WxMpProperties> predicate) {
        // 判断当前是否开启公众号模板消息能力
        WxMpProperties wxMpProperties = configService.getWxMpProperties(billPaymentRemindMsg.getCompanyId());
        if (!wxMpProperties.isEnableMsg()) {
            return false;
        }
        if (StringUtils.isBlank(wxMpProperties.getBillPaymentRemindMsg())) {
            return false;
        }
        // 检查openId参数
        if (CollectionUtil.isEmpty(openIds)) {
            throw new IllegalStateException("微信公众号模板消息推送：请提供要推送的目标用户openId参数");
        }
        // 检查必填参数
        if (!billPaymentRemindMsg.isValid()) {
            throw new IllegalStateException(String.format("微信公众号模板消息推送：请提供所有必填参数，billPaymentRemindMsg=%s", billPaymentRemindMsg));
        }
        if (!predicate.test(wxMpProperties)) {
            return true;
        }

        // 获取公司对应的微信公众号服务接口
        WxMpService wxMpService = wxMpProvider.wxMpService(billPaymentRemindMsg.getCompanyId());

        WxMpTemplateMessage msg = new WxMpTemplateMessage();

        msg.setTemplateId(wxMpProperties.getBillPaymentRemindMsg());
        // 获取请求参数
        String linkCode = billPaymentRemindMsg.getLinkCode();
        String customerName = billPaymentRemindMsg.getCustomerName();

        Bill bill = billPaymentRemindMsg.getBill();
        if (wxMpProperties.isTemplateV1()) {
            throw new IllegalStateException(String.format("微信公众号模板消息推送：不支持旧模板消息，billPaymentRemindMsg=%s", billPaymentRemindMsg));
        }
        // 用量向下取整
        BigDecimal totalUsedTon = bill.getTotalUsedTon().setScale(0, RoundingMode.DOWN);
        // 水费保留2位小数
        BigDecimal amount = bill.getAmount().setScale(2, RoundingMode.HALF_UP);
        WxMpTemplateData characterString2Data = new WxMpTemplateData("number2", linkCode);
        WxMpTemplateData thing17Data = new WxMpTemplateData("thing26", customerName);
        WxMpTemplateData characterString9Data = new WxMpTemplateData("thing5", totalUsedTon + "吨");
        WxMpTemplateData amount10Data = new WxMpTemplateData("amount4", amount + "元");
        WxMpTemplateData time11Data = new WxMpTemplateData("time11", DateUtil.format(bill.getStartDate(), "yyyy-MM"));
        msg.addData(characterString2Data);
        msg.addData(thing17Data);
        msg.addData(characterString9Data);
        msg.addData(amount10Data);
        msg.addData(time11Data);

        // 只要有一个openid成功推送，就视为成功
        boolean success = false;
        // 循环发送给所有指定的微信用户
        for (String openid : openIds) {
            try {
                msg.setToUser(openid);
                String response = wxMpService.getTemplateMsgService().sendTemplateMsg(msg);
                log.info("微信公众号模板消息推送: 账单缴费提醒发送结果，openid={}，response={}", openid, response);
                wxMsgRecordService.saveWxMsgRecord(msg, WxMsgRecord.TemplateName.BILL_PAYMENT_REMIND, response, billPaymentRemindMsg.getAccountNo(), null);
                success = true;
            } catch (WxErrorException e) {
                log.warn("微信公众号模板消息推送: 账单缴费提醒发送失败，openid={}\n{}", openid, e.getMessage());
                if (ERROR_CODE.contains(e.getError().getErrorCode())) {
                    payCustomerService.unBoundPayCustomer(billPaymentRemindMsg.getAccountNo(), null, openid);
                }
            }
        }
        return success;
    }

    /**
     * 发送开阀通知
     *
     * @param openIds
     * @param openValveMsg
     * @param predicate
     */
    public boolean openValveMsg(List<String> openIds, OpenValveMsg openValveMsg, Predicate<WxMpProperties> predicate) {
        WxMpProperties wxMpProperties = configService.getWxMpProperties(openValveMsg.getCompanyId());
        if (!wxMpProperties.isEnableMsg()) {
            return false;
        }
        if (StringUtils.isBlank(wxMpProperties.getOpenValveMsg())) {
            return false;
        }
        // 检查openId参数
        if (CollectionUtil.isEmpty(openIds)) {
            throw new IllegalStateException("微信公众号模板消息推送：请提供要推送的目标用户openId参数");
        }
        // 检查必填参数
        if (!openValveMsg.isValid()) {
            throw new IllegalStateException(String.format("微信公众号模板消息推送：请提供所有必填参数，openValveMsg=%s", openValveMsg));
        }
        if (!predicate.test(wxMpProperties)) {
            return true;
        }

        // 获取公司对应的微信公众号服务接口
        WxMpService wxMpService = wxMpProvider.wxMpService(openValveMsg.getCompanyId());

        WxMpTemplateMessage msg = new WxMpTemplateMessage();
        msg.setTemplateId(wxMpProperties.getOpenValveMsg());
        //获取请求参数
        String linkCode = openValveMsg.getLinkCode();
        String installLocation = openValveMsg.getInstallLocation();
        String balance = openValveMsg.getBalance();
        String deviceNo = openValveMsg.getDeviceNo();

        if (wxMpProperties.isTemplateV1()) {
            // 组织模板消息参数
            String content = String.format("尊敬的客户您好，您当前户号%s（地址：%s）对应的设备已成功开阀，请知悉。", linkCode, installLocation);
            WxMpTemplateData firstData = new WxMpTemplateData("first", content);
            WxMpTemplateData templateData1 = new WxMpTemplateData("keyword1", balance + " 元");
            WxMpTemplateData templateData2 = new WxMpTemplateData("keyword2", deviceNo);
            WxMpTemplateData remarkData = new WxMpTemplateData("remark", "该账户余额是通过计算后的实际可用余额！");

            msg.addData(firstData);
            msg.addData(templateData1);
            msg.addData(templateData2);
            msg.addData(remarkData);
        } else {
            WxMpTemplateData characterString1Data = new WxMpTemplateData("character_string1", deviceNo);
            WxMpTemplateData thing2Data = new WxMpTemplateData("thing2", getInstallLocationLessThan20(installLocation));
            WxMpTemplateData amount3Data = new WxMpTemplateData("amount3", balance);
            msg.addData(characterString1Data);
            msg.addData(thing2Data);
            msg.addData(amount3Data);
        }

        // 只要有一个openid成功推送，就视为成功
        boolean success = false;
        // 循环发送给所有指定的微信用户
        for (String openid : openIds) {
            try {
                msg.setToUser(openid);
                String response = wxMpService.getTemplateMsgService().sendTemplateMsg(msg);
                log.info("微信公众号模板消息推送: 开阀通知发送结果，openid={}，response={}", openid, response);
                wxMsgRecordService.saveWxMsgRecord(msg, WxMsgRecord.TemplateName.OPEN_VALVE_MSG, response, openValveMsg.getAccountNo(), null);
                success = true;
            } catch (WxErrorException e) {
                log.warn("微信公众号模板消息推送: 开阀通知发送失败，openid={}\n{}", openid, e.getMessage());
                if (ERROR_CODE.contains(e.getError().getErrorCode())) {
                    payCustomerService.unBoundPayCustomer(openValveMsg.getAccountNo(), null, openid);
                }
            }
        }
        return success;
    }

    /**
     * 发送关阀通知
     *
     * @param openIds
     * @param closeValveMsg
     * @param predicate
     */
    public boolean closeValveMsg(List<String> openIds, CloseValveMsg closeValveMsg, Predicate<WxMpProperties> predicate) {
        WxMpProperties wxMpProperties = configService.getWxMpProperties(closeValveMsg.getCompanyId());
        if (!wxMpProperties.isEnableMsg()) {
            return false;
        }
        if (StringUtils.isBlank(wxMpProperties.getCloseValveMsg())) {
            return false;
        }
        // 检查openId参数
        if (CollectionUtil.isEmpty(openIds)) {
            throw new IllegalStateException("微信公众号模板消息推送：请提供要推送的目标用户openId参数");
        }
        // 检查必填参数
        if (!closeValveMsg.isValid()) {
            throw new IllegalStateException(String.format("微信公众号模板消息推送：请提供所有必填参数，closeValveMsg=%s", closeValveMsg));
        }
        if (!predicate.test(wxMpProperties)) {
            return true;
        }

        // 获取公司对应的微信公众号服务接口
        WxMpService wxMpService = wxMpProvider.wxMpService(closeValveMsg.getCompanyId());

        WxMpTemplateMessage msg = new WxMpTemplateMessage();
        msg.setTemplateId(wxMpProperties.getCloseValveMsg());
        //获取请求参数
        String linkCode = closeValveMsg.getLinkCode();
        String installLocation = closeValveMsg.getInstallLocation();
        String oweAmount = closeValveMsg.getOweAmount();
        String deviceNo = closeValveMsg.getDeviceNo();

        if (wxMpProperties.isTemplateV1()) {
            // 组织模板消息参数
            String content = String.format("尊敬的客户您好，您当前户号%s（地址：%s）对应的设备已关阀，请知悉。", linkCode, installLocation);
            WxMpTemplateData firstData = new WxMpTemplateData("first", content);
            WxMpTemplateData templateData1 = new WxMpTemplateData("keyword1", deviceNo);
            WxMpTemplateData templateData2 = new WxMpTemplateData("keyword2", new BigDecimal(oweAmount).compareTo(BigDecimal.ZERO) == 0 ? "无欠费" : oweAmount + " 元");

            msg.addData(firstData);
            msg.addData(templateData1);
            msg.addData(templateData2);
        } else {
            WxMpTemplateData characterString1Data = new WxMpTemplateData("character_string1", deviceNo);
            WxMpTemplateData thing2Data = new WxMpTemplateData("thing2", getInstallLocationLessThan20(installLocation));
            WxMpTemplateData amount3Data = new WxMpTemplateData("amount3", oweAmount);

            msg.addData(characterString1Data);
            msg.addData(thing2Data);
            msg.addData(amount3Data);
        }

        // 只要有一个openid成功推送，就视为成功
        boolean success = false;
        // 循环发送给所有指定的微信用户
        for (String openid : openIds) {
            try {
                msg.setToUser(openid);
                String response = wxMpService.getTemplateMsgService().sendTemplateMsg(msg);
                log.info("微信公众号模板消息推送: 关阀通知发送结果，openid={}，response={}", openid, response);
                wxMsgRecordService.saveWxMsgRecord(msg, WxMsgRecord.TemplateName.CLOSE_VALVE_MSG, response, closeValveMsg.getAccountNo(), null);
                success = true;
            } catch (WxErrorException e) {
                log.warn("微信公众号模板消息推送: 关阀通知发送失败，openid={}， 原因：{}", openid, e.getMessage());
                if (ERROR_CODE.contains(e.getError().getErrorCode())) {
                    payCustomerService.unBoundPayCustomer(closeValveMsg.getAccountNo(), null, openid);
                }
            }
        }
        return success;
    }

    /**
     * 发送余额预警通知
     *
     * @param openIds
     * @param balanceWarnNotifyMsg
     */
    public boolean balanceWarnNotify(List<String> openIds, BalanceWarnNotifyMsg balanceWarnNotifyMsg, Predicate<WxMpProperties> predicate) {
        // 判断当前是否开启公众号模板消息能力
        WxMpProperties wxMpProperties = configService.getWxMpProperties(balanceWarnNotifyMsg.getCompanyId());
        if (!wxMpProperties.isEnableMsg()) {
            return false;
        }
        if (StringUtils.isBlank(wxMpProperties.getBalanceWarnNotifyMsg())) {
            return false;
        }
        // 检查openId参数
        if (CollectionUtil.isEmpty(openIds)) {
            throw new IllegalStateException("微信公众号模板消息推送：请提供要推送的目标用户openId参数");
        }
        // 检查必填参数
        if (!balanceWarnNotifyMsg.isValid()) {
            throw new IllegalStateException(String.format("微信公众号模板消息推送：请提供所有必填参数，balanceWarnNotifyMsg=%s", balanceWarnNotifyMsg));
        }
        if (!predicate.test(wxMpProperties)) {
            return true;
        }

        // 获取公司对应的微信公众号服务接口
        WxMpService wxMpService = wxMpProvider.wxMpService(balanceWarnNotifyMsg.getCompanyId());

        WxMpTemplateMessage msg = new WxMpTemplateMessage();
        msg.setTemplateId(wxMpProperties.getBalanceWarnNotifyMsg());
        // 获取请求参数
        String linkCode = balanceWarnNotifyMsg.getLinkCode();
        String installLocation = balanceWarnNotifyMsg.getInstallLocation();
        String warningAmount = balanceWarnNotifyMsg.getWarningAmount();
        String balance = balanceWarnNotifyMsg.getBalance();
        String deviceNo = balanceWarnNotifyMsg.getDeviceNo();

        if (wxMpProperties.isTemplateV1()) {
            // 组织模板消息参数
            String content = String.format("尊敬的客户您好，您当前户号%s（地址：%s）账户实际可用余额已小于系统预警值%s元，为不影响您设备的正常使用，请您尽快充值缴费，谢谢。",
                    linkCode, installLocation, warningAmount);
            WxMpTemplateData firstData = new WxMpTemplateData("first", content);
            WxMpTemplateData templateData1 = new WxMpTemplateData("keyword1", deviceNo);
            WxMpTemplateData templateData2 = new WxMpTemplateData("keyword2", balance + " 元");
            WxMpTemplateData remarkData = new WxMpTemplateData("remark", "该账户余额是通过计算后的实际可用余额！");

            msg.addData(firstData);
            msg.addData(templateData1);
            msg.addData(templateData2);
            msg.addData(remarkData);
        } else if (wxMpProperties.isTemplateV2()) {
            WxMpTemplateData characterString17Data = new WxMpTemplateData("character_string17", linkCode);
            WxMpTemplateData thing8Data = new WxMpTemplateData("thing8", getInstallLocationLessThan20(installLocation));
            WxMpTemplateData characterString16Data = new WxMpTemplateData("character_string16", deviceNo);
            WxMpTemplateData thing15Data = new WxMpTemplateData("thing15", String.format("账户余额:%s", balance));

            msg.addData(characterString17Data);
            msg.addData(thing8Data);
            msg.addData(characterString16Data);
            msg.addData(thing15Data);
        } else {
            WxMpTemplateData characterString17Data = new WxMpTemplateData("character_string17", linkCode);
            WxMpTemplateData thing8Data = new WxMpTemplateData("thing8", getInstallLocationLessThan20(installLocation));
            WxMpTemplateData characterString16Data = new WxMpTemplateData("character_string16", deviceNo);
            WxMpTemplateData thing15Data = new WxMpTemplateData("amount19", balance + "元");

            msg.addData(characterString17Data);
            msg.addData(thing8Data);
            msg.addData(characterString16Data);
            msg.addData(thing15Data);
        }

        // 只要有一个openid成功推送，就视为成功
        boolean success = false;
        // 循环发送给所有指定的微信用户
        for (String openid : openIds) {
            try {
                msg.setToUser(openid);
                String response = wxMpService.getTemplateMsgService().sendTemplateMsg(msg);
                log.info("微信公众号模板消息推送: 余额预警通知发送结果，openid={}，response={}", openid, response);
                wxMsgRecordService.saveWxMsgRecord(msg, WxMsgRecord.TemplateName.BALANCE_WARN_NOTIFY, response, balanceWarnNotifyMsg.getAccountNo(), null);
                success = true;
            } catch (WxErrorException e) {
                log.warn("微信公众号模板消息推送: 余额预警通知发送失败，openid={}\n{}", openid, e.getMessage());
                if (ERROR_CODE.contains(e.getError().getErrorCode())) {
                    payCustomerService.unBoundPayCustomer(balanceWarnNotifyMsg.getAccountNo(), null, openid);
                }
            }
        }
        return success;
    }

    /**
     * 发送在线开户申请结果通知
     *
     * @param openIds
     * @param openAccountNotifyMsg
     */
    public boolean openAccountWarnNotify(List<String> openIds, OpenAccountNotifyMsg openAccountNotifyMsg) {
        // 判断当前是否开启公众号模板消息能力
        WxMpProperties wxMpProperties = configService.getWxMpProperties(openAccountNotifyMsg.getCompanyId());
        if (!wxMpProperties.isEnableMsg()) {
            return false;
        }
        if (StringUtils.isBlank(wxMpProperties.getOpenAccountNotifyMsg())) {
            return false;
        }
        // 检查openId参数
        if (CollectionUtil.isEmpty(openIds)) {
            throw new IllegalStateException("微信公众号模板消息推送：请提供要推送的目标用户openId参数");
        }
        // 检查必填参数
        if (!openAccountNotifyMsg.isValid()) {
            throw new IllegalStateException(String.format("微信公众号模板消息推送：请提供所有必填参数，openAccountNotifyMsg=%s", openAccountNotifyMsg));
        }
        // 获取公司对应的微信公众号服务接口
        WxMpService wxMpService = wxMpProvider.wxMpService(openAccountNotifyMsg.getCompanyId());

        WxMpTemplateMessage msg = new WxMpTemplateMessage();
        msg.setTemplateId(wxMpProperties.getOpenAccountNotifyMsg());

        // 获取请求参数
        String userName = openAccountNotifyMsg.getUserName();
        String mobile = openAccountNotifyMsg.getMobile();
        String applyDate = openAccountNotifyMsg.getApplyDate();
        ApplyStatus applyStatus = openAccountNotifyMsg.getApplyStatus();

        if (wxMpProperties.isTemplateV1()) {
            String first = applyStatus == ApplyStatus.AUDIT_SUCCESS ? "尊敬的客户您好，您有一个开户申请已审核通过" : "尊敬的客户您好，您有一个开户申请审核未通过";
            String remake = applyStatus == ApplyStatus.AUDIT_SUCCESS ? "审核已通过，请前往公众号内查看详情" : openAccountNotifyMsg.getFailReason();

            // 组织模板消息参数
            WxMpTemplateData firstData = new WxMpTemplateData("first", first);
            WxMpTemplateData templateData1 = new WxMpTemplateData("keyword1", userName);
            WxMpTemplateData templateData2 = new WxMpTemplateData("keyword2", mobile);
            WxMpTemplateData templateData3 = new WxMpTemplateData("keyword3", "开户申请");
            WxMpTemplateData templateData4 = new WxMpTemplateData("keyword4", applyDate);
            WxMpTemplateData remarkData = new WxMpTemplateData("remark", remake);

            msg.addData(firstData);
            msg.addData(templateData1);
            msg.addData(templateData2);
            msg.addData(templateData3);
            msg.addData(templateData4);
            msg.addData(remarkData);
        } else {
            WxMpTemplateData thing8Data = new WxMpTemplateData("thing8", userName);
            WxMpTemplateData phoneNumber9Data = new WxMpTemplateData("phone_number9", mobile);
            WxMpTemplateData time2Data = new WxMpTemplateData("time2", applyDate);
            WxMpTemplateData phrase3Data = new WxMpTemplateData("phrase3", applyStatus == ApplyStatus.AUDIT_SUCCESS ? "审核通过" : applyStatus == ApplyStatus.AUDIT_FAIL ? "审核失败" : "待审核");

            msg.addData(thing8Data);
            msg.addData(phoneNumber9Data);
            msg.addData(time2Data);
            msg.addData(phrase3Data);
        }

        String url = WX_HOME_PAGE + wxMpProperties.getAppId();
        if (applyStatus != ApplyStatus.AUDIT_SUCCESS) {
            url = url + WX_MSG_TEMPLATE_PAGE + WX_OPEN_ACCOUNT_FAIL;
        }
        msg.setUrl(url);

        // 只要有一个openid成功推送，就视为成功
        boolean success = false;
        // 循环发送给所有指定的微信用户
        for (String openid : openIds) {
            try {
                msg.setToUser(openid);
                String response = wxMpService.getTemplateMsgService().sendTemplateMsg(msg);
                log.info("微信公众号模板消息推送: 在线开户申请结果通知发送结果，openid={}，response={}", openid, response);
                wxMsgRecordService.saveWxMsgRecord(msg, WxMsgRecord.TemplateName.OPEN_ACCOUNT_WARN_NOTIFY, response, null, openAccountNotifyMsg.getMeterId());
                success = true;
            } catch (WxErrorException e) {
                log.warn("微信公众号模板消息推送: 在线开户申请结果通知发送失败，openid={}\n{}", openid, e.getMessage());
                if (ERROR_CODE.contains(e.getError().getErrorCode())) {
                    payCustomerService.unBoundPayCustomer(null, openAccountNotifyMsg.getMeterId(), openid);
                }
            }
        }
        return success;
    }

    /**
     * 获取安装位置的缩略内容（小于20位）
     *
     * @param str
     * @return
     */
    private String getInstallLocationLessThan20(String str) {
        if (str == null) {
            return "暂无";
        }
        return str.length() < 20 ? str : str.substring(0, 17) + "...";
    }
}
