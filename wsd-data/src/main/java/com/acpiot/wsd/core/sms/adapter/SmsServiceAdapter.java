package com.acpiot.wsd.core.sms.adapter;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.core.service.ConfigService;
import com.acpiot.wsd.data.revenue.entity.Customer;
import com.acpiot.wsd.data.revenue.entity.MsgTask;
import com.acpiot.wsd.data.revenue.entity.RevenueConfig;
import com.acpiot.wsd.core.sms.bean.SmsReceiver;
import com.acpiot.wsd.core.sms.bean.SmsResult;
import com.acpiot.wsd.data.sms.entity.SmsConfig;
import com.acpiot.wsd.core.sms.properties.SmsProperties;
import com.acpiot.wsd.data.revenue.service.MsgTaskService;
import com.acpiot.wsd.core.sms.service.SmsService;
import com.acpiot.wsd.data.sys.entity.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;

import static com.acpiot.wsd.core.constant.CommonConstant.DEFAULT_SEND_END_HOUR;
import static com.acpiot.wsd.core.constant.CommonConstant.DEFAULT_SEND_START_HOUR;

/**
 * 短信发送适配器，用于组装短信参数并发送短信
 * Created by zc on 2021/5/26 16:09
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SmsServiceAdapter {

    private final ApplicationContext applicationContext;
    private final ConfigService configService;
    private final MsgTaskService msgTaskService;

    private SmsService getSmsService(SmsProperties smsProperties) {
        String prefix;
        SmsConfig.SmsPlatform smsPlatform = smsProperties.getSmsPlatform();
        if (smsPlatform == SmsConfig.SmsPlatform.ALI_YUN) {
            prefix = "ali";
        } else if (smsPlatform == SmsConfig.SmsPlatform.MAS) {
            prefix = "mas";
        } else if (smsPlatform == SmsConfig.SmsPlatform.MAS_TEMP) {
            prefix = "masTemp";
        } else {
            return null;
        }
        return applicationContext.getBean(String.format("%sSmsService", prefix), SmsService.class);
    }

    /**
     * 根据短信平台类型获取短信服务实现类发送短信
     *
     * @param smsReceiver
     * @return
     */
    private SmsResult sendSmsExecutorFunction(SmsReceiver smsReceiver, BiFunction<SmsProperties, SmsService, SmsResult> smsSender) {
        // 获取短信平台配置
        Optional<SmsProperties> optional = configService.getEnabledSmsProperties(smsReceiver.getSmsConfigCompanyId());
        // 判断是否开启短信功能
        if (optional.isEmpty()) {
            return SmsResult.returnNotEnableError();
        }
        SmsService smsService = getSmsService(optional.get());
        if (smsService == null) {
            return SmsResult.returnNotSupportError();
        }
        return smsSender.apply(optional.get(), smsService);
    }

    /**
     * 根据短信平台类型获取短信服务实现类发送短信
     *
     * @param smsReceiver
     * @return
     */
    private void sendSmsExecutorConsumer(SmsReceiver smsReceiver, BiConsumer<SmsProperties, SmsService> smsSender) {
        // 获取短信平台配置
        Optional<SmsProperties> optional = configService.getEnabledSmsProperties(smsReceiver.getSmsConfigCompanyId());
        // 判断是否开启短信功能
        if (optional.isEmpty()) {
            log.info("当前公司尚未开启短信功能，companyId={}", smsReceiver.getSmsConfigCompanyId());
            return;
        }
        SmsService smsService = getSmsService(optional.get());
        if (smsService == null) {
            log.error("未识别的短信平台");
            return;
        }
        smsSender.accept(optional.get(), smsService);
    }

    /**
     * 发送充值成功通知
     *
     * @param customer    客户
     * @param accountNo   户号
     * @param amount      充值金额
     * @param balance     账户余额
     * @param realBalance 实际可用余额
     */
    public void rechargeSuccess(Customer customer, String accountNo, BigDecimal amount, BigDecimal balance, BigDecimal realBalance) {
        // 构建短信参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("accountNo", accountNo);
        paramMap.put("amount", amount.stripTrailingZeros().toPlainString());
        paramMap.put("balance", balance.stripTrailingZeros().toPlainString());
        paramMap.put("realBalance", realBalance.stripTrailingZeros().toPlainString());
        // 发送短信
        SmsReceiver smsReceiver = new SmsReceiver(customer);
        sendSmsExecutorConsumer(smsReceiver, (smsProperties, smsService) -> smsService.rechargeSuccess(smsReceiver, paramMap));
    }

    /**
     * 发送缴费成功通知
     *
     * @param customer    客户
     * @param accountNo   户号
     * @param period      账单周期
     * @param amount      缴费金额
     * @param paymentType 缴费方式
     */
    public void paySuccess(Customer customer, String accountNo, String period, BigDecimal amount, String paymentType) {
        SmsReceiver smsReceiver = new SmsReceiver(customer);
        sendSmsExecutorConsumer(smsReceiver, (smsProperties, smsService) -> {
            // 如果没有配置模板编码则不处理
            if (StrUtil.isBlank(smsProperties.getSmsPaySuccess())) {
                return;
            }
            // 如果缴费类型为“系统自动余额结算”，并且不在发送时间内，才需要添加到消息任务中
            if ("系统自动余额结算".equals(paymentType) && unableSendByTime(smsProperties)) {
                // 将要短信添加到发送任务表中
                Map<String, Object> content = MapUtil.<String, Object>builder()
                        .put("customerId", customer.getId())
                        .put("accountNo", accountNo)
                        .put("period", period)
                        .put("amount", amount)
                        .put("paymentType", paymentType).map();
                MsgTask msgTask = buildMsgTask(customer, "paySuccessCode", "缴费成功通知", content);
                msgTaskService.save(msgTask);
            } else {
                // 构建短信参数
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("accountNo", accountNo);
                paramMap.put("period", period);
                paramMap.put("amount", amount.stripTrailingZeros().toPlainString());
                paramMap.put("paymentType", paymentType);
                smsService.paySuccess(smsReceiver, paramMap);
            }
        });
    }

    /**
     * 发送开户成功通知
     *
     * @param customer  客户
     * @param deviceNo  设备号
     * @param accountNo 户号
     */
    public void openAccountCode(Customer customer, String deviceNo, String accountNo) {
        // 构建短信参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("deviceNo", deviceNo);
        paramMap.put("accountNo", accountNo);
        // 发送短信
        SmsReceiver smsReceiver = new SmsReceiver(customer);
        sendSmsExecutorConsumer(smsReceiver, (smsProperties, smsService) -> smsService.openAccountCode(smsReceiver, paramMap));
    }

    /**
     * 发送销户成功通知
     *
     * @param customer  客户
     * @param deviceNo  设备号
     * @param accountNo 户号
     */
    public void closeAccountCode(Customer customer, String deviceNo, String accountNo) {
        // 构建短信参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("deviceNo", deviceNo);
        paramMap.put("accountNo", accountNo);
        // 发送短信
        SmsReceiver smsReceiver = new SmsReceiver(customer);
        sendSmsExecutorConsumer(smsReceiver, (smsProperties, smsService) -> smsService.closeAccountCode(smsReceiver, paramMap));
    }

    /**
     * 开阀通知
     *
     * @param customer  客户
     * @param accountNo 户号
     * @param balance   实际可用余额
     */
    public void openValve(Customer customer, String accountNo, BigDecimal balance) {
        SmsReceiver smsReceiver = new SmsReceiver(customer);
        sendSmsExecutorConsumer(smsReceiver, (smsProperties, smsService) -> {
            // 如果没有配置模板编码则不处理
            if (StrUtil.isBlank(smsProperties.getOpenValve())) {
                return;
            }
            // 如果不在发送时间内，需要添加到消息任务中
            if (unableSendByTime(smsProperties)) {
                // 将要短信添加到发送任务表中
                Map<String, Object> content = MapUtil.<String, Object>builder()
                        .put("customerId", customer.getId())
                        .put("accountNo", accountNo)
                        .put("balance", balance).map();
                MsgTask msgTask = buildMsgTask(customer, "openValveCode", "开阀通知", content);
                msgTaskService.save(msgTask);
            } else {
                // 构建短信参数
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("accountNo", accountNo);
                paramMap.put("balance", balance.stripTrailingZeros().toPlainString());
                smsService.openValve(smsReceiver, paramMap);
            }
        });
    }

    /**
     * 关阀通知
     *
     * @param customer  客户
     * @param accountNo 户号
     * @param balance   实际可用余额
     */
    public void closeValve(Customer customer, String accountNo, BigDecimal balance) {
        SmsReceiver smsReceiver = new SmsReceiver(customer);
        sendSmsExecutorConsumer(smsReceiver, (smsProperties, smsService) -> {
            // 如果没有配置模板编码则不处理
            if (StrUtil.isBlank(smsProperties.getCloseValve())) {
                return;
            }
            // 如果不在发送时间内，需要添加到消息任务中
            if (unableSendByTime(smsProperties)) {
                // 将要短信添加到发送任务表中
                Map<String, Object> content = MapUtil.<String, Object>builder()
                        .put("customerId", customer.getId())
                        .put("accountNo", accountNo)
                        .put("balance", balance).map();
                MsgTask msgTask = buildMsgTask(customer, "closeValveCode", "关阀通知", content);
                msgTaskService.save(msgTask);
            } else {
                // 构建短信参数
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("accountNo", accountNo);
                paramMap.put("balance", balance.stripTrailingZeros().toPlainString());
                smsService.closeValve(smsReceiver, paramMap);
            }
        });
    }

    /**
     * 账单缴费提醒
     *
     * @param customer     客户
     * @param billId       账单id
     * @param waterMeterId 水表id
     * @param accountNo    户号
     */
    public void billPaymentRemind(Customer customer, Long billId, Long waterMeterId, String accountNo) {
        SmsReceiver smsReceiver = new SmsReceiver(customer);
        sendSmsExecutorConsumer(smsReceiver, (smsProperties, smsService) -> {
            // 如果没有配置模板编码则不处理
            if (StrUtil.isBlank(smsProperties.getBillPaymentRemind())) {
                return;
            }
            // 如果不在发送时间内，需要添加到消息任务中
            if (unableSendByTime(smsProperties)) {
                // 将要短信添加到发送任务表中
                MapBuilder<String, Object> mapBuilder = MapUtil.<String, Object>builder()
                        .put("customerId", customer.getId())
                        .put("waterMeterId", waterMeterId);
                if (billId != null) {
                    mapBuilder.put("billId", billId);
                }
                Map<String, Object> content = mapBuilder.build();
                MsgTask msgTask = buildMsgTask(customer, "billPaymentRemindCode", "账单缴费提醒", content);
                msgTask.setAccountNo(accountNo);
                msgTaskService.save(msgTask);
            } else {
                // 构建短信参数
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("waterMeterId", String.valueOf(waterMeterId));
                if (billId != null) {
                    paramMap.put("billId", String.valueOf(billId));
                }
                smsService.billPaymentRemind(smsReceiver, paramMap);
            }
        });
    }

    /**
     * 主动发送账单缴费提醒，无视短信配置
     *
     * @param customer     客户
     * @param billId       账单id
     * @param waterMeterId 水表id
     */
    public void activeBillPaymentRemind(Customer customer, Long billId, Long waterMeterId) {
        SmsReceiver smsReceiver = new SmsReceiver(customer);
        sendSmsExecutorConsumer(smsReceiver, (smsProperties, smsService) -> {
            // 如果没有配置模板编码则不处理
            if (StrUtil.isBlank(smsProperties.getBillPaymentRemind())) {
                return;
            }
            // 构建短信参数
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("waterMeterId", String.valueOf(waterMeterId));
            if (billId != null) {
                paramMap.put("billId", String.valueOf(billId));
            }
            smsService.billPaymentRemind(smsReceiver, paramMap);
        });
    }

    /**
     * 余额预警通知
     *
     * @param customer       客户
     * @param accountNo      户号
     * @param balance        实际可用余额
     * @param balanceWarning 系统预警值
     */
    public void balanceWarnNotify(Customer customer, String accountNo, BigDecimal balance, BigDecimal balanceWarning) {
        SmsReceiver smsReceiver = new SmsReceiver(customer);
        sendSmsExecutorConsumer(smsReceiver, (smsProperties, smsService) -> {
            // 如果没有配置模板编码则不处理
            if (StrUtil.isBlank(smsProperties.getBalanceWarnNotify())) {
                return;
            }
            Optional<MsgTask> currentMonthTask = msgTaskService.findCurrentMonthBalanceWarnTask(accountNo, MsgTask.MsgType.SMS);
            Map<String, Object> content = MapUtil.<String, Object>builder()
                    .put("customerId", customer.getId())
                    .put("accountNo", accountNo)
                    .put("balance", balance)
                    .put("balanceWarning", balanceWarning).map();
            // 如果不在发送时间内，需要添加到消息任务中
            if (unableSendByTime(smsProperties)) {
                // 当月已缓存过任务，更新短信内容
                if (currentMonthTask.isPresent()) {
                    msgTaskService.updateContentById(currentMonthTask.get().getId(), content);
                } else {
                    // 将要短信添加到发送任务表中
                    MsgTask msgTask = buildMsgTask(customer, "balanceWarnNotifyCode", "余额预警通知", content);
                    msgTask.setAccountNo(accountNo);
                    msgTask.setSendNum(0);
                    msgTaskService.save(msgTask);
                }
            } else {
                // 判断当月是否已经缓存过该任务，若未缓存该任务，需要发送后创建一条发送次数为1、上次发送时间为当前时间的任务
                if (currentMonthTask.isEmpty()) {
                    // 将短信任务添加到发送任务表中，已发送1次
                    MsgTask msgTask = buildMsgTask(customer, "balanceWarnNotifyCode", "余额预警通知", content);
                    msgTask.setAccountNo(accountNo);
                    msgTask.setLastSendTime(new Date());
                    msgTask.setSendNum(1);
                    msgTaskService.save(msgTask);
                    this.sendBalanceWarnNotify(smsService, smsReceiver, accountNo, balance, balanceWarning);
                } else {
                    // 任务内容无论如何都得更新
                    msgTaskService.updateContentById(currentMonthTask.get().getId(), content);
                    // 根据公司ID查询当前公司的营收配置
                    Optional<RevenueConfig> revenueConfigOptional = configService.getEnabledRevenueConfigByCompanyId(customer.getCompanyId());
                    MsgTask msgTask = currentMonthTask.get();
                    // 没有营收配置或者配置了营收配置，但是没有配置次数和间隔天数，仅执行一次任务，删除该任务
                    if (revenueConfigOptional.isEmpty() || revenueConfigOptional.get().getWarningAmountTimes() == null) {
                        if (msgTask.getSendNum() == 0) {
                            this.sendBalanceWarnNotify(smsService, smsReceiver, accountNo, balance, balanceWarning);
                            // 发送完成后，更新任务状态
                            msgTaskService.updateSendNumAndLastSendTimeById(msgTask.getId(), 1, new Date());
                        }
                        return;
                    }
                    RevenueConfig revenueConfig = revenueConfigOptional.get();
                    // 否则取出当前任务中的最后执行时间，判断是否在允许执行的时间内
                    int currentSendNum = msgTask.getSendNum();
                    Date lastSendTime = msgTask.getLastSendTime();
                    if (lastSendTime != null) {
                        // 发送间隔时间
                        int warningAmountDayInterval = revenueConfig.getWarningAmountDayInterval();
                        // 配置的最大发送次数
                        int warningAmountTimes = revenueConfig.getWarningAmountTimes();
                        Date now = new Date();
                        long dayInterval = DateUtil.betweenDay(lastSendTime, now, true);
                        // 未满足发送条件，直接返回，不进行发送
                        if (dayInterval < warningAmountDayInterval || currentSendNum >= warningAmountTimes) {
                            return;
                        }
                    }
                    this.sendBalanceWarnNotify(smsService, smsReceiver, accountNo, balance, balanceWarning);
                    // 发送完成后，更新任务状态
                    msgTaskService.updateSendNumAndLastSendTimeById(msgTask.getId(), currentSendNum + 1, new Date());
                }
            }
        });
    }

    private void sendBalanceWarnNotify(SmsService smsService, SmsReceiver smsReceiver, String accountNo, BigDecimal balance, BigDecimal balanceWarning) {
        // 构建短信参数并发送
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("accountNo", accountNo);
        paramMap.put("balance", balance.stripTrailingZeros().toPlainString());
        paramMap.put("balanceWarning", balanceWarning.stripTrailingZeros().toPlainString());
        smsService.balanceWarnNotify(smsReceiver, paramMap);
    }

    /**
     * 发送登录验证码
     *
     * @param user 用户
     * @param code 验证码
     * @return
     */
    public SmsResult loginValidateCode(User user, String code) {
        // 构建短信参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("code", code);
        // 发送短信
        SmsReceiver smsReceiver = new SmsReceiver(user);
        return sendSmsExecutorFunction(smsReceiver, (smsProperties, smsService) -> smsService.loginValidateCode(smsReceiver, paramMap));
    }

    /**
     * 发送修改密码验证码
     *
     * @param user 用户
     * @param code 验证码
     * @return
     */
    public SmsResult updatePasswordCode(User user, String code) {
        // 构建短信参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("code", code);
        // 发送短信
        SmsReceiver smsReceiver = new SmsReceiver(user);
        return sendSmsExecutorFunction(smsReceiver, (smsProperties, smsService) -> smsService.updatePasswordCode(smsReceiver, paramMap));
    }

    /**
     * 发送找回密码验证码
     *
     * @param user 用户
     * @param code 验证码
     * @return
     */
    public SmsResult forgetPasswordCode(User user, String code) {
        // 构建短信参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("code", code);
        // 发送短信
        SmsReceiver smsReceiver = new SmsReceiver(user);
        return sendSmsExecutorFunction(smsReceiver, (smsProperties, smsService) -> smsService.forgetPasswordCode(smsReceiver, paramMap));
    }

    /**
     * 当前时间是否可以发送短信
     *
     * @return
     */
    private boolean unableSendByTime(SmsProperties smsProperties) {
        // 优先取客户自定义的发送时间段
        Integer sendStartHour = smsProperties.getSendStartHour();
        Integer sendEndHour = smsProperties.getSendEndHour();
        // 客户未自定义发送时间段，取系统默认值
        if (sendEndHour == null) {
            sendStartHour = DEFAULT_SEND_START_HOUR;
            sendEndHour = DEFAULT_SEND_END_HOUR;
        }
        // 判断当前时间是否在可发送短信的时间范围内
        LocalDateTime localDateTime = LocalDateTime.now();
        int hour = localDateTime.getHour();
        return hour < sendStartHour || hour > sendEndHour;
    }

    private MsgTask buildMsgTask(Customer customer, String msgCode, String msgName, Map<String, Object> content) {
        // 将要短信添加到发送任务表中
        MsgTask msgTask = new MsgTask();
        msgTask.setMsgType(MsgTask.MsgType.SMS);
        msgTask.setMsgCode(msgCode);
        msgTask.setMsgName(msgName);
        msgTask.setContent(content);
        msgTask.setCompanyId(customer.getCompanyId());
        return msgTask;
    }
}
