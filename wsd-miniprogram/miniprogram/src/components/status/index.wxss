/* components/status/index.wxss */
:host {
    height: 100%;
}

.status-group {
    display: inline-flex;
    align-items: center;
    justify-content: space-around;
    height: 100%;
}

.status-group .img-box {
    width: 50rpx;
    height: 50rpx;
}

.status-group .img {
    width: 100%;
    height: 100%;
}

.status-dialog {
    display: flex;
    justify-content: space-around;
    padding: 24rpx 0;
    font-size: 28rpx;
}

.status-dialog .status-item {
    text-align: center
}

.status-dialog .img {
    width: 70rpx;
    height: 70rpx;
}