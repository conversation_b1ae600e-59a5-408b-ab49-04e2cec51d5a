<!--components/status/index.wxml-->
<van-dialog catchtap="onClose" use-slot title="状态" show="{{ show }}" bind:confirm="confirm" bind:close="onClose" confirm-button-color="#000000">
    <view class="status-dialog">
        <view wx:if="{{customerId !== null || cancellation !== null}}" class="status-item">
            <block wx:if="{{!customerId}}">
                <image class="img" src="https://www.acp-iot.com/wsd-ctwing/mini/icons/accountState3.svg" mode="aspectFit"></image>
                <view>未开户</view>
            </block>
            <block wx:elif="{{customerId && !cancellation}}">
                <image class="img" src="https://www.acp-iot.com/wsd-ctwing/mini/icons/accountState1.svg" mode="aspectFit"></image>
                <view>已开户</view>
            </block>
            <block wx:else>
                <image class="img" src="https://www.acp-iot.com/wsd-ctwing/mini/icons/accountState2.svg" mode="aspectFit"></image>
                <view>已销户</view>
            </block>
        </view>

        <view wx:if="{{online != null}}" class="status-item">
            <block wx:if="{{online}}">
                <image class="img" src="https://www.acp-iot.com/wsd-ctwing/mini/icons/online1.svg" mode="aspectFit"></image>
                <view>在线</view>
            </block>
            <block wx:else>
                <image class="img" src="https://www.acp-iot.com/wsd-ctwing/mini/icons/online2.svg" mode="aspectFit"></image>
                <view>离线</view>
            </block>
        </view>

        <view wx:if="{{sync != null}}" class="status-item">
            <block wx:if="{{sync}}">
                <image class="img" src="https://www.acp-iot.com/wsd-ctwing/mini/icons/syn_success.svg" mode="aspectFit"></image>
                <view>已同步</view>
            </block>
            <block wx:else>
                <image class="img" src="https://www.acp-iot.com/wsd-ctwing/mini/icons/syn_fail.svg" mode="aspectFit"></image>
                <view>未同步</view>
            </block>
        </view>
    </view>
</van-dialog>