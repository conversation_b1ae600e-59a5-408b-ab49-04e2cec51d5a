<!--components/picker/picker.wxml-->
<van-field
        readonly
        required="{{required}}"
        label="{{labelWidth == 0 ? null : label}}"
        title-width="{{labelWidth}}"
        value="{{ value }}"
        placeholder="{{placeholder}}"
        bindtap="show"
        error-message="{{errorMessage}}"
        disabled="{{disabled}}"
        input-align="{{align}}"
        is-link="true"
        border="{{ border }}"
        arrow-direction="down"
/>
<van-popup show="{{ show }}" z-index="{{102}}" position="top" custom-style="height: auto;" close-on-click-overlay="true" bind:click-overlay="close">
    <van-field bind:clear="clear" wx:if="{{hasSearch}}" input-align="center" custom-style="font-size:30rpx!important;" center clearable placeholder="{{searchPlaceholder}}"
               model:value="{{filterValue}}" disabled="{{loading}}" border="{{ true }}" use-button-slot>
        <van-button disabled="{{loading}}" slot="button" size="small" type="primary" icon="search" bindtap="filter"></van-button>
    </van-field>
    <van-picker id="picker" loading="{{loading}}" cancel-button-text=" " toolbar-position="bottom" show-toolbar model:default-index="{{ index }}" title="{{title}}"
                columns="{{ filterColumns }}" bind:confirm="close" bind:change="change" bind:cancel="close"/>
</van-popup>