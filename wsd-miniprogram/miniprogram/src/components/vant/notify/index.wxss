@import '../common/index.wxss';

.van-notify {
    word-wrap: break-word;
    font-size: var(--notify-font-size, 14px);
    line-height: var(--notify-line-height, 20px);
    padding: var(--notify-padding, 6px 15px);
    text-align: center
}

.van-notify__container {
    box-sizing: border-box;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%
}

.van-notify--primary {
    background-color: var(--notify-primary-background-color, #1989fa)
}

.van-notify--success {
    background-color: var(--notify-success-background-color, #07c160)
}

.van-notify--danger {
    background-color: var(--notify-danger-background-color, #ee0a24)
}

.van-notify--warning {
    background-color: var(--notify-warning-background-color, #ff976a)
}