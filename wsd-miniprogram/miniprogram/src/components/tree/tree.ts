// components/tree/tree.ts
Component({
    /**
     * 组件的属性列表
     */
    properties: {
        data: {
            type: Array,
            value: []
        
        },
        keys: {
            type: Array,
            value: []
        },
        cascade: {
            type: Boolean,
            value: false
        }
    },

    /**
     * 组件的初始数据
     */
    data: {
        activeNames: [],

    },

    /**
     * 组件的方法列表
     */
    methods: {
        check(e: any) {
            let item = e.currentTarget.dataset.data;
            let index = this.data.keys.findIndex((id) => {
                return id === item.id
            });
            let checked;
            if (index === -1) {
                this.data.keys.push(item.id);
                checked = true
            } else {
                this.data.keys.splice(index, 1);
                checked = false
            }
            let updateKey = `data[${e.currentTarget.dataset.i}].checked`;
            this.setData({
                [updateKey]: checked,
                keys: [...this.data.keys]
            })
            this.triggerEvent('checked', this.data.keys);
        },
        onChecked(e: any) {
            this.setData({
                keys: [...e.detail]
            })
            this.triggerEvent('checked', e.detail);
        },


        setAll(checked: boolean) {
            this.data.data.forEach((e: any) => {
                e.checked = checked
            })
            this.setData({
                data: this.data.data,
            });
            this.selectAllComponents('.parent').forEach((item: any) => {
                item.setAll(checked)
            })
        },

       
    },

    lifetimes: {
        attached() {
            console.log('attached')
            this.data.data.forEach((item: any, index: number) => {
                if ((!item.children || !item.children.length ) && this.data.keys.includes(item.id)) {
                    let key = `data[${index}].checked`
                    this.setData({
                        [key]: true
                    })
                }
            });
        }
    }
})
