import {NbAbstractMonitorProcess} from "../NbAbstractMonitorProcess";
import {CommandCode} from "../../NbCmdConstants";
import stringUtils from '../../../../utils/StringUtils';

/**
 * NB单播抄表帧数据包解析
 */
export class NbRelayReadMetersProcess extends NbAbstractMonitorProcess {

    /**
     * 当前命令类型
     */
    private readonly commandCode: CommandCode.CMD_33;

    private imeis: string[] = [];

    constructor(commandCode: CommandCode.CMD_33) {
        super();
        this.commandCode = commandCode;
    }

    /**
     * 接收字节数组，完成NB协议解析
     * @param buffer NB协议报文体
     */
    process(buffer: ArrayBuffer): void {
        // 获取响应报文中的目标地址，用于验证是否是对应的设备返回
        let targetDeviceId = super.parseDeviceId(buffer, 12);
        super.setTargetDeviceId(targetDeviceId);
        let uint8Array: Uint8Array = new Uint8Array(buffer);
        // 判断报文中是否带有中继
        let isRelay: boolean = super.isRelay(buffer, 2);
        // 根据是否有中继调整读取数据域中的数据域的位置
        let dataIndex = isRelay ? 29 : 21;
        // 读取数据域长度
        let dataLength = uint8Array[dataIndex - 1];
        // 获取数据域内容区域
        let dataBuffer = buffer.slice(dataIndex, dataIndex + 1 + dataLength - 1);
        let uDataArray: Uint8Array = new Uint8Array(dataBuffer);
        // 中继下挂水表个数
        let number = uDataArray[0];
        for (let i = 1; i < number * 8; i = i + 8) {
            let imei: ArrayBuffer = uDataArray.slice(i, i + 8).reverse();
            this.imeis.push(stringUtils.buffer2hex(imei));
        }
        super.setResult(true);
    }

    public getCommandCode() {
        return this.commandCode;
    }

    public getImeis(): string[] {
        return this.imeis;
    }
}
