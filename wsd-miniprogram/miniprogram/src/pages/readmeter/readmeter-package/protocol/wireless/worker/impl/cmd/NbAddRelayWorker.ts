import {NbAbstractCmdWorker} from "../NbAbstractCmdWorker";
import {CommandCode} from "../../../NbCmdConstants";
import protocolUtils from '../../../../ProtocolUtils';
import { OperateLog } from '../../../../bean/OperateLog';

/**
 * NB无线协议工作单元-命令帧-水表添加中继
 */
export class NbAddRelayWorker extends NbAbstractCmdWorker {

    /**
     * 中继IMEI
     */
    private relayImei: string;

    public getCommandCode(): number {
        return CommandCode.CMD_01;
    }

    setRelayImei(relayImei: string) {
        if (relayImei.length !== 15) {
            throw new Error('中继IMEI必须为15位！');
        }
        this.relayImei = '0' + relayImei;
    }

    protected buildCommandData(): Array<number> {
        return protocolUtils.deviceIdToArray(this.relayImei).reverse();
    }
    
    /**
     * 操作参数，由具体命令帧提供
     */
    public getOperateParams(): OperateLog {
        let operateLog: OperateLog = new OperateLog();
        operateLog.imei = super.getTargetDeviceId();
        operateLog.operateName = '水表添加中继';
        operateLog.operateParams = '中继IMEI：' + this.relayImei;
        return operateLog;
    }
}
