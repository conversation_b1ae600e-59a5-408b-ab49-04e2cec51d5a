import {NbMonitorProcess} from "./NbMonitorProcess";
import {Factor} from "../NbCmdConstants";
import stringUtils from '../../../utils/StringUtils';

/**
 * 解析NB无线协议，从蓝牙协议中接收完整NB协议数据包完成最终解析
 */
export abstract class NbAbstractMonitorProcess implements NbMonitorProcess {

    /**
     * 解析出来的目标地址
     */
    private targetDeviceId: string;

    /**
     * 指令响应结果
     */
    private result: boolean;

    /**
     * 由具体命令帧负责返回对应命令码
     * @return commandCode
     */
    public abstract getCommandCode(): number;

    /**
     * 接收字节数组，完成NB协议解析
     * @param buffer NB协议报文体
     */
    public abstract process(buffer: ArrayBuffer): void;

    /**
     * 解析设备地址
     * @param buffer
     * @param startIndex
     * @return hex设备地址
     */
    protected parseDeviceId(buffer: ArrayBuffer, startIndex: number): string {
        let targetDeviceIds: ArrayBuffer = buffer.slice(startIndex, startIndex + 8);
        new Uint8Array(targetDeviceIds).reverse();
        return stringUtils.buffer2hex(targetDeviceIds);
    }

    /**
     * 判断报文中是否带有中继
     * @param buffer
     * @param index
     */
    protected isRelay(buffer: ArrayBuffer, index: number): boolean {
        let value = new Uint8Array(buffer)[index];
        return (value & 0x80) == 0x80;
    }

    /**
     * 解析计量类型的数据
     * @param buffer 未经反转的数据区块
     * @param factory 换算因子
     * @return
     */
    protected parseMeteringValue(buffer: ArrayBuffer, factory: number): number {
        // 反转数组
        new Uint8Array(buffer).reverse();
        // 转换16进制字符串
        let valueStr = stringUtils.buffer2hex(buffer);
        // 将字符串转换为整数
        let value: number = parseInt(valueStr);
        // 将值与换算因子相乘
        return value * factory;
    }

    /**
     * 解析计量类型的数据
     * @param buffer 未经反转的数据区块
     * @param factory 换算因子
     * @param scale 保留小数位数
     * @return
     */
    protected parseMeteringValueScale(buffer: ArrayBuffer, factory: number, scale: number): number {
        // 反转数组
        new Uint8Array(buffer).reverse();
        // 转换16进制字符串
        let valueStr = stringUtils.buffer2hex(buffer);
        // 将字符串转换为整数
        let value: number = parseInt(valueStr);
        // 将值与换算因子相乘
        return Number((value * factory).toFixed(scale));
    }

    /**
     * 设置目标地址
     * @param targetDeviceId
     */
    protected setTargetDeviceId(targetDeviceId: string) {
        this.targetDeviceId = targetDeviceId;
    }

    /**
     * 返回目标地址
     */
    public getTargetDeviceId() {
        return this.targetDeviceId;
    }

    /**
     * 设置响应结果
     * @param result
     */
    protected setResult(result: boolean) {
        this.result = result;
    }

    /**
     * 返回响应结果
     */
    public isResult() {
        return this.result;
    }

    /**
     * 获取换算因子
     * @param factorByte
     */
    protected getFactor(factorByte: number): number {
        let factor: number = factorByte & 0xFF;
        if (factor == 0) {
            return Factor.F_0;
        } else if (factor == 1) {
            return Factor.F_1;
        } else if (factor == 2) {
            return Factor.F_2;
        } else if (factor == 3) {
            return Factor.F_3;
        } else if (factor == 4) {
            return Factor.F_4;
        } else {
            console.log('换算因子小数位不能超过4位');
            return null;
        }
    }
}
