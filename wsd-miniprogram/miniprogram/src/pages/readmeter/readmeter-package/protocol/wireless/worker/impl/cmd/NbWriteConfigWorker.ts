import {NbAbstractCmdWorker} from "../NbAbstractCmdWorker";
import {
    CommandCode,
    MeterMode,
    WirelessRate,
    WirelessSleepPeriod,
    WriteMeterConfig
} from "../../../NbCmdConstants";
import {InfraredBaudRate, WirelessChannel} from "../../../../bluetooth/BlueCmdConstants";
import protocolUtils from '../../../../ProtocolUtils';
import { OperateLog } from '../../../../bean/OperateLog';

/**
 * NB无线协议工作单元-命令帧-写配置
 */
export class NbWriteConfigWorker extends NbAbstractCmdWorker {

    /**
     * 水表写配置指令中的数据域参数内容
     */
    private commandData: Array<number>;
    
    /**
     * 配置的水表表号
     */
    private meterCode: string;
    
    /**
     * 配置的每月结算日
     */
    private settlementDay: number;
    
    /**
     * 配置的表计底度
     */
    private baseValue: string;
    
    /**
     * 配置的整数位
     */
    private integerIndex: number;
    
    /**
     * 配置的脉冲量
     */
    private factory: string;
    
    /**
     * 配置的反向流量
     */
    private reverseFlow: string;
    
    /**
     * 配置的红外波特率
     */
    private infraredBaudRate: InfraredBaudRate;
    
    /**
     * 配置的阀门超时时间
     */
    private valveTimeout: number;

    /**
     * 配置水表工作模式
     * @param meterMode
     */
    setMeterMode(meterMode: MeterMode) {
        this.commandData = [];
        this.commandData[0] = WriteMeterConfig.WRITE_METER_MODE.valueOf();
        this.commandData[1] = meterMode.valueOf();
    }

    /**
     * 配置水表表号
     * @param meterCode
     */
    setMeterCode(meterCode: string) {
        this.commandData = [];
        this.commandData[0] = WriteMeterConfig.WRITE_METER_CODE.valueOf();
        // 小端模式，低字节在前，需要反转
        this.meterCode = meterCode;
        protocolUtils.deviceIdToArray(meterCode).reverse().forEach((value) => this.commandData.push(value));
    }

    /**
     * 配置水表每月结算日
     * @param settlementDay
     */
    setMonthSettlementDay(settlementDay: number) {
        this.commandData = [];
        this.commandData[0] = WriteMeterConfig.WRITE_MONTH_SETTLEMENT_DAY.valueOf();
        // BCD码，按16进制发送
        this.commandData[1] = parseInt(settlementDay.toString(), 16);
        this.settlementDay = settlementDay;
    }

    /**
     * 配置计量参数
     * @param integerIndex
     * @param factory
     * @param baseValue
     * @param reverseFlow
     */
    setMeasuringParams(integerIndex: number, factory: number, baseValue: string, reverseFlow: string) {
        this.commandData = [];
        this.commandData[0] = WriteMeterConfig.WRITE_MEASURING_PARAMS.valueOf();
        this.commandData[1] = factory;
        protocolUtils.calcReverseByteValue(baseValue, 4).forEach((value) => this.commandData.push(value));
        protocolUtils.calcReverseByteValue(reverseFlow, 4).forEach((value) => this.commandData.push(value));
        this.commandData.push(integerIndex);
        this.integerIndex = integerIndex;
        let pulseArray = ['100m³', '10m³', '1.0m³', '0.1m³', '0.01m³', '0.001m³', '0.0001m³'];
        this.factory = factory >= 10 ? factory + 'm³' : pulseArray[factory + 2];
        this.baseValue = baseValue;
        this.reverseFlow = reverseFlow;
    }

    /**
     * 设置设备参数
     * @param wirelessSleepPeriod
     * @param wirelessChannel
     * @param wirelessRate
     * @param infraredBaudRate
     * @param valveTimeout
     */
    setMeterParams(wirelessSleepPeriod: WirelessSleepPeriod, wirelessChannel: WirelessChannel, wirelessRate: WirelessRate, infraredBaudRate: InfraredBaudRate, valveTimeout: number) {
        this.commandData = [];
        this.commandData[0] = WriteMeterConfig.WRITE_METER_PARAMS.valueOf();
        this.commandData[1] = (wirelessSleepPeriod.valueOf() << 6) | (wirelessChannel.valueOf() << 2) | wirelessRate;
        this.commandData[2] = infraredBaudRate;
        this.commandData[3] = valveTimeout & 0xFF;
        this.commandData[4] = (valveTimeout >> 8) & 0xFF;
        this.infraredBaudRate = infraredBaudRate;
        this.valveTimeout = valveTimeout;
    }

    /**
     * 设置功能选项参数
     * @param magneticAttack
     * @param dailyFlowCloseValve
     * @param instantaneousFlowAlarm
     * @param ownTouchOpenValve
     * @param autoWashValve
     */
    setFunctionalOption(magneticAttack: number, dailyFlowCloseValve: number, instantaneousFlowAlarm: number, ownTouchOpenValve: number, autoWashValve: number) {
        this.commandData = [];
        this.commandData[0] = WriteMeterConfig.WRITE_FUNCTIONAL_OPTION.valueOf();
        this.commandData[1] = (autoWashValve << 4) | (ownTouchOpenValve << 3) | (instantaneousFlowAlarm << 2) | (dailyFlowCloseValve << 1) | magneticAttack;
        this.commandData[2] = 0x00;
    }

    public getCommandCode(): number {
        return CommandCode.CMD_22;
    }

    protected buildCommandData(): Array<number> {
        return this.commandData;
    }
    
    /**
     * 操作参数，由具体命令帧提供
     */
    public getOperateParams(): OperateLog {
        let operateLog: OperateLog = new OperateLog();
        operateLog.imei = super.getTargetDeviceId();
        let operateName = '';
        let operateParams = '';
        let config: WriteMeterConfig = this.commandData[0];
        switch (config) {
            // 配置水表模式
            case WriteMeterConfig.WRITE_METER_MODE:
                operateName = '配置水表模式';
                let mode = this.commandData[1];
                operateParams = '水表模式：' + (Number(mode) === 0 ? '水表工作模式' : '水表中继模式');
                break;
            // 配置水表表号
            case WriteMeterConfig.WRITE_METER_CODE:
                operateName = '配置水表表号';
                operateParams = '水表表号：' + this.meterCode;
                break;
            // 配置水表每月结算日
            case WriteMeterConfig.WRITE_MONTH_SETTLEMENT_DAY:
                operateName = '配置水表每月结算日';
                operateParams = '每月结算日：' + this.settlementDay;
                break;
            // 配置计量参数
            case WriteMeterConfig.WRITE_MEASURING_PARAMS:
                operateName = '配置计量参数';
                operateParams = '整数位：' + this.integerIndex + '，脉冲当量：' + this.factory + '，表底数：'
                    + this.baseValue + '，反向流量：' + this.reverseFlow;
                break;
            // 配置设备参数
            case WriteMeterConfig.WRITE_METER_PARAMS:
                operateName = '配置设备参数';
                operateParams = '红外波特率：' + this.getInfraredBaudRate(this.infraredBaudRate * 1)
                    + '，阀门超时时间：' + this.valveTimeout;
                break;
            // 配置水表功能选项
            case WriteMeterConfig.WRITE_FUNCTIONAL_OPTION:
                operateName = '配置水表功能选项';
                let functionalOption = this.commandData[1];
                // 取磁攻击
                let magneticAttack = functionalOption & 0x01;
                // 取日流量超限
                let dailyFlowCloseValve = (functionalOption >> 1) & 0x01;
                // 取瞬时流量超限
                let instantaneousFlowAlarm = (functionalOption >> 2) & 0x01;
                // 取欠费触摸
                let ownTouchOpenValve = (functionalOption >> 3) & 0x01;
                // 取自动洗阀
                let autoWashValve = (functionalOption >> 4) & 0x01;
                operateParams = '磁攻击：' + (magneticAttack === 0 ? '仅报警' : '关阀并报警')
                    + '，日流量超限：' + (dailyFlowCloseValve === 0 ? '忽略' : '关阀')
                    + '，瞬时流量超限：' + (instantaneousFlowAlarm === 0 ? '忽略' : '报警')
                    + '，欠费触摸：' + (ownTouchOpenValve === 0 ? '不开' : '开阀')
                    + '，自动洗阀：' + (autoWashValve === 0 ? '开启' : '关闭');
                break;
        }
        operateLog.operateName = operateName;
        operateLog.operateParams = operateParams;
        return operateLog;
    }
    
    getInfraredBaudRate(infraredBaudRate: number): string {
        switch (infraredBaudRate) {
            case 0x00:
                return '1200';
            case 0x01:
                return '2400';
            case 0x02:
                return '4800';
            case 0x03:
                return '9600';
            case 0x04:
                return '19200';
            case 0x05:
                return '38400';
            case 0x06:
                return '57600';
            case 0x07:
                return '115200';
            default:
                return '';
        }
    }
}
