import {NbAbstractCmdWorker} from "../NbAbstractCmdWorker";
import {CommandCode} from "../../../NbCmdConstants";

/**
 * NB无线协议工作单元-命令帧-读中继信号信息
 */
export class NbReadRelaySignalWorker extends NbAbstractCmdWorker {

    public isRelayCommand(): boolean {
        return true;
    }

    public getCommandCode(): number {
        return CommandCode.CMD_35;
    }

    protected buildCommandData(): Array<number> {
        return [];
    }
}
