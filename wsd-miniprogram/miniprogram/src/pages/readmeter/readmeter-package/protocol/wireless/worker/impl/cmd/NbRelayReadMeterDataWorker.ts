import {NbAbstractCmdWorker} from "../NbAbstractCmdWorker";
import {CommandCode} from "../../../NbCmdConstants";
import protocolUtils from '../../../../ProtocolUtils';

/**
 * NB无线协议工作单元-命令帧-读中继下挂水表数据
 */
export class NbRelayReadMeterDataWorker extends NbAbstractCmdWorker {

    /**
     * 水表IMEI
     */
    private imei: string;

    /**
     * 待抄读信道
     */
    private wirelessChannel: number;

    setImei(imei: string) {
        if (imei.length !== 16) {
            throw new Error('IMEI必须为16位！');
        }
        this.imei = imei;
    }

    setWirelessChannel(wirelessChannel: number) {
        this.wirelessChannel = wirelessChannel;
    }

    public isRelayCommand(): boolean {
        return true;
    }

    public getCommandCode(): number {
        return CommandCode.CMD_36;
    }

    protected buildCommandData(): Array<number> {
        let commandData = protocolUtils.deviceIdToArray(this.imei).reverse();
        commandData.push(this.wirelessChannel);
        return commandData;
    }
}
