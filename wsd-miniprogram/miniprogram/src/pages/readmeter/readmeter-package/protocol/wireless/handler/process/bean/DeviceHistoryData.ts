import {MeterSignalInfo} from "./MeterSignalInfo";
import {MeterDataInfo} from "./MeterDataInfo";
import {NoMagnetismInfo} from "./NoMagnetismInfo";
import {DeviceInfo} from "./DeviceInfo";
import {DeviceParameterInfo} from "./DeviceParameterInfo";
import {FunctionalOptionInfo} from "./FunctionalOptionInfo";
import {MeterStateInfo} from "./MeterStateInfo";
import {RelayParameterInfo} from "./RelayParameterInfo";
import {MeterStatisticsInfo} from "./MeterStatisticsInfo";
import {MeterWashValveInfo} from './MeterWashValveInfo';

/**
 * 设备历史数据，用于产测时存储产测结果信息，最多将最近10台产测过的设备的历史数据存储至缓存中
 */
export class DeviceHistoryData {

    /**
     * 当前正在产测的设备地址，用于在设备地址输入栏中显示
     */
    private readonly currentDeviceId: string;

    /**
     * 当前正在操作的设备ID，用于上报抄表结果到后台使用
     * @private
     */
    private readonly currentMeterId: number;

    /**
     * 当前正在操作的设备IMEI，用于下发命令时作为目标地址使用
     * @private
     */
    private readonly currentImei: string;

    /**
     * 表计基础数据（单播抄表、开关阀等命令均会更新该字段中的数据）
     * @private
     */
    private meterDataInfo: MeterDataInfo = new MeterDataInfo();

    /**
     * 水表信号信息
     * @private
     */
    private meterSignalInfo: MeterSignalInfo = new MeterSignalInfo();

    /**
     * 无磁计量模块数据
     */
    private noMagnetismInfo: NoMagnetismInfo = new NoMagnetismInfo();

    /**
     * 设备信息
     */
    private deviceInfo: DeviceInfo = new DeviceInfo();

    /**
     * 设备参数信息
     */
    private deviceParameterInfo: DeviceParameterInfo = new DeviceParameterInfo();

    /**
     * 水表功能选项信息
     */
    private functionalOptionInfo: FunctionalOptionInfo = new FunctionalOptionInfo();

    /**
     * 水表状态信息
     */
    private meterStateInfo: MeterStateInfo = new MeterStateInfo();

    /**
     * 中继选项信息
     */
    private relayParameterInfo: RelayParameterInfo = new RelayParameterInfo();

    /**
     * 水表统计信息
     */
    private meterStatisticsInfo: MeterStatisticsInfo = new MeterStatisticsInfo();

    /**
     * 水表洗阀信息
     */
    private meterWashValveInfo: MeterWashValveInfo = new MeterWashValveInfo();

    /**
     * 水表表号
     */
    private meterCode: string = null;

    /**
     * 反向流量，单播抄表时响应
     */
    private reverseFlow: number = null;

    /**
     * 一键读取成功标志(null：未知，0：失败，1：成功)
     */
    private allRead: number = null;

    /**
     * 读水表表号成功标志(null：未知，0：失败，1：成功)
     */
    private readMeterCode: number = null;

    /**
     * 写水表表号成功标志(null：未知，0：失败，1：成功)
     */
    private writeMeterCode: number = null;

    /**
     * 读水表数据成功标志(null：未知，0：失败，1：成功)
     */
    private readMeterData: number = null;

    /**
     * 写水表数据成功标志(null：未知，0：失败，1：成功)
     */
    private writeMeterData: number = null;
    
    /**
     * 发送洗阀命令成功标志(null：未知，0：失败，1：成功)
     */
    private sendWashValve: number = null;

    /**
     * 开阀成功标志(null：未知，0：失败，1：成功)
     */
    private openValve: number = null;

    /**
     * 关阀成功标志(null：未知，0：失败，1：成功)
     */
    private closeValve: number = null;

    /**
     * 重置NB成功标志(null：未知，0：失败，1：成功)
     */
    private resetNb: number = null;

    /**
     * 启用NB成功标志(null：未知，0：失败，1：成功)
     */
    private openNb: number = null;

    /**
     * 禁用NB成功标志(null：未知，0：失败，1：成功)
     */
    private closeNb: number = null;

    /**
     * 复位MCU成功标志(null：未知，0：失败，1：成功)
     */
    private resetMCU: number = null;

    /**
     * 上报抄表成功标志(null：未知，0：失败，1：成功)
     */
    private reportReadMeterData: number = null;

    /**
     * 流量清零成功标志(null：未知，0：失败，1：成功)
     */
    private resetBaseValue: number = null;

    /**
     * 是否为首次成功发送命令
     */
    private startTest: boolean = false;

    /**
     * 信息上报成功标志(null：未知，0：失败，1：成功)
     */
    private reportDeviceInfo: number = null;

    constructor(currentDeviceId: string, currentMeterId: number, currentImei: string, meterCode: string) {
        this.currentDeviceId = currentDeviceId;
        this.currentMeterId = currentMeterId;
        this.currentImei = currentImei;
        this.meterCode = meterCode;
    }

    static transferDeviceHistory(data: any) {
        const deviceHistory: DeviceHistoryData = new DeviceHistoryData(data.currentDeviceId, data.currentMeterId, data.currentImei, data.meterCode);
        deviceHistory.meterDataInfo = MeterDataInfo.transferMeterDataInfo(data.meterDataInfo);
        deviceHistory.meterSignalInfo = MeterSignalInfo.transferMeterSignalInfo(data.meterSignalInfo);
        deviceHistory.noMagnetismInfo = NoMagnetismInfo.transferNoMagnetismInfo(data.noMagnetismInfo);
        deviceHistory.deviceInfo = DeviceInfo.transferDeviceInfo(data.deviceInfo);
        deviceHistory.deviceParameterInfo = DeviceParameterInfo.transferDeviceParameterInfo(data.deviceParameterInfo);
        deviceHistory.functionalOptionInfo = FunctionalOptionInfo.transferFunctionalOptionInfo(data.functionalOptionInfo);
        deviceHistory.meterStateInfo = MeterStateInfo.transferMeterStateInfo(data.meterStateInfo);
        deviceHistory.relayParameterInfo = RelayParameterInfo.transferRelayParameterInfo(data.relayParameterInfo);
        deviceHistory.meterStatisticsInfo = MeterStatisticsInfo.transferMeterStatisticsInfo(data.meterStatisticsInfo);
        deviceHistory.meterWashValveInfo = MeterWashValveInfo.transferMeterWashValveInfo(data.meterWashValveInfo);
        deviceHistory.reverseFlow = data.reverseFlow;
        deviceHistory.allRead = data.allRead;
        deviceHistory.readMeterCode = data.readMeterCode;
        deviceHistory.writeMeterCode = data.writeMeterCode;
        deviceHistory.readMeterData = data.readMeterData;
        deviceHistory.writeMeterData = data.writeMeterData;
        deviceHistory.sendWashValve = data.sendWashValve;
        deviceHistory.openValve = data.openValve;
        deviceHistory.closeValve = data.closeValve;
        deviceHistory.resetNb = data.resetNb;
        deviceHistory.openNb = data.openNb;
        deviceHistory.closeNb = data.closeNb;
        deviceHistory.resetMCU = data.resetMCU;
        deviceHistory.reportReadMeterData = data.reportReadMeterData;
        deviceHistory.resetBaseValue = data.resetBaseValue;
        deviceHistory.startTest = data.startTest;
        deviceHistory.reportDeviceInfo = data.reportDeviceInfo;
        return deviceHistory;
    }

    getCurrentDeviceId() {
        return this.currentDeviceId;
    }

    getCurrentMeterId() {
        return this.currentMeterId;
    }

    getCurrentImei() {
        return this.currentImei;
    }

    setMeterDataInfo(meterDataInfo: MeterDataInfo) {
        // 如果之前已经读取过整数位了，则保留之前的值
        if (this.meterDataInfo && this.meterDataInfo.getInteger() && !meterDataInfo.getInteger()) {
            meterDataInfo.setInteger(this.meterDataInfo.getInteger());
        }
        this.meterDataInfo = meterDataInfo;
    }

    getMeterDataInfo() {
        return this.meterDataInfo;
    }

    setMeterSignalInfo(meterSignalInfo: MeterSignalInfo) {
        this.meterSignalInfo = meterSignalInfo;
    }

    getMeterSignalInfo() {
        return this.meterSignalInfo;
    }

    setMeterCode(meterCode: string) {
        this.meterCode = meterCode;
    }

    getMeterCode() {
        return this.meterCode;
    }

    setReverseFlow(reverseFlow: number) {
        this.reverseFlow = reverseFlow;
    }

    getReverseFlow() {
        return this.reverseFlow;
    }

    setResetNb(resetNb: number) {
        this.resetNb = resetNb;
    }

    getResetNb() {
        return this.resetNb;
    }

    getNoMagnetismInfo() {
        return this.noMagnetismInfo;
    }

    setNoMagnetismInfo(noMagnetismInfo: NoMagnetismInfo) {
        this.noMagnetismInfo = noMagnetismInfo;
    }

    getDeviceInfo() {
        return this.deviceInfo;
    }

    setDeviceInfo(deviceInfo: DeviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    getDeviceParameterInfo() {
        return this.deviceParameterInfo;
    }

    setDeviceParameterInfo(deviceParameterInfo: DeviceParameterInfo) {
        this.deviceParameterInfo = deviceParameterInfo;
    }

    getFunctionalOptionInfo() {
        return this.functionalOptionInfo;
    }

    setFunctionalOptionInfo(functionalOptionInfo: FunctionalOptionInfo) {
        this.functionalOptionInfo = functionalOptionInfo;
    }

    getMeterStateInfo() {
        return this.meterStateInfo;
    }

    setMeterStateInfo(meterStateInfo: MeterStateInfo) {
        this.meterStateInfo = meterStateInfo;
    }

    getRelayParameterInfo() {
        return this.relayParameterInfo;
    }

    setRelayParameterInfo(relayParameterInfo: RelayParameterInfo) {
        this.relayParameterInfo = relayParameterInfo;
    }

    getMeterStatisticsInfo() {
        return this.meterStatisticsInfo;
    }

    setMeterStatisticsInfo(meterStatisticsInfo: MeterStatisticsInfo) {
        this.meterStatisticsInfo = meterStatisticsInfo;
    }

    getMeterWashValveInfo() {
        return this.meterWashValveInfo;
    }

    setMeterWashValveInfo(meterWashValveInfo: MeterWashValveInfo) {
        this.meterWashValveInfo = meterWashValveInfo;
    }

    setOpenNb(openNb: number) {
        this.openNb = openNb;
    }

    getOpenNb() {
        return this.openNb;
    }

    setCloseNb(closeNb: number) {
        this.closeNb = closeNb;
    }

    getCloseNb() {
        return this.closeNb;
    }

    setResetMCU(resetMCU: number) {
        this.resetMCU = resetMCU;
    }

    getResetMCU() {
        return this.resetMCU;
    }

    getAllRead(): number {
        return this.allRead;
    }

    setAllRead(value: number) {
        this.allRead = value;
    }

    getReadMeterCode(): number {
        return this.readMeterCode;
    }

    setReadMeterCode(value: number) {
        this.readMeterCode = value;
    }

    getWriteMeterCode(): number {
        return this.writeMeterCode;
    }

    setWriteMeterCode(value: number) {
        this.writeMeterCode = value;
    }

    getReadMeterData(): number {
        return this.readMeterData;
    }

    setReadMeterData(value: number) {
        this.readMeterData = value;
    }

    getWriteMeterData(): number {
        return this.writeMeterData;
    }

    setWriteMeterData(value: number) {
        this.writeMeterData = value;
    }

    getOpenValve(): number {
        return this.openValve;
    }

    setOpenValve(value: number) {
        this.openValve = value;
    }

    getCloseValve(): number {
        return this.closeValve;
    }

    setCloseValve(value: number) {
        this.closeValve = value;
    }

    setReportReadMeterData(reportReadMeterData: number) {
        this.reportReadMeterData = reportReadMeterData;
    }

    getReportReadMeterData() {
        return this.reportReadMeterData;
    }

    setResetBaseValue(resetBaseValue: number) {
        this.resetBaseValue = resetBaseValue;
    }

    getResetBaseValue() {
        return this.resetBaseValue;
    }

    getStartTest(): boolean {
        return this.startTest;
    }

    setStartTest(value: boolean) {
        this.startTest = value;
    }
    
    getSendWashValve(): number {
        return this.sendWashValve;
    }

    setSendWashValve(value: number) {
        this.sendWashValve = value;
    }

    setReportDeviceInfo(reportDeviceInfo: number) {
        this.reportDeviceInfo = reportDeviceInfo;
    }

    getReportDeviceInfo() {
        return this.reportDeviceInfo;
    }
}
