<block wx:if="{{devices && devices.length > 0}}">
    <view class="common-list">
        <view class="list-item" wx:for="{{devices}}" wx:key="item" wx:for-item="device">
            <view>
                <image class="img" src="https://www.acp-iot.com/wsd-ctwing/mini/icons/bluetooth.svg" mode="scaleToFill"></image>
            </view>
            <view style="display:flex;flex-direction:column;width:80%;flex:1;">
                <text class="text-ellipsis" style="font-size:medium;word-break:break-all;margin-bottom:8rpx;margin-top:20rpx;">设备名称: {{device.name}}</text>
                <text style="font-size:x-small;color:gray;word-break:break-all;">设备ID: {{device.deviceId}}</text>
                <text style="font-size:x-small;color:gray;word-break:break-all;margin-bottom:20rpx;">信号强度RSSI: {{device.RSSI}}</text>
            </view>
            <view class="blue-btn-wrap">
                <button class="btn-success-outline" bindtap="chooseBlueTooth" data-index="{{index}}">连 接</button>
                <button class="btn-default" bindtap="setChannel" data-index="{{index}}">信道{{device.channel}}</button>
            </view>
        </view>
    </view>
</block>
<block wx:else>
    <van-empty description="暂无蓝牙设备"/>
</block>
<mp-actionSheet bindactiontap="actionTap" show="{{showActionsheet}}" actions="{{groups}}" title="设置通信通道">
</mp-actionSheet>

<view class="foot">
    <button wx:if="{{searching}}" class="sub-btn btn-primary" bindtap="stopSearch"><van-loading color="#ffffff" size="30rpx" vertical /> 停止搜索</button>
        <button wx:else class="sub-btn btn-primary" bindtap="startSearch">开始搜索</button>
</view>

<van-dialog use-slot title="连接设置" show="{{ dialogShow }}" showConfirmButton show-cancel-button bind:cancel="cancel" bind:confirm="confirm">
    <view style="padding: 40rpx;">
        <van-radio-group value="{{ deviceType }}" bind:change="onChange">
            <van-cell-group title="目标设备类型">
                <van-cell title="NB水表" clickable data-name="meter" bind:click="deviceTypeChange">
                    <van-radio slot="right-icon" name="meter"/>
                </van-cell>
                <van-cell title="NB采集器" clickable data-name="relay" bind:click="deviceTypeChange">
                    <van-radio slot="right-icon" name="relay"/>
                </van-cell>
            </van-cell-group>
        </van-radio-group>

        <van-radio-group value="{{ runType }}" bind:change="onChange">
            <van-cell-group title="小无线运行模式">
                <van-cell title="产测模式" clickable data-name="5" bind:click="runTypeChange">
                    <van-radio slot="right-icon" name="5"/>
                </van-cell>
                <van-cell title="工作模式" clickable data-name="15" bind:click="runTypeChange">
                    <van-radio slot="right-icon" name="15"/>
                </van-cell>
                <van-cell title="中继模式" clickable data-name="25" bind:click="runTypeChange">
                    <van-radio slot="right-icon" name="25"/>
                </van-cell>
            </van-cell-group>
        </van-radio-group>
    </view>
</van-dialog>