import security from "../../../../../mixins/security"
import httpUtils from '../../../../../utils/HttpUtils';
import commonUtils from '../../../../../utils/CommonUtils';

Page({
    mixins: [security],
    /**
     * 页面的初始数据
     */
    data: {
        totalCount: null,
        readCount: null,
        unReadCount: null,
        meterBookCount: null,
        autoNext: wx.getStorageSync('autoNext'),
    },
    
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad() {
        let that = this;
        httpUtils.requestFormUrl({
            url: 'auth/machineMonitor/statisticsMachineMeter',
            method: 'GET',
            params: {},
            success: (res: any) => {
                if (res) {
                    that.setData({
                        totalCount: res.totalCount,
                        readCount: res.readCount,
                        unReadCount: res.unReadCount,
                        meterBookCount: res.meterBookCount
                    });
                }
            },
            failure: (res: any) => {
            }
        });
    },
    
    navigateTo(e: any) {
        const url = e.currentTarget.dataset.url;
        // @ts-ignore
        if (this.isPermission(e.currentTarget.dataset.code)) {
            // 如果点击抄表或统计，判断当前抄表员抄表册下是否存在水表
            if (url === '/pages/readmeter/readmeter-package/pages/mechanical/read/read' || url === '/pages/readmeter/readmeter-package/pages/mechanical/statistics/statistics') {
                httpUtils.requestFormUrl({
                    url: 'auth/machineMonitor/existsWaterMeter',
                    method: 'GET',
                    params: {},
                    success: (res: any) => {
                        if (!res) {
                            commonUtils.showToast('当前抄表员无抄表册或抄表册下无水表', 'none', 1500);
                        } else {
                            wx.navigateTo({
                                url: url
                            });
                        }
                    },
                    failure: (res: any) => {
                    }
                });
            } else {
                wx.navigateTo({
                    url: url
                });
            }
        }
    },
    
    settingShow() {
        this.setData({
            showSetting: true
        })
    },
    
    autoNextChange(e: any) {
        this.setData({
            autoNext: e.detail
        })
        wx.setStorage({
            key: 'autoNext',
            data: e.detail
        })
    }
})
