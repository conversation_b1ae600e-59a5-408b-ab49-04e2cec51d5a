<van-tabs sticky z-index="999" shadow="5" line-width="20vw" model:active="{{active}}" bind:change="tabChange">
    <block wx:for="{{connected}}" wx:key="key">
        <block wx:if="{{item && item.deviceId }}">
            <van-tab title="{{item.name}}" name="{{item.deviceId}}">
                <production-item class="production" options="{{item}}" mqtt="{{mqtt}}" inputData="{{inputData}}" infrared="{{item.infrared}}" />
            </van-tab>
        </block>
    </block>
    <van-tab wx:if="{{connectedNum < maxConnectedNum}}" name="add" title="选择设备">
        <view class="bluetooth-list" style="height: calc(100vh - 192rpx);">
            <view  class="tips">请选择蓝牙设备连接</view>
            <block wx:if="{{devices && devices.length > 0}}">
                <view class="common-list" style="padding-bottom:0">
                    <view class="list-item" wx:for="{{devices}}" wx:key="item" wx:for-item="device" bindtap="selectChange" data-device="{{device}}">
                        <view>
                            <image class="img" src="https://www.acp-iot.com/wsd-ctwing/mini/icons/bluetooth.svg" mode="scaleToFill"></image>
                        </view>
                        <view style="display:flex;flex-direction:column;width:80%;flex:1;">
                            <text class="text-ellipsis" style="font-size:medium;word-break:break-all;margin-top:10rpx;">设备名称: {{device.name}}</text>
                            <text style="font-size:x-small;color:gray;word-break:break-all;">设备ID: {{device.deviceId}}</text>
                            <text style="font-size:x-small;color:gray;word-break:break-all;margin-bottom:10rpx;">信号强度RSSI: {{device.RSSI}}</text>
                        </view>
                        <view class="blue-btn-wrap">
                            <view wx:if="{{connectedObj[device.deviceId]}}" class="text-success" style="font-size:30rpx;padding-right: 20rpx;">已连接</view>
                            <block  wx:else>
                            <van-checkbox shape="square" value="{{ selectedObj[device.deviceId] }}"></van-checkbox>
                            </block>
                        </view>
                    </view>
                </view>
            </block>
            <block wx:else>
                <van-empty description="暂无蓝牙设备"></van-empty>
            </block>
        </view>
    </van-tab>
</van-tabs>

<view class="footer" active>
    <block wx:if="{{active === 'add'}}">
        <button wx:if="{{searching}}" class="btn-primary" bindtap="stopSearch"><van-loading color="#ffffff" size="30rpx" vertical /> 停止搜索</button>
        <button wx:else class="btn-primary" bindtap="startSearch">开始搜索</button>
        <button class="btn-success" bindtap="connect">连接</button>
        <button class="btn-primary-2" bindtap="scan">扫码连接</button>
    </block>
    <block wx:else>
        <button class="btn-danger" bindtap="disconnect">断开设备
        </button>
        <button type="primary" class="sub-btn" bindtap="reportReadMeterDataOpt" disabled="{{executeState}}">同步数据
            <block wx:if="{{!permissions['miniprogram-readmeter-wireless-reportReadMeterData']}}">
                <image class="over-res" src="https://www.acp-iot.com/wsd-ctwing/mini/icons/auth.svg"></image>
            </block>
        </button>
        <button class="btn-primary-2" bindtap="settingToggle">超时设置
        </button>
    </block>
</view>

<mp-dialog extClass="settingDialog dialog" title="超时设置" mask-closable="{{false}}" show="{{settingShow}}" buttons="{{settingButtons}}" bindbuttontap="settingBtnTap">
    <view class="my-cells " title="">
        <view class="my-cell item">
            <view class="input-wrap">
                <view class="label label-flex">命令超时时间</view>
                <input type="number" bindinput="inputMaxCtrl" bindblur="inputMinCtrl" data-max="120" data-min="1" data-key="settingCommandTimeout" class="has-unit" model:value="{{settingCommandTimeout}}" />
                <view class="unit">秒</view>
            </view>
        </view>
    </view>
</mp-dialog>


<van-action-sheet
        title="选择信道"
        cancel-text="取消"
        show="{{ actionsShow }}"
        actions="{{ actions }}"
        bind:select="selectChannel"
        bind:cancel="cancelChannel"
/>

<view wx:if="{{matching}}" class="loading" catchtap="" catchtouchmove="" catchtouchstart="">
    <view class="loading-box" style="padding-bottom: 50rpx;">
        <van-loading color="#1989fa" size="110rpx" vertical> <text style="color:rgb(233, 233, 233);font-size: 32rpx;">正在匹配</text></van-loading>
    </view>
    <view><button style="width:auto;height: 80rpx;line-height: 80rpx; padding:0 40rpx" type="primary" class="btn-primary" bindtap="stopMatching">取消</button></view>
</view>
