// pages/manage/community-consumption/community-consumption.ts
import httpUtils from "../../../utils/HttpUtils";
import moment from "../../../utils/moment.js"
import commonUtils from "../../../utils/CommonUtils";
import {Pagination} from "../../../pagination/Pagination";
import {Direction} from "../../../pagination/Direction";
import {Operator} from "../../../pagination/Operator";
import page from "../../../mixins/page-restricted.js";

Page({
    /**
     * 混入分页组件公共函数
     */
    mixins: [page],

    /**
     * 分页查询对象
     */
    pagination: null,
    monthPagination: new Pagination<any>('auth/communityConsumption/pageCommunityConsumptionByMonth'),
    dayPagination: new Pagination<any>('auth/communityConsumption/pageCommunityConsumptionByDay'),
    /**
     * 页面的初始数据
     */
    data: {
        active: 'month',//当前tab
        year: moment().format('YYYY'),
        currentYear: moment().format('YYYY'),
        date: [moment().subtract('days', 6).format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        communityId: '',
        total: null,
        chartData: null
    },

    tabChange(e: any) {
        this.setData({
            active: e.detail.name
        });
        this.getData();
        this.query();
    },

    //弹出层事件 避免canvas覆盖
    toggle(e: any) {
        this.selectComponent('#chart').toggle(e.detail)
    },
    //日期改变
    dateChange(e: any) {
        this.setData({
            date: [e.detail[0], e.detail[1]]
        });
        this.getData();
        this.query();
    },

    //月份改变
    monthChange(e: any) {
        this.getData();
        this.query();
    },
    getData() {
        commonUtils.showLoadingMask('查询中');
        let that = this;
        let url = '';
        let params = null;
        if (this.data.active === 'day') {
            url = 'auth/communityConsumption/getCommunityConsumptionDataByDay';
            params = {
                from: this.data.date[0],
                to: this.data.date[1],
                communityId: this.data.communityId
            }
        } else {
            url = 'auth/communityConsumption/getCommunityConsumptionDataByMonth';
            params = {
                year: this.data.year,
                communityId: this.data.communityId
            }
        }

        httpUtils.requestFormUrl({
            url: url,
            method: 'GET',
            params: params,
            success(res: any) {
                if (res && res.length > 0) {
                    let categories: any[] = [], data: any[] = [], total = 0;
                    res.forEach((item: any) => {
                        categories.push(that.data.active === 'day' ? moment(item.key).format('DD日') : moment(item.key).format('M月'));
                        data.push(Math.floor(item.value));
                        total += item.value * 1000;
                    });
                    that.setData({
                        total: total / 1000,
                        chartData: {
                            categories, data
                        }
                    });
                }
                commonUtils.hideLoading();
            }
        });
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad() {
        this.getData();
        this.query();
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    query() {
        this.applyCondition();
        // @ts-ignore
        this.loadData();
    },

    applyCondition() {
        // 表号、户号、计量点名称
        if (this.data.active === 'day') {
            this.pagination = this.dayPagination;
            this.pagination.addFilter('from', this.data.date[0], Operator.eq);
            this.pagination.addFilter('to', this.data.date[1], Operator.eq);
            this.pagination.addFilter('communityId', this.data.communityId, Operator.eq);
        } else {
            this.pagination = this.monthPagination;
            this.pagination.addFilter('year', this.data.year, Operator.eq);
            this.pagination.addFilter('communityId', this.data.communityId, Operator.eq);
        }
    }
})