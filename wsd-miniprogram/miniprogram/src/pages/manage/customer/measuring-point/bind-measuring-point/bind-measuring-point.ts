import httpUtils from "../../../../../utils/HttpUtils";
import Dialog from '../../../../../components/vant/dialog/dialog';
import commonUtils from "../../../../../utils/CommonUtils";
import security from "../../../../../mixins/security";
import {Pagination} from '../../../../../pagination/Pagination';
import page from '../../../../../mixins/page-infinity';
import {Operator} from '../../../../../pagination/Operator';
import {Direction} from '../../../../../pagination/Direction';

Page({

    /**
     * 混入分页组件公共函数，公共过滤条件字段,功能权限组件
     */
    mixins: [page, security],

    /**
     * 分页查询对象
     */
    pagination: new Pagination<any>('auth/customer/pageCanBoundMeasuringPoints'),

    /**
     * 页面的初始数据
     */
    data: {
        customerId: 0,
        communityId: null,
        measuringPointName: null,
        meterCode: null,
        sortOpt: [
            {text: '计量点名称降序', value: 'name-desc'},
            {text: '计量点名称升序', value: 'name-asc'},
            {text: '表号降序', value: 'meterCode-desc'},
            {text: '表号升序', value: 'meterCode-asc'}
        ],
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options: any) {
        this.setData({
            customerId: options.customerId,
            communityId: options.communityId,
        });

        this.applyDefaultFilter();
        // @ts-ignore
        this.loadData();
    },

    sortChange(e: any) {
        let sortInfo = e.detail.split("-");
        this.pagination.setSort(sortInfo[0], sortInfo[1] === "asc" ? Direction.asc : Direction.desc);
        // @ts-ignore
        this.loadData();
    },

    query() {
        this.applyCondition();
        // @ts-ignore
        this.loadData();
    },

    applyDefaultFilter() {
        this.pagination.addFilter('community.id', this.data.communityId);
        this.pagination.setSort('name', Direction.desc);
    },

    applyCondition() {
        // 计量点名称
        if (this.data.measuringPointName) {
            this.pagination.addFilter('name', this.data.measuringPointName, Operator.like);
        } else {
            this.pagination.removeFilter('name');
        }

        // 表号
        if (this.data.meterCode) {
            this.pagination.addFilter('meterCode', this.data.meterCode, Operator.like);
        } else {
            this.pagination.removeFilter('meterCode');
        }
    },

    reset() {
        this.pagination.clearFilters();
        this.setData({
            measuringPointName: '',
            meterCode: ''
        });
        this.applyDefaultFilter();
        // @ts-ignore
        this.loadData();
    },

    /**
     * 是否确认绑定
     */
    showBindConfirm(e: any) {
        const pointId = e.currentTarget.dataset.id;
        Dialog.confirm({
            title: '提示',
            message: '是否确认关联该计量点？',
        })
            .then(() => {
                commonUtils.showLoadingMask('开户中', true);
                let that = this;
                httpUtils.requestFormUrl({
                    url: 'auth/customer/bindMeasuringPoint',
                    method: 'PUT',
                    params: {
                        customerId: that.data.customerId,
                        pointId: pointId
                    },
                    success: (res: any) => {
                        wx.navigateBack();
                        commonUtils.showToast('开户成功，户号：' + res, 'none', 10000);
                    },
                    failure: (res: any) => {
                        commonUtils.showToast(res, 'none', 1500);
                    }
                });
            })
            .catch(() => {
                return;
            });
    },

    measuringPointDetail(e: any) {
        wx.navigateTo({
            url: '../../../meter-monitor/measuring-point-detail/measuring-point-detail?measuringPointId=' + e.currentTarget.dataset.id
        });
    }
})
