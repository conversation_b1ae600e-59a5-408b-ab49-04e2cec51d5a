<!--pages/manage/customer/add-customer/add-customer.wxml-->
<van-cell-group inset>
    <filter-picker border placeholder="请选择" required class="filter-row verify-item" labelWidth="6.2em" type="company"
                   model:value="{{companyId}}" selectAll="{{false}}" bind:closeChange="companyChange"/>

    <filter-picker border hasSearch required class="filter-row verify-item" labelWidth="6.2em" type="communityForAdd" model:value="{{communityId}}" selectAll="{{false}}"
                   params="{{communityParams}}" disabled="{{!companyId}}" bind:closeChange="communityCloseChange"/>

    <van-field class="verify-item" model:value="{{ name }}" maxlength="32" required clearable label="客户名称" placeholder="客户名称"/>

    <van-field class="verify-item" model:value="{{ mobile }}" maxlength="11" rule="mobile" required clearable label="手机号" placeholder="手机号"/>

    <van-field class="verify-item" model:value="{{ idCard }}" maxlength="18" required clearable label="身份证" placeholder="身份证"/>

    <van-cell required title="性别" center title-width="6.2em" title-style="text-align:center">
        <view style="padding-left: 24rpx;">
            <van-radio-group model:value="{{ gender }}" direction="horizontal">
                <van-radio name="UNKNOWN">保密</van-radio>
                <van-radio name="MALE">男</van-radio>
                <van-radio name="FEMALE">女</van-radio>
            </van-radio-group>
        </view>
    </van-cell>

    <van-field model:value="{{ contactAddress }}" maxlength="255" type="textarea" autosize show-word-limit clearable label="联系地址" placeholder="联系地址"/>
</van-cell-group>

<view style="margin: 30rpx 12rpx;">
    <van-button loading="{{loading}}" disabled="{{loading}}" type="primary" block bind:click="save">提交</van-button>
</view>

<van-dialog id="van-dialog"/>
