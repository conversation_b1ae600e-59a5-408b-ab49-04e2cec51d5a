import {Pagination} from "../../../../pagination/Pagination";
import {Direction} from "../../../../pagination/Direction";
import moment from "../../../../utils/moment";
import {Operator} from "../../../../pagination/Operator";
//混入对象
import page from "../../../../mixins/page-infinity";

Page({

    /**
     * 混入分页组件公共函数，导入该文件后必须在当前组件中创建【pagination】对象，如下所示
     */
    mixins: [page],

    /**
     * 分页查询对象
     */
    pagination: null,

    /**
     * 页面的初始数据
     */
    data: {
        meterId: null,
        loading: false,
        sortOpt: [
            {text: '全部事件类型', value: ''},
            {text: '阀门状态变化', value: 'VALVE_STATE_CHANGED'},
            {text: '报警状态变化事件', value: 'ALARM_FLAG_CHANGED'},
            {text: '设备上下线', value: 'ONLINE_OFFLINE'},
            {text: '传输延时', value: 'CLOCK_OFFSET'},
            {text: '基站切换', value: 'CELL_ID_CHANGED'},
            {text: '强磁攻击报警', value: 'MAGNETIC_ATTACK'},
            {text: '洗阀', value: 'WASH_VALVE'}
        ]
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options: any) {
        this.setData({
            meterId: options.meterId
        });
        this.pagination = new Pagination<any>('auth/monitorDetail/pagedMeterEvents?meterId=' + options.meterId, 20);
        this.pagination.setSort('id', Direction.desc);
        //@ts-ignore
        this.loadData();
    },

    /**
     * 选择时间区间并查询
     * @param event
     */
    dateChange(event: any) {
        const [start, end] = event.detail;
        // 清除掉之前的时间条件
        this.pagination.removeFilter('createdDate');
        if (start && end) {
            // 应用时间条件查询
            const startTime = moment(start).startOf('day');
            const endTime = moment(end).endOf('day');
            this.pagination.addFilter('createdDate', startTime, Operator.ge, false);
            this.pagination.addFilter('createdDate', endTime, Operator.le, false);
        }
        // @ts-ignore
        this.loadData();
    },

    /**
     * 选择事件类型并查询
     * @param event
     */
    eventTypeChange(event: any) {
        const eventType = event.detail;
        // 清除掉之前的时间条件
        this.pagination.removeFilter('eventType');
        if (eventType) {
            // 应用抄表类型条件查询
            this.pagination.addFilter('eventType', eventType);
        }
        // @ts-ignore
        this.loadData();
    },
})