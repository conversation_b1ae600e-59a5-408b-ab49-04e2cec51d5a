<wxs src="../../../../pipes/enumFormat.wxs" module="enumFormat"></wxs>

<van-cell-group title="基本信息">
    <van-cell title="计量点名称">
        <text user-select>{{waterMeter.measuringPoint.name}}</text>
    </van-cell>
    <van-cell title="户主">
        <text user-select>{{waterMeter.measuringPoint.customer.name || '-'}}</text>
    </van-cell>
    <van-cell title="户号">
        <text user-select>{{waterMeter.measuringPoint.accountNo || '-'}}</text>
    </van-cell>
    <van-cell title="开户状态">
        <text user-select>{{enumFormat.formatAccount(waterMeter.measuringPoint.customer.id, waterMeter.measuringPoint.cancellation)}}</text>
    </van-cell>
    <van-cell wx:if="{{waterMeter.measuringPoint.customer && waterMeter.measuringPoint.customer.mobile}}" center is-link="{{true}}" bindtap="call" title="联系方式">
        <van-icon  name="phone-o" size="36rpx" color="rgb(76,149,251)" />
        <text user-select>{{waterMeter.measuringPoint.customer.mobile}}</text>
    </van-cell>
    <van-cell wx:else center is-link="{{false}}"  title="联系方式">
        <text user-select>-</text>
    </van-cell>
    <van-cell title="小区楼栋">
        <text user-select>{{waterMeter.buildingUnitNames || '-'}}</text>
    </van-cell>
    <van-cell title="实际余额">
        <text user-select class="{{(waterMeter.measuringPoint.realBalance && waterMeter.measuringPoint.realBalance < 0) ? 'text-danger':''}}">{{waterMeter.measuringPoint.realBalance === null ? '-' : waterMeter.measuringPoint.realBalance}}</text>
        <view slot="right-icon" class="until">元</view>
    </van-cell>
    <van-cell title="公司名称">
        <text user-select>{{waterMeter.companyId || '-'}}</text>
    </van-cell>
</van-cell-group>
<van-cell-group title="业务数据">
    <van-cell center is-link="{{permissions['miniprogram-manage-monitor-detail-meterDetail-query']}}" title="水表详情" bind:click="navigateToOther"
              data-code="miniprogram-manage-monitor-detail-meterDetail-query" data-url="/pages/manage/meter-monitor/meter-detail/meter-detail">
        <van-icon slot="icon" size="42rpx" style="margin-right: 8rpx;" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/nb-meter.svg"></van-icon>
        <van-icon slot="right-icon" size="46rpx" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/auth.svg"></van-icon>
    </van-cell>
    <van-cell center is-link="{{permissions['miniprogram-manage-monitor-detail-meterData']}}" title="抄表数据" bind:click="navigateToOther"
              data-code="miniprogram-manage-monitor-detail-meterData" data-url="/pages/manage/meter-monitor/readmeter-data/readmeter-data">
        <van-icon slot="icon" size="42rpx" style="margin-right: 8rpx;" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/data.svg"></van-icon>
        <van-icon slot="right-icon" size="46rpx" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/auth.svg"></van-icon>
    </van-cell>
    <van-cell center is-link="{{permissions['miniprogram-manage-freezeData']}}" title="日冻结数据" bind:click="navigateToOther" data-code="miniprogram-manage-freezeData"
              data-url="/pages/manage/meter-monitor/day-freeze-data/day-freeze-data">
        <van-icon slot="icon" size="42rpx" style="margin-right: 8rpx;" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/freeze.svg"></van-icon>
        <van-icon slot="right-icon" size="46rpx" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/auth.svg"></van-icon>
    </van-cell>
    <van-cell wx:if="{{waterMeter.model.meterType !== 'MACHINE'}}" center is-link="{{permissions['miniprogram-manage-monitor-detail-meterEvents']}}" title="水表事件" bind:click="navigateToOther"
              data-code="miniprogram-manage-monitor-detail-meterEvents" data-url="/pages/manage/meter-monitor/meter-event/meter-event">
        <van-icon slot="icon" size="42rpx" style="margin-right: 8rpx;" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/event.svg"></van-icon>
        <van-icon slot="right-icon" size="46rpx" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/auth.svg"></van-icon>
    </van-cell>
    <van-cell wx:if="{{waterMeter.model.meterType !== 'MACHINE'}}" center is-link="{{permissions['miniprogram-manage-monitor-detail-meterCmdLogs']}}" title="历史命令" bind:click="navigateToOther"
              data-code="miniprogram-manage-monitor-detail-meterCmdLogs" data-url="/pages/manage/meter-monitor/history-command/history-command">
        <van-icon slot="icon" size="42rpx" style="margin-right: 8rpx;" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/cmd-history.svg"></van-icon>
        <van-icon slot="right-icon" size="46rpx" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/auth.svg"></van-icon>
    </van-cell>
    <van-cell center wx:if="{{waterMeter.model.meterType !== 'MACHINE' && waterMeter.valve}}" is-link="{{permissions['miniprogram-manage-monitor-detail-serviceValveCtrlRecord']}}" title="业务阀控记录"
              bind:click="navigateToOther" data-code="miniprogram-manage-monitor-detail-serviceValveCtrlRecord"
              data-url="/pages/manage/meter-monitor/service-valvectrl-record/service-valvectrl-record">
        <van-icon slot="icon" size="42rpx" style="margin-right: 8rpx;" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/record.svg"></van-icon>
        <van-icon slot="right-icon" size="46rpx" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/auth.svg"></van-icon>
    </van-cell>
</van-cell-group>
<van-cell-group title="业务操作"  wx:if="{{permissions['miniprogram-manage-monitor-manualReadingMeter'] || permissions['miniprogram-manage-monitor-meteringData'] || permissions['miniprogram-manage-monitor-openValue'] || permissions['miniprogram-manage-monitor-closeValve']}}">
    <van-cell center>
        <view class="cell-inner">
            <block wx:if="{{waterMeter.model.meterType !== 'MACHINE'}}">
                <view class="btn-wrap">
                    <van-button type="primary" block bindtap="monitor">实抄</van-button>
                    <van-icon wx:if="{{!permissions['miniprogram-manage-monitor-meteringData']}}" size="50rpx" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/auth.svg"></van-icon>
                </view>
                <view wx:if="{{waterMeter.valve}}" class="btn-wrap">
                    <van-button type="info" block bindtap="valveCtrl" data-permission="miniprogram-manage-monitor-openValue" data-type="开阀">开阀</van-button>
                    <van-icon wx:if="{{!permissions['miniprogram-manage-monitor-openValue']}}" size="50rpx" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/auth.svg"></van-icon>
                </view>
                <view wx:if="{{waterMeter.valve}}" class="btn-wrap">
                    <van-button type="warning" block bindtap="valveCtrl" data-permission="miniprogram-manage-monitor-closeValve" data-type="关阀">关阀</van-button>
                    <van-icon wx:if="{{!permissions['miniprogram-manage-monitor-closeValve']}}" size="50rpx" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/auth.svg"></van-icon>
                </view>
            </block>
            <view class="btn-wrap">
                <van-button style="white-space: nowrap;" type="primary" block bindtap="manual" data-permission="miniprogram-manage-monitor-manualReadingMeter" >人工抄表</van-button>
                <van-icon wx:if="{{!permissions['miniprogram-manage-monitor-manualReadingMeter']}}" size="50rpx" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/auth.svg"></van-icon>
            </view>
        </view>
    </van-cell>
</van-cell-group>


<van-dialog wx:if="{{waterMeter.valve}}" catchtap="" show-cancel-button="true" use-slot title="{{valveCtrlType}}" show="{{ valveCtrlShow }}" bind:confirm="valveCtrlConfirm"
            bind:close="valveCtrlClose">
    <view class="valve-ctrl">
        <view>
            <van-radio-group value="{{ valveCtrlTimerType }}">
                <van-cell-group>
                    <van-cell title="临时{{valveCtrlType}}" clickable data-name="temporary" bindtap="valveCtrlTimerTypeTap">
                        <van-radio slot="right-icon" name="temporary"/>
                    </van-cell>
                    <van-cell title="限时{{valveCtrlType}}" clickable data-name="time-limit" bindtap="valveCtrlTimerTypeTap">
                        <van-radio slot="right-icon" name="time-limit"/>
                    </van-cell>
                    <van-cell title="永久{{valveCtrlType}}" clickable data-name="infinity" bindtap="valveCtrlTimerTypeTap">
                        <van-radio slot="right-icon" name="infinity"/>
                    </van-cell>
                </van-cell-group>
            </van-radio-group>
        </view>
        <view wx:if="{{valveCtrlTimerType === 'time-limit'}}" style="border-top: 1px solid #d2d2d2;">
            <van-field maxlength="6" type="number" center title-width="140rpx" model:value="{{ valveCtrlTimer }}" label="生效时限" placeholder="生效时限" border="{{ true }}">
                <van-button slot="button" custom-class="time-until" type="primary" size="normal" bindtap="changeTimeUntil">{{valveCtrlTimerUntil}}
                    <van-icon size="24rpx" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/change.svg"/>
                </van-button>
            </van-field>
        </view>
        <view class="row msg">
            <view class="label">提示：</view>
            <view class="content">
                <view>永久{{valveCtrlType}}和限时{{valveCtrlType}}的生效时限内，</view>
                <view>设备的营收自动开关阀功能将失效</view>
            </view>
        </view>
    </view>
</van-dialog>

<van-dialog id="van-dialog"/>
