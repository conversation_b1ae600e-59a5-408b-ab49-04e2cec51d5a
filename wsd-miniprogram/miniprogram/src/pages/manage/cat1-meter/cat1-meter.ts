// pages/manage/cat1-meter/cat1-meter.ts
import {Pagination} from "../../../pagination/Pagination";
import {Operator} from "../../../pagination/Operator";
import {Direction} from "../../../pagination/Direction";
import httpUtils from "../../../utils/HttpUtils";
import commonUtils from "../../../utils/CommonUtils";
import Dialog from "../../../components/vant/dialog/dialog";
//混入对象
import page from "../../../mixins/page-infinity";
import security from "../../../mixins/security";

Page({

    /**
     * 混入分页组件公共函数、公共过滤条件字段
     */
    mixins: [page, security],

    /**
     * 分页查询对象
     */
    pagination: new Pagination<any>('auth/cat1WaterMeter/page'),

    /**
     * 页面的初始数据
     */
    data: {
        loading: false,
        sortOpt: [
            {text: '创建时间降序', value: 'createdDate-desc'},
            {text: '创建时间升序', value: 'createdDate-asc'},
            {text: '水表编号降序', value: 'code-desc'},
            {text: '水表编号升序', value: 'code-asc'},
            {text: '安装日期降序', value: 'installationDetails.installDate-desc'},
            {text: '安装日期升序', value: 'installationDetails.installDate-asc'},
            {text: '小区名称降序', value: 'community.name-desc'},
            {text: '小区名称升序', value: 'community.name-asc'}
        ],
        currentId: '',
        codeType: '',
        codeValue: '',
        customerType: '',
        customerValue: '',
        channelType:'',
        moduleType:'',
        imei: '',
        communityId: '',
        openAccountState: '',
        actionsShow: false,
        actions: [
            {name: '编辑', subname: '', color: '', type: 'edit', auth: false},
            {name: '删除', subname: '', color: '', type: 'delete', auth: false}
        ],
        options: [
            {text: '户主名', value: 'houseHolder', placeholder: '户主名'},
            {text: '手机号', value: 'mobile', placeholder: '手机号'},
            {text: '房号', value: 'roomNumber', placeholder: '房号'}
        ]
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad() {
        // 处理按钮权限样式
        let actions = this.data.actions;
        // @ts-ignore
        actions[0].auth = this.isPermission('miniprogram-manage-cat1WaterMeter-edit', false);
        // @ts-ignore
        actions[1].auth = this.isPermission('miniprogram-manage-cat1WaterMeter-delete', false);
        this.setData({
            actions: actions
        });
        this.applyDefaultSort();
        //@ts-ignore
        this.loadData();
    },

    onShow(): void {
    
    },

    /**
     * 添加水表
     */
    addWaterMeter(e: any) {
        // @ts-ignore
        if (this.isPermission(e.currentTarget.dataset.code)) {
            wx.navigateTo({
                url: 'add-water-meter/add-water-meter',
                success: (e: any) => {
                    e.eventChannel.on('back', (data: any) => {
                        this.pagination.clearFilters();
                        this.applyDefaultSort();
                        // @ts-ignore
                        this.loadData();
                    })
                }
            });
        }
    },

    showAction(e: any) {
        this.setData({
            currentId: e.currentTarget.dataset.id,
            actionsShow: true
        });
    },

    closeAction() {
        this.setData({
            actionsShow: false
        });
    },

    actionSelect(e: any) {
        this.setData({
            actionsShow: false
        }, () => {
            setTimeout(() => {
                    switch (e.detail.type) {
                        case 'edit':
                            wx.navigateTo({
                                url: 'edit-water-meter/edit-water-meter?id=' + this.data.currentId,
                                success: (e: any) => {
                                    e.eventChannel.on('back', (data: any) => {
                                        this.pagination.clearFilters();
                                        this.applyDefaultSort();
                                        // @ts-ignore
                                        this.loadData();
                                    })
                                }
                            });
                            break;
                        case 'delete':
                            Dialog.confirm({
                                title: '删除水表',
                                message: '是否确认删除该水表？',
                            }).then(() => {
                                let that = this;
                                httpUtils.requestFormUrl({
                                    url: 'auth/cat1WaterMeter/delete',
                                    method: 'DELETE',
                                    params: {
                                        id: that.data.currentId
                                    },
                                    success: (res: any) => {
                                        commonUtils.showToast("删除成功", 'success', 1000);
                                        // @ts-ignore
                                        this.loadData();
                                    },
                                    failure: (res: any) => {
                                        commonUtils.showToast(res, 'none', 1500);
                                    }
                                });
                            }).catch(() => {
                                return;
                            });
                            break;
                        default:
                            break;
                    }
                },
                100);
        });
    },


    query() {
        this.applyCondition();
        // @ts-ignore
        this.loadData();
    },

    applyDefaultSort() {
        this.pagination.clearSort();
        this.pagination.setSort('id', Direction.desc);
    },

    applyCondition() {
        // 表号、户号、计量点名称
        if (this.data.codeValue) {
            let value: string = this.data.codeValue;
            switch (this.data.codeType) {
                case 'meterCode': {
                    this.pagination.addFilter('code', value, Operator.like);
                    this.pagination.removeFilter('measuringPoint.accountNo');
                    this.pagination.removeFilter('measuringPoint.name');
                    this.pagination.removeFilter('imei');
                    break;
                }
                case 'accountNo': {
                    this.pagination.addFilter('measuringPoint.accountNo', value, Operator.like);
                    this.pagination.removeFilter('code');
                    this.pagination.removeFilter('measuringPoint.name');
                    this.pagination.removeFilter('imei');
                    break;
                }
                case 'measuringPointName': {
                    this.pagination.addFilter('measuringPoint.name', value, Operator.like);
                    this.pagination.removeFilter('code');
                    this.pagination.removeFilter('measuringPoint.accountNo');
                    this.pagination.removeFilter('imei');
                    break;
                }
                case 'imei': {
                    this.pagination.addFilter('imei', value, Operator.like);
                    this.pagination.removeFilter('measuringPoint.accountNo');
                    this.pagination.removeFilter('code');
                    this.pagination.removeFilter('measuringPoint.name');
                    break;
                }
            }
        } else {
            this.pagination.removeFilter('code');
            this.pagination.removeFilter('measuringPoint.accountNo');
            this.pagination.removeFilter('measuringPoint.name');
        }
        // 户主名、手机号码
        if (this.data.customerValue) {
            let value: string = this.data.customerValue;
            switch (this.data.customerType) {
                case 'houseHolder': {
                    this.pagination.addFilter('measuringPoint.customer.name', value, Operator.like);
                    this.pagination.removeFilter('measuringPoint.mobile');
                    this.pagination.removeFilter('measuringPoint.roomNumber');
                    break;
                }
                case 'mobile': {
                    this.pagination.addFilter('measuringPoint.mobile', value, Operator.like);
                    this.pagination.removeFilter('measuringPoint.customer.name');
                    this.pagination.removeFilter('measuringPoint.roomNumber');
                    break;
                }
                case 'roomNumber': {
                    this.pagination.addFilter('measuringPoint.roomNumber', value, Operator.like);
                    this.pagination.removeFilter('measuringPoint.mobile');
                    this.pagination.removeFilter('measuringPoint.customer.name');
                    break;
                }
            }
        } else {
            this.pagination.removeFilter('measuringPoint.mobile');
            this.pagination.removeFilter('measuringPoint.customer.name');
        }
        // 小区ID
        if (this.data.communityId) {
            this.pagination.addFilter('community.id', this.data.communityId);
        } else {
            this.pagination.removeFilter('community.id');
        }

        // 开户状态 openAccountState
        if (this.data.openAccountState) {
            switch (this.data.openAccountState) {
                // 已开户
                case '1': {
                    this.pagination.addFilter('measuringPoint.customer.id', true, Operator.isNotNull);
                    this.pagination.addFilter('measuringPoint.cancellation', false);
                    break;
                }

                // 未开户
                case '2': {
                    this.pagination.removeFilter('measuringPoint.cancellation');
                    this.pagination.addFilter('measuringPoint.customer.id', true, Operator.isNull);
                    break;
                }
                // 已销户
                case '3': {
                    this.pagination.addFilter('measuringPoint.customer.id', true, Operator.isNotNull);
                    this.pagination.addFilter('measuringPoint.cancellation', true);
                    break;
                }
            }
        } else {
            this.pagination.removeFilter('measuringPoint.customer.id');
            this.pagination.removeFilter('measuringPoint.cancellation');
        }

        if(this.data.channelType){
            this.pagination.addFilter('channelType', this.data.channelType);
        }else{
            this.pagination.removeFilter('channelType');
        }
        if(this.data.moduleType){
            this.pagination.addFilter('moduleType', this.data.moduleType);
        }else{
            this.pagination.removeFilter('moduleType');
        }
    },

    sortChange(e: any) {
        let sortInfo = e.detail.split("-");
        this.pagination.setSort(sortInfo[0], sortInfo[1] === "asc" ? Direction.asc : Direction.desc);
        // @ts-ignore
        this.loadData();
    },

    reset() {
        this.pagination.clearFilters();
        this.setData({
            codeValue: '',
            customerValue: '',
            communityId: '',
            openAccountState: ''
        });
        // @ts-ignore
        this.loadData();
    },

    measuringPointDetail(e: any) {
        wx.navigateTo({
            url: '../meter-monitor/measuring-point-detail/measuring-point-detail?measuringPointId=' + e.currentTarget.dataset.id
        });
    },

    showStatus(e:any){
        let data = e.currentTarget.dataset.data;
        if (!data.measuringPoint.customer) {
            return;
        }
        this.selectComponent('#meter-status-show').show({
            customerId: data.measuringPoint.customer.id,
            cancellation: data.measuringPoint.cancellation
        })
    }
})