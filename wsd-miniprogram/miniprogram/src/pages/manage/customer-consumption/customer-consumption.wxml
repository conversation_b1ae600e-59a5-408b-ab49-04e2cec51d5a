<van-tabs active="{{ active }}" bind:change="tabChange">
    <van-tab title="按月统计" name="month"></van-tab>
    <van-tab title="按日统计" name="day"></van-tab>
</van-tabs>
<view class="filter">
    <view>
        <filter-picker class="filter-row" align="center" placeholder="全部小区" labelWidth="0" hasSearch type="community" model:value="{{communityId}}" bind:toggle="toggle" bind:closeChange="getData" />
        <filter-date input-align="center" max-range="{{ 30 }}" style="display: {{active === 'day'?'block':'none'}};" value="{{date}}" bind:change="dateChange" bind:toggle="toggle" clearable="{{false}}" />
        <picker style="display: {{active === 'month' ? 'block' : 'none'}}" mode="date" model:value="{{year}}" fields="year" start="2020" end="{{currentYear}}" bindchange="monthChange">
            <view class="picker">
                {{year}}
            </view>
        </picker>
    </view>
    <more text="更多查询条件" show-button="{{true}}">
        <filter-code class="filter-row" model:type="{{codeType}}" model:value="{{codeValue}}" bind:toggle="toggle"/>
        <filter-customer model:type="{{customerType}}" model:value="{{customerValue}}" bind:toggle="toggle"/>
        <view style="padding:10rpx 20rpx">
            <van-button style="height:70rpx ;" icon="search" custom-class='btn-sub' type="primary" size="normal" block bindtap="query">查询</van-button>
        </view>
    </more>
</view>

<van-cell-group inset title="">
    <van-cell center>
        <view slot="title" class="van-ellipsis">合计用水量</view>
        <text class="text-1DA3FD total">{{total === null ? '' : total }}</text> 吨
    </van-cell>
</van-cell-group>

<chart-component id="chart" chartData="{{chartData}}"/>

<van-sticky z-index="9">
    <view class="detail-total">客户用水量详情<text wx:if="{{totalElements > 0}}">（共计{{totalElements}}条，已加载{{currentTotal}}条）</text>
    </view>
</van-sticky>
<cell-expand wx:for="{{content}}"
             url="{{'detail/detail?active=' + active + '&year=' + year + '&date=' + date + '&measuringPointId=' + item.measuringPointId + '&measuringPointName=' + item.measuringPointName}}"
             title="{{item.measuringPointName}}" until="吨" value="{{item.value}}">
    <view style="padding:10rpx 10rpx 10rpx 20rpx;" class="chart-detail">
        <view class="info-item">
            <text class="info-label">所属小区：</text>
            <text class="info-content">{{item.communityName || '-'}}</text>
        </view>
        <view class="info-item">
            <text class="info-label">户号：</text>
            <text class="info-content">{{item.accountNo || '-'}}</text>
        </view>
        <view class="info-item">
            <text class="info-label">表号：</text>
            <text class="info-content">{{item.meterCode || '-'}}</text>
        </view>
        <view class="info-item">
            <text class="info-label">户主名：</text>
            <text class="info-content">{{item.houseHolder || '-'}}</text>
        </view>
        <view class="info-item">
            <text class="info-label">手机号：</text>
            <text class="info-content">{{item.mobile || '-'}}</text>
        </view>
    </view>
</cell-expand>

<list-bottom total="{{totalElements}}" loaded="{{currentTotal}}" loading="{{moreLoading}}" initialHeight="300rpx"/>