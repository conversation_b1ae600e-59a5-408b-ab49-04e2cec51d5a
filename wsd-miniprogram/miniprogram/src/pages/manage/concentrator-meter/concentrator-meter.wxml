<wxs src="../../../pipes/enumFormat.wxs" module="enumFormat"></wxs>

<top sortOpt="{{sortOpt}}" total="{{totalElements}}" loaded="{{currentTotal}}" bind:change="sortChange" bind:reset="reset" bind:query="query">
    <filter-code model:type="{{codeType}}" model:value="{{codeValue}}" />
    <filter-customer option="{{options}}" model:type="{{customerType}}" model:value="{{customerValue}}" />
    <filter-picker class="filter-row" hasSearch type="community" model:value="{{communityId}}" />
    <filter-input class="filter-row" labelWidth="160rpx" label="集中器编号" placeholder="集中器编号" model:value="{{concentratorCode}}" />
    <filter-status class="filter-left" type="openAccountState" labelWidth="160rpx" model:value="{{openAccountState}}" />
    <filter-status class="filter-right" type="syncState" labelWidth="160rpx" model:value="{{syncState}}" />
</top>

<wxs src="../../../pipes/enumFormat.wxs" module="enumFormat"></wxs>

<scroll-view bindscroll="scroll" id="scroll" style="height: calc(100vh - 130rpx);" scroll-y="{{true}}" bind:scrolltolower="onReachBottom_" bindrefresherrefresh="onPullDownRefresh_" scroll-top="{{scrollTop}}" scroll-with-animation refresherTriggered="{{refresherTriggered}}" refresher-enabled>
    <view style="height: {{holdHeight}}px; background:#ffffff;"></view>
    <block wx:for="{{content}}">
        <view wx:key="{{index}}" wx:if="{{index >= indexStart && index <= indexEnd }}" id="view-item-{{index}}">
            <view bindtap="showAction" class="list-card" data-id="{{item.id}}">
                <view class="list-card-header">
                    <view class="list-card-title">
                        <view class="img-box">
                            <image class="img" src="https://www.acp-iot.com/wsd-ctwing/mini/icons/concentrator-meter.svg" mode="aspectFit"></image>
                        </view>
                        <view class="text van-ellipsis">{{item.code}}</view>
                    </view>
                    <view class="list-card-status">
                        <meter-status customerId="{{item.measuringPoint.customer.id}}" cancellation="{{item.measuringPoint.cancellation}}" sync="{{item.sync}}" catch:tap="showStatus" data-data="{{item}}"></meter-status>
                    </view>
                </view>
                <view class="list-card-body card-body-more">
                    <view class="card-info">
                        <view class="info-item">
                            <text class="info-label">所属集中器：</text>
                            <text class="info-content">{{item.concentrator.code}} ({{enumFormat.formatConcentratorType(item.concentrator.concentratorType)}})</text>
                        </view>
                        <view class="info-item">
                            <text class="info-label">集中器定位：</text>
                            <text class="info-content">{{item.concentrator.concentratorLocation || '-'}}</text>
                        </view>
                        <view class="info-item">
                            <text class="info-label">计量点名称：</text>
                            <text class="info-content">{{item.measuringPoint.name}}</text>
                            <van-icon wx:if="{{permissions['miniprogram-manage-monitor-detail']}}" style="margin-left: 10rpx" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/detail.svg" size="35rpx" color="#4c8ed9" data-id="{{item.measuringPoint.id}}" catch:tap="measuringPointDetail" />
                        </view>
                        <view class="info-item">
                            <text class="info-label">所属小区：</text>
                            <text class="info-content">{{item.community.name}}</text>
                        </view>
                        <view class="info-item">
                            <text class="info-label">创建时间：</text>
                            <text class="info-content">{{item.createdDate}}</text>
                        </view>
                        <more>
                            <view class="info-item">
                                <text class="info-label">水表型号：</text>
                                <text class="info-content">{{item.model.name}}</text>
                            </view>
                            <view class="info-item">
                                <text class="info-label">是否有阀：</text>
                                <text class="info-content">{{item.valve ? '是' : '否'}}</text>
                            </view>
                            <view class="info-item" wx:if="{{item.concentrator.concentratorType === 'WIRED_MULTI_FUNCTION'}}">
                                <text class="info-label">协议类型：</text>
                                <text class="info-content">{{item.protocolName}}</text>
                            </view>
                            <view class="info-item" wx:if="{{item.concentrator.concentratorType === 'WIRED_MULTI_FUNCTION'}}">
                                <text class="info-label">端口：</text>
                                <text class="info-content">{{item.portName}}</text>
                            </view>
                            <view class="info-item">
                                <text class="info-label">启用读数：</text>
                                <text class="info-content">{{item.startValue}}</text>
                            </view>
                            <view class="info-item">
                                <text class="info-label">口径：</text>
                                <text class="info-content">{{item.diameter || '-'}}</text>
                            </view>
                            <view class="info-item">
                                <text class="info-label">安装日期：</text>
                                <text class="info-content">{{item.installationDetails.installDate || '-'}}</text>
                            </view>
                            <view class="info-item">
                                <text class="info-label">定位地址：</text>
                                <text class="info-content">{{item.location || '-'}}</text>
                            </view>

                            <view class="info-item">
                                <text class="info-label">开户状态：</text>
                                <text class="info-content">{{enumFormat.formatAccount(item.measuringPoint.customer.id, item.measuringPoint.cancellation)}}</text>
                            </view>
                            <view class="info-item">
                                <text class="info-label">户主/户号：</text>
                                <text class="info-content">{{item.measuringPoint.customer.name || '-'}} / {{item.measuringPoint.accountNo || '-'}}</text>
                            </view>
                            <view class="info-item">
                                <text class="info-label">房号：</text>
                                <text class="info-content">{{item.measuringPoint.roomNumber || '-'}}</text>
                            </view>
                            <view class="info-item">
                                <text class="info-label">手机号：</text>
                                <text class="info-content">{{item.measuringPoint.mobile || '-'}}</text>
                            </view>
                            <view class="info-item">
                                <text class="info-label">公司名称：</text>
                                <text class="info-content">{{item.companyId}}</text>
                            </view>
                        </more>
                    </view>

                    <view class="option-btn">
                        <van-icon name="apps-o" size="60rpx" color="#4c8ed9" />
                    </view>
                </view>

            </view>
        </view>
    </block>
    <list-bottom total="{{totalElements}}" loaded="{{currentTotal}}" loading="{{moreLoading}}" />
</scroll-view>

<sticky-button catch:tap="addWaterMeter" permissionCode="miniprogram-manage-concentratorWaterMeter-add" data-code="miniprogram-manage-concentratorWaterMeter-add"></sticky-button>

<van-action-sheet bind:select="actionSelect" bind:cancel="closeAction" bind:click-overlay="closeAction" close-on-click-overlay="{{true}}" cancel-text="取消" show="{{ actionsShow }}" actions="{{ actions }}" description="水表操作" />

<meter-status-show id="meter-status-show"></meter-status-show>

<van-dialog id="van-dialog" />