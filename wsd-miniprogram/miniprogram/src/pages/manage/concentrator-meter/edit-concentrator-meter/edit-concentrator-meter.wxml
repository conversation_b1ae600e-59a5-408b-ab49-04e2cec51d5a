<!--pages/manage/concentrator-meter/add-water-meter/add-water-meter.wxml-->
<van-cell-group inset>
    <van-field disabled model:value="{{ companyName }}" label="所属公司"/>
    <van-field class="verify-item" model:value="{{ measuringPointName }}" maxlength="32" required clearable label="计量点名称" placeholder="计量点名称"/>

    <van-field disabled model:value="{{ communityName }}" label="所属小区"/>

    <van-field disabled model:value="{{ code }}" label="水表编号"/>

    <van-field model:value="{{ houseHolder }}" maxlength="32" clearable label="户主名" placeholder="户主名"/>

    <van-field class="verify-item" model:value="{{ mobile }}" rule="mobile" maxlength="11" clearable label="联系电话" placeholder="联系电话"/>

    <van-field disabled model:value="{{ meterModel }}" label="水表型号"/>

    <van-cell required title="是否有阀" center title-width="6.2em" title-style="text-align:center">
        <view style="padding-left: 24rpx;">
            <van-radio-group model:value="{{ valve }}" direction="horizontal">
                <van-radio name="{{true}}">有阀</van-radio>
                <van-radio name="{{false}}">无阀</van-radio>
            </van-radio-group>
        </view>
    </van-cell>

    <filter-picker border required class="filter-row" labelWidth="6.2em" type="concentrator" params="{{concentratorParams}}" model:value="{{concentrator}}" selectAll="{{false}}"
                   disabled="{{!communityId}}" bind:closeChange="concentratorCloseChange"/>

    <filter-picker border selectAll="{{false}}" required class="filter-row verify-item" labelWidth="6.2em" type="protocol" wx:if="{{isWMF === true}}" model:value="{{protocol}}"/>

    <filter-picker border selectAll="{{false}}" required class="filter-row verify-item" labelWidth="6.2em" type="port" wx:if="{{isWMF === true}}" model:value="{{port}}"/>

    <van-field center="{{collectorUids.length === 0}}" class="collector-input-wrap" label="中继器">
        <view slot="input" style="margin: -10px; margin-top: {{collectorUids.length ? '0' : '-10px'}}">
            <view style="margin: 0 10px">
                <van-tag style="margin:0 10rpx 10rpx 0;" wx:for="{{collectorUids}}" wx:key="index" closeable size="large" type="primary" bind:close="removeCollectorUid"
                         data-index="{{index}}">{{item}}
                </van-tag>
            </view>
            <van-field border="{{false}}" type="number" model:value="{{ collectorUid }}" maxlength="12" clearable placeholder="中继器编号">
                <van-button slot="button" disabled="{{collectorUids.length === 3}}" type="primary" size="small" bindtap="addCollector">添加</van-button>
            </van-field>
        </view>
    </van-field>

    <van-field class="verify-item" model:value="{{ startValue }}" type="digit" min="0.000" max="999999.999" maxlength="10" clearable label="启用读数" placeholder="启用读数" required/>

    <van-field class="verify-item" model:value="{{ diameter }}" type="digit" min="15" max="999999" maxlength="6" clearable label="口径" placeholder="口径"/>

    <filter-date border input-align="left" labelWidth="6.2em" label="安装日期" value="{{installDate}}" type="single" bind:change="dateChange"/>

    <van-field model:value="{{ installLocation }}" maxlength="255" type="textarea" autosize show-word-limit clearable label="安装位置" placeholder="安装位置"/>

    <van-field model:value="{{ location }}" type="textarea" autosize
               clearable readonly="true" label="定位地址" placeholder="定位地址" bind:tap="openMap" right-icon="location-o"/>

</van-cell-group>
<view style="margin: 30rpx 12rpx;">
    <van-button loading="{{loading}}" disabled="{{loading}}" type="primary" block bind:click="save">提交</van-button>
</view>

<van-dialog id="van-dialog"/>
