<!--components/community-consumption/day.wxml-->
<van-tabs active="{{ active }}" bind:change="tabChange">
    <van-tab title="按月统计" name="month"></van-tab>
    <van-tab title="按日统计" name="day"></van-tab>
</van-tabs>
<view class="filter">
    <view>
        <filter-picker class="filter-row" align="center" placeholder="全部小区" labelWidth="0" hasSearch type="community" model:value="{{communityId}}" bind:toggle="toggle" bind:closeChange="monthChange" />
        <filter-date input-align="center" max-range="{{ 30 }}" style="display: {{active === 'day'?'block':'none'}};" value="{{date}}" bind:change="dateChange" bind:toggle="toggle" clearable="{{false}}" />
        <picker style="display: {{active === 'month'?'block':'none'}};" mode="date" model:value="{{year}}" fields="year" start="2020" end="{{currentYear}}" bindchange="monthChange">
            <view class="picker">
                {{year}}
            </view>
        </picker>
    </view>
    <more text="更多查询条件" show-button="{{true}}">
        
        <filter-picker class="filter-row" type="businessType"  labelWidth="160rpx" model:value="{{businessType}}" bind:toggle="toggle"/>
        <filter-picker class="filter-row" type="incomeType"  labelWidth="160rpx" model:value="{{incomeType}}" bind:toggle="toggle"bind:toggle="toggle"/>
        <view style="padding:10rpx 20rpx">
            <van-button style="height:70rpx ;" icon="search" custom-class='btn-sub' type="primary" size="normal" block bindtap="query">查询</van-button>
        </view>
    </more>
</view>

<van-cell-group inset title="">
    <van-cell  center>
        <view slot="title" class="van-ellipsis">合计营收额</view>
        <text class="text-1DA3FD total">{{total === null ? '' : total }}</text> 元
    </van-cell>
</van-cell-group>

<chart-component id="chart" chartData="{{chartData}}" />

<van-sticky z-index="9">
    <view class="detail-total"> 小区营收详情<text wx:if="{{totalElements > 0}}">（共计{{totalElements}}条，已加载{{currentTotal}}条）</text></view>
</van-sticky>
<van-cell-group inset title="">
    <van-cell is-link url="{{'detail/detail?active=' + active + '&year=' + year + '&date=' + date + '&communityId=' + item.communityId + '&communityName=' + item.communityName}}" wx:for="{{content}}" center>
        <view slot="title" class="van-ellipsis">{{item.communityName}}</view>
        <text class="text-1DA3FD">{{item.value}}</text> 元
    </van-cell>
</van-cell-group>

<list-bottom total="{{totalElements}}" loaded="{{currentTotal}}" loading="{{moreLoading}}" initialHeight="300rpx" />