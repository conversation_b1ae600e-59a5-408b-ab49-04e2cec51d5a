<van-cell-group inset>
    <van-cell center custom-class="cell-info">
        <view slot="title" class="info">
            <view class="info-item">
                <text class="info-label">用户名：</text>
                <view class="info-content ">
                    <text class="van-ellipsis">{{name}}</text>
                    <van-icon style="vertical-align: text-bottom;" wx:if="{{gender==='MALE'}}" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/sex1.svg" size="30rpx" />
                    <van-icon style="vertical-align: text-bottom;" wx:elif="{{gender==='FEMALE'}}" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/sex0.svg" size="36rpx" />
                </view>
            </view>
            <view class="info-item">
                <text class="info-label">角色：</text>
                <text class="info-content van-ellipsis">{{roleNames}}</text>
            </view>
            <view class="info-item">
                <text class="info-label">真实姓名：</text>
                <text class="info-content van-ellipsis">{{realName || ''}}</text>
            </view>
            <view class="info-item">
                <text class="info-label">手机号：</text>
                <text class="info-content van-ellipsis">{{mobile || ''}}</text>
            </view>
            <view class="info-item">
                <text class="info-label">E-mail：</text>
                <text class="info-content van-ellipsis">{{email || ''}}</text>
            </view>
        </view>
        <van-icon slot="icon" name="contact" size="200rpx" color="#1296db"></van-icon>
    </van-cell>
</van-cell-group>

<van-cell-group inset>
    <van-cell center is-link title="修改密码" url="/pages/profile/edit-password/edit-password">
        <van-icon slot="icon" name="https://www.acp-iot.com/wsd-ctwing/mini/icons/password.svg" size="50rpx" color="#1296db"></van-icon>
    </van-cell>
    <van-cell center is-link title="修改个人信息" url="/pages/profile/edit-profile/edit-profile">
        <van-icon slot="icon" name="contact" size="50rpx" color="#1296db"></van-icon>
    </van-cell>
</van-cell-group>


<van-cell-group inset wx:if="{{show}}">
    <van-cell value-class="class-set" center is-link url="/pages/profile/company-select/company-select" use-label-slot>
        <view slot="title">
            当前数据范围<text wx:if="{{companyDescribe.total>0}}">（{{companyDescribe.total}}家公司）</text>
        </view>
        <van-icon slot="icon" name="points" size="50rpx" color="#5e2ae8"></van-icon>
        <view slot="label" style="font-size:30rpx;    white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
            {{companyDescribe.text}}
        </view>
        选择
    </van-cell>
</van-cell-group>

<view class="footer">
    <!-- <button type="primary" size="normal" class="sub-btn btn-danger" bindtap="logout" style="font-size:32rpx;">退出登录</button> -->
    <van-button type="danger" size="normal" block bindtap="logout">退出登录</van-button>
</view>

<van-dialog id="van-dialog" />
