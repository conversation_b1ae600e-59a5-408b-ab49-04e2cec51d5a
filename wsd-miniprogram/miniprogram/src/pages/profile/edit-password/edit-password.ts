import commonUtils from "../../../utils/CommonUtils";
import httpUtils from "../../../utils/HttpUtils";
import {JSEncrypt} from "../../../utils/jsEncrypt/JSEncrypt";
import Dialog from "../../../components/vant/dialog/dialog";

Page({

    /**
     * 页面的初始数据
     */
    data: {
        count: 60,
        oldPassword: '',
        newPassword: '',
        confirmPassword: '',
        smsCode: '',
        smsNewPassword: '',
        smsConfirmPassword: '',
        errorMsg: '',
        smsErrorMsg: '',
        loading: false
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad() {

    },

    /**
     * 提交请求
     */
    submit(e: any) {
        const type = e.target.dataset.type;
        if (type === 'oldPassword') {
            if (!this.data.oldPassword || this.data.oldPassword.length < 6) {
                commonUtils.showToast('旧密码不能为空且不能少于6位', 'none', 1500);
                return;
            }
            if (!this.data.newPassword || this.data.newPassword.length < 6) {
                commonUtils.showToast('新密码不能为空且不能少于6位', 'none', 1500);
                return;
            }
            if (this.data.newPassword !== this.data.confirmPassword) {
                commonUtils.showToast('新密码与确认密码不一致', 'none', 1500);
                return;
            }
        } else if (type === 'sms') {
            if (!this.data.smsCode || this.data.smsCode.length !== 4) {
                commonUtils.showToast('短信验证码为4位', 'none', 1500);
                return;
            }
            if (!this.data.smsNewPassword || this.data.smsNewPassword.length < 6) {
                commonUtils.showToast('新密码不能为空且不能少于6位', 'none', 1500);
                return;
            }
            if (this.data.smsNewPassword !== this.data.smsConfirmPassword) {
                commonUtils.showToast('新密码与确认密码不一致', 'none', 1500);
                return;
            }
        }
        Dialog.confirm({
            title: '修改密码',
            message: '是否确认修改密码？',
        }).then(() => {
            // @ts-ignore
            let encrypt = new JSEncrypt();
            let that = this;
            that.setData({
                loading: true
            });
            httpUtils.requestFormUrl({
                url: 'green/getPublicKey',
                method: 'GET',
                params: {},
                success: (publicKey: any) => {
                    encrypt.setPublicKey(publicKey);
                    const encryptPassword = type === 'oldPassword' ? encrypt.encrypt(this.data.newPassword) : encrypt.encrypt(this.data.smsNewPassword);
                    const encryptOldPassword = encrypt.encrypt(this.data.oldPassword);
                    that.updatePassword(type, encryptPassword, encryptOldPassword);
                    that.setData({
                        loading: false
                    });
                },
                failure: (error: any) => {
                    that.setData({
                        loading: false
                    });
                    commonUtils.showToast(error, 'none', 1500);
                }
            });
        }).catch(() => {
            this.setData({
                loading: false
            });
        });
    },

    updatePassword(type: string, encryptPassword: string | boolean, encryptOldPassword: string) {
        let that = this;
        if (type === 'oldPassword') {
            httpUtils.requestFormUrl({
                url: 'auth/mine/updatePasswordByOldPassword',
                method: 'PUT',
                params: {
                    oldPassword: encryptOldPassword,
                    newPassword: encryptPassword
                },
                success: (res: any) => {
                    commonUtils.showToast('修改密码成功', 'none', 1000);
                    // 跳转到登录页，移除token缓存，重新登录
                    wx.removeStorage({
                        key: 'loginUser',
                        success: () => {
                            wx.reLaunch({
                                url: '/pages/login/login'
                            });
                        }
                    });
                },
                failure: (error: any) => {
                    commonUtils.showToast(error, 'none', 1500);
                }
            });
        } else if (type === 'sms') {
            httpUtils.requestFormUrl({
                url: 'auth/mine/updatePasswordBySmsCode',
                method: 'PUT',
                params: {
                    newPassword: encryptPassword,
                    smsCode: that.data.smsCode
                },
                success: (res: any) => {
                    commonUtils.showToast('修改密码成功', 'none', 1000);
                    // 跳转到登录页，移除token缓存，重新登录
                    wx.removeStorage({
                        key: 'loginUser',
                        success: () => {
                            wx.reLaunch({
                                url: '/pages/login/login'
                            });
                        }
                    });
                },
                failure: (error: any) => {
                    commonUtils.showToast(error, 'none', 1500);
                }
            });
        }
    },

    /**
     * 发送短信验证码
     */
    sendCode() {
        if (this.data.count === 60) {
            httpUtils.requestFormUrl({
                url: 'auth/mine/smsCode',
                method: 'GET',
                params: {},
                success: (res: any) => {
                    this.setInterval();
                    commonUtils.showToast('发送成功', 'none', 1500);
                },
                failure: (error: any) => {
                    commonUtils.showToast(error, 'none', 1500);
                }
            });
        }
    },

    setInterval() {
        const interval = setInterval(() => {
            let count = this.data.count;
            if (count > 0) {
                this.setData({
                    count: count - 1
                });
            } else {
                this.setData({
                    count: 60
                });
                clearInterval(interval);
            }
        }, 1000);
    },

    /**
     * 验证两次密码是否相同
     */
    verificationPassword(e: any) {
        const type = e.target.dataset.type;
        if (type === 'oldPassword') {
            if (this.data.newPassword !== this.data.confirmPassword) {
                this.setData({errorMsg: '密码输入不一致'});
            } else {
                this.setData({errorMsg: ''});
            }
        } else if (type === 'sms') {
            if (this.data.smsNewPassword !== this.data.smsConfirmPassword) {
                this.setData({smsErrorMsg: '密码输入不一致'});
            } else {
                this.setData({smsErrorMsg: ''});
            }
        }
    }
})