/* pages/home/<USER>/
page {
    padding-bottom: 14rpx;
    --grid-item-content-background-color: #2491FF;
}

.row-region {
    margin: 14rpx;
}

.grid-region, .statistics {
    border-radius: 16rpx;
    overflow: hidden;
}

.tab-data-row {
    padding: 14rpx 8rpx;
    border-bottom: 1px solid #efefef;
}

.data-item {
    color: #fff;
}

.data-text {
    font-size: 30rpx;
    color: #ceefff;
}

.data-item {
    font-size: 36rpx;
}

.data-until {
    font-size: 26rpx;
    color: #ceefff;
}

.none {
    text-align: center;
    padding: 80rpx;
    font-size: 30rpx;
}

.rank-content-text {
    font-size: 30rpx;
    color: #727474;
    text-align: center;
}