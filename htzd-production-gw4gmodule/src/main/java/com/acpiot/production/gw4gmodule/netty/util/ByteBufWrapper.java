package com.acpiot.production.gw4gmodule.netty.util;

import cn.hutool.core.util.ArrayUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;

import java.nio.charset.StandardCharsets;

import static cn.hutool.core.util.HexUtil.decodeHex;
import static cn.hutool.core.util.HexUtil.encodeHexStr;
import static cn.hutool.core.util.PrimitiveArrayUtil.INDEX_NOT_FOUND;
import static cn.hutool.core.util.PrimitiveArrayUtil.reverse;

@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
public class ByteBufWrapper {

    public static ByteBufWrapper buffer(int initialCapacity) {
        return new ByteBufWrapper(Unpooled.buffer(initialCapacity));
    }

    public static ByteBufWrapper wrappedBuffer(byte[] arrays) {
        return new ByteBufWrapper(Unpooled.wrappedBuffer(arrays));
    }

    public static ByteBufWrapper wrappedBuffer(ByteBuf buf) {
        return new ByteBufWrapper(buf);
    }

    @Delegate
    @Getter
    private final ByteBuf buf;

    public void writeHex(String hex) {
        buf.writeBytes(decodeHex(hex));
    }

    public void writeHexLE(String hex) {
        buf.writeBytes(reverse(decodeHex(hex)));
    }

    /**
     * 扩展读取字节数组
     *
     * @param len
     * @return
     */
    public byte[] readBytesExt(int len) {
        byte[] bytes = new byte[len];
        buf.readBytes(bytes);
        return bytes;
    }

    /**
     * 扩展读取字节数组
     *
     * @param len
     * @return
     */
    public byte[] readReverseBytes(int len) {
        byte[] bytes = new byte[len];
        buf.readBytes(bytes);
        return reverse(bytes);
    }

    public String readAsciiStr(int len) {
        byte[] bytes = readBytesExt(len);
        int length = ArrayUtil.indexOf(bytes, (byte) 0);
        if (length == INDEX_NOT_FOUND) {
            length = bytes.length;
        }
        return new String(bytes, 0, length, StandardCharsets.US_ASCII);
    }

    public String readHexStr(int len) {
        return encodeHexStr(readBytesExt(len), false);
    }

    public String readHexStrLE(int len) {
        return encodeHexStr(reverse(readBytesExt(len)), false);
    }

}
