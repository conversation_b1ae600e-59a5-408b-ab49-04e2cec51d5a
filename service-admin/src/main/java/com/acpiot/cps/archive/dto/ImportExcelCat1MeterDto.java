package com.acpiot.cps.archive.dto;

import com.acpiot.cps.common.adapter.ImportBuildingUnit;
import com.acpiot.cps.common.utils.LocalDateTimeUtils;
import com.acpiot.cps.data.archive.dto.InstallationDetails;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

import static cn.hutool.core.util.StrUtil.isBlank;

/**
 * UDP直连水表档案Excel导入格式
 * Created by YoungLu on 2023-09-07-0001
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@Data
public class ImportExcelCat1MeterDto extends ImportExcelRoomAndCustomerDto implements ImportBuildingUnit {

    @NotBlank(message = "表号不能为空")
    @ColumnWidth(18)
    @ExcelProperty({"UDP直连水表档案", "表号"})
    private String meterCode;

    @Pattern(regexp = "[a-zA-Z0-9]{1,15}", message = "资产号为小于15位字母或数字组成的字符")
    @ColumnWidth(18)
    @ExcelProperty({"UDP直连水表档案", "资产号"})
    private String assetNo;

    /**
     * 用水类型
     */
    @ColumnWidth(10)
    @ExcelProperty({"UDP直连水表档案", "用水类型"})
    private String waterType;

    /**
     * IMEI
     */
    @Pattern(regexp = "\\d{15}", message = "IMEI为空或者长度为15位数字")
    @ColumnWidth(18)
    @ExcelProperty({"UDP直连水表档案", "IMEI"})
    private String imei;

    /**
     * 启用读数
     */
    @NumberFormat("0.000")
    @ColumnWidth(12)
    @ExcelProperty({"UDP直连水表档案", "启用读数"})
    private BigDecimal startValue;

    /**
     * 是否有阀
     */
    @NotBlank(message = "是否有阀不能为空")
    @ColumnWidth(12)
    @ExcelProperty({"UDP直连水表档案", "是否有阀"})
    private String valve;

    /**
     * 口径
     */
    @NumberFormat("0.00")
    @ColumnWidth(12)
    @Min(value = 15, message = "水表口径不能小于15")
    @ExcelProperty({"UDP直连水表档案", "水表口径"})
    private Integer diameter;

    /**
     * 安装位置
     */
    @Size(max = 64, message = "安装位置最多可输入64个字符")
    @ColumnWidth(22)
    @ExcelProperty({"UDP直连水表档案", "安装位置"})
    private String installLocation;

    /**
     * 安装日期
     */
    @DateTimeFormat("yyyy-MM-dd")
    @ColumnWidth(22)
    @ExcelProperty({"UDP直连水表档案", "安装日期"})
    private Date installDate;

    /**
     * 楼栋/单元
     */
    @Size(max = 48, message = "楼栋/单元最多可输入48个字符")
    @ColumnWidth(25)
    @ExcelProperty({"UDP直连水表档案", "楼栋/单元"})
    private String buildingUnit;

    public InstallationDetails getInstallationDetails() {
        if (isBlank(installLocation) && installDate == null) {
            return null;
        }
        InstallationDetails installationDetails = new InstallationDetails();
        installationDetails.setInstallLocation(installLocation);
        if (installDate != null) {
            installationDetails.setInstallDate(LocalDateTimeUtils.toLocalDate(installDate));
        }
        return installationDetails;
    }

    public void setInstallDate(Date installDate) {
        if (installDate != null) {
            this.installDate = new Date(installDate.getTime());
        }
    }
}
