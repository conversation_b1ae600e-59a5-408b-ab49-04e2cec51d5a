package com.acpiot.cps.archive.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.cps.archive.dto.ExcelBleMeterDto;
import com.acpiot.cps.archive.dto.ImportExcelBleMeterDto;
import com.acpiot.cps.archive.service.BuildingUnitService;
import com.acpiot.cps.archive.service.WmBleWaterMeterService;
import com.acpiot.cps.common.dto.DeleteRespDto;
import com.acpiot.cps.common.exception.BusinessException;
import com.acpiot.cps.data.archive.dto.BluePrepayInfo;
import com.acpiot.cps.data.archive.entity.*;
import com.acpiot.cps.data.archive.enums.ProductType;
import com.acpiot.cps.data.archive.enums.BluePrepayOpenAccountStatus;
import com.acpiot.cps.data.archive.repository.CommunityRepository;
import com.acpiot.cps.data.archive.repository.WmBleWaterMeterRepository;
import com.acpiot.cps.data.archive.repository.WmMeterModelRepository;
import com.acpiot.cps.data.archive.repository.WmWaterMeterRepository;
import com.acpiot.cps.data.monitor.entity.mongo.WmReadingFailMeter;
import com.acpiot.cps.data.monitor.repository.WmDayFreezeDataRectifyRepository;
import com.acpiot.cps.data.monitor.repository.WmDayFreezeDataRepository;
import com.acpiot.cps.data.monitor.repository.mongo.*;
import com.acpiot.cps.data.revenue.entity.mongo.WmDayFreezeAmount;
import com.acpiot.cps.data.revenue.repository.mongo.WmDayFreezeAmountRepository;
import com.acpiot.cps.event.EventUtils;
import com.acpiot.cps.event.revenue.WaterMeterBindPriceEvent;
import jakarta.persistence.criteria.JoinType;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.Filter;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.acpiot.cps.utils.AppUtils.checkModelCode;
import static com.acpiot.cps.utils.AppUtils.getRepeatData;

@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class WmBleWaterMeterServiceImpl extends ImportDeviceBindRoomAndCustomerHandler implements WmBleWaterMeterService {

    private final WmBleWaterMeterRepository wmBleWaterMeterRepository;
    private final CommunityRepository communityRepository;
    private final BuildingUnitService buildingUnitService;
    private final WmMeterModelRepository wmMeterModelRepository;
    private final WmWaterMeterRepository wmWaterMeterRepository;
    private final WmDayFreezeDataRectifyRepository wmDayFreezeDataRectifyRepository;
    private final WmMeterCmdLogRepository wmMeterCmdLogRepository;
    private final WmMeterEventRepository wmMeterEventRepository;
    private final WmMeterDataRepository wmMeterDataRepository;
    private final WmCurveDataRepository wmCurveDataRepository;
    private final WmReadingFailMeterRepository wmReadingFailMeterRepository;
    private final WmDayFreezeDataRepository wmDayFreezeDataRepository;
    private final WmDayFreezeAmountRepository wmDayFreezeAmountRepository;

    @Override
    @Transactional(readOnly = true)
    public Page<WmBleWaterMeter> pageWmBleWaterMeter(PagingQueryParams<WmBleWaterMeter> params) {
        return wmBleWaterMeterRepository.findAll(params);
    }

    @Override
    @Transactional(readOnly = true)
    public WmBleWaterMeter getWmBleWaterMeterById(Long id) {
        return wmBleWaterMeterRepository.findOne((root, query, cb) -> {
            root.fetch(WmBleWaterMeter_.COMMUNITY);
            root.fetch(WmBleWaterMeter_.BUILDING_UNIT, JoinType.LEFT);
            root.fetch(WmBleWaterMeter_.ROOM, JoinType.LEFT);
            root.fetch(WmBleWaterMeter_.MODEL);
            return cb.equal(root.get(WmBleWaterMeter_.id), id);
        }).orElseThrow(() -> new BusinessException("不存在该数据"));
    }

    @Override
    public WmBleWaterMeter addWmBleWaterMeter(WmBleWaterMeter wmBleWaterMeter) {
        checkAssetNo(wmBleWaterMeter);
        Community dbCommunity = communityRepository.findById(wmBleWaterMeter.getCommunity().getId()).orElseThrow(() -> new BusinessException("小区不存在"));
        wmBleWaterMeter.setCommunity(dbCommunity);
        wmBleWaterMeter.setCompanyId(dbCommunity.getCompanyId());
        wmBleWaterMeter.getBluePrepayInfo().setSalesCompanyId(dbCommunity.getCompanyId());
        if (!wmWaterMeterRepository.isUnique(wmBleWaterMeter, WmWaterMeter_.companyId, WmWaterMeter_.code)) {
            throw new BusinessException("该水表编号已存在：{}", wmBleWaterMeter.getCode());
        }
        if (!wmWaterMeterRepository.isAssetNoUnique(wmBleWaterMeter.getBluePrepayInfo().getAssetNo(), null)) {
            throw new BusinessException("该资产号已存在：{}", wmBleWaterMeter.getBluePrepayInfo().getAssetNo());
        }
        if (wmBleWaterMeter.getBuildingUnit() != null) {
            // 从数据库找到该楼栋单元信息
            BuildingUnit buildingUnit = buildingUnitService.getBuildingUnitById(wmBleWaterMeter.getBuildingUnit().getId());
            wmBleWaterMeter.setBuildingUnit(buildingUnit);
        }

        WmMeterModel dbMeterModel = wmMeterModelRepository.findById(wmBleWaterMeter.getModel().getId())
                .orElseThrow(() -> new BusinessException("水表型号不存在"));
        // 根据水表型号-通道机制判断表号是否符合要求
        checkModelCode(ProductType.WATER, dbMeterModel.getCommunicationMechanism(), List.of(wmBleWaterMeter.getCode()));
        wmBleWaterMeter.setModel(dbMeterModel);
        wmBleWaterMeter.setBluePrepay(true);

        return wmBleWaterMeterRepository.save(wmBleWaterMeter);
    }

    @Override
    public void updateWmBleWaterMeter(WmBleWaterMeter wmBleWaterMeter) {
        checkAssetNo(wmBleWaterMeter);
        WmBleWaterMeter dbWaterMeter = wmBleWaterMeterRepository.findById(wmBleWaterMeter.getId()).orElseThrow(() -> new BusinessException("该水表不存在"));
        if (!wmWaterMeterRepository.isAssetNoUnique(wmBleWaterMeter.getBluePrepayInfo().getAssetNo(), dbWaterMeter.getId())) {
            throw new BusinessException("该资产号已存在：{}", wmBleWaterMeter.getBluePrepayInfo().getAssetNo());
        }
        // 编辑时设置水价，或者修改水价，需处理日冻结金额和刷新可用余额
        boolean needRefresh = wmBleWaterMeter.getChargeScheme() != null && dbWaterMeter.getChargeScheme() == null || wmBleWaterMeter.getChargeScheme() != null && !Objects.equals(wmBleWaterMeter.getChargeScheme().getId(), dbWaterMeter.getChargeScheme().getId());

        if (wmBleWaterMeter.getBuildingUnit() != null) {
            BuildingUnit buildingUnit = buildingUnitService.getBuildingUnitById(wmBleWaterMeter.getBuildingUnit().getId());
            dbWaterMeter.setBuildingUnit(buildingUnit);
        } else {
            dbWaterMeter.setBuildingUnit(null);
        }

        dbWaterMeter.setStartValue(wmBleWaterMeter.getStartValue());
        dbWaterMeter.setDiameter(wmBleWaterMeter.getDiameter());
        dbWaterMeter.setInstallationDetails(wmBleWaterMeter.getInstallationDetails());
        dbWaterMeter.setLocation(wmBleWaterMeter.getLocation());
        dbWaterMeter.setLatitude(wmBleWaterMeter.getLatitude());
        dbWaterMeter.setLongitude(wmBleWaterMeter.getLongitude());
        dbWaterMeter.setInstallationDetails(wmBleWaterMeter.getInstallationDetails());
        dbWaterMeter.setChargeScheme(wmBleWaterMeter.getChargeScheme());
        dbWaterMeter.setBluePrepay(true);
        dbWaterMeter.getBluePrepayInfo().setAssetNo(wmBleWaterMeter.getBluePrepayInfo().getAssetNo());
        dbWaterMeter.setValve(wmBleWaterMeter.isValve());

        if (needRefresh) {
            EventUtils.publishEvent(this, new WaterMeterBindPriceEvent(dbWaterMeter.getId(), dbWaterMeter.getRoom() != null ? dbWaterMeter.getRoom().getId() : null));
        }
    }

    @Override
    public DeleteRespDto removeWaterMeters(Long... ids) {
        List<WmBleWaterMeter> waterMeters = wmBleWaterMeterRepository.findAll(new Filter[]{Filter.in(WmBleWaterMeter_.ID, ids)},
                null, WmBleWaterMeter_.ROOM, String.format("%s.%s", WmBleWaterMeter_.ROOM, Room_.CUSTOMER));
        if (CollectionUtil.isEmpty(waterMeters)) {
            throw new BusinessException("要删除的数据为空");
        }
        // 不能删除已经绑定房间的水表
        List<WmBleWaterMeter> bindRoomAndCustomer = waterMeters.stream().filter(wm -> wm.getRoom() != null).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(bindRoomAndCustomer)) {
            String codes = bindRoomAndCustomer.stream().map(WmWaterMeter::getCode).collect(Collectors.joining(","));
            throw new BusinessException("以下水表已绑定房间和住户，请先销表，表号：{}", codes);
        }

        // 删除日冻结数据校正记录
        wmDayFreezeDataRectifyRepository.deleteByWaterMeterIds(ids);
        // 删除关联的命令日志
        wmMeterCmdLogRepository.deleteByMeterIdIn(ids);
        // 删除关联的事件
        wmMeterEventRepository.deleteByMeterIdIn(ids);
        // 删除关联的历史数据
        wmMeterDataRepository.deleteByMeterIdIn(ids);
        // 删除关联的曲线数据
        wmCurveDataRepository.deleteByMeterIdIn(ids);
        // 删除抄表失败节点
        wmReadingFailMeterRepository.deleteByWaterMeterIdIn(ids);
        // 删除日冻结数据
        wmDayFreezeDataRepository.deleteByWaterMeter_IdIn(ids);
        // 删除日用水金额
        wmDayFreezeAmountRepository.deleteByMeterIdIn(ids);
        // 删除水表本身
        wmBleWaterMeterRepository.deleteAllInBatch(waterMeters);

        // 存档删除的信息
        return new DeleteRespDto(waterMeters.stream().map(DeleteRespDto::mapToWaterMeterDelDto).collect(Collectors.toList()));
    }

    @Override
    public void batchEditWaterMeters(String companyId, List<ImportExcelBleMeterDto> uploadMeterDtos) {
        // 找出excel中重复的表号
        List<String> meterCodes = checkUploadMeterDto(uploadMeterDtos, ImportExcelBleMeterDto::getMeterCode, "Excel中存在重复的表号：{}");
        checkUploadMeterDto(uploadMeterDtos, ImportExcelBleMeterDto::getAssetNo, "Excel中存在重复的资产号：{}");
        List<WmBleWaterMeter> bleWaterMeters = wmBleWaterMeterRepository.findByCompanyIdAndCodeIn(companyId, meterCodes);
        if (CollectionUtil.isEmpty(bleWaterMeters)) {
            throw new BusinessException("不存在待修改的水表");
        }
        if (bleWaterMeters.size() < meterCodes.size()) {
            // 处理不存在的表号
            String noExistsCodes = meterCodes.stream().filter(m -> bleWaterMeters.stream().noneMatch(w -> w.getCode().equals(m))).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(noExistsCodes)) {
                throw new BusinessException("Excel中的部分蓝牙水表表号在系统中不存在：[{}]", noExistsCodes);
            }
        }

        uploadMeterDtos.forEach(excelBleMeterDto -> {
            // 当前表号
            String excelMeterCode = excelBleMeterDto.getMeterCode();

            // 根据水表表号从集合取出UDP直连水表
            WmBleWaterMeter wmBleWaterMeter = bleWaterMeters.stream().filter(w -> w.getCode().equals(excelMeterCode))
                    .findFirst().orElseThrow(() -> new BusinessException("不存在该水表，code={}", excelMeterCode));
            String excelAssetNo = excelBleMeterDto.getAssetNo();
            if (StrUtil.isNotBlank(excelAssetNo)) {
                if (!wmWaterMeterRepository.isAssetNoUnique(excelAssetNo, wmBleWaterMeter.getId())) {
                    throw new BusinessException("该水表: {}对应资产号: {}在系统中已存在", excelMeterCode, excelAssetNo);
                }
                wmBleWaterMeter.getBluePrepayInfo().setAssetNo(excelAssetNo);
            }
            // 修改水表相关信息，启用读数依据表格中是否填入决定是否修改
            if (Objects.nonNull(excelBleMeterDto.getStartValue())) {
                wmBleWaterMeter.setStartValue(excelBleMeterDto.getStartValue());
            }
            wmBleWaterMeter.setDiameter(excelBleMeterDto.getDiameter());
            wmBleWaterMeter.setInstallationDetails(excelBleMeterDto.getInstallationDetails());

            // 自动处理归属的楼栋单元
            Map<Integer, List<BuildingUnit>> buildingUnits = super.validAndBuildBuildingUnits(wmBleWaterMeter.getCommunity(), List.of(excelBleMeterDto));
            // 设置楼栋单元
            super.fillLowerCommunity(wmBleWaterMeter, excelBleMeterDto, buildingUnits);
        });
    }

    @Override
    public void changeMeterCode(long id, String newMeterCode) {
        WmBleWaterMeter wmBleWaterMeter = wmBleWaterMeterRepository.findById(id).orElseThrow(() -> new BusinessException("不存在该水表，无法修改水表表号"));
        String oldMeterCode = wmBleWaterMeter.getCode();
        if (newMeterCode.equals(oldMeterCode)) {
            throw new BusinessException("新表号不能和旧表号相同");
        }
        // 检验新表表号是否允许使用
        WmWaterMeter newWaterMeter = new WmWaterMeter();
        newWaterMeter.setCompanyId(wmBleWaterMeter.getCompanyId());
        newWaterMeter.setCode(newMeterCode);
        if (!wmWaterMeterRepository.isUnique(newWaterMeter, WmWaterMeter_.companyId, WmWaterMeter_.code)) {
            throw new BusinessException("新表号:{} 已在系统中存在，请修改后再试！", newMeterCode);
        }
        // 根据水表型号-通道机制判断表号是否符合要求
        checkModelCode(ProductType.WATER, wmBleWaterMeter.getModel().getCommunicationMechanism(), List.of(newMeterCode));
        wmBleWaterMeter.setCode(newMeterCode);
        // 更新日冻结数据表号
        wmDayFreezeDataRepository.updateMeterCodeByMeterCode(wmBleWaterMeter, oldMeterCode, newMeterCode);
        // 更新日冻结数据矫正记录表号
        wmDayFreezeDataRectifyRepository.updateMeterCodeByMeterCode(wmBleWaterMeter, oldMeterCode, newMeterCode);
        // 更新抄表成功率中的表号
        Update updateReadingFailMeter = new Update();
        updateReadingFailMeter.set("meterCode", newMeterCode);
        Query queryReadingFailMeter = new Query(new Criteria("meterCode").is(oldMeterCode));
        wmReadingFailMeterRepository.getMongoTemplate().updateMulti(queryReadingFailMeter, updateReadingFailMeter, WmReadingFailMeter.class);
        // 更新日冻结金额表号
        Update updateDayFreezeAmount = new Update();
        updateDayFreezeAmount.set("meterCode", newMeterCode);
        Query queryDayFreezeAmount = new Query(new Criteria("meterCode").is(oldMeterCode));
        wmDayFreezeAmountRepository.getMongoTemplate().updateMulti(queryDayFreezeAmount, updateDayFreezeAmount, WmDayFreezeAmount.class);
    }

    @Override
    public void batchImportWaterMeters(long communityId, long modelId, List<ImportExcelBleMeterDto> uploadMeterDtos) {
        // 校验Excel导入数据
        checkExcelData(uploadMeterDtos);

        // 查询归属小区和设备型号
        Community community = communityRepository.findById(communityId).orElseThrow(() -> new BusinessException("该小区不存在"));
        String companyId = community.getCompanyId();
        WmMeterModel meterModel = wmMeterModelRepository.findById(modelId).orElseThrow(() -> new BusinessException("该水表型号不存在"));
        // 根据水表型号-通道机制判断表号是否符合要求
        checkModelCode(ProductType.WATER, meterModel.getCommunicationMechanism(), uploadMeterDtos.stream().map(ImportExcelBleMeterDto::getMeterCode).toList());
        // 自动处理归属的楼栋单元
        Map<Integer, List<BuildingUnit>> buildingUnits = super.validAndBuildBuildingUnits(community, uploadMeterDtos);

        if (CollectionUtil.isNotEmpty(uploadMeterDtos)) {
            checkUploadData(companyId, uploadMeterDtos);

            boolean bluePrepay = true;
            List<WmBleWaterMeter> waterMetersToSave = new ArrayList<>();

            uploadMeterDtos.forEach(meterDto -> {
                WmBleWaterMeter waterMeter = new WmBleWaterMeter();
                waterMeter.setModel(meterModel);
                waterMeter.setBluePrepay(bluePrepay);
                waterMeter.setCommunity(community);
                waterMeter.setCompanyId(companyId);
                waterMeter.setCode(meterDto.getMeterCode());
                BluePrepayInfo bluePrepayInfo = new BluePrepayInfo();
                bluePrepayInfo.setAssetNo(meterDto.getAssetNo());
                bluePrepayInfo.setSalesCompanyId(companyId);
                bluePrepayInfo.setOpenAccountStatus(BluePrepayOpenAccountStatus.UNOPENED);
                waterMeter.setBluePrepayInfo(bluePrepayInfo);

                BigDecimal startValue = meterDto.getStartValue();
                if (startValue == null) {
                    startValue = BigDecimal.ZERO;
                }
                waterMeter.setStartValue(startValue);
                waterMeter.setDiameter(meterDto.getDiameter());
                waterMeter.setInstallationDetails(meterDto.getInstallationDetails());
                waterMeter.setValve(Objects.equals("是", meterDto.getValve()));

                // 设置楼栋单元
                super.fillLowerCommunity(waterMeter, meterDto, buildingUnits);
                waterMetersToSave.add(waterMeter);
                super.importWaterMeterBindRoomAndCustomer(meterDto, community, waterMeter, buildingUnits);
            });

            // 循环结束后，批量保存所有水表对象
            wmBleWaterMeterRepository.saveAll(waterMetersToSave);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ExcelBleMeterDto> getExportBleWaterMeters(PagingQueryParams<WmBleWaterMeter> queryParams) {
        queryParams.fetch(WmBleWaterMeter_.ROOM).fetch(String.format("%s.%s", WmBleWaterMeter_.ROOM, Room_.CUSTOMER)).fetch(WmBleWaterMeter_.COMMUNITY);
        Page<WmBleWaterMeter> bleWaterMeters = wmBleWaterMeterRepository.findAll(queryParams, Pageable.unpaged());
        buildingUnitService.buildBuildingUnitName(bleWaterMeters.getContent());
        return bleWaterMeters.stream().map(ExcelBleMeterDto::new).collect(Collectors.toList());
    }

    /**
     * 检验导入的数据合法性
     *
     * @param importExcelBleMeterDtos 导入的数据列表
     */
    /**
     * 检验导入的数据合法性
     *
     * @param importExcelBleMeterDtos
     */
    private void checkExcelData(List<ImportExcelBleMeterDto> importExcelBleMeterDtos) {
        // 找出重复的表号
        checkUploadMeterDto(importExcelBleMeterDtos, ImportExcelBleMeterDto::getMeterCode,
                "Excel中存在重复的表号：{}");
        String emptyAssetNoMeterCodes = importExcelBleMeterDtos.stream().filter(importExcelBleMeterDto -> StrUtil.isNotBlank(importExcelBleMeterDto.getMeterCode())
                && StrUtil.isBlank(importExcelBleMeterDto.getAssetNo())).map(ImportExcelBleMeterDto::getMeterCode).collect(Collectors.joining(","));
        if (StrUtil.isNotBlank(emptyAssetNoMeterCodes)) {
            throw new BusinessException("这些表号: {}对应的资产号为空，请更正后重新导入", emptyAssetNoMeterCodes);
        }
        checkUploadMeterDto(importExcelBleMeterDtos, ImportExcelBleMeterDto::getAssetNo, "Excel中存在重复的资产号：{}");
    }

    /**
     * 校验设备批量上传新增数据合法性
     *
     * @param companyId
     * @param importExcelBleMeterDtos
     */
    private void checkUploadData(String companyId, List<ImportExcelBleMeterDto> importExcelBleMeterDtos) {
        // 检查水表编号是否已存在系统
        List<String> meterCodes = importExcelBleMeterDtos.stream().map(ImportExcelBleMeterDto::getMeterCode).collect(Collectors.toList());
        List<String> existMeterCodes = wmWaterMeterRepository.getExistMeterCodes(meterCodes, companyId);
        if (!existMeterCodes.isEmpty()) {
            throw new BusinessException("这部分表号在系统中已存在，请检查：{}", existMeterCodes);
        }
        List<String> assetNos = importExcelBleMeterDtos.stream().map(ImportExcelBleMeterDto::getAssetNo).collect(Collectors.toList());
        List<String> existAssetNos = wmWaterMeterRepository.getExistAssetNos(assetNos);
        if (!existAssetNos.isEmpty()) {
            throw new BusinessException("这部分资产号在系统中已存在，请检查：{}", existAssetNos);
        }
    }


    private List<String> checkUploadMeterDto(List<ImportExcelBleMeterDto> importExcelBleMeterDtos,
                                             Function<ImportExcelBleMeterDto, String> mapper, String format) {
        List<String> strList = importExcelBleMeterDtos.stream().map(mapper).collect(Collectors.toList());
        List<String> repeatList = getRepeatData(strList);
        if (!repeatList.isEmpty()) {
            throw new BusinessException(format, repeatList);
        }
        return strList;
    }

    private void checkAssetNo(WmBleWaterMeter wmBleWaterMeter) {
        BluePrepayInfo bluePrepayInfo = wmBleWaterMeter.getBluePrepayInfo();
        if (bluePrepayInfo == null || StrUtil.isBlank(bluePrepayInfo.getAssetNo())) {
            throw new BusinessException("资产号不能为空");
        }
        if (bluePrepayInfo.getAssetNo().length() > 15) {
            throw new BusinessException("资产号长度不能超过15位");
        }
    }
}
