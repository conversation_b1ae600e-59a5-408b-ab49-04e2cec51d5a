package com.acpiot.cps.archive.controller;

import com.acpiot.cps.archive.dto.ElectricMeterBoundDto;
import com.acpiot.cps.archive.service.BuildingUnitService;
import com.acpiot.cps.archive.service.EmElectricMeterService;
import com.acpiot.cps.common.logging.SystemLog;
import com.acpiot.cps.data.archive.entity.EmElectricMeter;
import com.acpiot.cps.data.archive.entity.EmElectricMeter_;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import pers.mx.jupiter.jpa.Filter;
import pers.mx.jupiter.jpa.PagingQueryParams;
import pers.mx.jupiter.web.util.ResponseUtils;

import java.util.List;

/**
 * 电表(EmElectricMeter)表控制层
 *
 * <AUTHOR>
 * @since 2022-08-30 14:14:30
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/auth/archive/meter/electric")
public class EmElectricMeterController {

    private final EmElectricMeterService emElectricMeterService;
    private final BuildingUnitService buildingUnitService;

    @PostMapping("page")
    public Page<EmElectricMeter> pageEmElectricMeter(@RequestBody PagingQueryParams<EmElectricMeter> params) {
        params.fetch(EmElectricMeter_.COMMUNITY);
        params.fetch(EmElectricMeter_.BUILDING_UNIT);
        params.fetch(EmElectricMeter_.MODEL);
        params.and(Filter.isNull(EmElectricMeter_.ROOM));
        params.fetch(EmElectricMeter_.CHARGE_SCHEME);
        params.and(Filter.eq(EmElectricMeter_.CANCELLATION, false));
        return emElectricMeterService.pageEmElectricMeter(params);
    }

    @GetMapping("getElectricMetersByRoomId")
    public List<EmElectricMeter> getElectricMetersByRoomId(@RequestParam("roomId") Long roomId) {
        return emElectricMeterService.getElectricMetersByRoomId(roomId);
    }

    @GetMapping("getParentMeter")
    public ElectricMeterBoundDto getParentMeter(@RequestParam("meterCode") String meterCode) {
        return emElectricMeterService.findParentMeterByCode(meterCode);
    }

    @GetMapping("getParentMeterById")
    public ElectricMeterBoundDto getParentMeterById(@RequestParam("id") long id) {
        return emElectricMeterService.findParentMeterById(id);
    }

    @GetMapping("findChildrenMetersById")
    @ResponseBody
    public List<EmElectricMeter> findChildrenMetersById(@RequestParam long meterId) {
        List<EmElectricMeter> meters = emElectricMeterService.findChildrenMetersById(meterId);
        buildingUnitService.buildBuildingUnitName(meters);
        return meters;
    }

    @SystemLog(description = "电表批量更换小区")
    @PostMapping("batchChangeCommunity")
    @PreAuthorize("hasAuthority('archive-meter-electric-changeCommunity')")
    public ResponseEntity<?> batchChangeCommunity(@RequestParam("ids[]") long[] ids,
                                                  @RequestParam("communityId") long communityId,
                                                  @RequestParam(value = "buildingUnitId", required = false) Long buildingUnitId) {
        emElectricMeterService.batchChangeCommunity(ids, communityId, buildingUnitId);
        return ResponseUtils.ok("操作成功");
    }
}