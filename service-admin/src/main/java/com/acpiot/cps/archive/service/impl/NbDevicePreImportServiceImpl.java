package com.acpiot.cps.archive.service.impl;

import com.acpiot.cps.archive.dto.ExcelNbMeterPreImportDto;
import com.acpiot.cps.archive.service.CommunityService;
import com.acpiot.cps.archive.service.NbDevicePreImportService;
import com.acpiot.cps.archive.service.WmNbWaterMeterService;
import com.acpiot.cps.common.exception.BusinessException;
import com.acpiot.cps.data.archive.entity.Community;
import com.acpiot.cps.data.archive.entity.WmMeterModel;
import com.acpiot.cps.data.archive.entity.WmNbWaterMeter;
import com.acpiot.cps.data.archive.entity.WmNbWaterMeter_;
import com.acpiot.cps.data.archive.enums.CommunicationMechanism;
import com.acpiot.cps.data.archive.repository.NbInfoRepository;
import com.acpiot.cps.data.archive.repository.WmMeterModelRepository;
import com.acpiot.cps.data.archive.repository.WmNbWaterMeterRepository;
import com.acpiot.cps.data.archive.repository.WmWaterMeterRepository;
import com.acpiot.cps.data.sys.entity.Company;
import com.acpiot.cps.data.sys.repository.CompanyRepository;
import com.acpiot.cps.nb.enums.IotPlatform;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.Filter;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class NbDevicePreImportServiceImpl implements NbDevicePreImportService {

    private final WmNbWaterMeterRepository nbWaterMeterRepository;
    private final CompanyRepository companyRepository;
    private final WmWaterMeterRepository waterMeterRepository;
    private final NbInfoRepository nbInfoRepository;
    private final CommunityService communityService;
    private final WmMeterModelRepository wmMeterModelRepository;
    private final WmNbWaterMeterService wmNbWaterMeterService;

    @Override
    @Transactional(readOnly = true)
    public Page<WmNbWaterMeter> pagedNbWaterMeterPreImport(PagingQueryParams<WmNbWaterMeter> params) {
        params.and(Filter.eq(WmNbWaterMeter_.PRE_IMPORT, true));
        params.fetch(WmNbWaterMeter_.NB_INFO);
        return nbWaterMeterRepository.findAll(params);
    }

    @Override
    public void batchImportNbWaterMeterPreImport(Long companyId, long modelId, boolean bluePrepay, List<ExcelNbMeterPreImportDto> excelNbMeterPreImportDtoList) {
        Company company = companyRepository.findById(companyId).orElseThrow(() -> new BusinessException("公司不存在"));

        checkUploadData(company.getCompanyId(), excelNbMeterPreImportDtoList);

        // 获取内置小区
        Community reserveCommunity = communityService.getReserveCommunity();

        // 获取水表型号
        WmMeterModel meterModel = wmMeterModelRepository.findById(modelId).orElseThrow(() -> new BusinessException("该水表型号不存在"));

        List<WmNbWaterMeter> nbWaterMeters = new ArrayList<>();
        excelNbMeterPreImportDtoList.forEach(excelNbMeterPreImportDto -> {
            WmNbWaterMeter nbWaterMeter = excelNbMeterPreImportDto.toNbWaterMeter();
            nbWaterMeter.setCommunity(reserveCommunity);
            nbWaterMeter.setModel(meterModel);
            nbWaterMeter.setBluePrepay(bluePrepay);
            nbWaterMeter.setCompanyId(company.getCompanyId());
            nbWaterMeters.add(nbWaterMeter);
        });
        nbWaterMeterRepository.saveAll(nbWaterMeters);

        // 向IoT平台注册
        if (meterModel.getCommunicationMechanism() == CommunicationMechanism.STANDARD) {
            Map<IotPlatform, List<WmNbWaterMeter>> iotPlatformWaterMeters = nbWaterMeters.stream()
                    .collect(Collectors.groupingBy(o -> o.getNbInfo().getIotPlatform()));
            iotPlatformWaterMeters.forEach((iotPlatform, iotPlatformWaterMeter)
                    -> wmNbWaterMeterService.batchCreateDevice(meterModel.getNbWmProgram(), iotPlatformWaterMeter, iotPlatform));
        }
    }

    /**
     * 校验预导入设备批量上传新增数据合法性
     */
    private void checkUploadData(String companyId, List<ExcelNbMeterPreImportDto> excelNbMeterPreImportDtoList) {
        // 检查水表编号是否已存在系统
        List<String> meterCodes = excelNbMeterPreImportDtoList.stream().map(ExcelNbMeterPreImportDto::getMeterCode).collect(Collectors.toList());
        List<String> existMeterCodes = waterMeterRepository.getExistMeterCodes(meterCodes, companyId);
        if (!existMeterCodes.isEmpty()) {
            throw new BusinessException("这部分表号在系统中已存在，请检查：{}", existMeterCodes);
        }
        // 检查imei是否已存在系统
        List<String> imeis = excelNbMeterPreImportDtoList.stream().map(ExcelNbMeterPreImportDto::getImei).collect(Collectors.toList());
        List<String> existImeis = nbInfoRepository.getExistImeis(imeis);
        if (!existImeis.isEmpty()) {
            throw new BusinessException("这部分IMEI在系统中已存在，请检查：{}", existImeis);
        }
        // 检查imsi是否已存在系统
        List<String> imsis = excelNbMeterPreImportDtoList.stream().map(ExcelNbMeterPreImportDto::getImsi).collect(Collectors.toList());
        List<String> existImsis = nbInfoRepository.getExistImsis(imsis);
        if (!existImsis.isEmpty()) {
            throw new BusinessException("这部分IMSI在系统中已存在，请检查：{}", existImsis);
        }
    }
}
