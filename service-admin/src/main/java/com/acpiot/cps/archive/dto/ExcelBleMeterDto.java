package com.acpiot.cps.archive.dto;

import cn.hutool.extra.spring.SpringUtil;
import com.acpiot.cps.common.cache.CompanyNameCache;
import com.acpiot.cps.common.utils.LocalDateTimeUtils;
import com.acpiot.cps.data.archive.dto.InstallationDetails;
import com.acpiot.cps.data.archive.entity.Customer;
import com.acpiot.cps.data.archive.entity.Room;
import com.acpiot.cps.data.archive.entity.WmBleWaterMeter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import static cn.hutool.core.util.StrUtil.isBlank;

/**
 * 蓝牙水表档案Excel导出格式
 * Created by YoungLu on 2024-04-29-0001
 *
 * <AUTHOR>
 */
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@Data
public class ExcelBleMeterDto {

    public ExcelBleMeterDto() {
    }

    public ExcelBleMeterDto(WmBleWaterMeter wmBleWaterMeter) {
        this.meterCode = wmBleWaterMeter.getCode();
        this.assetNo = wmBleWaterMeter.getBluePrepayInfo().getAssetNo();
        this.communityName = wmBleWaterMeter.getCommunity().getName();
        Room room = wmBleWaterMeter.getRoom();
        if (room != null) {
            this.roomNumber = room.getRoomNumber();
            Customer customer = room.getCustomer();
            if (customer != null) {
                this.customerName = customer.getName();
                this.mobile = customer.getMobile();
            }
        }
        this.startValue = wmBleWaterMeter.getStartValue();
        this.diameter = wmBleWaterMeter.getDiameter();
        InstallationDetails installationDetails = wmBleWaterMeter.getInstallationDetails();
        if (installationDetails != null) {
            this.installLocation = installationDetails.getInstallLocation();
            LocalDate installDate = installationDetails.getInstallDate();
            this.installDate = installDate != null ? LocalDateTimeUtils.toDate(installDate) : null;
        }
        this.buildingUnitNames = wmBleWaterMeter.getBuildingUnitNames();
        this.companyId = SpringUtil.getBean(CompanyNameCache.class).get(wmBleWaterMeter.getCompanyId());
        this.valve = wmBleWaterMeter.isValve() ? "是" : "否";
    }

    @ColumnWidth(20)
    @ExcelProperty({"蓝牙水表档案", "公司名称"})
    private String companyId;

    @NotBlank(message = "表号不能为空")
    @Pattern(regexp = "\\d{14}", message = "表号长度为14位数字")
    @ColumnWidth(18)
    @ExcelProperty({"蓝牙水表档案", "表号"})
    private String meterCode;

    @NotBlank(message = "资产号不能为空")
    @Pattern(regexp = "[a-zA-Z0-9]{1,15}", message = "资产号为小于15位字母或数字组成的字符")
    @ColumnWidth(18)
    @ExcelProperty({"蓝牙水表档案", "资产号"})
    private String assetNo;

    /**
     * 姓名
     */
    @Size(max = 64, message = "姓名最多可输入64个字符")
    @ColumnWidth(22)
    @ExcelProperty({"蓝牙水表档案", "姓名"})
    private String customerName;

    /**
     * 联系电话
     */
    @Size(max = 22, message = "联系电话最多可输入22个字符")
    @ColumnWidth(18)
    @ExcelProperty({"蓝牙水表档案", "联系电话"})
    private String mobile;

    /**
     * 小区名称
     */
    @ColumnWidth(18)
    @ExcelProperty({"蓝牙水表档案", "小区名称"})
    private String communityName;

    /**
     * 启用读数
     */
    @NumberFormat("0.000")
    @ColumnWidth(12)
    @ExcelProperty({"蓝牙水表档案", "启用读数"})
    private BigDecimal startValue;

    /**
     * 口径
     */
    @NumberFormat("0.00")
    @ColumnWidth(12)
    @Min(value = 15, message = "水表口径不能小于15")
    @ExcelProperty({"蓝牙水表档案", "水表口径"})
    private Integer diameter;

    /**
     * 是否有阀
     */
    @NotBlank(message = "是否有阀不能为空")
    @ColumnWidth(12)
    @ExcelProperty({"蓝牙水表档案", "是否有阀"})
    private String valve;

    /**
     * 安装位置
     */
    @Size(max = 64, message = "安装位置最多可输入64个字符")
    @ColumnWidth(22)
    @ExcelProperty({"蓝牙水表档案", "安装位置"})
    private String installLocation;

    /**
     * 安装日期
     */
    @DateTimeFormat("yyyy-MM-dd")
    @ColumnWidth(22)
    @ExcelProperty({"蓝牙水表档案", "安装日期"})
    private Date installDate;

    /**
     * 楼栋/单元
     */
    @ColumnWidth(25)
    @ExcelProperty({"蓝牙水表档案", "楼栋/单元"})
    private String buildingUnitNames;

    /**
     * 房号
     */
    @ColumnWidth(25)
    @ExcelProperty({"蓝牙水表档案", "房号"})
    private String roomNumber;

    @ColumnWidth(20)
    @ExcelProperty({"蓝牙水表档案", "是否本地预付费"})
    private String bluePrepay;

    /**
     * 销售公司
     */
    @ColumnWidth(24)
    @ExcelProperty({"蓝牙水表档案", "销售公司"})
    private String salesCompanyId;

    /**
     * 销售时间
     */
    @ColumnWidth(35)
    @ExcelProperty({"蓝牙水表档案", "销售时间"})
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime salesTime;

    /**
     * 最新余额
     */
    @ColumnWidth(10)
    @ExcelProperty({"蓝牙水表档案", "最新余额"})
    private BigDecimal lastBalance;

    /**
     * 水价启用日期
     */
    @ColumnWidth(12)
    @ExcelProperty({"蓝牙水表档案", "水价启用日期"})
    private String enableDate;

    /**
     * 水费结算周期
     */
    @ColumnWidth(12)
    @ExcelProperty({"蓝牙水表档案", "水费结算周期"})
    private String settlementPeriod;

    /**
     * 水价读取时间
     */
    @ColumnWidth(35)
    @ExcelProperty({"蓝牙水表档案", "水价读取时间"})
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime priceReadingTime;

    /**
     * 水价
     */
    @ColumnWidth(40)
    @ExcelProperty({"蓝牙水表档案", "水价"})
    private String price;

    /**
     * 开户状态
     */
    @ColumnWidth(12)
    @ExcelProperty({"蓝牙水表档案", "开户状态"})
    private String bluePrepayOpenAccountStatus;

    public InstallationDetails getInstallationDetails() {
        if (isBlank(installLocation) && installDate == null) {
            return null;
        }
        InstallationDetails installationDetails = new InstallationDetails();
        installationDetails.setInstallLocation(installLocation);
        if (installDate != null) {
            installationDetails.setInstallDate(LocalDateTimeUtils.toLocalDate(installDate));
        }
        return installationDetails;
    }
}
