package com.acpiot.cps.sys.service;

import com.acpiot.cps.common.mongodb.filter.MongoPagingQueryParams;
import com.acpiot.cps.data.sys.entity.mongo.WxMsgRecord;
import org.springframework.data.domain.Page;

/**
 * 微信消息推送记录(WxMsgRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2022-08-30 14:14:54
 */
public interface WxMsgRecordService {

    /**
     * 分页查询微信消息推送记录
     *
     * @param params
     * @return
     */
    Page<WxMsgRecord> pageWxPushMsg(MongoPagingQueryParams params);
}