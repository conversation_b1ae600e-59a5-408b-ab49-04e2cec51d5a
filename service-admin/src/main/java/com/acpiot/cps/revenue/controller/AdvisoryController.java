package com.acpiot.cps.revenue.controller;

import com.acpiot.cps.data.archive.entity.Customer;
import com.acpiot.cps.data.revenue.entity.mongo.Advisory;
import com.acpiot.cps.revenue.service.AdvisoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.util.List;

/**
 * 微信用户咨询控制层
 *
 * <AUTHOR>
 * @since 2022-08-30 14:14:21
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/auth/revenue/advisory")
public class AdvisoryController {

    private final AdvisoryService advisoryService;

    @PostMapping("page")
    @PreAuthorize("hasAuthority('revenue-weixin-weixinConsult')")
    public Page<Customer> page(@RequestBody PagingQueryParams<Customer> params) {
        return advisoryService.page(params);
    }

    @PostMapping("reply")
    @PreAuthorize("hasAuthority('revenue-weixin-weixinConsult')")
    public void reply(@RequestParam("roomId") Long roomId, @RequestParam("content") String content) {
        advisoryService.reply(roomId, content);
    }

    @GetMapping("getAdvisoryByCustomerId")
    public List<Advisory> getAdvisoryByCustomerId(@RequestParam("customerId") Long customerId) {
        return advisoryService.getAdvisoryByCustomerId(customerId);
    }
}