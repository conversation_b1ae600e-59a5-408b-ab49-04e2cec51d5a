package com.acpiot.cps.revenue.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.acpiot.cps.archive.service.BuildingUnitService;
import com.acpiot.cps.common.dto.FileNameDto;
import com.acpiot.cps.common.exception.BusinessException;
import com.acpiot.cps.common.logging.SystemLog;
import com.acpiot.cps.common.model.CmdRspModel;
import com.acpiot.cps.common.model.Rest;
import com.acpiot.cps.constant.CommonConstant;
import com.acpiot.cps.data.revenue.entity.Recharge;
import com.acpiot.cps.data.revenue.view.RechargeView;
import com.acpiot.cps.event.EventUtils;
import com.acpiot.cps.event.revenue.RechargeStatusSyncEvent;
import com.acpiot.cps.revenue.dto.ExcelRechargeDto;
import com.acpiot.cps.revenue.dto.TotalAmountDto;
import com.acpiot.cps.revenue.service.RechargeService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import pers.mx.jupiter.jpa.PagingQueryParams;
import pers.mx.jupiter.web.util.ResponseUtils;

import java.io.File;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

/**
 * 充值记录控制层
 *
 * <AUTHOR>
 * @since 2022-08-30 14:14:45
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/auth/revenue/recharge")
public class RechargeRecordController {

    private final RechargeService rechargeService;
    private final BuildingUnitService buildingUnitService;
    @Value("${file.download-dir.excel-base-path}")
    private String excelBasePath;

    @PostMapping("page")
    @PreAuthorize("hasAuthority('revenue-recharge-page')")
    public Page<RechargeView> pageRecharge(@RequestBody PagingQueryParams<RechargeView> params) {
        Page<RechargeView> rechargeViews = rechargeService.pageRechargeView(params);
        rechargeService.setRechargeRecharging(rechargeViews);
        rechargeService.setRechargeRefunding(rechargeViews);
        buildingUnitService.buildBuildingUnitName(rechargeViews.getContent());
        return rechargeViews;
    }

    @GetMapping("getRechargeById")
    @PreAuthorize("hasAuthority('revenue-recharge-page')")
    public Recharge getRechargeById(@RequestParam Long rechargeId) {
        return rechargeService.getRechargeById(rechargeId);
    }

    @GetMapping("getRechargeViewById")
    @PreAuthorize("hasAuthority('revenue-recharge-page')")
    public RechargeView getRechargeViewById(@RequestParam Long rechargeId) {
        RechargeView rechargeView = rechargeService.getRechargeViewById(rechargeId);
        buildingUnitService.buildBuildingUnitName(List.of(rechargeView));
        return rechargeView;
    }

    @PostMapping("getTotalAmount")
    @PreAuthorize("hasAuthority('revenue-recharge-page')")
    public TotalAmountDto getTotalAmount(@RequestBody PagingQueryParams<RechargeView> params) {
        return rechargeService.getTotalAmount(params);
    }

    @SystemLog(description = "撤销充值记录")
    @PostMapping("cancelRecharge")
    @PreAuthorize("hasAuthority('revenue-recharge-cancel')")
    public void cancelRecharge(@RequestParam Long rechargeId) {
        rechargeService.cancelPayIncome(rechargeId);
    }

    @SystemLog(description = "缴费退款")
    @PostMapping("refundRecharge")
    public ResponseEntity<?> refundRecharge(@RequestParam Long rechargeId, @RequestParam BigDecimal refundAmount) {
        rechargeService.refundRecharge(rechargeId, refundAmount);
        return ResponseUtils.ok("退款发起成功，等待退款结果...");
    }

    @SystemLog(description = "蓝牙预付费现金退款")
    @PostMapping("bluePrepayRefund")
    public Rest<CmdRspModel> bluePrepayRefund(@RequestParam Long rechargeId, @RequestParam BigDecimal refundAmount) {
        return rechargeService.bluePrepayRefund(rechargeId, refundAmount);
    }

    @PutMapping("syncRechargePayStatusByRechargeId")
    public ResponseEntity<?> syncRechargePayStatusByRechargeId(@RequestParam Long rechargeId) {
        rechargeService.syncRechargePayStatusByRechargeId(rechargeId);
        return ResponseUtils.ok("操作成功，请刷新同步结果");
    }

    @PostMapping("export")
    @PreAuthorize("hasAuthority('revenue-recharge-export')")
    public FileNameDto exportData(@RequestBody PagingQueryParams<RechargeView> queryParams) {
        queryParams.setLimit(CommonConstant.EXPORT_LIMIT);
        List<ExcelRechargeDto> exportRecharges = rechargeService.getExportRecharges(queryParams);
        if (CollectionUtil.isEmpty(exportRecharges)) {
            throw new BusinessException("导出的数据为空！");
        }
        // 设置文件导出的路径
        File folder = new File(excelBasePath);
        if (!folder.isDirectory()) {
            folder.mkdirs();
        }
        // 指定文件保存路径和文件名
        String suffix = LocalDateTimeUtil.format(LocalDateTime.now(),
                DateTimeFormatter.ofPattern("yyyyMMddhhmmss")) + UUID.randomUUID().toString().substring(0, 4) + ".xlsx";
        String fileName = "充值记录导出-" + suffix;
        String filePath = excelBasePath + fileName;

        // 创建ExcelWriter对象
        ExcelWriter excelWriter = EasyExcel.write(filePath, ExcelRechargeDto.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
        // 创建写入工作表对象
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet1").build();
        // 向工作表写入数据
        excelWriter.write(exportRecharges, writeSheet);
        // 关闭ExcelWriter对象
        excelWriter.finish();
        return new FileNameDto(URLEncoder.encode("充值记录导出-", StandardCharsets.UTF_8) + suffix);
    }
}