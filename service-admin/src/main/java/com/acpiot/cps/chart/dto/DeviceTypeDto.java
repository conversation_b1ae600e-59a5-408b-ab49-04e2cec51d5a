package com.acpiot.cps.chart.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 设备类型统计
 * Created by YoungLu on 2023/12/27
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceTypeDto {

    /**
     * 小区ID
     */
    private Long communityId;

    /**
     * 小区名称
     */
    private String communityName;

    /**
     * 水表总数
     */
    private Long waterMeterTotal = 0L;

    /**
     * Nb水表数量
     */
    private Long nbWaterMeterTotal = 0L;

    /**
     * 集中器水表数量
     */
    private Long conWaterMeterTotal = 0L;

    /**
     * UDP水表数量
     */
    private Long cat1WaterMeterTotal = 0L;

    /**
     * 有阀水表数量
     */
    private Long hasValveWaterMeterTotal = 0L;

    /**
     * 无阀水表数量
     */
    private Long noValveWaterMeterTotal = 0L;

    /**
     * 电表总数
     */
    private Long electricMeterTotal = 0L;

    /**
     * 集中器电表数量
     */
    private Long conElectricMeterTotal = 0L;

    /**
     * 4G电表数量
     */
    private Long cat1ElectricMeterTotal = 0L;

    public DeviceTypeDto(Long communityId, String communityName) {
        this.communityId = communityId;
        this.communityName = communityName;
    }
}
