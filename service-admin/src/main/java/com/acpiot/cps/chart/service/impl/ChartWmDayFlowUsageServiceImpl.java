package com.acpiot.cps.chart.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.acpiot.cps.chart.dto.WmDayFlowUsageDto;
import com.acpiot.cps.chart.service.AbstractChartService;
import com.acpiot.cps.chart.service.ChartWmDayFlowUsageService;
import com.acpiot.cps.common.dto.CommonKeyValue;
import com.acpiot.cps.common.dto.CommonKeyValueAndMapAndRoomAndCustomer;
import com.acpiot.cps.common.exception.BusinessException;
import com.acpiot.cps.data.archive.entity.QWmWaterMeter;
import com.acpiot.cps.data.archive.entity.WmWaterMeter;
import com.acpiot.cps.data.archive.repository.WmWaterMeterRepository;
import com.acpiot.cps.data.monitor.entity.QWmDayFreezeData;
import com.acpiot.cps.data.monitor.entity.WmDayFreezeData;
import com.google.common.collect.Lists;
import com.querydsl.core.QueryResults;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.StringTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.Filter;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class ChartWmDayFlowUsageServiceImpl extends AbstractChartService implements ChartWmDayFlowUsageService {

    private final JPAQueryFactory jpaQueryFactory;
    private final WmWaterMeterRepository wmWaterMeterRepository;

    @Override
    public WmDayFlowUsageDto getWmDayFlowUsageByDay(Long waterMeterId, LocalDate from, LocalDate to) {
        checkMasterWaterMeter(waterMeterId);
        datePeriodCheck(from, to);
        // 生成完整日期列表
        List<LocalDate> dateRange = generateDateRange(from, to);

        // 查询总表数据并构建Map
        QWmDayFreezeData wmDayFreezeData = QWmDayFreezeData.wmDayFreezeData;
        Map<LocalDate, BigDecimal> totalMap = new HashMap<>();
        JPAQuery<WmDayFreezeData> totalQuery = jpaQueryFactory.selectFrom(wmDayFreezeData)
                .where(wmDayFreezeData.waterMeter.id.eq(waterMeterId), wmDayFreezeData.freezeDate.between(from, to));
        totalQuery.fetch().forEach(data ->
                totalMap.put(data.getFreezeDate(), data.getDayFlow()));

        // 构建总表列表
        List<CommonKeyValue> parentList = dateRange.stream()
                .map(date -> new CommonKeyValue(
                        LocalDateTimeUtil.format(date, "yyyy-MM-dd"),
                        totalMap.getOrDefault(date, BigDecimal.ZERO)
                ))
                .collect(Collectors.toList());

        // 查询子表数据
        QWmWaterMeter subWaterMeter = QWmWaterMeter.wmWaterMeter;
        List<Long> subTableIds = jpaQueryFactory
                .select(subWaterMeter.id)
                .from(subWaterMeter)
                .where(subWaterMeter.parent.id.eq(waterMeterId))
                .fetch();

        Map<LocalDate, BigDecimal> childMap = new HashMap<>();
        if (CollUtil.isNotEmpty(subTableIds)) {
            JPAQuery<Tuple> subDataQuery = jpaQueryFactory
                    .select(wmDayFreezeData.freezeDate, wmDayFreezeData.dayFlow.sum())
                    .from(wmDayFreezeData)
                    .where(wmDayFreezeData.waterMeter.id.in(subTableIds), wmDayFreezeData.freezeDate.between(from, to))
                    .groupBy(wmDayFreezeData.freezeDate);
            subDataQuery.fetch().forEach(tuple ->
                    childMap.put(tuple.get(wmDayFreezeData.freezeDate), tuple.get(wmDayFreezeData.dayFlow.sum())));
        }

        // 构建子表列表
        List<CommonKeyValue> childList = dateRange.stream()
                .map(date -> new CommonKeyValue(
                        LocalDateTimeUtil.format(date, "yyyy-MM-dd"),
                        childMap.getOrDefault(date, BigDecimal.ZERO)
                ))
                .collect(Collectors.toList());

        return new WmDayFlowUsageDto(parentList, childList);
    }

    @Override
    public WmDayFlowUsageDto getWmDayFlowUsageByMonth(Long waterMeterId, int year) {
        checkMasterWaterMeter(waterMeterId);
        // 生成完整月份列表
        List<LocalDate> monthRange = generateMonthRange(year);

        // 处理总表数据
        QWmDayFreezeData wmDayFreezeData = QWmDayFreezeData.wmDayFreezeData;
        StringTemplate monthExpr = Expressions.stringTemplate("DATE_FORMAT({0},'%Y-%m')", wmDayFreezeData.freezeDate);
        Map<String, BigDecimal> totalMonthMap = new HashMap<>();
        JPAQuery<Tuple> totalQuery = jpaQueryFactory
                .select(monthExpr, wmDayFreezeData.dayFlow.sum())
                .from(wmDayFreezeData)
                .where(wmDayFreezeData.waterMeter.id.eq(waterMeterId), wmDayFreezeData.freezeDate.year().eq(year))
                .groupBy(monthExpr);
        totalQuery.fetch().forEach(tuple ->
                totalMonthMap.put(tuple.get(monthExpr), tuple.get(wmDayFreezeData.dayFlow.sum())));

        // 构建总表列表
        List<CommonKeyValue> parentList = monthRange.stream()
                .map(date -> new CommonKeyValue(
                        LocalDateTimeUtil.format(date, "yyyy-MM"),
                        totalMonthMap.getOrDefault(
                                LocalDateTimeUtil.format(date, "yyyy-MM"),
                                BigDecimal.ZERO
                        )
                ))
                .collect(Collectors.toList());

        // 处理子表数据
        QWmWaterMeter subWaterMeter = QWmWaterMeter.wmWaterMeter;
        List<Long> subTableIds = jpaQueryFactory
                .select(subWaterMeter.id)
                .from(subWaterMeter)
                .where(subWaterMeter.parent.id.eq(waterMeterId))
                .fetch();

        Map<String, BigDecimal> childMonthMap = new HashMap<>();
        if (!subTableIds.isEmpty()) {
            JPAQuery<Tuple> subDataQuery = jpaQueryFactory
                    .select(monthExpr, wmDayFreezeData.dayFlow.sum())
                    .from(wmDayFreezeData)
                    .where(wmDayFreezeData.waterMeter.id.in(subTableIds), wmDayFreezeData.freezeDate.year().eq(year))
                    .groupBy(monthExpr);
            subDataQuery.fetch().forEach(tuple ->
                    childMonthMap.put(tuple.get(monthExpr), tuple.get(wmDayFreezeData.dayFlow.sum())));
        }

        // 构建子表列表
        List<CommonKeyValue> childList = monthRange.stream()
                .map(date -> new CommonKeyValue(
                        LocalDateTimeUtil.format(date, "yyyy-MM"),
                        childMonthMap.getOrDefault(
                                LocalDateTimeUtil.format(date, "yyyy-MM"),
                                BigDecimal.ZERO
                        )
                ))
                .collect(Collectors.toList());

        return new WmDayFlowUsageDto(parentList, childList);
    }

    @Override
    public Page<CommonKeyValueAndMapAndRoomAndCustomer> pageWmChildDayFlowByMonth(PagingQueryParams<CommonKeyValueAndMapAndRoomAndCustomer> params) {
        int year = 0;
        Long waterMeterId = null;
        for (Filter filter : params.getAndFilters()) {
            if (filter.getValue() == null || StringUtils.isBlank(filter.getValue().toString())) {
                continue;
            }
            switch (filter.getProperty()) {
                case "year":
                    year = Integer.parseInt(filter.getValue().toString());
                    break;
                case "waterMeterId":
                    waterMeterId = Long.parseLong(filter.getValue().toString());
                    break;
            }
        }
        // 必须提供查询的年份和总表ID
        if (year == 0) {
            throw new BusinessException("请选择要查询的年份");
        }
        if (waterMeterId == null) {
            throw new BusinessException("请选择要查询的总表");
        }
        // 分页查询子表
        QueryResults<WmWaterMeter> wmWaterMeterQueryResults = getQueryResults(params);
        // 获取分页参数
        List<WmWaterMeter> subMeters = wmWaterMeterQueryResults.getResults();
        if (CollUtil.isEmpty(subMeters)) {
            return new PageImpl<>(Lists.newArrayList(), params.getPageRequest(), wmWaterMeterQueryResults.getTotal());
        }

        // 提取子表ID集合
        List<Long> subMeterIds = subMeters.stream().map(WmWaterMeter::getId).collect(Collectors.toList());
        // 查询冻结数据
        QWmDayFreezeData df = QWmDayFreezeData.wmDayFreezeData;
        List<Tuple> monthlyData = jpaQueryFactory.select(
                        df.waterMeter.id,
                        Expressions.numberTemplate(Integer.class, "MONTH({0})", df.freezeDate),
                        df.dayFlow.sum().coalesce(BigDecimal.ZERO)
                )
                .from(df)
                .where(df.waterMeter.id.in(subMeterIds)
                        .and(Expressions.numberTemplate(Integer.class, "YEAR({0})", df.freezeDate).eq(year)))
                .groupBy(df.waterMeter.id, Expressions.numberTemplate(Integer.class, "MONTH({0})", df.freezeDate))
                .fetch();

        // 构建流量映射表
        Map<Long, Map<Integer, BigDecimal>> consumptionMap = new HashMap<>();
        for (Tuple tuple : monthlyData) {
            Long meterId = tuple.get(df.waterMeter.id);
            Integer month = tuple.get(1, Integer.class);
            BigDecimal dayFlowSum = tuple.get(2, BigDecimal.class);
            consumptionMap.computeIfAbsent(meterId, k -> new HashMap<>(12))
                    .put(month, dayFlowSum);
        }

        // 构建返回结果
        List<CommonKeyValueAndMapAndRoomAndCustomer> content = new ArrayList<>();
        for (WmWaterMeter meter : subMeters) {
            CommonKeyValueAndMapAndRoomAndCustomer dto = new CommonKeyValueAndMapAndRoomAndCustomer();
            dto.setKey(meter.getCode());
            if (meter.getRoom() != null) {
                dto.setRoomNumber(meter.getRoom().getRoomNumber());
                if (meter.getRoom().getCustomer() != null) {
                    dto.setCustomerName(meter.getRoom().getCustomer().getName());
                }
            }

            Map<String, Object> monthMap = new LinkedHashMap<>();
            BigDecimal total = BigDecimal.ZERO;
            for (int month = 1; month <= 12; month++) {
                BigDecimal dayFlowSum = consumptionMap.getOrDefault(meter.getId(), Collections.emptyMap())
                        .getOrDefault(month, BigDecimal.ZERO);
                monthMap.put(String.format("%s-%02d", year, month), dayFlowSum.setScale(2, RoundingMode.HALF_UP));
                total = total.add(dayFlowSum);
            }
            dto.setData(monthMap);
            dto.setValue(total.setScale(2, RoundingMode.HALF_UP));
            content.add(dto);
        }

        return new PageImpl<>(content, params.getPageRequest(), wmWaterMeterQueryResults.getTotal());
    }

    @Override
    public Page<CommonKeyValueAndMapAndRoomAndCustomer> pageWmChildDayFlowByDay(PagingQueryParams<CommonKeyValueAndMapAndRoomAndCustomer> params) {
        // 从请求中获取查询参数
        LocalDate from = null;
        LocalDate to = null;
        Long waterMeterId = null;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (Filter filter : params.getAndFilters()) {
            if (filter.getValue() == null || StringUtils.isBlank(filter.getValue().toString())) {
                continue;
            }
            switch (filter.getProperty()) {
                case "waterMeterId":
                    waterMeterId = Long.parseLong(filter.getValue().toString());
                    break;
                case "from":
                    from = LocalDate.parse(filter.getValue().toString(), formatter);
                    break;
                case "to":
                    to = LocalDate.parse(filter.getValue().toString(), formatter);
                    break;
            }
        }
        // 判断是否提供日期区间参数
        if (from == null || to == null) {
            throw new BusinessException("请提供日期区间查询条件");
        }
        if (waterMeterId == null) {
            throw new BusinessException("请选择要查询的总表");
        }
        datePeriodCheck(from, to);
        // 分页查询子表
        QueryResults<WmWaterMeter> wmWaterMeterQueryResults = getQueryResults(params);
        List<WmWaterMeter> subMeters = wmWaterMeterQueryResults.getResults();
        if (CollUtil.isEmpty(subMeters)) {
            return new PageImpl<>(Collections.emptyList(), params.getPageRequest(), wmWaterMeterQueryResults.getTotal());
        }

        // 提取子表ID集合
        List<Long> subMeterIds = subMeters.stream().map(WmWaterMeter::getId).collect(Collectors.toList());
        // 查询日冻结数据（按天分组统计）
        QWmDayFreezeData df = QWmDayFreezeData.wmDayFreezeData;
        List<Tuple> dailyData = jpaQueryFactory.select(
                        df.waterMeter.id,
                        df.freezeDate,
                        df.dayFlow.coalesce(BigDecimal.ZERO)
                )
                .from(df)
                .where(df.waterMeter.id.in(subMeterIds)
                        .and(df.freezeDate.between(from, to)))
                .groupBy(df.waterMeter.id, df.freezeDate)
                .fetch();

        // 构建流量映射表：meterId -> { date -> amount }
        Map<Long, Map<LocalDate, BigDecimal>> consumptionMap = new HashMap<>();
        for (Tuple tuple : dailyData) {
            Long meterId = tuple.get(df.waterMeter.id);
            LocalDate date = tuple.get(df.freezeDate);
            BigDecimal dayFlow = tuple.get(2, BigDecimal.class);
            consumptionMap.computeIfAbsent(meterId, k -> new HashMap<>())
                    .put(date, dayFlow);
        }

        // 生成日期范围内的所有日期（确保连续）
        List<LocalDate> allDates = generateDateRange(from, to);

        // 构建返回结果
        List<CommonKeyValueAndMapAndRoomAndCustomer> content = new ArrayList<>();
        for (WmWaterMeter meter : subMeters) {
            CommonKeyValueAndMapAndRoomAndCustomer dto = new CommonKeyValueAndMapAndRoomAndCustomer();
            dto.setKey(meter.getCode());
            if (meter.getRoom() != null) {
                dto.setRoomNumber(meter.getRoom().getRoomNumber());
                if (meter.getRoom().getCustomer() != null) {
                    dto.setCustomerName(meter.getRoom().getCustomer().getName());
                }
            }

            Map<String, Object> dayMap = new LinkedHashMap<>();
            BigDecimal total = BigDecimal.ZERO;
            for (LocalDate date : allDates) {
                // 获取该日期消费量（无数据则为0）
                BigDecimal dayFlow = consumptionMap.getOrDefault(meter.getId(), Collections.emptyMap())
                        .getOrDefault(date, BigDecimal.ZERO);
                dayFlow = dayFlow.setScale(2, RoundingMode.HALF_UP);
                // 格式化日期为yyyy-MM-dd作为键
                dayMap.put(date.format(formatter), dayFlow);
                total = total.add(dayFlow);
            }
            dto.setData(dayMap);
            dto.setValue(total.setScale(2, RoundingMode.HALF_UP));
            content.add(dto);
        }

        return new PageImpl<>(content, params.getPageRequest(), wmWaterMeterQueryResults.getTotal());
    }

    /**
     * 生成指定日期范围的日期列表
     *
     * @param start
     * @param end
     * @return
     */
    private List<LocalDate> generateDateRange(LocalDate start, LocalDate end) {
        List<LocalDate> dates = new ArrayList<>();
        LocalDate currentDate = start;
        while (!currentDate.isAfter(end)) {
            dates.add(currentDate);
            currentDate = currentDate.plusDays(1);
        }
        return dates;
    }

    /**
     * 生成指定年份的月份列表
     *
     * @param year
     * @return
     */
    private List<LocalDate> generateMonthRange(int year) {
        List<LocalDate> months = new ArrayList<>();
        LocalDate currentDate = LocalDate.of(year, 1, 1);
        while (currentDate.getYear() == year) {
            months.add(currentDate);
            currentDate = currentDate.plusMonths(1);
        }
        return months;
    }

    /**
     * 检验该水表是否是父表
     *
     * @param meterId
     */
    private void checkMasterWaterMeter(long meterId) {
        if (!wmWaterMeterRepository.findById(meterId).orElseThrow(() -> new BusinessException("该水表不存在")).isMaster()) {
            throw new BusinessException("该水表不是父表，无法执行总子表流量统计");
        }
    }

    private QueryResults<WmWaterMeter> getQueryResults(PagingQueryParams<CommonKeyValueAndMapAndRoomAndCustomer> params) {
        Filter waterMeterIdFilter = params.getAndFilters().stream().filter(filter -> filter.getProperty().equals("waterMeterId")).findFirst().orElseThrow(() -> new BusinessException("必须传入总表ID条件"));
        long waterMeterId = Long.parseLong(waterMeterIdFilter.getValue().toString());
        QWmWaterMeter wm = QWmWaterMeter.wmWaterMeter;
        return jpaQueryFactory.selectFrom(wm).leftJoin(wm.room).fetchJoin().leftJoin(wm.room.customer).fetchJoin()
                .where(wm.parent.id.eq(waterMeterId))
                .limit(params.getLimit())
                .offset(params.getOffset())
                .orderBy(wm.id.asc()).fetchResults();
    }
}
