package com.acpiot.cps.cat1.electric.owk.msg;

import com.acpiot.cps.common.enums.DeviceType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 推送消息
 * Created by moxin on 2020-10-21-0021
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OwkEmPushMessage {

    /**
     * 设备类型
     */
    private DeviceType deviceType;

    /**
     * 设备uid
     */
    private String uid;

    /**
     * 推送消息时间戳
     */
    private long timestamp;

    public static OwkEmPushMessage ofElectricMeterMsg(String uid) {
        OwkEmPushMessage msg = new OwkEmPushMessage();
        msg.deviceType = DeviceType.ELECTRIC_METER;
        msg.uid = uid;
        msg.timestamp = System.currentTimeMillis();
        return msg;
    }
}
