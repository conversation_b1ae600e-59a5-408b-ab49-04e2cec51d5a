package com.acpiot.cps.cat1.water;

import com.acpiot.cps.cat1.water.dto.command.*;
import com.acpiot.cps.common.dto.water.*;
import com.acpiot.cps.common.model.Rest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * UDP直连水表操作命令接口
 */
@FeignClient(name = "cps-device", contextId = "cps-device-cat1WaterMeterCmd", path = "/cps-device/cat1WaterMeterCmd")
public interface Cat1WaterMeterCmd {

    /**
     * 读 Cat.1 参数信息
     *
     * @param reqDto
     * @return
     */
    @PostMapping("readParam")
    Rest<Cat1WaterCmdRespDto> readParam(@RequestBody Cat1WaterCmdReqDto<Void> reqDto);

    /**
     * 读MCU参数信息
     *
     * @param reqDto
     * @return
     */
    @PostMapping("readMcuParam")
    Rest<Cat1WaterCmdRespDto> readMcuParam(@RequestBody Cat1WaterCmdReqDto<Void> reqDto);

    /**
     * 抄表
     *
     * @param reqDto
     * @return
     */
    @PostMapping("meteringData")
    Rest<Cat1WaterCmdRespDto> meteringData(@RequestBody Cat1WaterCmdReqDto<Void> reqDto);

    /**
     * 业务阀门控制
     *
     * @param reqDto
     * @return
     */
    @PostMapping("serviceValve")
    Rest<Cat1WaterCmdRespDto> serviceValve(@RequestBody Cat1WaterCmdReqDto<C05Param> reqDto);

    /**
     * 强制阀门控制
     *
     * @param reqDto
     * @return
     */
    @PostMapping("forceValve")
    Rest<Cat1WaterCmdRespDto> forceValve(@RequestBody Cat1WaterCmdReqDto<C06Param> reqDto);

    /**
     * 读曲线数据
     *
     * @param reqDto
     * @return
     */
    @PostMapping("readCurve")
    Rest<Cat1WaterCmdRespDto> readCurve(@RequestBody Cat1WaterCmdReqDto<C09Param> reqDto);

    /**
     * 设置上报参数
     *
     * @param reqDto
     * @return
     */
    @PostMapping("setReportParams")
    Rest<Cat1WaterCmdRespDto> setReportParams(@RequestBody Cat1WaterCmdReqDto<C10Param> reqDto);

    /**
     * 设置IP信息
     *
     * @param reqDto
     * @return
     */
    @PostMapping("setIp")
    Rest<Cat1WaterCmdRespDto> setIp(@RequestBody Cat1WaterCmdReqDto<C18Param> reqDto);

    /**
     * 设表底数
     *
     * @param reqDto
     * @return
     */
    @PostMapping("setBaseValue")
    Rest<Cat1WaterCmdRespDto> setBaseValue(@RequestBody Cat1WaterCmdReqDto<C12Param> reqDto);

    /**
     * 设每日流量阈值
     *
     * @param reqDto
     * @return
     */
    @PostMapping("setDailyFlowThreshold")
    Rest<Cat1WaterCmdRespDto> setDailyFlowThreshold(@RequestBody Cat1WaterCmdReqDto<C15Param> reqDto);

    /**
     * 设置上报模式
     *
     * @param commandReqDto
     * @return
     */
    @PostMapping("setReportMode")
    Rest<Cat1WaterCmdRespDto> setReportMode(@RequestBody Cat1WaterCmdReqDto<C19Param> commandReqDto);

    /**
     * 设置关阀后密集上报参数
     *
     * @param commandReqDto
     * @return
     */
    @PostMapping("setValveClosedReportParam")
    Rest<Cat1WaterCmdRespDto> setValveClosedReportParam(@RequestBody Cat1WaterCmdReqDto<C1AParam> commandReqDto);

    /**
     * 设置基表参数
     *
     * @param commandReqDto
     * @return
     */
    @PostMapping("setBaseParam")
    Rest<Cat1WaterCmdRespDto> setBaseParam(@RequestBody Cat1WaterCmdReqDto<C1BParam> commandReqDto);

    /**
     * 读预付费参数
     *
     * @param commandReqDto
     * @return
     */
    @PostMapping("readPrepaidParam")
    Rest<Cat1WaterCmdRespDto> readPrepaidParam(@RequestBody Cat1WaterCmdReqDto<Void> commandReqDto);

    /**
     * 读预付费数据
     *
     * @param commandReqDto
     * @return
     */
    @PostMapping("readPrepaidData")
    Rest<Cat1WaterCmdRespDto> readPrepaidData(@RequestBody Cat1WaterCmdReqDto<Void> commandReqDto);

    /**
     * 设置预付费参数
     *
     * @param commandReqDto
     * @return
     */
    @PostMapping("setPrepaidParam")
    Rest<Cat1WaterCmdRespDto> setPrepaidParam(@RequestBody Cat1WaterCmdReqDto<C62Param> commandReqDto);

    /**
     * 预付费设置价格
     *
     * @param commandReqDto
     * @return
     */
    @PostMapping("setPrepaidPrice")
    Rest<Cat1WaterCmdRespDto> setPrepaidPrice(@RequestBody Cat1WaterCmdReqDto<C63Param> commandReqDto);

    /**
     * 预付费开户
     *
     * @param commandReqDto
     * @return
     */
    @PostMapping("openPrepaid")
    Rest<Cat1WaterCmdRespDto> openPrepaid(@RequestBody Cat1WaterCmdReqDto<C64Param> commandReqDto);

    /**
     * 一键开户
     *
     * @param commandReqDto
     * @return
     */
    @PostMapping("oneKeyOpenPrepaid")
    Rest<Cat1WaterCmdRespDto> oneKeyOpenPrepaid(@RequestBody Cat1WaterCmdReqDto<C69Param> commandReqDto);

    /**
     * 预付费充值
     *
     * @param commandReqDto
     * @return
     */
    @PostMapping("rechargePrepaid")
    Rest<Cat1WaterCmdRespDto> rechargePrepaid(@RequestBody Cat1WaterCmdReqDto<C65Param> commandReqDto);

    /**
     * 预付费退款
     *
     * @param commandReqDto
     * @return
     */
    @PostMapping("refundPrepaid")
    Rest<Cat1WaterCmdRespDto> refundPrepaid(@RequestBody Cat1WaterCmdReqDto<C66Param> commandReqDto);

    /**
     * 重置预付费
     *
     * @param commandReqDto
     * @return
     */
    @PostMapping("resetPrepaid")
    Rest<Cat1WaterCmdRespDto> resetPrepaid(@RequestBody Cat1WaterCmdReqDto<Void> commandReqDto);

    /**
     * 设定制功能配置项
     *
     * @param commandReqDto
     * @return
     */
    @PostMapping("setFuncVal")
    Rest<Cat1WaterCmdRespDto> setFuncVal(@RequestBody Cat1WaterCmdReqDto<C14Param> commandReqDto);

    /**
     * 取消命令
     *
     * @param commandId
     * @return
     */
    @PutMapping("cancelCommand")
    Rest<Void> cancelCommand(@RequestParam String commandId);
}
