const $table = $('#table');
const $month = $('#month');
const $communityId = $('#communityId');
const $unReadCountTable = $('#unReadCountTable');
let unReadCountId = null;
let now = new Date();
let datepicker;

function getDaysByMonth(date) {
    const dataArr = [];
    let stickyNum = 0;
    if ($('#showCompanyId').length > 0) {
        dataArr.push({
            title: '公司名称',
            field: 'companyId',
            class: 'f-col col70 wrap',
            formatter:"tdTipFormatter"
        });
        stickyNum++;
    }
    dataArr.push({
        title: '小区名称',
        field: 'communityName',
        class: 'f-col wrap left-last',
        formatter:"tdTipFormatter"
    });
    $('#table').addClass(`f-table-l-id-${stickyNum}`);

    let nowYear = date.getFullYear();
    let nowMonth = date.getMonth() + 1;
    let day = new Date(nowYear, nowMonth, 0).getDate();
    // 对月份进行处理，1-9月在前面添加一个“0”
    if (nowMonth >= 1 && nowMonth <= 9) {
        nowMonth = "0" + nowMonth;
    }
    for (let k = 1; k <= day; k++) {
        // 对日期进行处理，1-9号在前面添加一个“0”
        if (k >= 0 && k <= 9) {
            k = "0" + k;
        }
        dataArr.push({
            title: nowYear + '-' + nowMonth + '-' + k,
            field: 'data.' + nowYear + '-' + nowMonth + '-' + k,
            formatter: 'rateFormatter'
        });
    }
    return dataArr;
}

/**
 * 导出集中器水表档案
 */
function exportExcel() {
    if (!unReadCountId) {
        $.notifyInfo('当前无数据可导出');
        return;
    }
    $('#table').reloadTable();
    $('#rateId').val(unReadCountId);
    $('#export-data-form').submit();
}

/**
 * 每日成功率详情
 */
function rateFormatter(value, row, index, field) {
    let today = new Date().toISOString().split('T')[0] === field.split('.')[1];
    if (!value || !value.rateId || !value.totalCount) {
        if (today ) {
            return `<button class="btn btn-xs btn-default text-success" onclick="refreshSuccessRate(${row.communityId})"><i class="mdi mdi-autorenew"></i></button>`
        }
        return '';
    }
    let unReadCountStr;
    if (value.unReadCount > 0) {
        let date =  field.split('.')[1];
        unReadCountStr = `<button class="btn btn-xs btn-default text-danger read-count" onClick="getUnReadCount(${value.rateId},'${date}',${row.communityId})" title="抄收失败水表">${value.unReadCount}</button>`;
    } else {
        unReadCountStr = `<span class="text-gray">0</span>`;
    }
    let rateClass = !value.successRate ? '' : value.successRate === '1.0' ? 'text-success' : value.successRate === '0.0' ? 'text-danger' : 'text-warning';

    let detail = `<div class="nowrap">${value.totalCount} / ${unReadCountStr} / <span class="${value.readCount > 0 ? '' : 'text-gray'}">${value.readCount}</span> / <span class="${rateClass}">${value.successRate ? successRateFormatter(value.successRate) : '-'}</span>`;

    // 时间为当天，允许刷新抄表成功率
    if (today) {
        detail += `<span><button class="btn btn-xs btn-default text-success" onclick="refreshSuccessRate(${row.communityId})"><i class="mdi mdi-autorenew"></i></button></span>`
    }
    detail += `</div>`
    return detail;
}



/**
 * 刷新指定小区当日抄表成功率
 * @param communityId
 */
function refreshSuccessRate(communityId) {
    $.warnConfirm('确认刷新今日抄表成功率吗？', function () {
        $.ajax({
            type: 'post',
            url: 'calcCommunityReadingSuccessRateToday',
            data: {
                communityId: communityId
            },
            success: function (data) {
                $.notifySuccess(data.msg);
                $table.refreshTable();
            },
            beforeSend: function (xhr) {
                $.showLoading();
            },
            complete: function () {
                $.hideLoading();
            }
        });
    });
}

function getUnReadCount(rateId,date,communityId) {
    window.open(`fail-list?rateId=${rateId}&communityId=${communityId}&date=${date}&failType=community`,'_self');
}

function successRateFormatter(value, row, index) {
    return (Number(value) * 100).toFixed(2) + '%';
}

$(function () {
    $('.select2').select2({
        language: 'zh-CN'
    });

    datepicker = $('.datepicker').datepicker({
        language: "zh-CN", //语言
        format: 'yyyy-mm',
        endDate: new Date(),
        startView: 'months', //开始视图层，为月视图层
        maxViewMode: 'years', //最大视图层，为年视图层
        minViewMode: 'months', //最小视图层，为月视图层
        autoclose: true
    });
    datepicker.datepicker("setDate", 'now');

    $('#btn-query').click(function () {
        if (!$month.val()) {
            $.notifyInfo('请选择查询月份');
            return;
        }
        now = new Date($month.val());
        getCommunityRateByMonth();
    });

    $('#btn-cancel').click(function () {
        $('#communityId').val(null).trigger('change');
        now = new Date();
        datepicker.datepicker("setDate", 'now');
        getCommunityRateByMonth();
    });

    // 进页面默认查询当前月份的成功率
    getCommunityRateByMonth();

    $unReadCountTable.bootstrapTable({
        queryParams: function (params) {
            return {
                'rateId': unReadCountId
            };
        },
    });
});

function getCommunityRateByMonth() {
    initTable();
    $table.refreshTable();
}

function initTable() {
    $table.bootstrapTable('destroy').bootstrapTable({
        iconsPrefix: "mdi",
        icons: "icons",
        deferUrl: "page",
        striped: "true",
        cache: "false",
        method: "post",
        toolbar: "#toolbar",
        showColumns: "true",
        showRefresh: "true",
        pagination: "true",
        dataField: "content",
        sidePagination: "server",
        totalField: "totalElements",
        pageSize: "10",
        queryParams: function (params) {
            params.andFilters = [{
                property: 'communityId',
                operator: 'eq',
                ignoreCase: false,
                value: $communityId.val()
            }, {
                property: 'month',
                operator: 'eq',
                ignoreCase: false,
                value: $month.val()
            }];
            return params;
        },
        columns: getDaysByMonth(now)
    });
}