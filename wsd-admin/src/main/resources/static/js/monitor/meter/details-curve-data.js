var $meterCurveTable = $('#meter-curve-table');
let meterLine = null;
let curveData = [];

function meterCurveOperateFormatter(value, row, index) {
    var btnArray = ['<div class="btn-group">'];
    btnArray.push('<a class="btn btn-xs btn-default view-curve text-primary" href="javascript:void(0);" title="查看曲线"><i class="mdi mdi-chart-line"></i></a>');
    btnArray.push('</div>');
    return btnArray.join('');
}

window.meterCurveOperateEvents = {
    'click .view-curve': function (e, value, row, index) {
        var curveData = $meterCurveTable.getRowByUniqueId(row.id);
        $('#curve-modal').addClass('curve-show');
        // 显示曲线图表
        refreshCurveChart(curveData.id);
    }
};

$(function () {
    //实例化
    meterLine = echarts.init(document.getElementById('curve-line'));
    $(window).resize(function () {
        meterLine.resize({
            animation: {
                duration: 1.5,
                easing: 'ease-in-out'
            }
        });
    });

    $meterCurveTable.bootstrapTable({
        queryParams: function (params) {
            params.meterId = meterId;
            return $('#meter-curve-table-filter').appendFilters(params);
        }
    });
    $('#curve-date').on('change', function () {
        $meterCurveTable.refreshTable();
    });

    $('input[name=curve-type]').on('change', function () {
        drawingBar()
    })
});

/**
 * 补抄曲线数据
 */
function readCurveData() {
    let curveDate = $('#curve-date').val();
    if (!curveDate) {
        $.notifyWarning('请选择要补抄的日期');
        return;
    }
    $.infoConfirm('确认补抄曲线数据吗？', function () {
        $.ajax({
            type: 'post',
            url: 'readCurveData',
            data: {
                waterMeterId: meterId,
                curveDate: curveDate
            },
            success: function (data) {
                $.notifySuccess('已提交补抄请求');
            },
            beforeSend: function (xhr) {
                $.showLoading();
            },
            complete: function () {
                $.hideLoading();
            }
        });
    });
}

/**
 * 刷新曲线数据
 * @param curveDataId
 */
function refreshCurveChart(curveDataId) {
    $.get('details/curvePoints', {
        curveDataId: curveDataId
    }, function (array) {
        curveData = array;
        drawingBar();
    });
}

/**
 * 绘制折线图
 * @param result
 */
function drawingBar() {
    let type = $('input[name=curve-type]:checked').val();
    const option = {
        title: {
            text: '',
            textStyle: {
                fontSize: 25,
                fontWeight: 'normal',
                color: '#fff',
            },
            x: 'center'
        },
        tooltip: {
            show: true,
            trigger: 'axis',
            axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                type: 'line'        // 默认为直线，可选为：'line' | 'shadow'
            }
        },
        dataZoom: {
            type: 'slider',
        },
        grid: {
            top: '50',
            left: '10',
            right: '10',
            bottom: '20',
            containLabel: true,
        },
        legend: {
            top: 0,
            itemHeight: 10,
            data: type === 'value' ? ['正向读数', '反向读数'] : ['用水量'],
            textStyle: {
                fontSize: 12,
            }
        },
        xAxis: {
            axisLine: {
                lineStyle: {
                    color: '#aaa',
                    width: 1,
                }
            },
            axisLabel: {
                color: '#444'
            },
            axisTick: {
                show: true,
                alignWithLabel: true,
            },
            data: []
        },
        yAxis: {
            name: 'm³',
            nameTextStyle: {
                color: '#333',
                verticalAlign: 'bottom'
            },
            axisLine: {
                lineStyle: {
                    color: '#aaa',
                    width: 1,
                }
            },
            axisLabel: {
                color: '#444'
            },
            axisTick: {
                show: true
            },

        },
        series: [],
    };
    let xAxisData = [];
    if(curveData.length === 24){
        xAxisData = curveData.map((item, index) => {
            return `${index}:00`;
        })
    }else{
        let step = curveData.length/24;
        xAxisData = curveData.map((item, index) => {
            if(index<step){
                return '0:' + (index*(60/step) || '00');
            }else{
                return `${Math.floor(index/step)}:` + ((index%step)*(60/step) || '00');
            }
        });
    }
    if(type === 'value' ){
        option.xAxis.data = xAxisData;
    }else{
        option.xAxis.data = xAxisData.slice(1);
    }

    if (type === 'value') {
        option.series = [
            {
                name: '正向读数',
                type: 'line',
                smooth: false, //是否平滑曲线显示
                showSymbol: true,
                symbolSize: 8,
                lineStyle: {
                    normal: {
                        color: "rgba(51,202,187,0.9)", // 线条颜色
                    },
                },
                label: {
                    show: false,
                    position: 'top',
                    distance: 3,
                    color: '#666'
                },
                itemStyle: {
                    normal: {
                        color: "#33CABB",
                    }
                },
                data: curveData.map((item, index) => {
                    return item.forwardValue;
                })
            }, {
                name: '反向读数',
                type: 'line',
                smooth: false, //是否平滑曲线显示
                showSymbol: true,
                symbolSize: 8,
                lineStyle: {
                    normal: {
                        color: "rgba(202,51,177,0.9)", // 线条颜色
                    },
                },
                label: {
                    show: false,
                    position: 'top',
                    distance: 3,
                    color: '#666'
                },
                itemStyle: {
                    normal: {
                        color: "rgba(202,51,177,0.9)",
                    }
                },
                data: curveData.map((item, index) => {
                    return item.backwardValue
                })
            }
        ]
    } else {
        option.series = [
            {
                name: '用水量',
                type: 'line',
                smooth: false, //是否平滑曲线显示
                showSymbol: true,
                symbolSize: 8,
                lineStyle: {
                    normal: {
                        color: "rgba(52,131,250,0.9)", // 线条颜色
                    },
                },
                label: {
                    show: false,
                    position: 'top',
                    distance: 3,
                    color: '#666'
                },
                itemStyle: {
                    normal: {
                        color: "rgba(52,131,250,0.9)",
                    }
                },
                data: curveData.map((item, index) => {
                    if(index>0){
                        return  Decimal(item.forwardValue).sub(curveData[index-1].forwardValue).toNumber();
                    }else{
                        return '-'
                    }

                }).slice(1)
            }
        ]
    }
    meterLine.setOption(option, true);
}

