let $table = $('#table');
let communityId = $('#communityId');
let regionId = $('#regionId');
let params_
$(function () {
    $('.select2').select2({
        language: 'zh-CN'
    });

    $('.search-bar.meter .dropdown-menu li').click(function () {
        let field = $(this).data('field') || '';
        $(`.form-group #${field}`).show().siblings('input').hide().val('');
        $('.search-bar.meter .dropdown-toggle').html($(this).text() + ' <span class="caret"></span>');
    });

    $table.bootstrapTable({
        queryParams: function (params) {
            params = $('#tableFilter').appendFilters(params);
            $('table.sum').hide();
            params_ = JSON.stringify(params);
            $('#export-data-params').val(JSON.stringify(params).replace(/"/g, "'"));
            return params;
        }
    }).on('load-success.bs.table', function (data) {
        $.ajax({
            method: 'post',
            url: 'getBillDetailSummary',
            data: {params:params_},
            success: function (data) {
                if(data){
                    $('#sum1').text(data.receivableConsumption);
                    $('#sum2').text(data.receivedConsumption);
                    $('#sum3').text(data.receivableAmount);
                    $('#sum4').text(data.receivedAmount);
                    $('#sum5').text(data.unreceivedAmount);
                    $('#sum6').text(data.receivableWasteWaterFee);
                    $('#sum7').text(data.receivedWasteWaterFee);
                    $('#sum8').text(data.unreceivedWasteWaterFee);
                    $('table.sum').show();
                }

            }

        })
        if (isExport) {
            isExport = false;
            if (data.length === 0) {
                $.notifyInfo('当前无数据可导出');
                return;
            }
            $('#export-data-form').submit();
        }
    });
});

var isExport = false;

/**
 * 导出账单信息
 */
function exportExcel() {
    isExport = true;
    $table.reloadTable();
}
