let defaultBatchId = $('#defaultBatchId').val();
let defaultCompanyId = $('#defaultCompanyId').val();

$(function () {
  $("#form").validate({
    submitHandler: function (form) {
      $.ajax({
        type: 'post',
        url: $(form).attr('action'),
        data: new FormData($(form)[0]),
        processData: false,
        contentType: false,
        success: function () {
          $.notifyInfo('测试水表导入成功');
          setTimeout(function () {
            location.href = "list";
          }, 1000);
        },
        error: function(data) {
          $.notifyError(data.responseText);
          $("#file").val(null);
        },
        beforeSend: function (xhr) {
          $.showLoading();
        },
        complete: function () {
          $.hideLoading();
        }
      })
    }
  });
  $('.select2').on('change', function () {
    $(this).valid();
  }).select2({
    language: 'zh-CN'
  });


  $('#parent-text').on('click', function() {
    $('.select-community-wrap').show();
  })
  $('.select-community-wrap').on('mouseleave',function() {
    $(this).hide();
  })

  if (defaultCompanyId) {
    $('#companyId').val(defaultCompanyId).trigger('change');
  }
});

function companyChange() {
  $.ajax({
    type: "GET",
    url: `../testBatch/getTestBatchesByCompanyId`,
    data: {
      companyId: $('#companyId').val()
    },
    success: function (data) {
      const $testBatchId = $('#testBatchId');
      $testBatchId.html('');
      var options = $.map(data, function (obj) {
        if (defaultBatchId && defaultBatchId == obj.id) {
          return new Option(obj.batchName, obj.id, true, true);
        }
        return new Option(obj.batchName, obj.id, false, false);
      });
      if (!defaultBatchId) {
        options.splice(0, 0, new Option('请选择', '', true, true));
      }
      defaultBatchId = null;
      $testBatchId.append(options).trigger('change');
    }
  });
}