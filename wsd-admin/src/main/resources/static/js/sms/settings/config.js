var $table = $('#table');
var hasEdit = $('#hasEdit').length > 0,
    hasDel = $('#hasDel').length > 0;
var hasOperate = hasEdit || hasDel;
let operateFormatterStr = null;

function detailFormatter(index, row, element) {
    return $.templates("#detailViewTmpl").render(row);
}

function enabledFormatter(value, row, index) {
    switch (value) {
        case true:
            return '<span class="text-success">开启</span>';
        case false:
            return '<span class="text-danger">关闭</span>';
        default:
            return value;
    }
}

function getOperateFormatterStr() {
    let btnArray = ['<div class="btn-group" style="width:70px">'];
    if (hasEdit) {
        btnArray.push('<a class="btn btn-xs btn-default text-primary edit" href="javascript:void(0);" title="编辑"><i class="mdi mdi-pencil"></i></a>');
    }
    if (hasOperate) {
        btnArray.push('<a class="btn btn-xs btn-default text-danger remove" href="javascript:void(0);" title="删除"><i class="mdi mdi-window-close"></i></a>');
    }
    btnArray.push('</div>');
    operateFormatterStr = btnArray.join('');
    return operateFormatterStr;
}

function operateFormatter(value, row, index) {
    return operateFormatterStr || getOperateFormatterStr();
}

window.operateEvents = {
    'click .remove': function (e, value, row, index) {
        $.warnConfirm('确定删除吗？',
            function () {
                $.ajax({
                    type: 'delete',
                    url: 'delete',
                    data: {
                        id: row.id
                    },
                    success: function () {
                        $.notifySuccess('删除成功！');
                        $table.refreshTable();
                    },
                    beforeSend: function (xhr) {
                        $.showLoading();
                    },
                    complete: function () {
                        $.hideLoading();
                    }
                });
            });
    },
    'click .edit': function (e, value, row, index) {
        $table.editByUniqueId('id', row.id);
    }
};

$(function () {
    if(location.search.includes('back-button')){
        $('#back-button').show();
    };
    $('#back-button').on('click',function(){
        history.back();
    });

    $('.select2').select2({
        language: 'zh-CN'
    });

    $table.bootstrapTable({
        queryParams: function (params) {
            return $('#tableFilter').appendFilters(params);
        }
    });

    if (!hasOperate) {
        $table.bootstrapTable('hideColumn', 'operate');
        $('table.f-table-r').removeClass('f-table-r')
    }

    $("#add-button").click(function () {
        window.location.href = 'add';
    })
});
