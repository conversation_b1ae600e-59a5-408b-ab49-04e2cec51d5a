window.icons = {
  paginationSwitchDown: 'mdi-grid',
  paginationSwitchUp: 'mdi-grid-off',
  refresh: 'mdi-refresh',
  toggleOff: 'mdi-toggle-switch-off',
  toggleOn: 'mdi-toggle-switch',
  columns: 'mdi-format-list-bulleted',
  fullscreen: 'mdi-fullscreen',
  detailOpen: 'mdi-plus',
  detailClose: 'mdi-minus',
  autoRefresh: 'mdi-clock',
  export: 'mdi-download'
};

$.extend({
  deleteConfirm: function (callBack, content) {
    $.warnConfirm(content ? content : '你确认删除选中的数据吗？', callBack);
  }
});

$.fn.bootstrapTable.defaults['escape'] = true;

/**
 * 自定义扩展函数
 */
$.fn.extend({
  /**
   * 获取表格选中的数据
   * @returns {JQuery | any | jQuery}
   */
  getTableSelections: function () {
    return $(this).bootstrapTable("getSelections");
  },

  /**
   * 重新加载表格数据
   */
  reloadTable: function () {
    $(this).bootstrapTable("refresh", {pageNumber: 1});
  },

  /**
   * 重新加载表格数据
   */
  refreshTable: function () {
    console.log('重新加载表格数据')
    $(this).bootstrapTable("refresh");
  },

  /**
   * 计算实际索引值
   * @param index
   * @returns {*}
   */
  calcIndex: function (index) {
    var options = $(this).bootstrapTable('getOptions');
    // 返回每条的序号： 每页条数 * （当前页 - 1 ）+ 序号 + 1
    return options.pageSize * (options.pageNumber - 1) + index + 1;
  },

  /**
   * 获取要选中的表格数据
   * @returns {null|*}
   */
  getTableSelection: function () {
    var selectedItems = $(this).getTableSelections();
    if (selectedItems.length <= 0 || selectedItems.length > 1) {
      return null;
    }
    return selectedItems[0];
  },

  /**
   * 根据唯一ID获取数据
   * @param uniqueId
   * @returns {JQuery | any | jQuery}
   */
  getRowByUniqueId: function (uniqueId) {
    return $(this).bootstrapTable('getRowByUniqueId', uniqueId);
  },

  /**
   * 从选中的表格数据中，获取指定字段的值数组
   * @param field
   * @returns {null|*|jQuery}
   */
  mapTableSelections: function (field) {
    var selectedItems = $(this).getTableSelections();
    if (selectedItems.length <= 0) {
      return null;
    }
    return $.map(selectedItems, function (item, i) {
      if (field.indexOf(".") > 0) {
        var split = field.split(".");
        var result = item;
        for (let i = 0; i < split.length; i++) {
          result = result[split[i]];
        }
        return result;
      }
      return item[field];
    });
  },

  /**
   * 通过唯一ID进行操作
   * @param pkName 唯一ID属性名
   * @param uniqueId 唯一ID值
   * @param url 路径，默认为delete
   */
  optByUniqueId: function (pkName, uniqueId, url) {
    var $table = $(this);
    $table.bootstrapTable('checkBy', {field: pkName, values: [uniqueId]});
    var selectedItem = $table.getRowByUniqueId(uniqueId);
    if (selectedItem) {
      window.location.href = url + '?' + pkName + "=" + selectedItem[pkName];
    } else {
      $.notifyInfo("该条要操作的数据不存在");
    }
  },

  /**
   * 通过唯一ID跳转编辑数据
   * @param pkName 唯一ID属性名
   * @param uniqueId 唯一ID值
   * @param url 路径，默认为delete
   */
  editByUniqueId: function (pkName, uniqueId, url) {
    var $table = $(this);
    $table.bootstrapTable('checkBy', {field: pkName, values: [uniqueId]});
    var selectedItem = $table.getRowByUniqueId(uniqueId);
    if (selectedItem) {
      window.location.href = (url ? url : 'edit') + '?' + pkName + "=" + selectedItem[pkName];
    } else {
      $.notifyInfo("该条要编辑的数据不存在");
    }
  },

  /**
   * 通过唯一ID删除数据
   * @param pkName 唯一ID属性名
   * @param uniqueId 唯一ID值
   * @param url 路径，默认为delete
   * @param successCallback 成功后的回调
   */
  deleteByUniqueId: function (pkName, uniqueId, url, successCallback) {
    var $table = $(this);
    $table.bootstrapTable('checkBy', {field: pkName, values: [uniqueId]});
    var selectedItem = $table.getRowByUniqueId(uniqueId);
    if (selectedItem) {
      $.deleteConfirm(function () {
        $.ajax({
          type: 'delete',
          url: (url ? url : 'delete') + '?' + pkName + "=" + uniqueId,
          success: function () {
            $.notifySuccess("删除成功！");
            if (successCallback) {
              successCallback();
            } else {
              $table.reloadTable();
            }
          },
          beforeSend: function (xhr) {
            $.showLoading();
          },
          complete: function () {
            $.hideLoading();
          }
        });
      }, '确认删除该条数据吗？');
    } else {
      $.notifyInfo("该条要删除的数据不存在");
    }
  },

  /**
   * 批量删除表格选中的数据
   * @param pkName
   * @param url
   */
  deleteTableSelections: function (pkName, url) {
    var $table = $(this);
    var ids = $table.mapTableSelections(pkName);
    if (!ids) {
      $.notifyInfo("请选择要删除的数据");
      return;
    }

    $.deleteConfirm(function () {
      $.ajax({
        type: 'delete',
        url: (url ? url : 'batchDel'),
        data: {
          ids: ids
        },
        success: function () {
          $.notifySuccess("删除成功！");
          $table.reloadTable();
        },
        beforeSend: function (xhr) {
          $.showLoading();
        },
        complete: function () {
          $.hideLoading();
        }
      });
    });
  },

  /**
   * 跳转进入选中数据的编辑界面
   * @param pkName
   * @param url
   */
  editTableSelection: function (pkName, url) {
    var selectedItem = $(this).getTableSelection();
    if (selectedItem) {
      window.location.href = (url ? url : 'edit') + '?' + pkName + "=" + selectedItem[pkName];
    } else {
      $.notifyInfo("请选择要编辑的数据");
    }
  },

  /**
   * 删除表格选择的一条数据
   * @param pkName
   * @param url
   */
  deleteTableSelection: function (pkName, url) {
    var $table = $(this);
    var selectedItem = $table.getTableSelection();
    if (selectedItem) {
      $.deleteConfirm(function () {
        $.ajax({
          url: (url ? url : 'delete') + '?' + pkName + "=" + selectedItem[pkName],
          type: 'delete',
          success: function () {
            $.notifySuccess("删除成功！");
            $table.reloadTable();
          },
          beforeSend: function (xhr) {
            $.showLoading();
          },
          complete: function () {
            $.hideLoading();
          }
        });
      });
    } else {
      $.notifyInfo("请选择要删除的数据");
    }
  }
});