$(function (){
    $('.select2').on('change', function () {
        $(this).valid();
    }).select2({
        language: 'zh-CN'
    });

    // 设置页面参数动态校验
    // 支付宝线上扫码支付参数动态校验
    $('#enable1').on('change',function () {
        if ($(this).prop('checked')) {
            $('.enable-online').prop('required', true);
        }else {
            $('.enable-online').prop('required', false);
        }
    })
    // 支付宝生活缴费参数动态校验
    $('#enableLifePay1').on('change',function (){
        if ($(this).prop('checked')) {
            $('.enable-life').prop('required', true)
        }else {
            $('.enable-life').prop('required', false)
        }
    })
})