var $table = $('#table');

function detailFormatter(index, row, element) {
    return $.templates("#detailViewTmpl").render(row);
}

$(function () {

    $('.select2').select2({
        language: 'zh-CN'
    })

    $('.search-bar.meter .dropdown-menu li').click(function () {
        let field = $(this).data('field') || '';
        $(`.form-group #${field}`).show().siblings('input').hide().val('');
        $('.search-bar.meter .dropdown-toggle').html($(this).text() + ' <span class="caret"></span>');
    });
    $('.search-bar.customer .dropdown-menu li').click(function () {
        let field = $(this).data('field') || '';
        $(`.form-group #${field}`).show().siblings('input').hide().val('');
        $('.search-bar.customer .dropdown-toggle').html($(this).text() + ' <span class="caret"></span>');
    });
    $table.bootstrapTable({
        queryParams: function (params) {
            params = $('#tableFilter').appendFilters(params);
            $('#export-own-measuring-point').val(JSON.stringify(params).replace(/"/g, "'"));
            return params;
        }
    }).one('post-header.bs.table', function () {
        $(this).find('.help .th-inner').append(`<span class="mdi mdi-help-circle"></span>`);
    }).on('load-success.bs.table', function (e, data) {
        if (isExport) {
            isExport = false;
            if (data.length === 0) {
                $.notifyInfo('当前无数据可导出');
                return;
            }
            $('#export').submit();
        }
        // 统计当前条件下的总欠费金额
        $.get('getTotalOweAmount', {
            params: $("#export-own-measuring-point").val()
        }, function (data) {
            let amount = data ? data : 0;
            $('#cashier').show().text('总欠费金额为：' + amount + '元');
        });
    });
});

var isExport = false;

function exportOweMeasuringPoint() {
    isExport = true;
    $table.reloadTable();
}

function showWaterPrice(waterPrice) {
    if (waterPrice) {
        $.dialog({
            title: '水价详情',
            boxWidth: '500px',
            useBootstrap: false,
            backgroundDismiss: true,
            content: `<div style="text-align: left; padding: 0 10px; font-size: 16px;">${waterPrice}</div>`,
        });
    }
}