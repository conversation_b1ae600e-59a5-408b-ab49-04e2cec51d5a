$(function () {
    $('.select2').select2({
        language: 'zh-CN'
    });

    $('#width').attr("value", px2mm($('#width').val()));
    $('#height').attr("value", px2mm($('#height').val()));

    defineSize($('#width').val(), $('#height').val());

});

function sizeChange(value) {
    switch (value) {
        case 'A4-H':
            $('#width').attr("value", 297);
            $('#height').attr("value", 210);
            $('#width').prop('readonly', true);
            $('#height').prop('readonly', true);
            break;
        case 'A4-Z':
            $('#width').attr("value", 210);
            $('#height').attr("value", 297);
            $('#width').prop('readonly', true);
            $('#height').prop('readonly', true);
            break;
        case 'A3-H':
            $('#width').attr("value", 420);
            $('#height').attr("value", 297);
            $('#width').prop('readonly', true);
            $('#height').prop('readonly', true);
            break;
        case 'A3-Z':
            $('#width').attr("value", 297);
            $('#height').attr("value", 420);
            $('#width').prop('readonly', true);
            $('#height').prop('readonly', true);
            break;
        case 'B3':
            $('#width').attr("value", 500);
            $('#height').attr("value", 353);
            $('#width').prop('readonly', true);
            $('#height').prop('readonly', true);
            break;
        case '标准收据':
            $('#width').attr("value", 176);
            $('#height').attr("value", 85);
            $('#width').prop('readonly', true);
            $('#height').prop('readonly', true);
            break;
        case 'custom':
            $('#width').prop('readonly', false);
            $('#height').prop('readonly', false);
            break;
        default:
            break;
    }
}

function defineSize(width, height) {
    if (width === '297' && height === '210') {
        $('#select2-sizeSelect-container').text('A4横向(297mm*210mm)');
    } else if (width === '210' && height === '297') {
        $('#select2-sizeSelect-container').text('A4纵向(210mm*297mm)');
    } else if (width === '420' && height === '297') {
        $('#select2-sizeSelect-container').text('A3横向(420mm*297mm)');
    } else if (width === '297' && height === '420') {
        $('#select2-sizeSelect-container').text('A3纵向(297mm*420mm)');
    } else if (width === '500' && height === '353') {
        $('#select2-sizeSelect-container').text('B3(500mm*353mm)');
    } else if (width === '176' && height === '85') {
        $('#select2-sizeSelect-container').text('标准收据(176mm*85mm)');
    } else {
        $('#select2-sizeSelect-container').text('自定义');
        $('#width').prop('readonly', false);
        $('#height').prop('readonly', false);
    }
}

/**
 * mm转换为px
 * @param value
 * @returns {number}
 */
const mm2px = function (value) {
    var inch = value / 25.4;
    var c_value = inch * conversion_getDPI();
    return c_value.toFixed(0);
};

/**
 * px转换为mm
 * @param value
 * @returns {number}
 */
const px2mm = function (value) {
    var inch = value / conversion_getDPI();
    return (inch * 25.4).toFixed(0);
};

/**
 * 获取DPI 每英寸像素点
 * @returns {Array}
 */
const conversion_getDPI = function () {
    var DPI = 0;
    if (window.screen.deviceXDPI) {
        DPI = window.screen.deviceXDPI;
    } else {
        var tmpNode = document.createElement("DIV");
        tmpNode.style.cssText =
            "width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden";
        document.body.appendChild(tmpNode);
        DPI = parseInt(tmpNode.offsetWidth);
        tmpNode.parentNode.removeChild(tmpNode);
    }
    return DPI;
};
