var $table = $('#table');

var hasReply = $('#hasReply').length > 0;
var hasOperate = hasReply;

function imgFormatter(value, row, index) {
    return `<img src="${value}" style="width:40px;height: 40px;"/>`;
}

function operateFormatter(value, row, index) {
    if (hasReply) {
        return `<div class="btn-group">
                    <a class="btn btn-xs btn-default reply text-info" href="javascript:void(0);" title="回复"><i class="mdi mdi-message-reply"></i></a>
                </div>`

    }
    return ''
}

function contentFormatter(value) {
    return `<div class="content-td"><div class="tiptoggle">${value}</div><div class="content-btn" data-content="${value}">详情>></div></div>`
}

window.operateEvents = {
    'click .reply': function (e, value, row, index) {
        $('.mode3').addClass('show');
        $('#iframe_reply').attr('src', 'toReply?advisoryId=' + row.id);
        $('body').addClass('over-hidden');
    },
    'click .content-btn': function (e, value, row, index) {
        // $(e.target).parent().hide().siblings('p').show();
        $.dialog({
            title: '咨询详情',
            boxWidth: '500px',
            useBootstrap: false,
            backgroundDismiss: true,
            content: `<div style="word-break: break-all; padding: 0 10px; font-size: 16px;">${e.target.dataset.content}</div>`,
        });
    }
};


$(function () {
    document.querySelector('#table').addEventListener('click', function (e) {
        if (e.target.nodeName === 'IMG' && e.target.currentSrc) {
            $.dialog({
                title: '用户头像',
                backgroundDismiss: true,
                content: `<img src="${e.target.currentSrc}"/>`,
            });
        }
    })
    $('.select2').select2({
        language: 'zh-CN'
    });
    initDatetimePicker("created-datetimes");
    $table.bootstrapTable({
        queryParams: function (params) {
            params = $('#tableFilter').appendFilters(params);
            const createdDateRange = $("#created-datetimes").val();
            if (createdDateRange != '') {
                const createdDateRangeArr = createdDateRange.split(" - ");
                const startTime = createdDateRangeArr[0];
                const endTime = createdDateRangeArr[1];
                const createdDateStartFilter = {
                    property: "createdDate",
                    operator: "ge",
                    ignoreCase: false,
                    value: startTime
                };
                params.andFilters.push(createdDateStartFilter);
                const createdDateEndFilter = {
                    property: "createdDate",
                    operator: "le",
                    ignoreCase: false,
                    value: endTime
                };
                params.andFilters.push(createdDateEndFilter);
            }
            return params;
        }
    });
    var options = $table.bootstrapTable('getOptions');
    options.columns[0][5].events = window.operateEvents;
    options.columns[0][0].events = window.operateEvents;

    if (!hasOperate) {
        $table.bootstrapTable('hideColumn', 'operate');
        $('table.f-table-r').removeClass('f-table-r')
    }

    $('.search-bar.meter .dropdown-menu li').click(function () {
        let field = $(this).data('field') || '';
        $(`.form-group #${field}`).show().siblings('input').hide().val('');
        $('.search-bar.meter .dropdown-toggle').html($(this).text() + ' <span class="caret"></span>');
    });
});