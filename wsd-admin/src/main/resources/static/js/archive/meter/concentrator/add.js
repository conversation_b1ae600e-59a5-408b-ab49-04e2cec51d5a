let $communityId = $('#measuringPoint\\.community\\.id'),
    $collectorUid = $('#collectorUid');
let communityTree = $('#communityTree');
let isJxConcentrator = false;

// 无效的中继器编码
let EMPTY_REPEATER_CODE = padPre('0', 12, '0');

function changeConcentrator() {
    let val = $('#concentrator\\.id').val();
    if (!val) {
        return;
    }
    let text = $('#concentrator\\.id').find('[value=' + val + ']').text();

    if (text.includes('第三方-捷先集中器')) {
        isJxConcentrator = true;
        $("#jx-concentrator-div").show();
        if (text.includes('第三方-捷先集中器-旧')) {
            fillJxOldSelect2();
        } else {
            fillJxNewSelect2();
        }
    } else {
        isJxConcentrator = false;
        $("#jx-concentrator-div").hide();
    }

    if (text.includes('有线多功能')) {
        $("#protocolPort").show();
    } else {
        $("#protocol").val("").trigger('change');
        $("#port").val("").trigger('change');
        $("#protocolPort").hide();
    }
}

function refreshSelect() {
    if ($communityId.val() && $communityId.val() >= 0) {
        concentrators();
    }
}

$(function () {
    $("#form").validate();
    submitOnce();
    $('.select2').on('change', function () {
        $(this).valid();
    }).select2({
        language: 'zh-CN'
    });

    $('#parent-text').on('click', function () {
        $('.select-community-wrap').show();
    })
    $('.select-community-wrap').on('mouseleave', function () {
        $(this).hide();
    })

    initCommunityTree('#communityTree', function (data) {
        $('#parent-text').val(getFullCommunity(data));
        $('.select-community-wrap').hide();

        if (data.parent.startsWith('r-')) {
            $communityId.val(data.original.id);
            $('#lowerCommunity\\.id').val(0);
            // 查询集中器
            concentrators();
        } else {
            // 找到顶级父节点
            const rootNodeId = data.parents[data.parents.length - 4];
            $communityId.val(rootNodeId);
            $('#lowerCommunity\\.id').val(data.original.id);
        }
    }, function (result, communityId) {
        if (communityId) {
            result.forEach(item => {
                item.children = !item.leaf;
            })
        }else if (result && result.length > 0) {
            result.forEach(c1 => {
                c1.children.forEach(c2 => {
                    c2.state.disabled = true;
                    c2.children.forEach(c3 => {
                        c3.children = !c3.leaf;
                    })
                })
            });
        }
        return result;
    }, null, true);

    $collectorUid.tagsInput({
        height: $collectorUid.data('height') ? $collectorUid.data('height') : '38px',
        width: '100%',
        defaultText: $collectorUid.attr("placeholder"),
        removeWithBackspace: true,
        delimiter: [','],
        interactive: false
    });
    $('#repeater-input').keydown(function (evt) {
        if (evt.which === 13 || evt.which === 9) {
            var tags = $collectorUid.val();
            console.log(tags)
            if (isJxConcentrator && tags.split(',').filter(t => t).length >= 1) {
                $.notifyInfo('捷先集中器下的水表最多支持一个中继器');
                return;
            }
            if (tags.split(',').length >= 3) {
                $.notifyInfo('最多支持三个中继器');
            } else {
                var newTag = padPre($(this).val(), 12, '0');
                if (newTag !== EMPTY_REPEATER_CODE) {
                    if (tags.indexOf(newTag) === -1) {
                        $collectorUid.addTag(newTag);
                    } else {
                        $.notifyInfo('该中继器已存在');
                    }
                }
            }
            $(this).val('');
            evt.preventDefault();
        }
    });

    $('input[name=standard]').on('change', function () {
        if ($(this).val() === 'true') {
            $('#code').attr('minlength', '14');
        } else {
            $('#code').attr('minlength', '0');
        }

        refreshSelect();
        $("#form").validate();
    });

    $('input[name=openAccount]').on('change', function () {
        if ($(this).val() === 'true') {
            $('input[name=measuringPoint\\.houseHolder]').prop('required', true);
            $('#houseHolder-require').show();
            $('#start-balance-div').show();
        } else {
            $('input[name=measuringPoint\\.houseHolder]').prop('required', false);
            $('#houseHolder-require').hide();
            $('#start-balance-div').hide();
        }
    });
});

function concentrators() {
    const standard = $("input[name=standard]:checked").val();
    $.get('concentrators', {
        communityId: $communityId.val(),
        standard: standard
    }, function (data) {
        $('#concentrator\\.id').html('');
        var options = $.map(data, function (obj) {
            return new Option(obj.code + concentratorTypeToContent(obj.concentratorType, obj.concentratorDockingType), obj.id, false, false);
        });
        options.splice(0, 0, new Option('请选择', '', true, true));
        $('#concentrator\\.id').append(options).trigger('change');
    });
}

/**
 * 初始化旧捷先协议相关select2
 */
function fillJxOldSelect2() {
    // 填充旧捷先协议类型
    var protocol = [
        {id: '10', text: '188协议'},
        {id: '01', text: '645-97协议'},
        {id: '20', text: '内部协议'}
    ];

    $('#protocolPort-select2').html('').select2({
        data: protocol
    });

    // 填充旧捷先表波特率
    var meterBaud = [
        {id: 1, text: '1200bps'},
        {id: 2, text: '2400bps'},
        {id: 4, text: '4800bps'},
        {id: 9, text: '9600bps'}
    ];

    $("#meterBaud-select2").html('').select2({
        data: meterBaud
    });

    // 填充旧捷表端口
    var concentratorPort = [
        {id: 1, text: '端口1'},
        {id: 2, text: '端口2'}
    ];

    $("#concentratorPort-select2").html('').select2({
        data: concentratorPort
    });
}

/**
 * 初始化新捷先协议相关select2
 */
function fillJxNewSelect2() {
    // 填充新捷先协议类型
    var protocol = [
        {id: 1, text: '内部规约'},
        {id: 2, text: '标准 CJ188'},
        {id: 3, text: '标准 645-97'},
        {id: 4, text: '混合协议'},
        {id: 5, text: '汉水 188'},
        {id: 6, text: '长沙 188'},
        {id: 7, text: '乌鲁木齐 188'},
        {id: 8, text: '宁波水表'},
        {id: 9, text: 'Mobus 协议'},
        {id: 10, text: '645-07 协议'},
        {id: 11, text: '西岛 188 协议'},
        {id: 12, text: '瑞泉 188 协议'},
        {id: 13, text: '积成 188 协议'},
        {id: 14, text: '千宝通 188'},
        {id: 15, text: '通达协议'}
    ];

    $("#protocolPort-select2").html('').select2({
        data: protocol
    });

    // 填充新捷先表波特率
    var meterBaud = [
        {id: 1, text: '1200bps'},
        {id: 2, text: '2400bps'},
        {id: 3, text: '4800bps'},
        {id: 4, text: '9600bps'},
        {id: 5, text: '19200bps'}
    ];

    $("#meterBaud-select2").html('').select2({
        data: meterBaud
    });

    // 填充新捷先表端口
    var concentratorPort = [
        {id: 1, text: '无线'},
        {id: 2, text: '485'},
        {id: 3, text: '485-2'},
        {id: 4, text: '485-3'},
        {id: 5, text: '485-4'},
        {id: 6, text: 'mbus'},
        {id: 7, text: 'mbus-2'},
        {id: 8, text: 'mbus-3'},
        {id: 9, text: 'mbus-4'}
    ];

    $("#concentratorPort-select2").html('').select2({
        data: concentratorPort
    });
}