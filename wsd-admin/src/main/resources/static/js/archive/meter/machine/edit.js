let communityTree = $('#communityTree');

$(function () {
    // 初始化地图
    let lng = $('#longitude').val();
    let lat = $('#latitude').val();
    if (lng && lat) {
        // 同时添加一个点标记
        marker = new AMap.Marker({
            icon: "https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png",
            position: [lng, lat],
            zoom: 15,
            offset: new AMap.Pixel(0, 0), // icon底部中部与坐标原点对齐
            anchor:'bottom-center' // 设置锚点方位
        });
        map.add(marker);
        map.setZoomAndCenter(15, [lng, lat]);
    }

    $("#form").validate();
    submitOnce();
    $('.select2').on('change', function () {
        $(this).valid();
    }).select2({
        language: 'zh-CN'
    });

    $('#parent-text').on('click', function () {
        $('.select-community-wrap').show();
    })
    $('.select-community-wrap').on('mouseleave', function () {
        $(this).hide();
    })

    let rootCommunityId = $('#community\\.id').val(),
        rootCommunityName = $('#community\\.name').val(),
        lowerLayerCode = $('#lowerCommunity\\.layerCode').val(),
        lowerName = $('#lowerCommunity\\.name').val();

    //有lowerName就显示lowerName 没有就显示顶级小区name
    $('#parent-text').val(lowerName || rootCommunityName);

    initCommunityTree('#communityTree', function (data) {
        if (!data) {
            return;
        }
        $('#parent-text').val(getFullCommunity(data));
        $('.select-community-wrap').hide();

        if (data.parent === '#') {
            $('#lowerCommunity\\.id').val(null);
        } else {
            let node = communityTree.jstree("get_node", data.original.id);
            $('#lowerCommunity\\.id').val(node.original.id);
        }
    }, function (result, communityId) {
        result.forEach(item => {
            item.children = !item.leaf;
            if (lowerLayerCode === item.code) {
                item.state.selected = true;
                setTimeout(()=>{
                    let node = communityTree.jstree("get_node", item.id);
                    node && $('#parent-text').val(getFullCommunity(node));
                }, 1000);
            } else if (lowerLayerCode.startsWith(item.code)) {
                item.state.opened = true;
            }
        });
        if (rootCommunityId === communityId) {
            return {
                children: result,
                code: '',
                id: rootCommunityId,
                leaf: true,
                state: {opened: true, disabled: false, selected: false, checked: false},
                text: rootCommunityName,
                type: "community",
            };
        } else {
            return result;
        }
    }, rootCommunityId);
    changeMeterBook();
});

function changeMeterBook() {
    let val = $('#meterBook\\.id').val();
    if (val) {
        $("#list-order-div").show();
    } else {
        $("#list-order-div").hide();
    }
}