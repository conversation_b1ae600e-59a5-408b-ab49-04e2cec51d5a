<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/layout}">
<head>
    <meta charset="UTF-8">
    <title>新建小区保水</title>
    <link rel="stylesheet" th:href="@{/webjars/light-year-admin/js/jconfirm/jquery-confirm.min.css}">
    <link rel="stylesheet" th:href="@{/webjars/bootstrap-table/dist/bootstrap-table.min.css}">
    <link rel="stylesheet" th:href="@{/webjars/select2/css/select2.min.css}">
    <!--日期选择插件-->
    <link rel="stylesheet" th:href="@{/webjars/light-year-admin/js/bootstrap-datepicker/bootstrap-datepicker3.min.css}">
    <link rel="stylesheet" th:href="@{/css/ly-select2.css}">
    <link rel="stylesheet" th:href="@{/css/reset-style.css}">
    <style>
        .mode .card {
            top: 0;
            left: 0;
            padding: 0 20px 20px;
            width: 80%;
            max-height: 98%;
        }

        .mode.show .card {
            animation: modeshow 0.25s;
        }

        .mode.show .card .fixed-table-body {
            max-height: calc(90vh - 270px);
        }

        .community-table .fixed-table-body {
            max-height: 300px;
        }


        .picture-wrap {
            display: flex;
        }

        .picture-wrap .item {
            position: relative;
            margin-right: 10px;
            width: 100px;
            height: 100px;
            display: block;
            border: 1px solid #aaa;
            border-radius: 5px;
            margin-bottom: 5px;
            padding: 3px;
            cursor: pointer;
        }

        .picture-wrap img {
            width: 100%;
            height: 100%;
        }

        .picture-wrap .item.add input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100% !important;
            opacity: 0;
            cursor: pointer;
        }

        .picture-wrap .item .mdi {
            position: absolute;
            top: 0;
            right: 0;
            font-size: 24px;
            line-height: 1;
            color: #ff7777;
        }

        .picture-wrap .item .mdi:hover {
            transform: scale(1.3);
        }

        .img-preview {
            display: block;
            margin: 0 auto;
        }

        .code-item {
            background: #33cabb;
            color: #fff;
            font-size: 16px;
            display: inline-flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 4px;
            padding: 6px;
        }

        .code-item .mdi-close-box {
            color: #fc7878;
            cursor: pointer;
            margin-left: 5px;

        }

        .code-item .mdi-close-box:hover {
            transform: scale(1.4);
        }

        .open-account-item{
            margin: 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
    </style>
</head>
<body>

<th:block layout:fragment="page-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="card table-card-30">
                <div class="card-header"><h4><div  class="back-button" onclick="history.back()" data-original-title="返回" >
                    <span class="mdi mdi-chevron-left"></span>返回</div><span class="divide-line">|</span>新增小区保水</h4></div>
                <div class="card-body" style="overflow: auto; padding-bottom: 20px">

                    <form id="form" th:action="@{/revenue/settings/waterRetention/add}" method="post" th:object="${communityWaterRetention}">

                        <div class="form-group line-group col-md-6">
                            <label for="startDate"><span class="text-danger">*</span>保水开始日期</label>
                            <input class="form-control js-datepicker" type="text" th:field="*{startDate}" placeholder="保水开始日期" data-date-format="yyyy-mm-dd"
                                   data-rule-date="true" required>
                            <label th:if="${#fields.hasErrors('startDate')}" th:errors="*{startDate}" class="text-danger"></label>
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="endDate"><span class="text-danger">*</span>保水结束日期</label>
                            <input class="form-control js-datepicker" type="text" th:field="*{endDate}" placeholder="保水结束日期" data-date-format="yyyy-mm-dd"
                                   data-rule-date="true" required>
                            <label th:if="${#fields.hasErrors('endDate')}" th:errors="*{endDate}" class="text-danger"></label>
                        </div>

                        <div class="form-group line-group col-md-12">
                            <label for="remark">备注</label>
                            <textarea rows="5" class="form-control" th:field="*{remark}" placeholder="请输入备注" maxlength="255">备注</textarea>
                            <label th:if="${#fields.hasErrors('remark')}" th:errors="*{remark}" class="text-danger"></label>
                        </div>

                        <div class="divider">选择保水小区</div>

                        <div class="form-group line-group col-md-12">
                            <input class="form-control" id="communityIds" type="hidden" name="communityIds" placeholder="小区ID">
                            <div class="community-table" style="display: none; margin-bottom: 10px">
                                <table id="community-table"
                                       class="f-table-r stripe-table"
                                       data-icons-prefix="mdi"
                                       data-icons="icons"
                                       data-id-field="id"
                                       data-unique-id="id"
                                       data-striped="true"
                                       >
                                    <thead>
                                    <tr>
                                        <th data-field="name" data-sortable="true">小区名称</th>
                                        <th data-field="region.name" data-sortable="true">所属管辖区</th>
                                        <th data-field="address" data-sortable="true">小区地址</th>
                                        <th data-field="remark" data-sortable="true">备注</th>
                                        <th data-field="companyId" data-sortable="true" >公司名称</th>
                                        <th data-field="operate" data-align="center" data-width="60" data-formatter="operateFormatter2"
                                            data-events="operateEvents2">操作
                                        </th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="code-list">
                            </div>
                            <button class="btn btn-info m-r-5 " id="add-community" type="button"><i
                                    class="mdi mdi-plus"></i>选择小区
                            </button>
                        </div>

                        <div class="col-md-12">
                            <button type="button" class="btn btn-primary" onclick="formDataSub()"
                                    style="width:70px;margin-right:5px;">保 存
                            </button>
                            <button type="button" class="btn btn-default" onclick="history.back()" style="width:70px">返
                                回
                            </button>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>

    <div class="mode">
        <div class="card" style="padding-top: 16px">
            <form id="tableFilter" class="form-inline" role="form" onsubmit="return false;">
                <div class="default-filter-wrap">
                    <div style="margin-right: 10px">
                        <div class="form-group mar_t">
                            <div class="input-group">
                                <span class="input-group-addon">小区名称</span>
                                <input style="width: 160px;" type="text" class="form-control table-filter"
                                       data-filter="name" data-operator="like" data-ignorecase="false"
                                       placeholder="请输入小区名称"/>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mar_t filter-btn-group">
                        <button class="btn btn-primary" type="button" onclick="$('#table').reloadTable();">查询
                        </button>
                        <button class="btn btn btn-secondary" type="button"
                                onclick="resetSelect2();$('#tableFilter').clearFilters();$('#table').reloadTable();">
                            清除
                        </button>
                    </div>
                </div>
            </form>
            <table id="table"
                   class="f-table-r stripe-table"
                   data-icons-prefix="mdi"
                   data-icons="icons"
                   data-defer-url="../../../archive/community/pageForAdd"
                   data-toolbar="#toolbar"
                   data-id-field="id"
                   data-unique-id="id"
                   data-striped="true"
                   data-cache="false"
                   data-method="post"
                   data-pagination="true"
                   data-side-pagination="server"
                   data-data-field="content"
                   data-total-field="totalElements"
                   data-page-list="[20, 50, 100, 200]" data-page-size="20">
                <thead>
                <tr>
                    <th data-field="name" data-sortable="true">小区名称</th>
                    <th data-field="region.name" data-sortable="true">所属管辖区</th>
                    <th data-field="address" data-sortable="true">小区地址</th>
                    <th data-field="remark" data-sortable="true">备注</th>
                    <th data-field="companyId" data-sortable="true" >公司名称</th>
                    <th data-field="operate" data-align="center" data-width="84" data-formatter="operateFormatter"
                        data-events="operateEvents">操作
                    </th>
                </tr>
                </thead>
            </table>
            <button class="btn btn-default m-t-10" style="float: right" onclick="$('.mode').removeClass('show')">关闭</button>
        </div>
    </div>
</th:block>

<th:block layout:fragment="page-script">
    <script th:src="@{/webjars/light-year-admin/js/jconfirm/jquery-confirm.min.js}"></script>
    <script th:src="@{/webjars/select2/js/select2.min.js}"></script>
    <script th:src="@{/webjars/select2/js/i18n/zh-CN.js}"></script>

    <script th:src="@{/webjars/bootstrap-table/dist/bootstrap-table.min.js}"></script>
    <script th:src="@{/webjars/bootstrap-table/dist/locale/bootstrap-table-zh-CN.min.js}"></script>
    <script th:src="@{/js/common/bootstrap-table-custom.js}"></script>
    <script th:src="@{/js/common/query-filter.js}"></script>
    <script th:src="@{/webjars/bootstrap-table/dist/extensions/defer-url/bootstrap-table-defer-url.min.js}"></script>
    <script th:src="@{/webjars/light-year-admin/js/bootstrap-datetimepicker/moment.min.js}"></script>
    <!--日期选择插件-->
    <script th:src="@{/webjars/light-year-admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js}"></script>
    <script th:src="@{/webjars/light-year-admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js}"></script>
    <script th:src="@{/js/common/enum-formatter.js}"></script>
    <script th:src="@{/webjars/jquery-validation/dist/jquery.validate.min.js}"></script>
    <script th:src="@{/webjars/jquery-validation/dist/localization/messages_zh.min.js}"></script>
    <script th:src="@{/js/common/jq-validation-bs.js}"></script>
    <script th:src="@{/js/revenue/settings/water-retention/add.js}"></script>
    <script>
        let communityIdList = [], communityDataList =[];
        let $table = $('#table'), $communityTable = $('#community-table');

        function operateFormatter(value, row, index) {
            if (communityIdList.includes(row.id)) {
                return '<span class="text-success">已选择</span>'
            } else {
                return '<div class="btn-box"><a class="btn btn-xs btn-default text-primary add" href="javascript:void(0);" title="添加"><i class="mdi mdi-library-plus" style="pointer-events: none"></i></a></div>';
            }
        }

        function operateFormatter2(value, row, index) {
             return '<div class="btn-box"><a class="btn btn-xs btn-default text-danger remove" href="javascript:void(0);" title="移除"><i class="mdi mdi-window-close" style="pointer-events: none"></i></a></div>';
        }

        window.operateEvents = {
            'click .add': function (e, value, row, index) {
                communityIdList.push(row.id);
                communityDataList.push(row);
                $('#communityIds').val(communityIdList.join(','));
                $('.community-table').show();
                $communityTable.bootstrapTable('refreshOptions', {
                    data:communityDataList
                })
                $(e.target).parent().html('<span class="text-success">已选择</span>');
            }
        }

        window.operateEvents2 = {
            'click .remove': function (e, value, row, index) {
                communityIdList = communityIdList.filter(item => item !== row.id);
                communityDataList = communityDataList.filter(item => item.id !== row.id);
                $('#communityIds').val(communityIdList.join(','));
                $communityTable.bootstrapTable('refreshOptions', {
                    data:communityDataList
                })
                if(communityDataList.length === 0){
                    $('.community-table').hide();
                }
            }
        }

        $(function () {
            $table.bootstrapTable({
                queryParams: function (params) {
                    return $('#tableFilter').appendFilters(params);
                }
            });

            $communityTable.bootstrapTable({
                data:[]
            });

            $('#add-community').on('click', function () {
                $('.mode').addClass('show');
                $table.reloadTable();
            })
        })
    </script>
</th:block>

</body>
</html>