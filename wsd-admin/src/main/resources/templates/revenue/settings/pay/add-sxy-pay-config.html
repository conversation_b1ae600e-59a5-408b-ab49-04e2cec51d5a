<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/layout}">
<head>
    <meta charset="UTF-8">
    <title>新建首信易支付参数配置</title>
    <!--日期选择插件-->
    <link rel="stylesheet" th:href="@{/webjars/light-year-admin/js/bootstrap-datepicker/bootstrap-datepicker3.min.css}">
    <link rel="stylesheet" th:href="@{/webjars/select2/css/select2.min.css}">
    <link rel="stylesheet" th:href="@{/css/ly-select2.css}">
    <link rel="stylesheet" th:href="@{/css/reset-style.css}">
</head>
<body>

<th:block layout:fragment="page-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="card table-card-30">
                <div class="card-header">
                    <h4>
                        <div class="back-button" onclick="history.back()" data-original-title="返回">
                            <span class="mdi mdi-chevron-left"></span>返回
                        </div>
                        <span class="divide-line">|</span>新建首信易支付参数配置
                    </h4>
                </div>
                <div class="card-body" style="overflow: auto">

                    <form id="form" th:action="@{/revenue/settings/pay/addSxyPayConfig}" enctype="multipart/form-data"
                          method="post" th:object="${sxyPayConfig}">
                        <div class="form-group line-group col-md-6">
                            <label for="companyId"><span class="text-danger">*</span>&nbsp;公司</label>
                            <select name="companyId" class="form-control select2" required
                                    style="width: 100%!important;height: auto!important" data-placeholder="请选择公司">
                                <option value="">请选择</option>
                                <option th:each="company : ${companies}" th:value="${company.companyId}"
                                        th:text="${company.name}"></option>
                            </select>
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="enable"><span class="text-danger">*</span>是否启用首信易支付
                            </label>
                            <label style="vertical-align: sub;margin-left: 10px;"
                                   class="lyear-switch switch-primary switch-solid">
                                <input type="checkbox" th:field="*{enable}">
                                <span></span>
                            </label>
                            <span id="helpBlock" class="help-block" style="margin-bottom: 12px">
                                若启用首信易支付，将优先使用首信易商户收款
                            </span>
                        </div>

                        <div class="form-group line-group col-md-6" >
                            <label for="chargeRate"><span class="text-danger">*</span>&nbsp;平台交易手续费</label>
                            <input class="form-control" type="number" min="0.0025" max="0.006" name="chargeRate"
                                   placeholder="平台交易手续费(0.0025~0.006)" required >
                        </div>

                        <div class="form-group line-group col-md-6" >
                            <label for="reservedBalance"><span class="text-danger">*</span>&nbsp;账户预留资金</label>
                            <input class="form-control" type="text" data-rule-number="true" value="0" name="reservedBalance" placeholder="账户预留资金" required >
                        </div>

                        <div class="form-group line-group col-md-6" >
                            <label for="appId"><span
                                    class="text-danger">*</span>&nbsp;公众号appId</label>
                            <input class="form-control" type="text" name="appId"
                                   placeholder="公众号appId" required maxlength="64">
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="merchantType"><span class="text-danger">*</span>&nbsp;商户类型</label>
                            <select name="merchantType" class="form-control select2" required
                                    style="width: 100%!important;height: auto!important" data-placeholder="请选择商户类型">
                                <option value="">请选择</option>
                                <option value="PAYMENT">收单商户</option>
                                <option value="SPLIT_BILL">分账商户</option>
                            </select>
                        </div>

                        <div class="form-group type-PAYMENT line-group col-md-6" style="display: none">
                            <label for="wechatAppletName"><span
                                    class="text-danger">*</span>&nbsp;公众号/小程序/生活号</label>
                            <input class="form-control" type="text" name="wechatAppletName"
                                   placeholder="公众号/小程序/生活号" required maxlength="80">
                        </div>

                        <div class="form-group type-PAYMENT line-group col-md-6" style="display: none">
                            <label for="wechatAppletFile"><span class="text-danger">*</span>&nbsp;业务流程截图</label>
                            <input class="form-control" type="file" name="wechatAppletFile" id="wechatAppletFile"
                                   placeholder="业务流程截图" required data-rule-fileSize="********"
                                   data-msg-fileSize="上传文件应小于10MB">
                        </div>


                        <div class="form-group line-group col-md-6">
                            <label for="registerRole"><span class="text-danger">*</span>&nbsp;商户性质</label>
                            <input class="form-control" type="hidden" name="registerRole" required>
                            <select id="registerRole" class="form-control select2" required
                                    style="width: 100%!important;height: auto!important" data-placeholder="请选择商户性质">
                                <option value="">请选择</option>
                                <option value="ENTERPRISE_LEGAL_PERSON">企业</option>
                                <option value="NATURAL_PERSON">私人</option>
                            </select>
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="signedShorthand"><span class="text-danger">*</span>&nbsp;商户简称</label>
                            <input class="form-control" type="text" name="signedShorthand" placeholder="商户简称"
                                   required
                                   maxlength="20">
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="businessAddressProvince"><span class="text-danger">*</span>&nbsp;经营地</label>
                            <div style="display: flex">
                                <select name="businessAddressProvince" class="form-control select2 city0" required
                                        style="width: 100%!important;height: auto!important" data-placeholder="省">
                                    <option value="">请选择</option>
                                </select>

                                <select name="businessAddressCity" class="form-control select2 city1" required
                                        style="width: 100%!important;height: auto!important" data-placeholder="市">
                                    <option value="">请选择</option>

                                </select>

                                <div id="area-select" style="width: 100%">
                                    <select name="businessAddressArea" class="form-control select2 city2" required
                                            style="width: 100%!important;height: auto!important" data-placeholder="区">
                                        <option value="">请先选择</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="businessAddress"><span class="text-danger">*</span>&nbsp;地址</label>
                            <input class="form-control" type="text" name="businessAddress" placeholder="地址" required
                                   maxlength="200">
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="contactName"><span class="text-danger">*</span>&nbsp;联系人姓名</label>
                            <input class="form-control" type="text" name="contactName" placeholder="联系人姓名" required
                                   maxlength="50">
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="contactEmail"><span class="text-danger">*</span>&nbsp;联系人邮箱</label>
                            <input class="form-control" type="text" name="contactEmail" data-rule-email="true"
                                   placeholder="联系人邮箱" required
                                   maxlength="50">
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="contactPhone"><span class="text-danger">*</span>&nbsp;联系人电话</label>
                            <input class="form-control" type="text" name="contactPhone" data-rule-mobile="true"
                                   placeholder="联系人电话" required
                                   maxlength="11">
                        </div>

                        <div class="divider">结算信息</div>

                        <div class="form-group line-group col-md-6 form-PUBLIC">
                            <label for="bankName"><span class="text-danger">*</span>&nbsp;开户行名称</label>
                            <input class="form-control" type="text" name="bankName" placeholder="开户行名称"
                                   maxlength="50" required>
                        </div>

                        <div class="form-group line-group col-md-6 form-PUBLIC">
                            <label for="bankBranchName"><span class="text-danger">*</span>&nbsp;支行名称</label>
                            <input class="form-control" type="text" name="bankBranchName" placeholder="支行名称"
                                   maxlength="80" required>
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="accountName"><span class="text-danger">*</span>&nbsp;开户名称</label>
                            <input class="form-control" type="text" name="accountName" placeholder="开户名称" required
                                   maxlength="80">
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="bankCardNo"><span class="text-danger">*</span>&nbsp;开户账号</label>
                            <input class="form-control" type="text" data-rule-number="true" name="bankCardNo"
                                   placeholder="开户账号" required
                                   maxlength="80">
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="reservedPhoneNo"><span class="text-danger">*</span>&nbsp;银行预留手机号</label>
                            <input class="form-control" type="text" name="reservedPhoneNo" data-rule-mobile="true"
                                   placeholder="银行预留手机号" required
                                   maxlength="11">
                        </div>

                        <div class="divider">商户资质信息</div>

                        <div class="form-group line-group col-md-6">
                            <label for="legalPersonName"><span class="text-danger">*</span>&nbsp;法人姓名</label>
                            <input class="form-control" type="text" name="legalPersonName" placeholder="法人姓名" required
                                   maxlength="50">
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="legalPersonIdNo"><span class="text-danger">*</span>&nbsp;证件号码</label>
                            <input class="form-control" type="text" data-rule-number="true" name="legalPersonIdNo"
                                   placeholder="证件号码" required
                                   maxlength="20">
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="idEffectiveDateStart"><span class="text-danger">*</span>证件有效期开始时间</label>
                            <input class="form-control js-datepicker" type="text"
                                   name="idEffectiveDateStart" placeholder="证件有效期开始时间"
                                   data-date-format="yyyy-mm-dd"
                                   data-rule-date="true" required>
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="idEffectiveDateEnd"><span class="text-danger">*</span>证件有效期结束时间</label>
                            <input class="form-control js-datepicker" type="text"
                                   name="idEffectiveDateEnd" placeholder="证件有效期结束时间"
                                   data-date-format="yyyy-mm-dd"
                                   data-rule-date="true" required>
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="legalIdCardProsFile"><span class="text-danger">*</span>&nbsp;证件照人像面</label>
                            <input class="form-control" type="file" name="legalIdCardProsFile" id="legalIdCardProsFile"
                                   placeholder="证件照人像面" required data-rule-fileSize="********"
                                   data-msg-fileSize="上传文件应小于10MB">
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="legalIdCardConsFile"><span class="text-danger">*</span>&nbsp;证件照国徽面</label>
                            <input class="form-control" type="file" name="legalIdCardConsFile" id="legalIdCardConsFile"
                                   placeholder="证件照国徽面" required data-rule-fileSize="********"
                                   data-msg-fileSize="上传文件应小于10MB">
                        </div>

                        <div class="form-group line-group col-md-6 form-PRIVATE">
                            <label for="legalPersonBankCardFile"><span class="text-danger">*</span>&nbsp;法人银行卡照片</label>
                            <input class="form-control" type="file" name="legalPersonBankCardFile"
                                   id="legalPersonBankCardFile"
                                   placeholder="法人银行卡照片" required data-rule-fileSize="********"
                                   data-msg-fileSize="上传文件应小于10MB">
                        </div>


                        <div class="form-group line-group col-md-6 form-PUBLIC">
                            <label for="cerNo"><span class="text-danger">*</span>&nbsp;资质证书编号</label>
                            <input class="form-control" type="text" name="cerNo"
                                   placeholder="资质证书编号"
                                   maxlength="20" required>
                        </div>

                        <div class="form-group line-group col-md-6 form-PUBLIC">
                            <label for="businessLicenseFile"><span class="text-danger">*</span>&nbsp;营业执照照片</label>
                            <input class="form-control" type="file" name="businessLicenseFile" id="businessLicenseFile"
                                   placeholder="营业执照照片" data-rule-fileSize="********"
                                   data-msg-fileSize="上传文件应小于10MB" required>
                        </div>

                        <div class="form-group line-group col-md-6 form-PUBLIC">
                            <label for="openAccountFile"><span class="text-danger">*</span>&nbsp;开户许可证照片</label>
                            <input class="form-control" type="file" name="openAccountFile" id="openAccountFile"
                                   placeholder="开户许可证照片" data-rule-fileSize="********"
                                   data-msg-fileSize="上传文件应小于10MB" required>
                        </div>

                        <div class="form-group line-group col-md-12">
                            <label for="remark">备注</label>
                            <input class="form-control" type="text" name="remark" placeholder="备注"
                                   maxlength="255">
                        </div>


                        <div class="col-md-12">
                            <button type="button" class="btn btn-primary" onclick="formDataSub()"
                                    style="width:70px;margin-right:5px;">保 存
                            </button>
                            <button type="button" class="btn btn-default" onclick="history.back()" style="width:70px">返
                                回
                            </button>
                        </div>

                    </form>

                </div>
            </div>
        </div>

    </div>
</th:block>

<th:block layout:fragment="page-script">
    <script th:src="@{/webjars/select2/js/select2.min.js}"></script>
    <script th:src="@{/webjars/select2/js/i18n/zh-CN.js}"></script>
    <!--日期选择插件-->
    <script th:src="@{/webjars/light-year-admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js}"></script>
    <script th:src="@{/webjars/light-year-admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js}"></script>

    <script th:src="@{/webjars/jquery-validation/dist/jquery.validate.min.js}"></script>
    <script th:src="@{/webjars/jquery-validation/dist/localization/messages_zh.min.js}"></script>
    <script th:src="@{/js/common/jq-validation-bs.js}"></script>
    <script th:src="@{/js/common/sub.js}"></script>
    <script th:src="@{/js/revenue/settings/pay/sxy-config.js}"></script>
</th:block>

</body>
</html>