<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security"
      layout:decorate="~{layout/layout}">
<head>
    <meta charset="UTF-8">
    <title>支付参数配置</title>
    <link rel="stylesheet" th:href="@{/webjars/light-year-admin/js/jconfirm/jquery-confirm.min.css}">
    <link rel="stylesheet" th:href="@{/webjars/bootstrap-table/dist/bootstrap-table.min.css}">

    <link rel="stylesheet" th:href="@{/css/select2.min.css}">
    <link rel="stylesheet" th:href="@{/css/ly-select2.css}">
    <link rel="stylesheet" th:href="@{/css/reset-style.css}">
    <style>
        body {
            margin: 0 -7px;
        }

        .line-lable {
            display: flex;
            align-items: center;
        }

        .line-lable > hr:nth-of-type(1) {
            width: 10px;
            background: #ccc;
        }

        .line-lable div {
            padding: 0 5px;
            font-size: 15px;
        }

        .line-lable > hr:nth-of-type(2) {
            width: 100%;
            flex: 1;
            background: #ccc;
        }
        #back-button{
            padding: 0 0 15px!important;
        }
    </style>
</head>
<body>

<th:block layout:fragment="page-content">
    <th:block th:replace="fragments/back-button :: back-button"/>
    <div class="card table-card-30">
        <div class="card-body">

            <ul class="nav nav-tabs nav-justified" style="width: 650px;">
                <li class="active" sec:authorize-url="/revenue/settings/pay/pageWxConfig">
                    <a data-toggle="tab" href="#wx-pay">微信支付参数配置</a>
                </li>
                <li class="nav-item" sec:authorize-url="/revenue/settings/pay/pageAliConfig">
                    <a data-toggle="tab" href="#ali-pay">支付宝支付参数配置</a>
                </li>
                <li class="nav-item" sec:authorize-url="/revenue/settings/pay/pageAbchinaPayConfig">
                    <a data-toggle="tab" href="#abchina-pay">农行支付参数配置</a>
                </li>

                <li class="nav-item" sec:authorize-url="/revenue/settings/pay/pageSxyPayConfig">
                    <a data-toggle="tab" href="#sxy-pay">首信易支付参数配置</a>
                </li>
            </ul>

            <div class="tab-content" style="padding: 0 12px 10px">
                <div class="tab-pane" id="wx-pay">
                    <div class="row">
                        <div class="col-lg-12">
                            <div style="margin-top:15px ">
                                <div id="toolbar" class="toolbar-btn-action">


                                    <a class="btn btn-primary m-r-5" style="float: left" onclick="addWxConfig()"
                                       sec:authorize-url="/revenue/settings/pay/addWxConfig**"><i class="mdi mdi-plus"></i>
                                        新增</a>

                                    <form id="tableFilter" style="float: left" class="form-inline" role="form"
                                          onsubmit="return false;">
                                        <div class="form-group">
                                            <div class="input-group">
                                                <span class="input-group-addon">公众号appId</span>
                                                <input style="width: 187px;" type="text" class="form-control table-filter"
                                                       data-filter="appId" data-operator="like" data-ignorecase="false"
                                                       placeholder="请输入appId"/>
                                            </div>
                                        </div>
                                        <div class="form-group filter-btn-group">
                                            <button class="btn btn-primary" type="button" onclick="$('#table').reloadTable();">查询
                                            </button>
                                        </div>
                                    </form>

                                    <input type="hidden" id="hasWxConfig" value="true" sec:authorize-url="/revenue/settings/pay/pageWxConfig">
                                    <input type="hidden" id="hasWxConfigEdit" value="true" sec:authorize-url="/revenue/settings/pay/editWxConfig**">
                                    <input type="hidden" id="hasWxConfigDel" value="true" sec:authorize-url="/revenue/settings/pay/deleteWxConfig**">
                                </div>

                                <table id="table"
                                       class="stripe-table f-table-r"
                                       data-icons-prefix="mdi"
                                       data-icons="icons"
                                       data-url="pageWxConfig"
                                       data-sort-order="desc"
                                       data-sort-name="id"
                                       data-id-field="id"
                                       data-unique-id="id"
                                       data-striped="true"
                                       data-cache="false"
                                       data-method="post"
                                       data-show-columns="true"
                                       data-show-refresh="true"
                                       data-pagination="true"
                                       data-side-pagination="server"
                                       data-data-field="content"
                                       data-total-field="totalElements"
                                       data-page-list="[20, 50, 100, 200]" data-page-size="20"
                                       data-detail-view="true"
                                       data-detail-formatter="detailFormatter">
                                    <thead>
                                    <tr>
                                        <th data-field="companyId" >公司名称</th>
                                        <th data-field="enablePay" data-formatter="enableFormatter" data-sortable="true">微信支付能力</th>
                                        <th data-field="mchId">微信商户号</th>
                                        <th data-field="appId">公众号appid</th>
                                        <th data-field="enableMsg" data-formatter="enableFormatter" data-sortable="true">公众号模板消息</th>
                                        <th data-field="createdDate" data-sortable="true" data-width="180">创建时间</th>
                                        <th data-field="operate" data-align="center" data-width="100"
                                            data-click-to-select="false" data-formatter="wxConfigOperateFormatter"
                                            data-events="operateEvents">操作
                                        </th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-pane" id="ali-pay">
                    <div class="row">
                        <div class="col-lg-12">
                            <div style="margin-top:15px ">
                                <div id="toolbarAli" class="toolbar-btn-action">
                                    <a class="btn btn-primary m-r-5" style="float: left" onclick="addAliConfig()"
                                       sec:authorize-url="/revenue/settings/pay/addAliConfig**"><i class="mdi mdi-plus"></i>
                                        新增</a>

                                    <input type="hidden" id="hasAliConfig" value="true" sec:authorize-url="/revenue/settings/pay/pageAliConfig">
                                    <input type="hidden" id="hasAliConfigEdit" value="true" sec:authorize-url="/revenue/settings/pay/editAliConfig**">
                                    <input type="hidden" id="hasAliConfigDel" value="true" sec:authorize-url="/revenue/settings/pay/deleteAliConfig**">
                                </div>

                                <table id="tableAli"
                                       class="stripe-table f-table-r"
                                       data-icons-prefix="mdi"
                                       data-icons="icons"
                                       data-url="pageAliConfig"
                                       data-sort-order="desc"
                                       data-sort-name="id"
                                       data-id-field="id"
                                       data-unique-id="id"
                                       data-striped="true"
                                       data-cache="false"
                                       data-method="post"
                                       data-show-columns="true"
                                       data-show-refresh="true"
                                       data-pagination="true"
                                       data-side-pagination="server"
                                       data-data-field="content"
                                       data-total-field="totalElements"
                                       data-page-list="[20, 50, 100, 200]" data-page-size="20"
                                       data-detail-view="true"
                                       data-detail-formatter="aliDetailFormatter">
                                    <thead>
                                    <tr>
                                        <th data-field="companyId" >公司名称</th>
                                        <th data-field="enable" data-formatter="enableFormatter" data-sortable="true">支付宝线上扫码支付能力</th>
                                        <th data-field="appId">网页应用appId</th>
                                        <th data-field="gatewayHost">网关域名</th>
                                        <th data-field="encryptKey">AES密钥</th>
                                        <th data-field="enableLifePay" data-formatter="enableFormatter" data-sortable="true">支付宝生活缴费能力</th>
                                        <th data-field="acctOrgNo">机构编号</th>
                                        <th data-field="sftpHost">sftp地址</th>
                                        <th data-field="sftpPort">sftp端口</th>
                                        <th data-field="createdDate" data-sortable="true" data-width="180">创建时间</th>
                                        <th data-field="operate" data-align="center" data-width="100"
                                            data-click-to-select="false" data-formatter="aliConfigOperateFormatter"
                                            data-events="operateEvents">操作
                                        </th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-pane" id="abchina-pay">
                    <div class="row">
                        <div class="col-lg-12">
                            <div style="margin-top:15px ">
                                <div id="toolbarAbc" class="toolbar-btn-action">
                                    <a class="btn btn-primary m-r-5" style="float: left" onclick="addAbcConfig()"
                                       sec:authorize-url="/revenue/settings/pay/addAbchinaPayConfig**"><i class="mdi mdi-plus"></i>
                                        新增</a>

                                    <form id="abcTableFilter" style="float: left" class="form-inline" role="form"
                                          onsubmit="return false;">
                                        <div class="form-group">
                                            <div class="input-group">
                                                <span class="input-group-addon">公众号appId</span>
                                                <input style="width: 187px;" type="text" class="form-control table-filter"
                                                       data-filter="appId" data-operator="like" data-ignorecase="false"
                                                       placeholder="请输入appId"/>
                                            </div>
                                        </div>
                                        <div class="form-group filter-btn-group">
                                            <button class="btn btn-primary" type="button" onclick="$('#tableAbc').reloadTable();">查询
                                            </button>
                                        </div>
                                    </form>

                                    <input type="hidden" id="hasAbchinaPayConfig" value="true" sec:authorize-url="/revenue/settings/pay/pageAbchinaPayConfig">
                                    <input type="hidden" id="hasAbchinaPayConfigEdit" value="true" sec:authorize-url="/revenue/settings/pay/editAbchinaPayConfig**">
                                    <input type="hidden" id="hasAbchinaPayConfigDel" value="true" sec:authorize-url="/revenue/settings/pay/deleteAbchinaPayConfig**">
                                </div>
                                <table id="tableAbc"
                                       class="stripe-table f-table-r"
                                       data-icons-prefix="mdi"
                                       data-icons="icons"
                                       data-url="pageAbchinaPayConfig"
                                       data-sort-order="desc"
                                       data-sort-name="id"
                                       data-id-field="id"
                                       data-unique-id="id"
                                       data-striped="true"
                                       data-cache="false"
                                       data-method="post"
                                       data-show-columns="true"
                                       data-show-refresh="true"
                                       data-pagination="true"
                                       data-side-pagination="server"
                                       data-data-field="content"
                                       data-total-field="totalElements"
                                       data-page-list="[20, 50, 100, 200]" data-page-size="20"
                                       data-detail-view="true"
                                       data-detail-formatter="abcDetailFormatter">
                                    <thead>
                                    <tr>
                                        <th data-field="companyId">公司名称 </th>
                                        <th data-field="enable" data-formatter="enableFormatter" data-sortable="true">农行支付能力</th>
                                        <th data-field="appId">公众号appId</th>
                                        <th data-field="merchantId">商户id</th>
                                        <th data-field="merchantCertPassword">商户证书密码</th>
                                        <th data-field="payResultNotifyUrl">通知回调地址</th>
                                        <th data-field="createdDate" data-sortable="true" data-width="180">创建时间</th>
                                        <th data-field="operate" data-align="center" data-width="100"
                                            data-click-to-select="false" data-formatter="abcConfigOperateFormatter"
                                            data-events="operateEvents">操作
                                        </th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-pane" id="sxy-pay">
                    <div class="row">
                        <div class="col-lg-12">
                            <div style="margin-top:15px ">
                                <div id="toolbarshouxinyi" class="toolbar-btn-action">
                                    <a class="btn btn-primary m-r-5" style="float: left" onclick="addSxyConfig()"
                                       sec:authorize-url="/revenue/settings/pay/addSxyPayConfig**"><i class="mdi mdi-plus"></i>
                                        新增</a>

                                    <form id="sxyTableFilter" style="float: left" class="form-inline" role="form"
                                          onsubmit="return false;">
                                        <div class="form-group">
                                            <div class="input-group">
                                                <span class="input-group-addon">公众号appId</span>
                                                <input style="width: 187px;" type="text" class="form-control table-filter"
                                                       data-filter="appId" data-operator="like" data-ignorecase="false"
                                                       placeholder="请输入appId"/>
                                            </div>
                                        </div>
                                        <div class="form-group filter-btn-group">
                                            <button class="btn btn-primary" type="button" onclick="$('#tableSxy').reloadTable();">查询
                                            </button>
                                        </div>
                                    </form>

                                    <div style="display: flex ;float: left; margin-left: 20px;">
                                        <button sec:authorize-url="/revenue/settings/pay/sxy/pageSxySplitMerchantConfig" class="btn btn-success mar_r_10" type="button" onclick="showList('listSxySplitMerchantConfig')">分成商户管理
                                        </button>
                                        <button sec:authorize-url="/revenue/settings/pay/pageSxySplitMerchantConfig**" class="btn btn-info mar_r_10" type="button" onclick="showList('listSxySplitOrderRecord')">分账记录查询
                                        </button>
                                        <button sec:authorize-url="/revenue/settings/pay/pageSxySplitMerchantConfig**" class="btn btn-cyan" type="button" onclick="showList('listSxyWithdrawRecord')">提现记录查询
                                        </button>
                                    </div>

                                    <input type="hidden" id="hasSxyPayConfig" value="true" sec:authorize-url="/revenue/settings/pay/pageSxyPayConfig">
                                    <input type="hidden" id="hasSxyPayConfigEdit" value="true" sec:authorize-url="/revenue/settings/pay/editSxyPayConfig**">
                                    <input type="hidden" id="hasSxyPayConfigDel" value="true" sec:authorize-url="/revenue/settings/pay/deleteSxyPayConfig**">
                                    <input type="hidden" id="hasSxyConfigSync" value="true" sec:authorize-url="/revenue/settings/pay/queryAndSyncSubMerchantStatus**">
                                    <input type="hidden" id="hasSxyAllCashWithdraw" value="true" sec:authorize-url="/revenue/settings/pay/sxyWithdraw**">
                                    <input type="hidden" id="pageSxyRevenueSplitConfig" value="true" sec:authorize-url="/revenue/settings/pay/sxy/pageSxyRevenueSplitConfig**">

                                </div>
                                <table id="tableSxy"
                                       class="stripe-table f-table-r"
                                       data-icons-prefix="mdi"
                                       data-icons="icons"
                                       data-url="pageSxyPayConfig"
                                       data-sort-order="desc"
                                       data-sort-name="id"
                                       data-id-field="id"
                                       data-unique-id="id"
                                       data-striped="true"
                                       data-cache="false"
                                       data-method="post"
                                       data-show-columns="true"
                                       data-show-refresh="true"
                                       data-pagination="true"
                                       data-side-pagination="server"
                                       data-data-field="content"
                                       data-total-field="totalElements"
                                       data-page-list="[20, 50, 100, 200]" data-page-size="20"
                                       data-detail-view="true"
                                       data-detail-formatter="sxyDetailFormatter">
                                    <thead>
                                    <tr>
                                        <th data-field="companyId">公司名称 </th>
                                        <th data-field="enable" data-formatter="enableFormatter" data-sortable="true">首信易支付能力</th>
                                        <th data-field="appId" >公众号appId</th>
                                        <th data-field="merchantType"  data-formatter="merchantTypeFormatter">商户类型</th>
                                        <th data-field="requestId">请求ID</th>
                                        <th data-field="merchantId">商户编号</th>
                                        <th data-field="availableBalance">可提现金额(元)</th>
                                        <th data-field="subMerchantReviewStatus" data-formatter="subMerchantReviewStatusFormatter">入网状态</th>
                                        <th data-field="createdDate" data-sortable="true" data-width="180">创建时间</th>
                                        <th data-field="operate" data-align="center" data-width="100"
                                            data-click-to-select="false" data-formatter="sxyConfigOperateFormatter"
                                            data-events="operateEvents">操作
                                        </th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mode mode-list">
        <div class="card" style="top: 0; left: 0; position: absolute; width: 100%; height: 100%;">
            <iframe id="iframe-list" class="tab-pane active" width="100%" height="100%" frameborder="0" src="" style="height: 100%;"></iframe>
        </div>
    </div>
</th:block>

<th:block layout:fragment="page-script">
    <script th:src="@{/webjars/select2/js/select2.min.js}"></script>
    <script th:src="@{/webjars/select2/js/i18n/zh-CN.js}"></script>
    <script th:src="@{/webjars/jsrender/jsrender.min.js}"></script>
    <script id="detailViewTmpl" type="text/x-jsrender">
    <table class="table table-bordered">
      <tbody>
        <tr>
          <th style="width: 140px;">创建人:</th>
          <td>{{:createdBy}}</td>

          <th style="width: 140px;">修改人:</th>
          <td>{{:lastModifiedBy}}</td>

          <th style="width: 140px;">更新时间:</th>
          <td>{{:lastModifiedDate}}</td>
        </tr>

        <tr>
          <th style="width: 140px;">充值成功通知消息编码:</th>
          <td colspan="2">{{:rechargeSuccessMsg}}</td>

          <th style="width: 140px;">缴费成功通知消息编码:</th>
          <td colspan="2">{{:paySuccessMsg}}</td>
        </tr>

        <tr>
          <th style="width: 140px;">结算账单通知消息编码:</th>
          <td colspan="2">{{:billPaymentRemindMsg}}</td>

          <th style="width: 140px;">开阀成功通知消息编码:</th>
          <td colspan="2">{{:openValveMsg}}</td>
        </tr>

        <tr>
          <th style="width: 140px;">预存关阀通知消息编码:</th>
          <td colspan="2">{{:closeValveMsg}}</td>

          <th style="width: 140px;">余额预警通知消息编码:</th>
          <td colspan="2">{{:balanceWarnNotifyMsg}}</td>
        </tr>

        <tr>
          <th style="width: 140px;">在线开户申请结果通知消息编码:</th>
          <td colspan="2">{{:openAccountNotifyMsg}}</td>
          {{if sendStartHour != null}}
          <th style="width: 140px;">推送消息时间段:</th>
          <td>{{:sendStartHour}}-{{:sendEndHour}}</td>
          {{/if}}
        </tr>

        <tr>
          <th style="width: 140px;">公众号最低起充金额:</th>
          <td>{{:minAmount}}</td>
          <th style="width: 140px;">公众号最大充值金额:</th>
          <td>{{:maxAmount}}</td>
        </tr>
      </tbody>
    </table>

    </script>

    <script id="aliDetailViewTmpl" type="text/x-jsrender">
    <table class="table table-bordered">
      <tbody>
        <tr>
          <th style="width: 140px;">创建人:</th>
          <td>{{:createdBy}}</td>

          <th style="width: 140px;">修改人:</th>
          <td>{{:lastModifiedBy}}</td>

          <th style="width: 140px;">更新时间:</th>
          <td>{{:lastModifiedDate}}</td>
        </tr>
        <tr>
          <th style="width: 140px;">AES密钥:</th>
          <td colspan="5">{{:encryptKey}}</td>
        </tr>

        <tr>
          <th style="width: 140px;">应用私钥:</th>
          <td colspan="5">
            <div>{{:merchantPrivateKey}}</div>
          </td>
        </tr>

        <tr>
          <th style="width: 140px;">支付宝根证书文件路径:</th>
          <td colspan="2">{{:alipayRootCertPath}}</td>

          <th style="width: 140px;">支付宝公钥证书文件路径:</th>
          <td colspan="2">{{:alipayCertPath}}</td>
        </tr>

        <tr>
          <th style="width: 140px;">应用公钥证书文件路径:</th>
          <td colspan="2">{{:merchantCertPath}}</td>

          <th style="width: 140px;">异步通知回调地址:</th>
          <td colspan="2">{{:notifyUrl}}</td>
        </tr>

        <tr>
          <th style="width: 140px;">sftp用户名:</th>
          <td colspan="1">{{:sftpUserName}}</td>

          <th style="width: 140px;">sftp下载路径:</th>
          <td colspan="3">{{:sftpDownloadPath}}</td>
        </tr>
      </tbody>
    </table>

    </script>

    <script id="abcDetailViewTmpl" type="text/x-jsrender">
    <table class="table table-bordered">
      <tbody>
        <tr>
          <th style="width: 140px;">创建人:</th>
          <td>{{:createdBy}}</td>

          <th style="width: 140px;">修改人:</th>
          <td>{{:lastModifiedBy}}</td>

          <th style="width: 140px;">更新时间:</th>
          <td>{{:lastModifiedDate}}</td>
        </tr>

        <tr>
          <th style="width: 140px;">线上支付证书路径:</th>
          <td colspan="2">{{:trustPayCertFilePath}}</td>

          <th style="width: 140px;">商户证书文件路径:</th>
          <td colspan="2">{{:merchantCertFilePath}}</td>
        </tr>
      </tbody>
    </table>

    </script>

    <script id="sxyDetailViewTmpl" type="text/x-jsrender">
    <table class="table table-bordered">
      <tbody>
        <tr>
          <th style="width: 140px;">创建人:</th>
          <td>{{:createdBy}}</td>

          <th style="width: 140px;">修改人:</th>
          <td>{{:lastModifiedBy}}</td>
        </tr>
        <tr>
          <th style="width: 140px;">审核备注:</th>
          <td>{{:subMerchantReviewRemarks}}</td>

          <th style="width: 140px;">更新时间:</th>
          <td>{{:lastModifiedDate}}</td>
        </tr>

      </tbody>
    </table>

    </script>

    <!--对话框-->
    <script th:src="@{/webjars/light-year-admin/js/jconfirm/jquery-confirm.min.js}"></script>
    <script th:src="@{/js/common/confirm-extend.js}"></script>
    <script th:src="@{/webjars/bootstrap-table/dist/bootstrap-table.min.js}"></script>
    <script th:src="@{/webjars/bootstrap-table/dist/locale/bootstrap-table-zh-CN.min.js}"></script>
    <script th:src="@{/js/common/bootstrap-table-custom.js}"></script>
    <script th:src="@{/js/common/query-filter.js}"></script>
    <script th:src="@{/js/common/enum-formatter.js}"></script>

    <script th:src="@{/js/revenue/settings/pay/list.js}"></script>
</th:block>

</body>
</html>