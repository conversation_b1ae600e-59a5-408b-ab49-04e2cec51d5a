<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/layout}">
<head>
    <meta charset="UTF-8">
    <title>编辑支付宝参数配置</title>
    <link rel="stylesheet" th:href="@{/webjars/select2/css/select2.min.css}">
    <link rel="stylesheet" th:href="@{/css/ly-select2.css}">
    <link rel="stylesheet" th:href="@{/css/reset-style.css}">
    <style>
        .cancel-button {
            display: inline-block;
            position: absolute;
            right: 80px;
            bottom: 5px;
        }
    </style>
</head>
<body>

<th:block layout:fragment="page-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="card table-card-30">
                <div class="card-header"><h4><div  class="back-button" onclick="history.back()" data-original-title="返回" >
                    <span class="mdi mdi-chevron-left"></span>返回</div><span class="divide-line">|</span>编辑支付宝参数配置</h4></div>
                <div class="card-body" style="overflow: auto">

                    <form id="form" th:action="@{/revenue/settings/pay/editAliConfig}" method="post" th:object="${aliConfig}">
                        <input type="hidden" th:field="*{id}">
                        <div class="form-group line-group col-md-12">
                            <label for="companyId"><span class="text-danger">*</span>&nbsp;公司</label>
                            <input class="form-control" type="text" th:field="*{companyId}" readonly>
                        </div>

                        <div class="my-line-label" style="float: left;width: 100%">
                            <hr class="hr1"><div>线上扫码支付相关参数</div><hr class="hr2">
                        </div>
                        <div class="form-group line-group col-md-12">
                            <label for="enable"><span class="text-danger">*</span>是否启用支付宝在线扫码支付
                            </label>
                            <label style="vertical-align: sub;margin-left: 10px;" class="lyear-switch switch-primary switch-solid">
                                <input type="checkbox" th:field="*{enable}">
                                <span></span>
                            </label>
                        </div>
                        <div class="form-group line-group col-md-6">
                            <label for="appId">网页应用appId</label>
                            <input class="form-control enable-online" type="text" th:field="*{appId}" th:required="*{enable}" placeholder="网页应用appId" maxlength="64">
                        </div>
                        <div class="form-group line-group col-md-6">
                            <label for="gatewayHost">网关域名</label>
                            <input class="form-control enable-online" type="text" th:field="*{gatewayHost}" th:required="*{enable}" placeholder="网关域名" maxlength="64">
                        </div>
                        <div class="form-group line-group col-md-12">
                            <label for="merchantPrivateKey">应用私钥</label>
                            <textarea th:field="*{merchantPrivateKey}" class="form-control enable-online" th:required="*{enable}" rows="5" placeholder="应用私钥" maxlength="2048"></textarea>
                        </div>
                        <div class="form-group line-group col-md-6">
                            <label for="encryptKey">AES密钥</label>
                            <input class="form-control enable-online" type="text" th:field="*{encryptKey}" th:required="*{enable}" placeholder="AES密钥" maxlength="64">
                        </div>
                        <div class="form-group line-group col-md-6">
                            <label>支付宝根证书文件路径</label>
                            <input id="alipayRootCertPathText" onfocus="$(this).hide();$('#alipayRootCertFile').show();$('#alipayRootCertPathButton').show();" class="form-control enable-online" type="text" th:value="*{alipayRootCertPathFileName}" th:required="*{enable}">
                            <input style="display: none;" class="form-control" type="file" name="alipayRootCertFile" id="alipayRootCertFile" placeholder="支付宝根证书" data-rule-fileSize="1048576" data-msg-fileSize="上传文件应小于1MB">
                            <button class="cancel-button" type="button" id="alipayRootCertPathButton" style="display: none;" onclick="$(this).hide();$('#alipayRootCertFile').hide();$('#alipayRootCertPathText').show();$('#alipayRootCertFile-error').hide();">取消</button>
                        </div>
                        <div class="form-group line-group col-md-6">
                            <label>支付宝公钥证书文件路径</label>
                            <input id="alipayCertPathText" onfocus="$(this).hide();$('#alipayCertPathFile').show();$('#alipayCertPathButton').show();" class="form-control enable-online" type="text" th:value="*{alipayCertPathFileName}" th:required="*{enable}">
                            <input style="display: none;" class="form-control" type="file" name="alipayCertPathFile" id="alipayCertPathFile" placeholder="支付宝公钥证书" data-rule-fileSize="1048576" data-msg-fileSize="上传文件应小于1MB">
                            <button class="cancel-button" type="button" id="alipayCertPathButton" style="display: none;" onclick="$(this).hide();$('#alipayCertPathFile').hide();$('#alipayCertPathText').show();$('#alipayCertPathFile-error').hide();">取消</button>
                        </div>
                        <div class="form-group line-group col-md-6">
                            <label>应用公钥证书文件路径</label>
                            <input id="merchantCertPathText" onfocus="$(this).hide();$('#merchantCertPathFile').show();$('#merchantCertPathButton').show();" class="form-control enable-online" type="text" th:value="*{merchantCertPathFileName}" th:required="*{enable}">
                            <input style="display: none;" class="form-control" type="file" name="merchantCertPathFile" id="merchantCertPathFile" placeholder="应用公钥证书" data-rule-fileSize="1048576" data-msg-fileSize="上传文件应小于1MB">
                            <button class="cancel-button" type="button" id="merchantCertPathButton" style="display: none;" onclick="$(this).hide();$('#merchantCertPathFile').hide();$('#merchantCertPathText').show();$('#merchantCertPathFile-error').hide();">取消</button>
                        </div>
                        <div class="form-group line-group col-md-6">
                            <label for="notifyUrl">异步通知回调地址</label>
                            <input class="form-control enable-online" type="text" th:field="*{notifyUrl}" th:required="*{enable}" placeholder="异步通知回调地址" maxlength="1024">
                        </div>

                        <div class="my-line-label" style="float: left;width: 100%">
                            <hr class="hr1"><div>生活缴费相关参数</div><hr class="hr2">
                        </div>
                        <div class="form-group line-group col-md-12">
                            <label for="enableLifePay"><span class="text-danger">*</span>是否启用支付宝生活缴费
                            </label>
                            <label style="vertical-align: sub;margin-left: 10px;" class="lyear-switch switch-primary switch-solid">
                                <input type="checkbox" th:field="*{enableLifePay}">
                                <span></span>
                            </label>
                        </div>
                        <div class="form-group line-group col-md-6">
                            <label for="acctOrgNo">接入机构编号</label>
                            <input class="form-control enable-life" type="text" th:field="*{acctOrgNo}" th:required="*{enableLifePay}" placeholder="接入机构编号" maxlength="16">
                        </div>
                        <div class="form-group line-group col-md-6">
                            <label for="sftpHost">sftp地址</label>
                            <input class="form-control enable-life" type="text" th:field="*{sftpHost}" th:required="*{enableLifePay}" placeholder="sftp地址" maxlength="15">
                        </div>
                        <div class="form-group line-group col-md-6">
                            <label for="sftpPort">sftp端口</label>
                            <input class="form-control enable-life" type="number" th:field="*{sftpPort}" th:required="*{enableLifePay}" placeholder="sftp端口" maxlength="15">
                        </div>
                        <div class="form-group line-group col-md-6">
                            <label for="sftpUserName">sftp用户名</label>
                            <input class="form-control enable-life" type="text" th:field="*{sftpUserName}" th:required="*{enableLifePay}" placeholder="sftp用户名" maxlength="32">
                        </div>
                        <div class="form-group line-group col-md-6">
                            <label for="sftpPassword">sftp密码</label>
                            <input class="form-control enable-life" type="text" th:field="*{sftpPassword}" th:required="*{enableLifePay}" placeholder="sftp密码" maxlength="32">
                        </div>
                        <div class="form-group line-group col-md-6">
                            <label for="sftpDownloadPath">sftp下载路径</label>
                            <input class="form-control enable-life" type="text" th:field="*{sftpDownloadPath}" th:required="*{enableLifePay}" placeholder="sftp下载路径" maxlength="128">
                        </div>

                        <div class="col-md-12">
                            <button type="button" class="btn btn-primary" onclick="formDataSub()" style="width:70px;margin-right:5px;">保 存
                            </button>
                            <button type="button" class="btn btn-default" onclick="history.back()" style="width:70px">返 回</button>
                        </div>
                    </form>

                </div>
            </div>
        </div>

    </div>
</th:block>

<th:block layout:fragment="page-script">
    <script th:src="@{/webjars/select2/js/select2.min.js}"></script>
    <script th:src="@{/webjars/select2/js/i18n/zh-CN.js}"></script>
    <script th:src="@{/webjars/jquery-validation/dist/jquery.validate.min.js}"></script>
    <script th:src="@{/webjars/jquery-validation/dist/localization/messages_zh.min.js}"></script>
    <script th:src="@{/js/common/jq-validation-bs.js}"></script>
    <script th:src="@{/js/common/sub.js}"></script>
    <script th:src="@{/js/revenue/settings/pay/ali-config.js}"></script>
</th:block>

</body>
</html>