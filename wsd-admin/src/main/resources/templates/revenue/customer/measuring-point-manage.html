<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security"
      layout:decorate="~{layout/layout}">
<head>
    <meta charset="UTF-8">
    <title>计量点管理</title>

    <!--对话框-->
    <link rel="stylesheet" th:href="@{/webjars/light-year-admin/js/jconfirm/jquery-confirm.min.css}">
    <link rel="stylesheet" th:href="@{/webjars/bootstrap-table/dist/bootstrap-table.min.css}">
    <link rel="stylesheet" th:href="@{/webjars/select2/css/select2.min.css}">
    <link rel="stylesheet" th:href="@{/css/alifont/iconfont.css}">
    <link rel="stylesheet" th:href="@{/webjars/jstree/themes/default/style.min.css}">
    <link rel="stylesheet" th:href="@{/webjars/bootstrap-daterangepicker/daterangepicker.css}">
    <link rel="stylesheet" th:href="@{/webjars/light-year-admin/js/bootstrap-datepicker/bootstrap-datepicker3.min.css}">
    <link rel="stylesheet" th:href="@{/css/ly-select2.css}">
    <link rel="stylesheet" th:href="@{/css/reset-style.css}">
    <style>

        .mode2 .card2 {
            top: 0;
            left: 0;
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .mode2.show .card2 {
            animation: modeshow 0.25s;
        }

        .mode3 .card3 {
            top: 0;
            left: 0;
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .mode3.show .card3 {
            animation: modeshow 0.25s;
        }


        td .btn-xs {
            border-radius: 3px !important;
        }

        .table-card-30 .bootstrap-table .fixed-table-body {
            max-height: calc(100vh - 198px);
        }

        .content-td {
            display: flex;
            width: 100%;
            cursor: pointer;
            color: #1eb6fe;
        }

        .mode.show .card {
            animation: modeshow 0.4s;
        }

        .model .card-body {
            padding: 10px 0 15px;
        }

        .mode .card-body .list {
            margin-top: 12px;
            max-height: calc(100vh - 300px);
            overflow-y: auto;
            padding: 2px;
        }

        .mode .card-body .list .list-item {
            position: relative;
            margin-bottom: 5px;
            background: #fff;
            color: #000;
            padding: 0px 12px 0px 32px;
            font-size: 14px;
            display: block;
            cursor: pointer;
            font-weight: unset;
            border: 1px solid #d1d1d1;
            border-radius: 5px;
        }

        .mode .list .list-item .item-name {
            display: block;
            line-height: 32px;
            border-left: 1px solid #eee;
            padding-left: 5px;
            min-width: 280px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .mode .card-body .list .list-item input {
            visibility: hidden;
            position: absolute;
        }

        .mode .card-body .list .list-item .point {
            position: absolute;
            top: 9px;
            left: 9px;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            border: 2px solid #d1d1d1;
            box-shadow: 0 0 2px 0 #5c5c5c;
        }

        .mode .card-body .list .list-item input:checked + .point {
            background: #4fded0;
            border-color: #d1d1d1;
        }

        .mode .card-body .list .list-item:hover {
            /*background: #4fded0;;*/
            color: #4fded0;
        }

        .mode .card-footer {
            text-align: right;
        }

    </style>
</head>
<body>

<th:block layout:fragment="page-content">

    <div class="row" style="display: flex;position: relative">

        <!--小区树-->
        <th:block th:replace="fragments/community-tree :: community-tree"/>

        <div class="tree-page-card">
            <div class="card " id="search-card">
                <div class="card-body pd-t-5">

                    <form id="tableFilter" class="form-inline" role="form" onsubmit="return false;">
                        <input type="hidden" id="userName" th:value="${tollCollectorName}"/>
                        <input class="form-control table-filter" type="hidden" onchange="$('#table').reloadTable();"  data-filter="community.region.id" id="regionId" data-operator="eq">
                        <input class="form-control table-filter" data-unexpand="true" type="hidden" onchange="$('#table').reloadTable();"  data-filter="community.id" id="communityId" data-operator="eq">
                        <input class="form-control table-filter" type="hidden" data-filter="lowerCommunity.layerCode" id="lowerCommunityLayerCode" data-operator="startLike">

                        <div class="default-filter-wrap">
                            <div class="default-wrap">
                                <div class="form-group mar_t">
                                    <div class="input-group customer-search-group">
                                        <div class="input-group-btn search-bar customer">
                                            <input type="hidden" name="search_field" id="search-field" value="mobile">
                                            <button class="btn btn-default dropdown-toggle" id="search-btn"
                                                    data-toggle="dropdown" type="button" aria-haspopup="true"
                                                    aria-expanded="false">
                                                手机号 <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li data-field="customerMobile">手机号</li>
                                                <li data-field="customerIdCard">身份证</li>
                                                <li data-field="customerName">客户名</li>
                                            </ul>
                                        </div>
                                        <input type="text" class="form-control table-filter radius-r-4" id="customerMobile"
                                               data-filter="measuringPoint.customer.mobile" data-operator="like" data-ignorecase="false"
                                               placeholder="请输入手机号"/>
                                        <input type="text" class="form-control table-filter radius-r-4"
                                               style="display: none;" id="customerIdCard" data-filter="measuringPoint.customer.idCard"
                                               data-operator="like" data-ignorecase="false" placeholder="请输入身份证"/>
                                        <input type="text" class="form-control table-filter radius-r-4"
                                               style="display: none;" id="customerName" data-filter="measuringPoint.customer.name" data-operator="like"
                                               data-ignorecase="false" placeholder="请输入客户名称"/>
                                    </div>
                                </div>

                                <div class="form-group mar_t">
                                    <div class="input-group customer-search-group">
                                        <div class="input-group-btn search-bar meter">
                                            <input type="hidden" name="search_field" value="meterCodes">
                                            <button class="btn btn-default dropdown-toggle" id="search-btn-meter"
                                                    data-toggle="dropdown" type="button" aria-haspopup="true"
                                                    aria-expanded="false">
                                                表号 <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li data-field="meterCode">表号</li>
                                                <li data-field="accountNo">户号</li>
                                                <li data-field="name">计量点名称</li>
                                                <li data-field="linkCode">关联编号</li>
                                                <li data-field="roomNumber">房号</li>
                                            </ul>
                                        </div>
                                        <input type="text" class="form-control table-filter radius-r-4" id="name"
                                               data-filter="measuringPoint.name" data-operator="like" data-ignorecase="false"
                                               style="display: none;" placeholder="请输入计量点名称"/>
                                        <input type="text" class="form-control table-filter radius-r-4"
                                               id="meterCode" data-filter="code"
                                               data-operator="like" data-ignorecase="false" placeholder="请输入表号"/>
                                        <input type="text" class="form-control table-filter radius-r-4"
                                               style="display: none;" id="accountNo" data-filter="measuringPoint.accountNo" data-operator="like"
                                               data-ignorecase="false" placeholder="请输入户号"/>
                                        <input type="text" class="form-control table-filter radius-r-4"
                                               style="display: none;" id="linkCode" data-filter="measuringPoint.linkCode" data-operator="like"
                                               data-ignorecase="false" placeholder="请输入关联编号"/>
                                        <input type="text" class="form-control table-filter radius-r-4" id="roomNumber"
                                               data-filter="measuringPoint.roomNumber" data-operator="like"
                                               data-ignorecase="false" style="display: none;"
                                               placeholder="请输入房号"/>
                                    </div>
                                </div>

                                <div class="form-group mar_t">
                                    <div class="input-group">
                                        <span class="input-group-addon">开户状态</span>
                                        <select style="width: 158px;" class="form-control select2 select2-form-inline"
                                                id="openAccount-select" onchange="changeOpenAccount()">
                                            <option value="">全部状态</option>
                                            <option value="1">已开户</option>
                                            <option value="2">已销户</option>
                                        </select>
                                    </div>
                                    <input type="hidden" data-filter="measuringPoint.accountNo" id="accountNoInput"
                                           value="true"/>
                                    <input type="hidden" data-filter="measuringPoint.cancellation" data-operator="eq"
                                           id="cancellation"/>
                                </div>
                            </div>

                            <div class="batch-wrap" style="display: none;flex: 1">
                                <div class="form-group mar_t" style="width: calc(100% - 30px);">
                                    <textarea class="form-control" id="batch-value" data-operator="eq"
                                              placeholder="输入多个表号/户号/关联编号以逗号、空格或换行分隔" style="width: 100%; max-width: 100%"
                                              rows="3"></textarea>
                                </div>
                                <div class="form-group mar_t ">
                                    <div class="input-group">
                                        <span class="input-group-addon">查询类型</span>
                                        <select id="batch-type" style="width: 170px;min-width:170px"
                                                class="form-control select2 unclear"
                                                data-filter="batchType" data-operator="eq">
                                            <option value="meterCodes">表号</option>
                                            <option value="acountNos">户号</option>
                                            <option value="linkCodes">关联编号</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mar_t  filter-btn-group" style="margin-right: 10px;">
                                <button class="btn btn-primary" type="button" onclick="$('#table').reloadTable();">查询
                                </button>
                                <button class="btn btn btn-secondary" type="button"
                                        onclick="$('#tableFilter').clearFilters();resetSelect2();$('#batch-value').val(null);$('#table').reloadTable();">清除
                                </button>
                                <button class="btn btn-info" type="button" id="batch-query-btn">批量查询
                                </button>
                                <button class="btn btn-info" type="button" id="default-query-btn" style="display: none">条件查询
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="card table-card">
                <div class="card-body">
                    <div id="toolbar" class="toolbar-btn-action">

                        <button class="btn btn-info" type="button" onclick="exportExcel();"
                                sec:authorize-url="/revenue/customer/export**"><i
                                class="mdi mdi-file-export"></i> 导出Excel
                        </button>
                        <button class="btn btn-info" type="button" onclick="batchCalcBills();"
                                sec:authorize-url="/revenue/customer/configMeters/batchCalcBills**"><i
                                class="mdi mdi-file-export"></i> 批量结算账单
                        </button>
                        <button class="btn btn-danger" type="button" onclick="batchCancelPoint();"
                                sec:authorize-url="/revenue/customer/configMeters/cancelPoint**"><i
                                class="mdi mdi-account-off"></i> 批量销户
                        </button>

                        <button class="btn btn-info" type="button" onclick="printWaterFee();"
                                sec:authorize-url="/revenue/customer/configMeters/printWaterFee**"><i
                                class="mdi mdi-printer"></i> 打印水费报表
                        </button>
                        <form th:action="@{/revenue/customer/configMeters/exportMeasuringPoints}" method="post" id="export-data-form"
                              style='display:none' >
                            <input type="hidden" name="params" id="export-data-params"/>
                        </form>

                        <input type="hidden" id="hasCancelPoint" value="true" sec:authorize-url="/revenue/customer/configMeters/cancelPoint**">
                        <input type="hidden" id="hasReOpenAccount" value="true" sec:authorize-url="/revenue/customer/configMeters/reOpenAccount**">
                        <input type="hidden" id="hasWxUserList" value="true" sec:authorize-url="/revenue/customer/bindOrUnBindWechatUser**">
                        <input type="hidden" id="hasRefreshRealBalance" value="true" sec:authorize-url="/revenue/customer/refreshRealBalance**">
                        <input type="hidden" id="hasChangeWaterPrice" value="true" sec:authorize-url="/revenue/customer/configMeasuringPointClassification">
                        <input type="hidden" id="hasChangeEnableWxPay" value="true" sec:authorize-url="/revenue/customer/configMeters/changeEnableWxPay**">
                        <input type="hidden" id="hasAutoSwitchValve" value="true" sec:authorize-url="/revenue/customer/configMeters/changeAutoSwitchValve**">
                        <input type="hidden" id="hasChangeChargeValue" value="true" sec:authorize-url="/revenue/customer/configMeters/changeChargeValue**">
                        <input type="hidden" id="hasChangeDelayCloseValveDays" value="true" sec:authorize-url="/revenue/customer/configMeters/changeDelayCloseValveDays**">
                        <input type="hidden" id="hasPrintWaterFee" value="true" sec:authorize-url="/revenue/customer/configMeters/printWaterFee**">
                    </div>
                    <table
                            id="table"
                            class="f-table-r stripe-table"
                            data-icons-prefix="mdi"
                            data-icons="icons"
                            data-url="pageMeasuringPoints"
                            data-toolbar="#toolbar"
                            data-id-field="id"
                            data-unique-id="id"
                            data-click-to-select="true"
                            data-striped="true"
                            data-cache="false"
                            data-method="post"
                            data-show-columns="true"
                            data-show-refresh="true"
                            data-pagination="true"
                            data-side-pagination="server"
                            data-data-field="content"
                            data-total-field="totalElements"
                            data-page-list="[20, 50, 100, 200]" data-page-size="20"
                            data-detail-view="true"
                            data-detail-formatter="detailFormatter">
                        <thead>
                        <tr>
                            <th data-checkbox="true">选择</th>
                            <th data-field="companyId" >公司名称</th>
                            <th data-field="community.name" >所属小区</th>
                            <th data-field="buildingUnitNames" >楼栋/单元</th>
                            <th data-field="measuringPoint.accountNo" data-sortable="true">户号</th>
                            <th data-field="measuringPoint.name" data-sortable="true" data-class="wrap">计量点名称</th>
                            <th data-field="measuringPoint.customer.name" data-sortable="true">客户名</th>
                            <th data-field="code" data-sortable="true">表号</th>
                            <th data-field="measuringPoint.linkCode" data-sortable="true">关联编号</th>
                            <th data-field="startValue">启用读数(吨)</th>
                            <th data-field="latestData.value">表读数(吨)</th>
                            <th data-field="measuringPoint.chargeValue">起始收费读数(吨)</th>
                            <th data-field="measuringPoint.realBalance" data-sortable="true">可用余额</th>
                            <th data-field="measuringPoint.cancellation" data-formatter="measuringPointOpenAccountStateFormatter">开户状态</th>
                            <th data-field="finalWaterClassificationName" data-formatter="waterPriceStrFormatter" data-events="events">水价</th>
                            <th data-field="measuringPoint.enableWxPay" data-formatter="booleanFormatter">公众号支付</th>
                            <th data-field="measuringPoint.finalAutoSwitchValve" data-formatter="booleanFormatter">自动开关阀</th>
                            <th data-field="measuringPoint.finalDelayCloseValveDays">延迟关阀天数</th>
                            <th data-field="operate" data-align="center" data-width="150" data-click-to-select="false" data-formatter="measuringPointOperateFormatter" data-events="measuringPointOperateEvents">操作</th>
                        </tr>
                        </thead>
                    </table>

                </div>
            </div>
        </div>

    </div>

    <div class="mode mode3">
        <div class="card card3">
            <iframe id="iframe_wechat_user" class="tab-pane active" width="100%" height="100%" frameborder="0" src=""
                    seamless="" style="height: 100%;"></iframe>
        </div>
    </div>

    <div class="mode mode4">
        <div class="card card1" style="width: 400px">
            <div class="card-header" style=" padding: 8px 16px;">
                <h4>选择水价</h4>
            </div>
            <div class="card-body" style=" padding: 0 20px; ">
                <div id="item-list" class="list">

                </div>
            </div>
            <div class="card-footer">
                <button class="btn btn-primary confirm" id="confirm-classification">确 定</button>
                <button class="btn btn-default cancel" onclick="$('.mode').removeClass('show')">取 消</button>
            </div>
        </div>
    </div>

    <iframe id="print"  style="display: none"></iframe>

</th:block>

<th:block layout:fragment="page-script">

    <script th:src="@{/webjars/jsrender/jsrender.min.js}"></script>
    <script id="detailViewTmpl" type="text/x-jsrender">
        <table class="table table-bordered">
          <tbody>
            <tr>
              <th style="width: 130px;">房号:</th>
              <td>
                {{:measuringPoint.roomNumber}}
              </td>

              <th style="width: 130px;">身份证:</th>
              <td>
                {{:measuringPoint.customer.idCard}}
              </td>

              <th style="width: 130px;">手机号:</th>
              <td>
                {{:measuringPoint.customer.mobile}}
              </td>
            </tr>
          </tbody>
        </table>

    </script>

    <script th:src="@{/webjars/select2/js/select2.min.js}"></script>
    <script th:src="@{/webjars/select2/js/i18n/zh-CN.js}"></script>

    <!--日期选择插件-->
    <script th:src="@{/webjars/light-year-admin/js/bootstrap-datetimepicker/moment.min.js}"></script>
    <script th:src="@{/webjars/light-year-admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js}"></script>
    <script th:src="@{/webjars/light-year-admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js}"></script>

    <!--对话框-->
    <script th:src="@{/webjars/light-year-admin/js/jconfirm/jquery-confirm.min.js}"></script>
    <script th:src="@{/js/common/confirm-extend.js}"></script>
    <script th:src="@{/webjars/bootstrap-table/dist/bootstrap-table.min.js}"></script>
    <script th:src="@{/webjars/bootstrap-table/dist/locale/bootstrap-table-zh-CN.min.js}"></script>
    <script th:src="@{/js/common/bootstrap-table-custom.js}"></script>
    <script th:src="@{/js/common/query-filter.js}"></script>
    <script th:src="@{/js/common/enum-formatter.js}"></script>
    <script th:src="@{/webjars/jquery-validation/dist/jquery.validate.min.js}"></script>
    <script th:src="@{/webjars/jquery-validation/dist/localization/messages_zh.min.js}"></script>
    <script th:src="@{/js/common/jq-validation-bs.js}"></script>
    <script th:src="@{/webjars/jstree/jstree.min.js}"></script>
    <script th:src="@{/js/common/jstree-extend.js}"></script>
    <script th:src="@{/js/archive/community/community-tree-select.js}"></script>
    <script th:src="@{/js/revenue/customer/measuring-point-manage.js}"></script>
</th:block>
</body>
</html>