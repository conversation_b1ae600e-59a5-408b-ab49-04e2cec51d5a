<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/layout}">
<head>
  <meta charset="UTF-8">
  <title>编辑通知</title>
  <link rel="stylesheet" th:href="@{/webjars/select2/css/select2.min.css}">
  <link rel="stylesheet" th:href="@{/css/ly-select2.css}">
  <link rel="stylesheet" th:href="@{/css/reset-style.css}">
</head>
<body>

<th:block layout:fragment="page-content">
  <div class="row">
    <div class="col-lg-12">
      <div class="card table-card-30">
        <div class="card-header"><h4><div  class="back-button" onclick="history.back()" data-original-title="返回" >
            <span class="mdi mdi-chevron-left"></span>返回</div><span class="divide-line">|</span>编辑通知</h4></div>
        <div class="card-body" style="overflow: auto">

          <form id="form" th:action="@{/sys/notification/edit}" method="post" th:object="${notification}">
            <input type="hidden" th:field="*{id}">
            <div class="form-group line-group col-md-6">
              <label for="subject"><span class="text-danger">*</span>&nbsp;主题</label>
              <input class="form-control" type="text" th:field="*{subject}" placeholder="主题" required maxlength="64">
              <label th:if="${#fields.hasErrors('subject')}" th:errors="*{subject}" class="text-danger"></label>
            </div>

            <div class="form-group line-group col-md-12">
              <label for="content"> 内容</label>
              <textarea rows="5" class="form-control" th:field="*{content}" placeholder="请输入内容" maxlength="4096">内容</textarea>
              <label th:if="${#fields.hasErrors('content')}" th:errors="*{content}" class="text-danger"></label>
            </div>
            <div class="col-md-12"><th:block th:replace="fragments/form-footer :: submit-back-footer"/></div>

          </form>

        </div>
      </div>
    </div>

  </div>
</th:block>

<th:block layout:fragment="page-script">
  <script th:src="@{/webjars/select2/js/select2.min.js}"></script>
  <script th:src="@{/webjars/select2/js/i18n/zh-CN.js}"></script>
  <script th:src="@{/webjars/jquery-validation/dist/jquery.validate.min.js}"></script>
  <script th:src="@{/webjars/jquery-validation/dist/localization/messages_zh.min.js}"></script>
  <script th:src="@{/js/common/jq-validation-bs.js}"></script>
  <script th:src="@{/js/sys/company/add.js}"></script>
</th:block>

</body>
</html>