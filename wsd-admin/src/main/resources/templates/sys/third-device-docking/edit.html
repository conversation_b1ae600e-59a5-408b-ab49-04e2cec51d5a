<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/layout}">
<head>
    <meta charset="UTF-8">
    <title>编辑第三方设备对接类型</title>
    <link rel="stylesheet" th:href="@{/webjars/select2/css/select2.min.css}">
    <link rel="stylesheet" th:href="@{/css/ly-select2.css}">
    <link rel="stylesheet" th:href="@{/css/reset-style.css}">
</head>
<body>

<th:block layout:fragment="page-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="card table-card-30">
                <div class="card-header"><h4><div  class="back-button" onclick="history.back()" data-original-title="返回" >
                    <span class="mdi mdi-chevron-left"></span>返回</div><span class="divide-line">|</span>编辑第三方设备对接类型</h4>
                </div>
                <div class="card-body">
                    <form id="form" th:action="@{/sys/thirdDeviceDocking/edit}" method="post" th:object="${thirdDeviceDocking}">
                        <input type="hidden" th:field="*{id}">
                        <div class="form-group line-group col-md-6">
                            <label for="name"><span class="text-danger">*</span>&nbsp;第三方对接类型名称</label>
                            <input class="form-control" type="text" th:field="*{name}" placeholder="第三方对接类型名称" required maxlength="24">
                            <label th:if="${#fields.hasErrors('name')}" th:errors="*{name}" class="text-danger"></label>
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="enable"><span class="text-danger">*</span>&nbsp;是否启用</label>
                            <div style="display: flex;align-items: center;height: 36px">
                                <label class="lyear-radio radio-inline radio-primary">
                                    <input type="radio" th:field="*{enable}" value="true">
                                    <span>开启</span>
                                </label>
                                <label class="lyear-radio radio-inline radio-danger">
                                    <input type="radio" th:field="*{enable}" value="false">
                                    <span>关闭</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="enableMetering"><span class="text-danger">*</span>&nbsp;是否启用抄表</label>
                            <div style="display: flex;align-items: center;height: 36px">
                                <label class="lyear-radio radio-inline radio-primary">
                                    <input type="radio" th:field="*{enableMetering}" value="true">
                                    <span>开启</span>
                                </label>
                                <label class="lyear-radio radio-inline radio-danger">
                                    <input type="radio" th:field="*{enableMetering}" value="false">
                                    <span>关闭</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-group line-group col-md-6">
                            <label for="enableValve"><span class="text-danger">*</span>&nbsp;是否启用阀控</label>
                            <div style="display: flex;align-items: center;height: 36px">
                                <label class="lyear-radio radio-inline radio-primary">
                                    <input type="radio" th:field="*{enableValve}" value="true">
                                    <span>开启</span>
                                </label>
                                <label class="lyear-radio radio-inline radio-danger">
                                    <input type="radio" th:field="*{enableValve}" value="false">
                                    <span>关闭</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-group line-group col-md-12">
                            <label for="baseUrl"><span class="text-danger">*</span>&nbsp;baseUrl</label>
                            <input class="form-control" type="text" th:field="*{baseUrl}" placeholder="baseUrl" required maxlength="255">
                            <label th:if="${#fields.hasErrors('baseUrl')}" th:errors="*{baseUrl}" class="text-danger"></label>
                        </div>

                        <div class="form-group line-group col-md-12">
                            <label for="token"><span class="text-danger">*</span>对接token</label>
                            <textarea rows="5" class="form-control" th:field="*{token}" placeholder="对接token" required maxlength="1024">对接token</textarea>
                            <label th:if="${#fields.hasErrors('token')}" th:errors="*{token}" class="text-danger"></label>
                        </div>

                        <div class="form-group line-group col-md-12">
                            <label for="remark">备注</label>
                            <textarea rows="5" class="form-control" th:field="*{remark}" placeholder="请输入备注" maxlength="1024">备注</textarea>
                            <label th:if="${#fields.hasErrors('remark')}" th:errors="*{remark}" class="text-danger"></label>
                        </div>

                        <div class="col-md-12"> <th:block th:replace="fragments/form-footer :: submit-back-footer"/></div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</th:block>

<th:block layout:fragment="page-script">
    <script th:src="@{/webjars/select2/js/select2.min.js}"></script>
    <script th:src="@{/webjars/select2/js/i18n/zh-CN.js}"></script>
    <script th:src="@{/webjars/jquery-validation/dist/jquery.validate.min.js}"></script>
    <script th:src="@{/webjars/jquery-validation/dist/localization/messages_zh.min.js}"></script>
    <script th:src="@{/js/common/jq-validation-bs.js}"></script>
</th:block>

</body>
</html>