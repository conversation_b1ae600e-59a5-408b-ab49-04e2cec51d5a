<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security"
      layout:decorate="~{layout/layout}">
<head>
  <meta charset="UTF-8">
  <title>集中器维护</title>

  <!--对话框-->
  <link rel="stylesheet" th:href="@{/webjars/light-year-admin/js/jconfirm/jquery-confirm.min.css}">
  <link rel="stylesheet" th:href="@{/webjars/bootstrap-table/dist/bootstrap-table.min.css}">

  <link rel="stylesheet" th:href="@{/webjars/select2/css/select2.min.css}">
  <link rel="stylesheet" th:href="@{/css/ly-select2.css}">
  <link rel="stylesheet" th:href="@{/css/reset-style.css}">
  <style>
    .tab-pane1 .bootstrap-table .fixed-table-body{
      max-height: calc(100vh - 280px);
    }
    .tab-pane3 .bootstrap-table .fixed-table-body{
      max-height: calc(100vh - 226px);
    }
    #wrap-card.fixed{
        position: fixed;
        width: 100%;
        height: 100%;
        margin: 0;
        border: none;
        left: 0;
        box-shadow: none;
        top: 0;
        border-radius: 0;
        overflow-y: auto;
    }

    .sr-only {
        position: inherit;
        width: auto;
        height: auto;
        padding: 0;
        margin-bottom: -6px;
    }
    .select2-container{
        min-width: 100px;
    }
  </style>
</head>
<body>

<th:block layout:fragment="page-content">
  <div id="wrap-card" class="card table-card-30" >
    <div class="card-body">

      <input type="hidden" th:field="${concentrator.code}">

      <ul id="myTabs" class="nav nav-tabs" role="tablist">
        <li class="active"><a href="#archive" id="archive-tab" role="tab" data-toggle="tab">档案维护</a></li>
        <li><a href="#maintain" role="tab" id="maintain-tab" data-toggle="tab">其它操作</a></li>
        <li><a href="#event" role="tab" data-toggle="tab">集中器事件</a></li>
        <li th:if="${concentrator.concentratorType.name() == 'WIRED_MULTI_FUNCTION'}"><a href="#test" class="hide" role="tab" data-toggle="tab" id="test-tab">集中器测试</a></li>
        <li class="pull-right" id="back"><a id="back-btn" href="javascript:;">返回</a></li>
      </ul>
      <div id="myTabContent" class="tab-content">
          <div class="tab-pane fade active in tab-pane1" id="archive">

          <div id="toolbar" class="toolbar-btn-action">
            <button class="btn btn-danger" id="btn-reset"><i class="mdi mdi-redo"></i> 初始化</button>
            <button class="btn btn-purple" id="btn-sync-archive"><i class="mdi mdi-sync"></i> 同步档案</button>
            <button class="btn btn-info" id="btn-reset-serial-no"><i class="mdi mdi-redo"></i> 重新分配测量点</button>
          </div>
          <table id="table"
                 data-icons-prefix="mdi"
                 data-icons="icons"
                 data-toolbar="#toolbar"
                 data-search="true"
                 data-sort-order="asc"
                 data-sort-name="measuredPoint"
                 data-url="settings/waterMeters"
                 data-id-field="id"
                 data-unique-id="id"
                 data-striped="true"
                 data-cache="false"
                 data-show-refresh="true"
                 data-pagination="true"
                 data-side-pagination="client"
                 data-page-list="[20, 50, 100, 200]" data-page-size="20">
            <thead>
            <tr>
                <th data-field="serialNo" data-sortable="true" data-width="100">测量点号</th>
                <th data-field="code" data-sortable="true">水表编号</th>
                <th data-field="collectorUid" data-sortable="true">中继器</th>
                <th data-field="protocol" data-sortable="true" data-formatter="protocolFormatter">协议</th>
                <th data-field="port" data-sortable="true" data-formatter="portFormatter">端口</th>
                <th data-field="sync" data-sortable="true" data-width="90" data-align="center" data-formatter="syncedFormatter">是否已同步</th>
            </tr>
            </thead>
          </table>

          <div class="divider text-uppercase">查询集中器已配置档案</div>

          <div id="archive-toolbar" class="toolbar-btn-action">
            <form class="form-inline" method="post" onsubmit="return false;">
              <div class="form-group">
                <label class="sr-only" for="start-num">开始序号</label>
                <input class="form-control" style="min-width: 100px;" type="number" id="start-num" placeholder="开始序号" min="1" max="2048">
              </div>
              <div class="form-group">
                <label class="sr-only" for="end-num">结束序号</label>
                <input class="form-control" style="min-width: 100px;" type="number" id="end-num" placeholder="结束序号" min="1" max="2048">
              </div>
              <div class="form-group">
                <button class="btn btn-primary" type="button" id="btn-archive-query">查询</button>
              </div>
            </form>
          </div>
          <table id="archive-table"
                 data-icons-prefix="mdi"
                 data-icons="icons"
                 data-toolbar="#archive-toolbar"
                 data-search="true"
                 data-sort-order="asc"
                 data-sort-name="measuredPoint"
                 data-striped="true"
                 data-cache="false"
                 data-pagination="true"
                 data-side-pagination="client"
                 data-page-list="[20, 50, 100, 200]" data-page-size="20">
            <thead>
            <tr>
                <th data-field="serialNo" data-sortable="true" data-width="100">表序号</th>
                <th data-field="code" data-sortable="true">水表编号</th>
                <th data-field="collectorCode" data-sortable="true">中继器</th>
                <th data-field="protocol" data-sortable="true" data-formatter="protocolFormatter">协议</th>
                <th data-field="port" data-sortable="true" data-formatter="portFormatter">端口</th>
            </tr>
            </thead>
          </table>
        </div>

          <div class="tab-pane fade" id="maintain">
          <div>
            <button class="btn btn-primary" id="btn-metering-yesterday">补抄冻结数据</button>
            <button class="btn btn-primary" id="btn-metering-realtime-data">补抄实时数据</button>
            <button class="btn btn-primary" id="btn-metering">启动抄表</button>
            <button class="btn btn-primary" id="btn-report">启动上报</button>
            <button class="btn btn-primary" id="btn-read-rssi">读取RSSI值</button>
            <button class="btn btn-primary" id="btn-reset-concentrator">复位集中器</button>
            <button class="btn btn-danger" id="btn-offline">强制下线集中器</button>
          </div>

          <div class="divider text-uppercase">查询/设置时钟</div>
          <div>
            <button class="btn btn-purple" id="btn-sync-clock">同步系统时钟</button>
              <button class="btn btn-info" id="btn-query-clock">查询集中器时钟</button>
              <span class="m-l-10" id="clock"></span>
          </div>

            <div class="divider text-uppercase">查询集中器版本</div>
            <div>
                <button class="btn btn-primary" id="btn-query-version">查询版本</button>
                <span class="m-l-10" id="version"></span>
            </div>

            <th:block th:if="${concentrator.concentratorType.name()} eq 'WIRELESS'">
                <div class="divider text-uppercase">查询LoRa集中器模块版本</div>
                <div>
                    <button class="btn btn-primary" id="btn-query-lora-version">查询版本</button>
                    <span class="m-l-10" id="lora-version"></span>
                </div>

                <div class="divider text-uppercase">读取/设置抄表时间</div>
                <div>
                    <form class="form-inline" id="set-metering-time-form" action="#" method="post" onsubmit="return false;">
                        <div class="form-group">
                            <label class="sr-only" for="meteringTime">抄表时间</label>
                            <input class="form-control" style="width: 185.5px;" type="number" id="meteringTime" name="meteringTime" placeholder="抄表时间" required min="0" max="23">
                        </div>
                        <div class="form-group">
                            <button class="btn btn-danger" type="submit">设置</button>
                        </div>
                    </form>
                    <form class="form-inline m-t-10" id="read-metering-time-form" action="#" method="post" onsubmit="return false;">
                        <div class="form-group">
                            <label class="sr-only" for="rMeteringTime">抄表时间</label>
                            <input class="form-control" type="text" id="rMeteringTime" readonly>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-primary" type="submit">读取</button>
                        </div>
                    </form>
                </div>
            </th:block>
            <th:block th:unless="${concentrator.concentratorType.name()} eq 'WIRELESS'">
                <div class="divider text-uppercase">读取/设置充电时间</div>
                <div>
                    <form class="form-inline" id="set-charging-time-form" action="#" method="post" onsubmit="return false;">
                        <div class="form-group">
                            <label class="sr-only" for="chargingTime">充电时间</label>
                            <input class="form-control" style="width: 185.5px;" type="number" id="chargingTime" name="chargingTime" placeholder="充电时间" required min="0" max="255">
                        </div>
                        <div class="form-group">
                            <button class="btn btn-danger" type="submit">设置</button>
                        </div>
                    </form>
                    <form class="form-inline m-t-10" id="read-charging-time-form" action="#" method="post" onsubmit="return false;">
                        <div class="form-group">
                            <label class="sr-only" for="rChargingTime">充电时间</label>
                            <input class="form-control" type="text" id="rChargingTime" readonly>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-primary" type="submit">读取</button>
                        </div>
                    </form>
                </div>

                <div class="divider text-uppercase">读取/设置协议和充电电压</div>
                <div>
                    <form class="form-inline" id="set-charging-voltage-form" action="#" method="post" onsubmit="return false;">
                        <div class="form-group">
                            <label class="sr-only" for="protocolType">协议类型</label>
                            <input class="form-control" style="width: 185.5px;" type="number" id="protocolType" name="protocolType" placeholder="协议类型" required min="0" max="255">
                        </div>
                        <div class="form-group">
                            <label class="sr-only" for="voltageVal">电压值</label>
                            <select class="form-control" style="width: 185.5px;" name="voltageVal" id="voltageVal" required>
                                <option value="">请选择</option>
                                <option value="0">低电压</option>
                                <option value="1">高电压</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-danger" type="submit">设置</button>
                        </div>
                    </form>
                    <form class="form-inline m-t-10" id="read-charging-voltage-form" action="#" method="post" onsubmit="return false;">
                        <div class="form-group">
                            <label class="sr-only" for="rProtocolType">协议类型</label>
                            <input class="form-control" type="text" id="rProtocolType" placeholder="协议类型" readonly>
                        </div>
                        <div class="form-group">
                            <label class="sr-only" for="rVoltageVal">电压值</label>
                            <input class="form-control" type="text" id="rVoltageVal" placeholder="电压值" readonly>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-primary" type="submit">读取</button>
                        </div>
                    </form>
                </div>
            </th:block>

              <!--<th:block th:if="${concentrator.concentratorType.name()} eq 'WIRED_MULTI_FUNCTION'">
                  <div class="divider text-uppercase">读取/设置上报间隔时间</div>
                  <div>
                      <form class="form-inline" id="set-report-time-form" action="#" method="post" onsubmit="return false;">
                          <div class="form-group">
                              <label class="sr-only" for="reportIntervalTime">上报间隔时间</label>
                              <input class="form-control" style="width: 185.5px;" type="number" id="reportIntervalTime" name="reportIntervalTime" placeholder="上报间隔时间" required
                                     min="1" max="24">
                          </div>
                          <div class="form-group">
                              <label class="sr-only" for="reportIntervalTimeMark">标志</label>
                              <select class="form-control" style="width: 185.5px;" name="reportIntervalTimeMark" id="reportIntervalTimeMark" required>
                                  <option value="">请选择</option>
                                  <option value="1">间隔上报</option>
                                  <option value="0">非间隔上报</option>
                              </select>
                          </div>
                          <div class="form-group">
                              <button class="btn btn-danger" type="submit">设置</button>
                          </div>
                      </form>
                      <form class="form-inline m-t-10" id="read-report-time-form" action="#" method="post" onsubmit="return false;">
                          <div class="form-group">
                              <label class="sr-only" for="rReportIntervalTime">上报间隔时间</label>
                              <input class="form-control" type="text" id="rReportIntervalTime" placeholder="上报间隔时间" readonly>
                          </div>
                          <div class="form-group">
                              <label class="sr-only" for="rReportIntervalTimeMark">标志</label>
                              <input class="form-control" type="text" id="rReportIntervalTimeMark" placeholder="上报标志" readonly>
                          </div>
                          <div class="form-group">
                              <button class="btn btn-primary" type="submit">读取</button>
                          </div>
                      </form>
                  </div>
              </th:block>-->

              <div class="divider text-uppercase">查询/设置主站IP端口</div>
              <div>
                  <form id="form">
                      <div class="form-group">
                          <div class="input-group">
                              <span class="input-group-addon">主用IP地址</span>
                              <input type="text" class="form-control" id="ip1" name="masterIp" placeholder="请输入主用IP地址" required data-rule-ip="true"/>
                          </div>
                      </div>
              <div class="form-group">
                <div class="input-group">
                  <span class="input-group-addon">主用端口</span>
                  <input type="number" class="form-control" id="port1" name="masterPort" placeholder="请输入主用端口" required min="0" max="65535"/>
                </div>
              </div>
              <div class="form-group">
                <div class="input-group">
                  <span class="input-group-addon">备用IP地址</span>
                  <input type="text" class="form-control" id="ip2" name="slaveIp" placeholder="请输入备用IP地址" required data-rule-ip="true"/>
                </div>
              </div>
              <div class="form-group">
                <div class="input-group">
                  <span class="input-group-addon">备用端口</span>
                  <input type="text" class="form-control" id="port2" name="slavePort" placeholder="请输入备用端口" required min="0" max="65535"/>
                </div>
              </div>
              <div class="form-group">
                <div class="input-group">
                  <span class="input-group-addon">APN</span>
                  <input type="text" class="form-control" id="apn" name="apn" placeholder="请输入APN" required maxlength="16"/>
                </div>
              </div>
              <div class="form-group">
                <button class="btn btn-primary" type="button" id="btn-query-ip">查询</button>
                <button class="btn btn btn-danger" type="button" id="btn-set-ip">设置</button>
              </div>
            </form>
          </div>
        </div>

          <div class="tab-pane fade tab-pane3" id="event">

          <div id="event-toolbar">
            <form id="event-table-filter" class="form-inline" role="form" onsubmit="return false;">
              <select class="form-control select2 select2-form-inline table-filter" data-filter="eventType" data-operator="eq">
                <option value="">请选择事件类型</option>
                <option th:each="eventType : ${eventTypes}" th:value="${eventType}" th:text="#{'Concentrator_EventType_' + ${eventType}}"></option>
              </select>
            </form>
          </div>
          <table id="event-table"
                 data-icons-prefix="mdi"
                 data-icons="icons"
                 data-url="settings/concentratorEvents"
                 data-sort-order="desc"
                 data-sort-name="id"
                 data-toolbar="#event-toolbar"
                 data-id-field="id"
                 data-unique-id="id"
                 data-click-to-select="true"
                 data-striped="true"
                 data-cache="false"
                 data-method="post"
                 data-show-columns="true"
                 data-show-refresh="true"
                 data-pagination="true"
                 data-side-pagination="server"
                 data-data-field="content"
                 data-total-field="totalElements"
                 data-page-list="[20, 50, 100, 200]" data-page-size="20">
            <thead>
            <tr>
                <th data-checkbox="true">选择</th>
                <th data-field="eventType" data-sortable="true" data-formatter="concentratorEventTypeFormatter">事件类型</th>
                <th data-field="details" data-sortable="true">事件详细</th>
                <th data-field="createdDate" data-sortable="true" data-width="180">创建时间</th>
            </tr>
            </thead>
          </table>

        </div>

          <div th:if="${concentrator.concentratorType.name() == 'WIRED_MULTI_FUNCTION'}" class="tab-pane fade" id="test">
              <div>
                  <div class="row">
                      <div class="col-lg-4">
                          <form class="form" id="add-test-form" method="post" onsubmit="return false;">
                              <div class="form-group">
                                  <label for="code"><span class="text-danger">*</span>&nbsp;水表编号</label>
                                  <input class="form-control" type="text" name="code" id="code" placeholder="水表编号" onchange="this.value=padPre(this.value, 14, '0')" required
                                         minlength="14" maxlength="14" data-rule-digits="true">
                              </div>
                              <div class="form-group">
                                  <label for="protocol"><span class="text-danger">*</span>&nbsp;协议类型</label>
                                  <select class="form-control select2" name="protocol" id="protocol" style="width: 100%;" data-placeholder="请选择协议类型" required>
                                      <option value="">请选择</option>
                                      <option value="1">DL645-97</option>
                                      <option value="2">DL645-07</option>
                                      <option value="3">CJ_T188</option>
                                      <option value="4">卓正CJ_T188</option>
                                      <option value="5">航天中电</option>
                                      <option value="6">无锡聚为</option>
                                      <option value="7">保定大水表</option>
                                      <option value="8">千宝通通CJ_T188</option>
                                      <option value="11">东润</option>
                                      <option value="12">北京集万讯</option>
                                  </select>
                              </div>
                              <div class="form-group">
                                  <label for="port"><span class="text-danger">*</span>&nbsp;端口</label>
                                  <select class="form-control select2" name="port" id="port" style="width: 100%;" data-placeholder="请选择端口" required>
                                      <option value="">请选择</option>
                                      <option value="0">M-BUS-1</option>
                                      <option value="1">M-BUS-2</option>
                                      <option value="2">M-BUS-3</option>
                                      <option value="3">M-BUS-4</option>
                                      <option value="4">RS485-1</option>
                                      <option value="5">RS485-2</option>
                                      <option value="6">RS485-3</option>
                                      <option value="7">RS485-4</option>
                                  </select>
                              </div>
                              <div class="form-group">
                                  <button class="btn btn-primary" type="submit" id="btn-add-test">添加测试档案</button>
                              </div>
                          </form>
                      </div>
                      <div class="col-lg-8">
                          <div id="test-archive-toolbar" class="toolbar-btn-action">
                              <button class="btn btn-primary" id="btn-start-test">一键测试</button>
                          </div>
                          <table id="test-archive-table"
                                 data-icons-prefix="mdi"
                                 data-icons="icons"
                                 data-toolbar="#test-archive-toolbar"
                                 data-id-field="code"
                                 data-unique-id="code"
                                 data-click-to-select="true"
                                 data-url="settings/testArchives"
                                 data-show-refresh="true"
                                 data-search="true"
                                 data-striped="true"
                                 data-cache="false">
                              <thead>
                              <tr>
                                  <th data-field="code" data-sortable="true">水表编号</th>
                                  <th data-field="protocol" data-sortable="true" data-formatter="protocolFormatter">协议</th>
                                  <th data-field="port" data-sortable="true" data-formatter="portFormatter">端口</th>
                                  <th data-field="operate" data-align="center" data-width="80" data-click-to-select="false" data-formatter="operateFormatter"
                                      data-events="operateEvents">操作
                                  </th>
                              </tr>
                              </thead>
                          </table>
                      </div>
                  </div>

                  <div class="row">
                      <div class="my-line-label" style="float: left;width: 100%;margin: 10px 0 5px;">
                          <hr class="hr1" style="flex-grow: 1"><div>协议透传</div><hr class="hr2">
                      </div>
                      <div class="form-group">
                          <label for="code">透传报文</label>
                          <textarea rows="4" class="form-control" type="text"  id="message" placeholder="请输入待透传的报文" ></textarea>
                      </div>
                      <div class="form-group">
                          <label for="code">响应报文</label>
                          <textarea class="form-control" type="text"  id="response" placeholder="此处显示接收到的响应报文"
                                    readonly  rows="4"></textarea>
                      </div>
                      <div class="form-group">
                          <button class="btn btn-primary" type="button" id="btn-send">发送</button>
                      </div>
                  </div>
              </div>
          </div>
      </div>

    </div>
  </div>
</th:block>

<th:block layout:fragment="page-script">
  <!--对话框-->
  <script th:src="@{/webjars/light-year-admin/js/jconfirm/jquery-confirm.min.js}"></script>
  <script th:src="@{/js/common/confirm-extend.js}"></script>
  <script th:src="@{/webjars/bootstrap-table/dist/bootstrap-table.min.js}"></script>
  <script th:src="@{/webjars/bootstrap-table/dist/locale/bootstrap-table-zh-CN.min.js}"></script>
  <script th:src="@{/js/common/bootstrap-table-custom.js}"></script>
  <script th:src="@{/js/common/query-filter.js}"></script>

  <script th:src="@{/webjars/select2/js/select2.min.js}"></script>
    <script th:src="@{/webjars/select2/js/i18n/zh-CN.js}"></script>

    <script th:src="@{/webjars/jquery-validation/dist/jquery.validate.min.js}"></script>
    <script th:src="@{/webjars/jquery-validation/dist/localization/messages_zh.min.js}"></script>
    <script th:src="@{/js/common/jq-validation-bs.js}"></script>

    <script th:src="@{/js/common/enum-formatter.js}"></script>
    <script th:src="@{/js/monitor/concentrator/settings-archive.js}"></script>
    <script th:src="@{/js/monitor/concentrator/settings-other.js}"></script>
    <script th:src="@{/js/monitor/concentrator/settings-event.js}"></script>
    <script th:src="@{/js/monitor/concentrator/settings-test.js}"></script>
    <script>
        $(function () {
            if(location.search.includes('noback')){
                $('#back').hide();
                $('#wrap-card').addClass('fixed');
            }else{
                $('#back-btn').click(function () {
                    window.parent.$('.mode').removeClass('show')
                    window.parent.$('body').removeClass('over-hidden')
                });
            }
        })
    </script>
</th:block>
</body>
</html>
