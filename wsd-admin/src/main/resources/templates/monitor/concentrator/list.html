<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security"
      layout:decorate="~{layout/layout}">
<head>
    <meta charset="UTF-8">
    <title>集中器监测</title>
    <link rel="stylesheet" th:href="@{/webjars/bootstrap-table/dist/bootstrap-table.min.css}">
    <!-- select2 -->
    <link rel="stylesheet" th:href="@{/webjars/select2/css/select2.min.css}">
    <link rel="stylesheet" th:href="@{/webjars/jstree/themes/default/style.min.css}">
    <link rel="stylesheet" th:href="@{/css/alifont/iconfont.css}">
    <link rel="stylesheet" th:href="@{/css/ly-select2.css}">
    <link rel="stylesheet" th:href="@{/css/reset-style.css}">
    <style>
        .btn-default:not([disabled]):not(.disabled).active, .btn-default:not([disabled]):not(.disabled):active, .show > .btn-default.dropdown-toggle {
            color: #f00;
        }

        .mode .card {
            top: 0;
            left: 0;
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .mode.show .card {
            animation: modeshow 0.25s;
        }
    </style>
</head>
<body>

<th:block layout:fragment="page-content">
    <div class="row" style="display: flex;position: relative">

        <!--小区树-->
        <th:block th:replace="fragments/community-tree :: community-tree"/>

        <div class="tree-page-card">
            <div class="card" id="search-card">
                <div class="card-body pd-t-5">
                    <form id="tableFilter" class="form-inline" role="form" onsubmit="return false;">
                        <input class="form-control table-filter" type="hidden" onchange="$('#table').reloadTable();"  data-filter="community.region.id" id="regionId" data-operator="eq">
                        <input class="form-control table-filter" type="hidden" onchange="$('#table').reloadTable();"  data-filter="community.id" id="communityId" data-operator="eq">
                        <div class="default-filter-wrap">
                            <div>
                                <div class="form-group mar_t">
                                    <div class="input-group">
                                        <span class="input-group-addon">集中器编号</span>
                                        <input style="width: 170px;" type="text" class="form-control table-filter"
                                               data-filter="code" data-operator="like" data-ignorecase="false"
                                               placeholder="请输入集中器编号"/>
                                    </div>
                                </div>

                                <div class="form-group mar_t">
                                    <div class="input-group">
                                        <span class="input-group-addon">集中器类型</span>
                                        <select style="width: 160px;" id="concentratorType"
                                                class="form-control select2 select2-form-inline table-filter"
                                                data-filter="concentratorType" data-operator="eq">
                                            <option value="">请选择</option>
                                            <option th:each="concentratorType : ${concentratorTypes}" th:value="${concentratorType}" th:text="#{'ConcentratorType_' + ${concentratorType}}"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group mar_t ">
                                    <div class="input-group">
                                        <span class="input-group-addon">是否在线</span>
                                        <select style="width: 170px;min-width:170px"
                                                class="form-control table-filter select2"
                                                data-filter="online" data-operator="eq">
                                            <option value="">全部</option>
                                            <option value="true">在线</option>
                                            <option value="false">离线</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mar_t filter-btn-group">
                                <button class="btn btn-primary" type="button" onclick="$('#table').reloadTable();">查询
                                </button>
                                <button class="btn btn btn-secondary" type="button"
                                        onclick="resetSelect2();$('#tableFilter').clearFilters();$('#table').reloadTable();">
                                    清除
                                </button>
                                <button type="button" class="btn btn-default toggle-more-filter"
                                        onclick="$(this).children().toggleClass('mdi-rotate-180')"
                                        data-toggle="collapse" data-target="#collapseFilter" aria-expanded="false"
                                        aria-controls="collapseExample">
                                    <span data-toggle="tooltip" data-placement="top" title="" data-original-title="更多筛选"
                                          class="mdi mdi-chevron-double-down"></span>
                                </button>
                            </div>
                        </div>

                        <div class="collapse" id="collapseFilter">
                            <div style="margin: 10px 0 0; border-top: 1px solid #eee">
                                <div class="form-group mar_t ">
                                    <div class="input-group">
                                        <span class="input-group-addon">档案是否同步</span>
                                        <select style="width: 170px;min-width:170px"
                                                class="form-control table-filter select2"
                                                data-filter="archiveSynced" data-operator="eq">
                                            <option value="">全部</option>
                                            <option value="true">已同步</option>
                                            <option value="false">未同步</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card table-card">
                <div class="card-body">

                    <input type="hidden" id="hasSettings" value="true"
                           sec:authorize-url="/monitor/concentrator/**/settings">

                    <table id="table"
                           class="f-table-r stripe-table"
                           data-icons-prefix="mdi"
                           data-icons="icons"
                           data-url="page"
                           data-sort-order="desc"
                           data-sort-name="id"
                           data-id-field="id"
                           data-unique-id="id"
                           data-striped="true"
                           data-cache="false"
                           data-method="post"
                           data-show-columns="true"
                           data-show-refresh="true"
                           data-auto-refresh="true"
                           data-auto-refresh-interval="10"
                           data-pagination="true"
                           data-side-pagination="server"
                           data-data-field="content"
                           data-total-field="totalElements"
                           data-page-list="[20, 50, 100, 200]" data-page-size="20"
                           data-detail-view="true"
                           data-detail-formatter="detailFormatter">
                        <thead>
                        <tr>
                            <th data-field="code" data-sortable="true">集中器编号</th>
                            <th data-field="community.name" data-class="td-150" data-formatter="tdTipFormatter">所属小区</th>
                            <th data-field="installationDetails.installLocation" data-sortable="true">安装位置</th>
                            <th data-field="concentratorType" data-width="180" data-formatter="concentratorTypeFormatter">集中器类型</th>
                            <th data-field="online" data-width="70" data-align="center"
                                data-formatter="onlineFormatter">是否在线
                            </th>
                            <th data-field="archiveSynced" data-width="90" data-align="center"
                                data-formatter="syncedFormatter">档案是否同步
                            </th>
                            <th data-field="onlineTime" data-width="160">上线时间</th>
                            <th data-field="offlineTime" data-width="160">离线时间</th>
                            <th data-field="concentratorVersion" data-sortable="true">集中器版本</th>
                            <th data-field="concentratorDockingType">对接方式</th>
                            <th data-field="companyId" >公司名称</th>
                            <th data-field="createdDate" data-sortable="true" data-width="160">创建时间</th>
                            <th data-field="operate" data-formatter="operateFormatter" data-events="operateEvents"
                                data-align="center" data-width="80">操作
                            </th>
                        </tr>
                        </thead>
                    </table>

                </div>
            </div>
        </div>
    </div>

    <div class="mode">
        <div class="card">
            <iframe id="iframe-meter" class="tab-pane active" width="100%" height="100%" frameborder="0" src=""
                    seamless="" style="height: 100%;"></iframe>
        </div>
    </div>
</th:block>

<th:block layout:fragment="page-script">
    <script th:src="@{/webjars/jsrender/jsrender.min.js}"></script>
    <script id="detailViewTmpl" type="text/x-jsrender">
    <table class="table table-bordered">
      <tbody>
        <tr>
          <th style="width: 120px;">安装日期:</th>
          <td>
            {{if installationDetails}}
            {{:installationDetails.installDate}}
            {{else}}
            &nbsp;
            {{/if}}
          </td>

          <th style="width: 120px;">物联卡卡号:</th>
          <td>{{:simCard}}</td>
        </tr>
      </tbody>
    </table>

    </script>

    <!-- select2 -->
    <script th:src="@{/webjars/select2/js/select2.min.js}"></script>
    <script th:src="@{/webjars/select2/js/i18n/zh-CN.js}"></script>

    <script th:src="@{/webjars/bootstrap-table/dist/bootstrap-table.min.js}"></script>
    <script th:src="@{/webjars/bootstrap-table/dist/extensions/auto-refresh/bootstrap-table-auto-refresh.js}"></script>
    <script th:src="@{/webjars/bootstrap-table/dist/locale/bootstrap-table-zh-CN.min.js}"></script>
    <script th:src="@{/js/common/bootstrap-table-custom.js}"></script>
    <script th:src="@{/webjars/jstree/jstree.min.js}"></script>
    <script th:src="@{/js/common/jstree-extend.js}"></script>
    <script th:src="@{/js/archive/community/community-tree-select.js}"></script>
    <script th:src="@{/js/common/query-filter.js}"></script>
    <script th:src="@{/js/common/enum-formatter.js}"></script>
    <script th:src="@{/js/monitor/concentrator/list.js}"></script>
</th:block>
</body>
</html>