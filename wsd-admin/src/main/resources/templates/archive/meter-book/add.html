<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/layout}">
<head>
  <meta charset="UTF-8">
  <title>新建基表</title>
  <link rel="stylesheet" th:href="@{/webjars/select2/css/select2.min.css}">
  <link rel="stylesheet" th:href="@{/css/ly-select2.css}">
  <link rel="stylesheet" th:href="@{/webjars/jstree/themes/default/style.min.css}">
  <link rel="stylesheet" th:href="@{/css/alifont/iconfont.css}">
  <link rel="stylesheet" th:href="@{/css/reset-style.css}">
</head>
<body>

<th:block layout:fragment="page-content">
  <div class="row">
    <div class="col-lg-12">
      <div class="card table-card-30">
        <div class="card-header"><h4><div  class="back-button" onclick="history.back()" data-original-title="返回" >
            <span class="mdi mdi-chevron-left"></span>返回</div><span class="divide-line">|</span>新建抄表册</h4></div>
        <div class="card-body">

          <form id="form" th:action="@{/archive/meterBook/add}" method="post" th:object="${meterBook}">
            <div class="company-height">
              <div class="form-group line-group col-md-6 community-tree-select">
                <label for="parent"><span class="text-danger">*</span>所属公司</label>
                <input class="form-control" type="text" id="parent-text" placeholder="所属公司" readonly required>
                <input type="hidden" th:field="*{companyId}">
                <label th:if="${#fields.hasErrors('companyId')}" th:errors="*{companyId}" class="text-danger"></label>
                <div class="select-community-wrap">
                  <div id="selectCompanyTree"></div>
                </div>
              </div>
            </div>
            <div class="form-group line-group col-md-6">
              <label for="meterBookNo"><span class="text-danger">*</span>&nbsp;抄表册号</label>
              <input class="form-control" type="number" th:field="*{meterBookNo}" placeholder="抄表册号" required maxlength="64"  max="999999">
              <label th:if="${#fields.hasErrors('meterBookNo')}" th:errors="*{meterBookNo}" class="text-danger"></label>
            </div>
            <div class="form-group line-group col-md-6">
              <label for="name"><span class="text-danger">*</span>&nbsp;名称</label>
              <input class="form-control" type="text" th:field="*{name}" placeholder="名称" required maxlength="64">
              <label th:if="${#fields.hasErrors('name')}" th:errors="*{name}" class="text-danger"></label>
            </div>

            <div class="form-group line-group col-md-6">
              <label for="user.id"><span class="text-danger">*</span>&nbsp;抄表员</label>
              <select class="form-control select2" th:field="*{user.id}" style="width: 100%;" data-placeholder="请选择" required>
              </select>
            </div>

            <div class="form-group line-group col-md-6">
              <label for="businessUnit">&nbsp;营业单位</label>
              <input class="form-control" type="text" th:field="*{businessUnit}" placeholder="营业单位" maxlength="64">
              <label th:if="${#fields.hasErrors('businessUnit')}" th:errors="*{businessUnit}" class="text-danger"></label>
            </div>
            <div class="form-group line-group col-md-6">
              <label for="population">人口数</label>
              <input class="form-control" type="number" th:field="*{population}" placeholder="人口数" min="0" step="1"  max="999999" data-rule-digits="true">
              <label th:if="${#fields.hasErrors('population')}" th:errors="*{population}" class="text-danger"></label>
            </div>
            <div class="form-group line-group col-md-6">
              <label for="area">区域面积（km²）</label>
              <input class="form-control" type="number" th:field="*{area}" placeholder="区域面积" min="0.00" step="0.01" max="999999">
              <label th:if="${#fields.hasErrors('area')}" th:errors="*{area}" class="text-danger"></label>
            </div>
            <div class=" line-group col-md-12"><th:block th:replace="fragments/form-footer :: submit-back-footer"/></div>

          </form>

        </div>
      </div>
    </div>

  </div>
</th:block>

<th:block layout:fragment="page-script">
  <script th:src="@{/webjars/select2/js/select2.min.js}"></script>
  <script th:src="@{/webjars/select2/js/i18n/zh-CN.js}"></script>
  <script th:src="@{/webjars/jquery-validation/dist/jquery.validate.min.js}"></script>
  <script th:src="@{/webjars/jquery-validation/dist/localization/messages_zh.min.js}"></script>
  <script th:src="@{/js/common/jq-validation-bs.js}"></script>
  <script th:src="@{/webjars/jstree/jstree.min.js}"></script>
  <script th:src="@{/js/common/jstree-extend.js}"></script>
  <script th:src="@{/js/archive/meter-book/add.js}"></script>
</th:block>

</body>
</html>