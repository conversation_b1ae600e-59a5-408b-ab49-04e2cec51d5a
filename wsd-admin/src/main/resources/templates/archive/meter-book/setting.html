<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security"
      layout:decorate="~{layout/layout}">

<head>
    <meta charset="UTF-8">
    <title>机械水表管理</title>

    <!--对话框-->
    <link rel="stylesheet" th:href="@{/webjars/light-year-admin/js/jconfirm/jquery-confirm.min.css}">
    <link rel="stylesheet" th:href="@{/webjars/bootstrap-table/dist/bootstrap-table.min.css}">

    <link rel="stylesheet" th:href="@{/webjars/select2/css/select2.min.css}">
    <link rel="stylesheet" th:href="@{/css/ly-select2.css}">
    <link rel="stylesheet" th:href="@{/css/alifont/iconfont.css}">
    <link rel="stylesheet" th:href="@{/css/reset-style.css}">
    <style>
        .jconfirm {
            z-index: 10000;
        }

        #div-vue{
            display: flex;
        }
        .left,.right{
            width: 50%;
            flex-shrink: 1;
            display: flex;
            flex-direction: column;
        }
        .left .title,.right .title{
            text-align:center;
            background: #eeeeee;
            padding: 3px;
            border-radius: 4px;
        }
        .right-title{
            display: flex;
        }
        .right-title .title{
            background: #ffffff;
            color: #33cabb;
            border: 1px solid #33cabb;
            cursor: pointer;
            flex: 1;
        }
        .right-title .title:first-child{
            border-bottom-right-radius: 0;
            border-top-right-radius: 0;
        }
        .right-title .title:last-child{
            border-bottom-left-radius: 0;
            border-top-left-radius: 0;
        }

        .right-title .title.active{
            background: #33cabb;
            color: #ffffff
        }

        .list-wrap{
            border: 1px solid #c8c8c8;
            height: calc(100vh - 225px);
            overflow-y: auto;
            border-radius: 3px;
            overflow-x: hidden;
            padding: 2px;
        }
        .list-wrap .list-item{
            font-size: 14px;
            line-height: 26px;
            padding: 0 5px;
            position: relative;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            border-radius: 4px;
        }
        .list-wrap .list-item.actived{
            animation: actived 1.4s ease-in-out
        }

        .list-wrap .list-item .mdi {
            font-size: 22px;
            margin-right: 3px;
            color: #ff9800;
        }

        .list-wrap .list-item div{
            pointer-events: none;
        }

        .list-wrap .item-name{
            width: 100%;
            flex-shrink: 1;
        }
        .list-wrap .item-order{
            margin-left: 20px;
            flex-shrink: 0;
        }


        .list-wrap .list-item:hover{
            box-shadow: 0 0 4px 1px #33cabb inset;
        }

        .list-wrap:not(.undrop).drop-active .list-item:last-child:after{
            content: '';
            left: 0;
            position: absolute;
            top: 100%;
            display: block;
            width: 100%;
            height: 10px;
            box-shadow: 0 0 3px 1px #33cabb inset;
        }
        .list-wrap:not(.undrop).drop-active .list-item:last-child{
            margin-bottom: 10px;
        }

        .list-item:not(.undrop).drop-active{
            margin-top: 10px;
        }
        .list-item:not(.undrop).drop-active:before{
            content: '';
            left: 0;
            position: absolute;
            bottom: 100%;
            display: block;
            width: 100%;
            height: 10px;
            box-shadow: 0 0 3px 1px #33cabb inset;
        }

        .filter-wrap{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .filter-wrap .form-group{
            margin: 0;
        }
        .filter-wrap .form-group.filter{
            flex: 1;
        }
        .control{
            width: 100px;
            justify-content: center;
            display: flex;
            flex-direction: column;
        }
        .control button{
            padding: 4px 10px;
            margin: 5px 8px;
        }
        .none{
            margin-top: calc( (100vh - 245px) / 2 );
            text-align: center;
            padding: 0 20px;
            color: #888888;
        }
        @keyframes actived {
            0%{
                background: #ffffff;
            }
            25%{
                background: rgba(51, 202, 187, 0.7);
            }
            50%{
                background: #ffffff;
            }
            75%{
                 background: rgba(51, 202, 187, 0.7);
             }
            100%{
                background: #ffffff;
            }
        }

    </style>
</head>
<body>

<th:block layout:fragment="page-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="card table-card-30">
                <div class="card-header">
                    <h4>
                        <div class="back-button" onclick="history.back()" id="back-btn" data-original-title="返回">
                            <span class="mdi mdi-chevron-left"></span>返回
                        </div>
                        <span class="divide-line">|</span>抄表册维护<span
                            style="display: inline-block; margin: 0 4px; color: #dedede;">|</span>当前:《 <span
                            th:text="${name}"></span> 》
                    </h4>
                    <div style="font-size: 13px;line-height: 24px;float: right;display: flex">
                        <span class="mdi mdi-help-circle" style="font-size: 18px;"></span>
                        <p style="margin: 0;">左侧可绑定列表点击或拖拽添加至已绑定列表，右侧已绑定列表点击移除、拖拽调整位置及序号</p>
                    </div>
                </div>
                <div class="card-body">
                    <input type="hidden" id="meterBookId" th:value="*{meterBookId}">
                   <div id="div-vue">
                       <div class="left">
                           <div class="filter-wrap">
                               <div v-show="leftPlace === 'canBindList'" class="form-group" style="margin: 0 10px 0 0;">
                                   <div class="input-group">
                                       <span class="input-group-addon">所属小区</span>
                                       <select style="width: 160px;" id="community"
                                               class="form-control select2 select2-form-inline table-filter"
                                               data-filter="community.id" data-operator="eq">
                                           <option th:each="community : ${communities}" th:value="${community.id}"
                                                   th:text="${community.name}"></option>
                                       </select>
                                   </div>
                               </div>
                               <div class="form-group filter">
                                   <input class="form-control" type="text" v-model.trim="leftSearchVal" placeholder="搜索">
                               </div>
                           </div>
                           <div class="list-wrap" draggable>
                              <template v-for="(item, index) in showList">
                                  <div class="list-item" @click="clickItem('leftList', index)"  @dragstart="dragstart('leftList',index)"  draggable>
                                      <div class="item-name">{{item.measuringPoint? item.measuringPoint.name :''}} ( {{item.code}} )</div>
                                  </div>
                              </template>
                               <template v-if="showList.length === 0">
                                   <div v-if="leftSearchVal" class="none">
                                       未搜索到！
                                   </div>
                                   <div v-else-if="leftPlace === 'canBindList'" class="none">
                                       当前小区下没有可绑定的机械表，请选择其他小区！
                                   </div>
                                   <div v-else-if="leftPlace === 'removeList'" class="none">
                                       没有被移除的机械表！
                                   </div>
                               </template>
                           </div>
                           <div class="right-title">
                               <div class="title" :class="{'active': leftPlace==='canBindList'}" @click="leftSearchVal = null;leftPlace='canBindList'">可绑定({{noBindList.length}})</div>
                               <div class="title" :class="{'active': leftPlace==='removeList'}"@click="leftSearchVal = null;leftPlace='removeList'">已移除({{removeList.length}})</div>
                           </div>
                       </div>
                       <div class="control">
                           <button class="btn btn-default" style="padding: 4px 10px;" @click="allMove('left')"  :disabled="rightList.length === 0"><span class="mdi mdi-chevron-double-left"></span>全部</button>
                           <button class="btn btn-default" style="padding: 4px 10px;" @click="allMove('right')"  :disabled="showList.length === 0 ">全部<span class="mdi mdi-chevron-double-right"></span></button>
                       </div>
                       <div class="right">
                           <div class="filter-wrap">
                               <div class="form-group filter">
                                   <input class="form-control" type="text" v-model.trim="rightSearchVal" placeholder="搜索"  >
                               </div>
                           </div>
                           <div class="list-wrap" :class="{'undrop': dragIndex === rightList.length-1 && dragPlace === 'rightList'}"  @drop="drop('rightList', rightList.length)" @dragover="e=>e.preventDefault()" @dragenter="dragenter($event)" @dragleave="dragleave($event)" draggable>
                               <template v-for="(item, index) in rightList">
                                    <div  v-show="itemShow(item)"   class="list-item" :class="{'undrop': ((dragIndex === index) || ((dragIndex + 1) === index)) && dragPlace === 'rightList', 'actived': activedCode.includes(item.code)}" @click="clickItem('rightList', index)" @drop.stop="drop('rightList',index)" @dragstart="dragstart('rightList',index)" @dragenter.stop="dragenter($event, 1)" @dragleave.stop="dragleave($event, 1)" @dragover="e=>e.preventDefault()" draggable>
                                        <span v-if="canBindSelected.includes(item.code)" class="mdi mdi-new-box"></span><div class="item-name">{{item.measuringPoint? item.measuringPoint.name :''}} ( {{item.code}} )</div> <div class="item-order">序号{{index+1}}</div>
                                    </div>
                               </template>
                               <div v-if="rightSearchVal && rightShowList.length === 0" class="none">
                                   未搜索到！
                               </div>
                               <div v-else-if="rightList.length === 0" class="none">
                                   没有绑定的机械表！
                               </div>
                           </div>
                           <div class="title">已绑定({{rightList.length}})</div>
                       </div>
                   </div>
                    <div class="form-group m-t-15" style="margin-bottom: 0">
                        <button type="button" class="btn btn-primary m-r-5" onclick="submit()">确 定</button>
                        <button type="button" class="btn btn-default" onclick="history.back();return false;">返 回</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</th:block>

<th:block layout:fragment="page-script">
    <!--对话框-->
    <script th:src="@{/webjars/select2/js/select2.min.js}"></script>
    <script th:src="@{/webjars/select2/js/i18n/zh-CN.js}"></script>
    <script th:src="@{/webjars/vue/dist/vue.min.js}"></script>
    <script th:src="@{/webjars/light-year-admin/js/jconfirm/jquery-confirm.min.js}"></script>
    <script th:src="@{/js/common/confirm-extend.js}"></script>
    <script th:src="@{/js/common/query-filter.js}"></script>
    <script th:src="@{/js/common/enum-formatter.js}"></script>
    <script th:src="@{/js/archive/meter-book/setting.js}"></script>
</th:block>
</body>
</html>