<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security"
      layout:decorate="~{layout/layout}">
<head>
    <meta charset="UTF-8">
    <title>产测批次管理</title>

    <!--对话框-->
    <link rel="stylesheet" th:href="@{/webjars/light-year-admin/js/jconfirm/jquery-confirm.min.css}">
    <link rel="stylesheet" th:href="@{/webjars/bootstrap-table/dist/bootstrap-table.min.css}">
    <link rel="stylesheet" th:href="@{/css/reset-style.css}">

</head>
<body>

<th:block layout:fragment="page-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="card" id="search-card">
                <div class="card-body pd-t-5">

                    <form id="tableFilter" class="form-inline" role="form" onsubmit="return false;">
                        <div class="default-filter-wrap">
                            <div>
                                <div class="form-group mar_t">
                                    <div class="input-group">
                                        <span class="input-group-addon">批次名称</span>
                                        <input type="text" class="form-control table-filter" data-filter="batchName"
                                               data-operator="like" data-ignorecase="false"
                                               placeholder="请输入批次名称"/>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group mar_t filter-btn-group">
                                <button class="btn btn-primary" type="button" onclick="$('#table').reloadTable();">
                                    查询
                                </button>
                                <button class="btn btn btn-secondary" type="button"
                                        onclick="resetSelect2();$('#tableFilter').clearFilters();$('#table').reloadTable();">
                                    清除
                                </button>
                            </div>
                        </div>
                    </form>

                </div>
            </div>

            <div class="card table-card">
                <div class="card-body">

                    <div id="toolbar" class="toolbar-btn-action">
                        <a class="btn btn-primary m-r-5" th:href="@{/productionTest/testBatch/add}"
                           sec:authorize-url="/productionTest/testBatch/add"><i class="mdi mdi-plus"></i> 新增</a>
                        <a class="btn btn-danger" href="javascript:$('#table').deleteTableSelections('id');"
                           sec:authorize-url="/productionTest/testBatch/batchDel**"><i class="mdi mdi-window-close"></i>
                            删除</a>

                        <input type="hidden" id="hasEdit" value="true"
                               sec:authorize-url="/productionTest/testBatch/edit**">
                        <input type="hidden" id="hasDel" value="true"
                               sec:authorize-url="/productionTest/testBatch/delete**">
                    </div>
                    <table id="table"
                           class="f-table-r stripe-table"
                           data-icons-prefix="mdi"
                           data-icons="icons"
                           data-url="page"
                           data-sort-order="desc"
                           data-sort-name="id"
                           data-toolbar="#toolbar"
                           data-id-field="id"
                           data-unique-id="id"
                           data-click-to-select="true"
                           data-striped="true"
                           data-cache="false"
                           data-method="post"
                           data-show-columns="true"
                           data-show-refresh="true"
                           data-pagination="true"
                           data-side-pagination="server"
                           data-data-field="content"
                           data-total-field="totalElements"
                           data-page-list="[20, 50, 100, 200]" data-page-size="20"
                           data-detail-view="true"
                           data-detail-formatter="detailFormatter">
                        <thead>
                        <tr>
                            <th data-checkbox="true">选择</th>
                            <th data-field="batchName" data-sortable="true">名称</th>
                            <th data-field="integer" data-sortable="true">整数位数</th>
                            <th data-field="fraction" data-sortable="true">小数位数</th>
                            <th data-field="masterIp" data-sortable="true">主用IP</th>
                            <th data-field="masterPort" data-sortable="true">主用端口</th>
                            <th data-field="slaveIp" data-sortable="true">备用IP</th>
                            <th data-field="slavePort" data-sortable="true">备用端口</th>
                            <th data-field="companyId" data-sortable="true">公司名称</th>
                            <th data-field="remark" data-sortable="true">备注</th>
                            <th data-field="createdDate" data-sortable="true" data-width="180">创建时间</th>
                            <th data-field="operate" data-align="center" data-width="100" data-click-to-select="false"
                                data-formatter="operateFormatter" data-events="operateEvents">操作
                            </th>
                        </tr>
                        </thead>
                    </table>

                </div>
            </div>
        </div>
    </div>
</th:block>

<th:block layout:fragment="page-script">
    <script th:src="@{/webjars/jsrender/jsrender.min.js}"></script>
    <script id="detailViewTmpl" type="text/x-jsrender">
        <table class="table table-bordered">
          <tbody>
            <tr>
              <th style="width: 120px;">创建人:</th>
              <td>{{:createdBy}}</td>

              <th style="width: 120px;">修改人:</th>
              <td>{{:lastModifiedBy}}</td>

              <th style="width: 120px;">更新时间:</th>
              <td>{{:lastModifiedDate}}</td>
            </tr>
          </tbody>
        </table>
    </script>

    <!--对话框-->
    <script th:src="@{/webjars/light-year-admin/js/jconfirm/jquery-confirm.min.js}"></script>
    <script th:src="@{/js/common/confirm-extend.js}"></script>
    <script th:src="@{/webjars/bootstrap-table/dist/bootstrap-table.min.js}"></script>
    <script th:src="@{/webjars/bootstrap-table/dist/locale/bootstrap-table-zh-CN.min.js}"></script>
    <script th:src="@{/js/common/bootstrap-table-custom.js}"></script>
    <script th:src="@{/js/common/query-filter.js}"></script>

    <script th:src="@{/js/common/enum-formatter.js}"></script>
    <script th:src="@{/js/production-test/test-batch/list.js}"></script>
</th:block>
</body>
</html>