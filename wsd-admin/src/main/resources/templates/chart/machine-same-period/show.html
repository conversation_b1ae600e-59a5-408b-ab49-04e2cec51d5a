<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security"
      layout:decorate="~{layout/layout}">
<head>
    <meta charset="UTF-8">
    <title>同期用水分析</title>
    <link rel="stylesheet" th:href="@{/webjars/light-year-admin/js/bootstrap-datepicker/bootstrap-datepicker3.min.css}">
    <!--对话框-->
    <link rel="stylesheet" th:href="@{/webjars/light-year-admin/js/jconfirm/jquery-confirm.min.css}">
    <link rel="stylesheet" th:href="@{/webjars/bootstrap-table/dist/bootstrap-table.min.css}">
    <link rel="stylesheet" th:href="@{/css/alifont/iconfont.css}">
    <link rel="stylesheet" th:href="@{/webjars/select2/css/select2.min.css}">
    <link rel="stylesheet" th:href="@{/css/ly-select2.css}">
    <link rel="stylesheet" th:href="@{/css/reset-style.css}">
    <style>
        select.select2[multiple]+.select2-container{
            width: auto!important;
            min-width: 160px;
        }
    </style>
</head>
<body>

<th:block layout:fragment="page-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="card" id="search-card">
                <div class="card-body pd-t-5">

                    <form id="tableFilter" class="form-inline" role="form" onsubmit="return false;">
                        <div class="default-filter-wrap">
                            <div>

                                <div class="form-group mar_t mar_r_10" >
                                    <div class="input-group" >
                                        <span class="input-group-addon">统计维度</span>
                                        <select id="dimension" style="width:120px;"  class="form-control select2 select2-form-inline" name="dimension">
                                            <option value="community">小区</option>
                                            <option value="meterBook">抄表册</option>
                                            <option value="user">抄表员</option>
                                            <option value="waterClassification">用水类型</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group mar_t mar_r_10" >
                                    <div class="input-group" >
                                        <span class="input-group-addon">数据类型</span>
                                        <select id="dataType" style="width:120px;"  class="form-control select2 select2-form-inline" name="dataType">
                                            <option value="amount">金额</option>
                                            <option value="consumption">用水量</option>

                                        </select>
                                    </div>
                                </div>

                                <div class="form-group mar_t mar_r_10" >
                                    <div class="input-group" >
                                        <span class="input-group-addon">统计指标</span>
                                        <select id="indicator" style="width:120px;"  class="form-control select2 select2-form-inline" name="indicator">
                                            <option value="monitor">实抄</option>
                                            <option value="revenue">实收</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group mar_t mar_r_10">
                                    <div class="input-group">
                                        <span class="input-group-addon">统计月份</span>
                                        <div class="input-daterange input-group">
                                            <input class="form-control datepicker" type="text"
                                                   id="month" name="yearMonth" placeholder="请选择统计月份" readonly
                                                   style="background: #fff;width: 100px;border-top-right-radius: 4px;border-bottom-right-radius: 4px;">
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="form-group mar_t filter-btn-group">
                                <button class="btn btn-primary" type="button" onclick="loadData()">查询
                                </button>
                                <button class="btn btn btn-secondary" type="button" id="btn-reset">
                                    清除
                                </button>
                                <button type="button" class="btn btn-default toggle-more-filter"
                                        onclick="$(this).children().toggleClass('mdi-rotate-180')"
                                        data-toggle="collapse" data-target="#collapseFilter" aria-expanded="false"
                                        aria-controls="collapseExample">
                                    <span data-toggle="tooltip" data-placement="top" title="" data-original-title="更多筛选"
                                          class="mdi mdi-chevron-double-down"></span>
                                </button>
                            </div>
                        </div>
                        <div class="collapse" id="collapseFilter">
                            <div style="margin: 10px 0 0; border-top: 1px solid #eee">
                                <div class="form-group mar_t ">
                                    <div class="input-group">
                                        <span class="input-group-addon">抄表册</span>
                                        <select id="meterBookId" multiple="multiple" name="meterBookId" style="min-width:160px" class="form-control select2">
                                            <option value="">全部</option>
                                            <option th:each="meterBook : ${meterBooks}" th:value="${meterBook.id}" th:text="${meterBook.name}"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group mar_t" >
                                    <div class="input-group">
                                        <span class="input-group-addon">抄表员</span>
                                        <select id="userId" multiple="multiple" name="userId" style="min-width:160px" class="form-control select2 select2-form-inline">
                                            <option value="">全部</option>
                                            <option th:each="tollCollector : ${tollCollectors}" th:value="${tollCollector.id}" th:text="${tollCollector.realName}"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group mar_t mar_r_10" >
                                    <div class="input-group">
                                        <span class="input-group-addon">选择小区</span>
                                        <select id="communityId" name="communityId" style="width:165px;" class="form-control select2 select2-form-inline">
                                            <option value="">所有小区</option>
                                            <option th:each="community : ${communities}" th:value="${community.id}"
                                                    th:text="${community.name}"></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group mar_t mar_r_10">
                                    <div class="input-group">
                                        <div class="input-group-btn search-bar meter">
                                            <input type="hidden" id="search-field-account-no"
                                                   value="accountNo">
                                            <button class="btn btn-default dropdown-toggle" id="search-btn-account-no"
                                                    data-toggle="dropdown" type="button" aria-haspopup="true"
                                                    aria-expanded="false">
                                                水表编号 <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li data-field="meter-code">水表编号</li>
                                                <li data-field="meter-name">计量点名称</li>
                                                <li data-field="account-no">户号</li>
                                            </ul>
                                        </div>
                                        <input type="text" class="form-control table-filter radius-r-4"
                                               id="meter-code" name="meterCode"
                                               data-ignorecase="false" placeholder="请输入水表编号"/>
                                        <input type="text" class="form-control table-filter radius-r-4"
                                               style="display: none;width: 170px;" id="meter-name"
                                               name="pointName"
                                               data-ignorecase="false" placeholder="请输入计量点名称"/>
                                        <input type="text" class="form-control table-filter radius-r-4"
                                               style="display: none;width: 170px;" id="account-no"
                                               name="accountNo"
                                               data-ignorecase="false" placeholder="请输入户号"/>
                                    </div>
                                </div>
                                <div class="form-group mar_t">
                                    <div class="input-group customer-search-group">
                                        <div class="input-group-btn search-bar customer">
                                            <input type="hidden" id="search-field-mobile"
                                                   value="mobile">
                                            <button class="btn btn-default dropdown-toggle" id="search-btn-mobile"
                                                    data-toggle="dropdown" type="button" aria-haspopup="true"
                                                    aria-expanded="false">
                                                手机号 <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li data-field="mobile">手机号</li>
                                                <li data-field="roomNumber">身份证号</li>
                                                <li data-field="houseHolder">客户姓名</li>
                                            </ul>
                                        </div>
                                        <input type="text" class="form-control table-filter radius-r-4" id="mobile"
                                               data-ignorecase="false" name="mobile"
                                               placeholder="请输入手机号"/>
                                        <input type="text" class="form-control table-filter radius-r-4" id="roomNumber"
                                               data-ignorecase="false" style="display: none;" name="idCard"
                                               placeholder="请输入身份证号"/>
                                        <input type="text" class="form-control table-filter radius-r-4"
                                               id="houseHolder" style="display: none;"
                                               name="customerName"  data-ignorecase="false" placeholder="请输入客户姓名"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card table-card">
                <div class="card-body">
                    <div id="myChart" style="width: 100%;height:calc(100vh - 140px)"></div>
                </div>
            </div>
        </div>
    </div>
</th:block>

<th:block layout:fragment="page-script">
    <!--图表插件-->
    <script th:src="@{/js/chart/echarts.builder.min.js}"></script>

    <script th:src="@{/webjars/select2/js/select2.min.js}"></script>
    <script th:src="@{/webjars/select2/js/i18n/zh-CN.js}"></script>

    <!--对话框-->
    <script th:src="@{/webjars/light-year-admin/js/jconfirm/jquery-confirm.min.js}"></script>
    <script th:src="@{/js/common/confirm-extend.js}"></script>
    <script th:src="@{/webjars/bootstrap-table/dist/bootstrap-table.min.js}"></script>
    <script th:src="@{/webjars/bootstrap-table/dist/locale/bootstrap-table-zh-CN.min.js}"></script>
    <script th:src="@{/js/common/bootstrap-table-custom.js}"></script>

    <!--日期选择插件-->
    <script th:src="@{/webjars/light-year-admin/js/bootstrap-datetimepicker/moment.min.js}"></script>
    <script th:src="@{/webjars/light-year-admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js}"></script>
    <script th:src="@{/webjars/light-year-admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js}"></script>
    <script th:src="@{/js/common/query-filter.js}"></script>
    <script th:src="@{/js/common/enum-formatter.js}"></script>
    <script th:src="@{/js/chart/machine-same-period/show.js}"></script>
</th:block>
</body>
</html>
