<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security"
      layout:decorate="~{layout/layout}">
<head>
    <meta charset="UTF-8">
    <title>客户用水量统计</title>
    <link rel="stylesheet" th:href="@{/webjars/select2/css/select2.min.css}">
    <link rel="stylesheet" th:href="@{/webjars/bootstrap-table/dist/bootstrap-table.min.css}">
    <link rel="stylesheet" th:href="@{/css/ly-select2.css}">
    <link rel="stylesheet" th:href="@{/webjars/jstree/themes/default/style.min.css}">
    <link rel="stylesheet" th:href="@{/css/alifont/iconfont.css}">
    <!--日期选择插件-->
    <link rel="stylesheet" th:href="@{/webjars/light-year-admin/js/bootstrap-datepicker/bootstrap-datepicker3.min.css}">
    <link rel="stylesheet" th:href="@{/css/reset-style.css}">
    <style>
        body {
            margin: 0 -7px;
        }

        body .form-group .select2-container--default .select2-selection--single {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
        }

        .data-type .btn.btn-default.active {
            background-color: #33cabb !important;
            border-color: #33cabb !important;
            color: #fff !important;
        }

        .left-last {
            max-width: 132px;
        }
    </style>
</head>
<body>

<th:block layout:fragment="page-content">
    <div class="row" style="display: flex;position: relative; margin-right: -7px; margin-left: -7px;">
        <!--小区树-->
        <th:block th:replace="fragments/community-tree :: community-tree"/>

        <div class="tree-page-card" style="background: #ffffff;border-radius: 4px;margin-bottom: 12px">
            <div class="card-header">
                <form id="tableFilter" class="form-inline" role="form" style="display:block">
                    <input class="form-control table-filter" type="hidden" onchange="query();"  data-filter="regionId" id="regionId" data-operator="eq">
                    <input class="form-control table-filter" type="hidden" onchange="query()"  data-filter="communityId" id="communityId" data-operator="eq">

                    <div class="default-filter-wrap">
                        <div>
                            <div class="form-group mar_t" style="margin-right:10px">
                                <div class="input-group">
                                    <span class="input-group-addon">用水类型</span>
                                    <select id="waterClassificationId" style="width: 165px;"
                                            class="form-control select2 select2-form-inline">
                                        <option value="">所有用水类型</option>
                                        <option th:each="waterClassification : ${waterClassifications}"
                                                th:value="${waterClassification.id}"
                                                th:text="${waterClassification.name}"></option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group mar_t mar_r_10">
                                <div class="input-group">
                                    <div class="input-group-btn search-bar meter">
                                        <input type="hidden" name="search_field" id="search-field-meter-code"
                                               value="meterCode">
                                        <button class="btn btn-default dropdown-toggle" id="search-btn-meter-code"
                                                data-toggle="dropdown" type="button" aria-haspopup="true"
                                                aria-expanded="false">
                                            水表编号 <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li data-field="meter-code">水表编号</li>
                                            <li data-field="account-no">户号</li>
                                            <li data-field="measuring-point-name">计量点名称</li>
                                            <li data-field="mobile">手机号</li>
                                            <li data-field="customerName">客户姓名</li>
                                            <li data-field="idCard">身份证号码</li>
                                        </ul>
                                    </div>
                                    <input type="text" class="form-control table-filter radius-r-4"
                                           style="width: 170px;" id="meter-code"
                                           data-filter="meterCode" data-operator="like" data-ignorecase="false"
                                           placeholder="请输入水表编号"/>
                                    <input type="text" class="form-control table-filter radius-r-4"
                                           style="display: none;width: 184px;" id="account-no" data-filter="accountNo"
                                           data-operator="like" data-ignorecase="false" placeholder="请输入户号"/>
                                    <input type="text" class="form-control table-filter radius-r-4"
                                           style="display: none;width: 170px;" id="measuring-point-name"
                                           data-filter="measuringPointName" data-operator="like"
                                           data-ignorecase="false" placeholder="请输入计量点名称"/>
                                    <input type="text" class="form-control table-filter radius-r-4"
                                           style="display: none;width: 170px;" id="mobile"
                                           data-filter="mobile" data-operator="like"
                                           data-ignorecase="false" placeholder="请输入手机号码"/>
                                    <input type="text" class="form-control table-filter radius-r-4"
                                           style="display: none;width: 170px;" id="customerName"
                                           data-filter="customerName" data-operator="like"
                                           data-ignorecase="false" placeholder="请输入客户姓名"/>
                                    <input type="text" class="form-control table-filter radius-r-4"
                                           style="display: none;width: 170px;" id="idCard"
                                           data-filter="idCard" data-operator="like"
                                           data-ignorecase="false" placeholder="请输入身份证号码"/>
                                </div>
                            </div>
                            <div class="form-group mar_t mar_r_10">
                                <div class="input-group">
                                    <span class="input-group-addon">统计方式</span>
                                    <select id="dateType" style="width: 120px;"
                                            class="form-control select2 select2-form-inline"
                                            name="date-type">
                                        <option value="month">按月统计</option>
                                        <option value="day">按日统计</option>
                                    </select>
                                </div>
                            </div>
                            <div id="by-month" class="form-group mar_t mar_r_10">
                                <div class="input-group">
                                    <span class="input-group-addon">年份</span>
                                    <select id="year" style="width: 120px;"
                                            class="form-control select2 select2-form-inline">
                                    </select>
                                </div>
                            </div>
                            <div id="by-day" class="form-group mar_t mar_r_10" style="width:260px;display: none">
                                <div class="input-daterange input-group">
                                    <input class="form-control js-datepicker" data-date-format="yyyy-mm-dd" type="text"
                                           id="from" name="from" placeholder="从">
                                    <span class="input-group-addon"><i class="mdi mdi-chevron-right"></i></span>
                                    <input class="form-control js-datepicker" data-date-format="yyyy-mm-dd" type="text"
                                           id="to"
                                           name="to" placeholder="至">
                                </div>
                            </div>
                        </div>
                        <div class="form-group mar_t filter-btn-group">
                            <button class="btn btn-primary" type="button" id="btn-query">查询</button>
                            <button class="btn btn-default" type="button" id="btn-cancel">清除</button>
                        </div>
                    </div>
                </form>
            </div>

            <div class="card-body">
                <div class="row" style="margin: 0">
                    <div style="height: 40vh;" id="consumption-line"></div>
                </div>
                <div class="row" style="margin: 0;">
                    <input id="showCompanyId" type="hidden" value="true"/>
                    <div id="tableDiv">
                        <div class="toolbar-btn-action" style="float: left;">
                            <button class="btn btn-info" id="export-month-btn" type="button"
                                    onclick="exportConsumptionByMonth();"
                                    sec:authorize-url="/chart/customerConsumption/exportConsumptionByMonth">
                                <i class="mdi mdi-file-export"></i> 导出Excel
                            </button>
                            <form th:action="@{/chart/customerConsumption/exportConsumptionByMonth}"
                                  sec:authorize-url="/chart/customerConsumption/exportConsumptionByMonth" method="post"
                                  id="export-data-form-month" style='display:none'>
                                <input type="hidden" name="params" id="export-data-params-month"/>
                            </form>
                        </div>
                        <table id="table"
                               class="table f-table-l has-detail table-responsive stripe-table  table-bordered table-hover table-condensed"></table>
                    </div>
                    <div id="dayTableDiv" style="display: none;">
                        <div class="toolbar-btn-action" style="float: left;">
                            <button class="btn btn-info" id="export-day-btn" type="button"
                                    onclick="exportConsumptionByDay();"
                                    sec:authorize-url="/chart/customerConsumption/exportConsumptionByDay">
                                <i class="mdi mdi-file-export"></i> 导出Excel
                            </button>
                            <form th:action="@{/chart/customerConsumption/exportConsumptionByDay}"
                                  sec:authorize-url="/chart/customerConsumption/exportConsumptionByDay" method="post"
                                  id="export-data-form" style='display:none'>
                                <input type="hidden" name="filename" id="filename">
                                <input type="hidden" name="params" id="export-data-params"/>
                            </form>
                        </div>
                        <table id="dayTable"
                               class="table f-table-l has-detail table-responsive stripe-table table-bordered table-hover table-condensed"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

</th:block>

<th:block layout:fragment="page-script">
    <!--图表插件-->
    <script th:src="@{/js/chart/echarts.builder.min.js}"></script>

    <script th:src="@{/webjars/select2/js/select2.min.js}"></script>
    <script th:src="@{/webjars/select2/js/i18n/zh-CN.js}"></script>
    <!--日期选择插件-->
    <script th:src="@{/webjars/light-year-admin/js/bootstrap-datetimepicker/moment.min.js}"></script>
    <script th:src="@{/webjars/light-year-admin/js/bootstrap-datepicker/bootstrap-datepicker.min.js}"></script>
    <script th:src="@{/webjars/light-year-admin/js/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js}"></script>

    <script th:src="@{/webjars/bootstrap-table/dist/bootstrap-table.min.js}"></script>
    <script th:src="@{/js/common/bootstrap-table-fixed-columns-pro/bootstrap-table-fixed-columns-pro.js}"></script>
    <script th:src="@{/webjars/bootstrap-table/dist/extensions/defer-url/bootstrap-table-defer-url.min.js}"></script>
    <script th:src="@{/webjars/bootstrap-table/dist/locale/bootstrap-table-zh-CN.min.js}"></script>
    <script th:src="@{/js/common/bootstrap-table-custom.js}"></script>
    <script th:src="@{/js/common/query-filter.js}"></script>
    <script th:src="@{/webjars/jstree/jstree.min.js}"></script>
    <script th:src="@{/js/common/jstree-extend.js}"></script>
    <script th:src="@{/js/archive/community/community-tree-select.js}"></script>

    <script th:src="@{/js/common/enum-formatter.js}"></script>
    <script th:src="@{/js/chart/customer-consumption/show.js}"></script>
</th:block>

</body>
</html>