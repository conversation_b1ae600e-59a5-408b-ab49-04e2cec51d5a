[{"name": "首页", "code": "dashboard", "type": "MENU", "cssClass": "mdi mdi-home", "value": "/dashboard"}, {"name": "地图监控", "code": "map", "type": "MENU", "cssClass": "mdi mdi-google-maps", "value": "/map"}, {"name": "大屏展示", "code": "screen", "type": "MENU", "cssClass": "mdi mdi-airplay", "children": [{"name": "抄表大屏展示", "code": "screen-monitor", "type": "MENU", "value": "/screen/monitor/show"}, {"name": "营收大屏展示", "code": "screen-revenue", "type": "MENU", "value": "/screen/revenue/show"}]}, {"name": "抄表管理", "code": "monitor", "type": "MENU", "cssClass": "mdi mdi-access-point-network", "children": [{"name": "集中器维护", "code": "monitor-concentrator", "type": "MENU", "value": "/monitor/concentrator/list", "children": [{"name": "查询", "code": "monitor-concentrator-page", "type": "BUTTON", "value": "/monitor/concentrator/page"}, {"name": "配置", "code": "monitor-concentrator-settings", "type": "BUTTON", "value": "/monitor/concentrator/**/settings"}]}, {"name": "设备升级", "code": "monitor-upgrade", "type": "MENU", "value": "/monitor/upgrade/list", "children": [{"name": "查询", "code": "monitor-upgrade-page", "type": "BUTTON", "value": "/monitor/upgrade/page"}, {"name": "新增", "code": "monitor-upgrade-add", "type": "BUTTON", "value": "/monitor/upgrade/add**"}, {"name": "删除", "code": "monitor-upgrade-delete", "type": "BUTTON", "value": "/monitor/upgrade/delete**"}, {"name": "下载", "code": "monitor-upgrade-download", "type": "BUTTON", "value": "/monitor/upgrade/download**"}, {"name": "升级", "code": "monitor-upgrade-upgrade", "type": "BUTTON", "value": "/monitor/upgrade/upgrade**"}]}, {"name": "NB采集器维护", "code": "monitor-nbcollector", "type": "MENU", "value": "/monitor/nbcollector/list", "children": [{"name": "查询", "code": "monitor-nbcollector-page", "type": "BUTTON", "value": "/monitor/nbcollector/page"}, {"name": "配置", "code": "monitor-nbcollector-settings", "type": "BUTTON", "value": "/monitor/nbcollector/settings**"}, {"name": "读取版本", "code": "monitor-nbcollector-version", "type": "BUTTON", "value": "/monitor/nbcollector/version**"}]}, {"name": "水表(计量点)监测", "code": "monitor-meter", "type": "MENU", "value": "/monitor/meter/list", "children": [{"name": "查询", "code": "monitor-meter-page", "type": "BUTTON", "value": "/monitor/meter/page"}, {"name": "批量抄表", "code": "monitor-meter-batchMeteringData", "type": "BUTTON", "value": "/monitor/meter/batch/meteringData**"}, {"name": "批量开阀", "code": "monitor-meter-batchValveOpen", "type": "BUTTON", "value": "/monitor/meter/batch/valve/open**"}, {"name": "批量关阀", "code": "monitor-meter-batchValveClose", "type": "BUTTON", "value": "/monitor/meter/batch/valve/close**"}, {"name": "详细", "code": "monitor-meter-details", "type": "BUTTON", "value": "/monitor/meter/details**"}, {"name": "实抄", "code": "monitor-meter-meteringData", "type": "BUTTON", "value": "/monitor/meter/meteringData**"}, {"name": "开阀", "code": "monitor-meter-valveOpen", "type": "BUTTON", "value": "/monitor/meter/valve/open**"}, {"name": "关阀", "code": "monitor-meter-valveClose", "type": "BUTTON", "value": "/monitor/meter/valve/close**"}, {"name": "业务开阀", "code": "monitor-meter-serviceValveOpen", "type": "BUTTON", "value": "/monitor/meter/valve/serviceOpen**"}, {"name": "业务关阀", "code": "monitor-meter-serviceValveClose", "type": "BUTTON", "value": "/monitor/meter/valve/serviceClose**"}, {"name": "导出", "code": "monitor-meter-export", "type": "BUTTON", "value": "/monitor/meter/export"}, {"name": "设置基表底数", "code": "monitor-meter-setBaseValue", "type": "BUTTON", "value": "/monitor/meter/setBaseValue"}, {"name": "设置基表参数", "code": "monitor-meter-setBaseParam", "type": "BUTTON", "value": "/monitor/meter/setBaseParam"}, {"name": "设置上报模式", "code": "monitor-meter-setReportMode", "type": "BUTTON", "value": "/monitor/meter/setReportMode"}, {"name": "设置关阀后密集上报参数", "code": "monitor-meter-setValveClosedReportParam", "type": "BUTTON", "value": "/monitor/meter/setValveClosedReportParam"}, {"name": "设置上报参数", "code": "monitor-meter-setReportParams", "type": "BUTTON", "value": "/monitor/meter/setReportParams"}, {"name": "设置日流量阈值", "code": "monitor-meter-writeDailyFlow", "type": "BUTTON", "value": "/monitor/meter/writeDailyFlow"}, {"name": "设置IP参数", "code": "monitor-meter-setIp", "type": "BUTTON", "value": "/monitor/meter/setIp"}, {"name": "设置上报间隔", "code": "monitor-meter-setReportInterval", "type": "BUTTON", "value": "/monitor/meter/setReportInterval"}, {"name": "设置瞬时流量功能参数", "code": "monitor-meter-setInstantaneousFlowConfig", "type": "BUTTON", "value": "/monitor/meter/setInstantaneousFlowConfig"}, {"name": "设定制功能配置项", "code": "monitor-meter-setFuncValConfig", "type": "BUTTON", "value": "/monitor/meter/setFuncValConfig"}]}, {"name": "历史命令查询", "code": "monitor-command", "type": "MENU", "value": "/monitor/command/list", "children": [{"name": "查询", "code": "monitor-command-page", "type": "BUTTON", "value": "/monitor/command/page"}, {"name": "导出", "code": "monitor-command-export", "type": "BUTTON", "value": "/monitor/command/export**"}, {"name": "取消指令", "code": "monitor-command-cancelCmdLog", "type": "BUTTON", "value": "/monitor/command/cancelCmdLog**"}]}, {"name": "人工抄收", "code": "monitor-manualReading", "type": "MENU", "value": "/monitor/manualReading/list", "children": [{"name": "查询", "code": "monitor-manualReading-page", "type": "BUTTON", "value": "/monitor/manualReading/page"}, {"name": "上传抄收数据", "code": "monitor-manualReading-upload", "type": "BUTTON", "value": "/monitor/manualReading/upload**"}]}, {"name": "抄表成功率", "code": "monitor-successRate", "type": "MENU", "children": [{"name": "小区抄表成功率", "code": "monitor-successRate-community", "type": "MENU", "value": "/monitor/successRate/community/list", "children": [{"name": "查询", "code": "monitor-successRate-community-page", "type": "BUTTON", "value": "/monitor/successRate/community/page"}, {"name": "导出失败水表信息", "code": "monitor-successRate-community-export", "type": "BUTTON", "value": "/monitor/successRate/community/export"}]}, {"name": "集中器抄表成功率", "code": "monitor-successRate-concentrator", "type": "MENU", "value": "/monitor/successRate/concentrator/list", "children": [{"name": "查询", "code": "monitor-successRate-concentrator-page", "type": "BUTTON", "value": "/monitor/successRate/concentrator/page"}, {"name": "导出失败水表信息", "code": "monitor-successRate-concentrator-export", "type": "BUTTON", "value": "/monitor/successRate/concentrator/export"}]}]}, {"name": "数据查询", "code": "monitor-dataQuery", "type": "MENU", "children": [{"name": "日冻结数据查询", "code": "monitor-dataQuery-freeze", "type": "MENU", "value": "/monitor/freeze/list", "children": [{"name": "查询", "code": "monitor-dataQuery-freeze-page", "type": "BUTTON", "value": "/monitor/freeze/page"}, {"name": "数据导出", "code": "monitor-dataQuery-freeze-export", "type": "BUTTON", "value": "/monitor/freeze/export**"}, {"name": "日冻结数据校对", "code": "monitor-dataQuery-freeze-rectify", "type": "BUTTON", "value": "/monitor/freeze/dayFreezeDataRectify**"}, {"name": "删除日冻结数据", "code": "monitor-dataQuery-freeze-remove", "type": "BUTTON", "value": "/monitor/freeze/removeDayFreezeDataById**"}]}]}, {"name": "事件查询", "code": "monitor-event", "type": "MENU", "children": [{"name": "水表事件查询", "code": "monitor-event-meter", "type": "MENU", "value": "/monitor/event/meter/list", "children": [{"name": "查询", "code": "monitor-event-meter-events", "type": "BUTTON", "value": "/monitor/event/meter/events"}]}]}]}, {"name": "营收管理", "code": "revenue", "type": "MENU", "cssClass": "mdi mdi-currency-cny", "children": [{"name": "客户管理", "code": "revenue-customer", "type": "MENU", "value": "/revenue/customer/list", "children": [{"name": "查询", "code": "revenue-customer-query", "type": "BUTTON", "value": "/revenue/customer/page"}, {"name": "客户开户", "code": "revenue-customer-add", "type": "BUTTON", "value": "/revenue/customer/add"}, {"name": "编辑", "code": "revenue-customer-edit", "type": "BUTTON", "value": "/revenue/customer/edit**"}, {"name": "单个删除", "code": "revenue-customer-delete", "type": "BUTTON", "value": "/revenue/customer/delete**"}, {"name": "设置计量点", "code": "revenue-customer-configMeasuringPoint", "type": "BUTTON", "value": "/revenue/customer/configMeters**"}, {"name": "销户", "code": "revenue-customer-cancelPoint", "type": "BUTTON", "value": "/revenue/customer/configMeters/cancelPoint**"}, {"name": "重新开户", "code": "revenue-customer-reOpenAccount", "type": "BUTTON", "value": "/revenue/customer/configMeters/reOpenAccount**"}, {"name": "微信用户管理", "code": "revenue-customer-wxUserList", "type": "BUTTON", "value": "/revenue/customer/bindOrUnBindWechatUser**"}, {"name": "刷新可用余额", "code": "revenue-customer-refreshRealBalance", "type": "BUTTON", "value": "/revenue/customer/refreshRealBalance**"}, {"name": "配置计量点水价", "code": "revenue-customer-configMeasuringPointClassification", "type": "BUTTON", "value": "/revenue/customer/configMeasuringPointClassification"}, {"name": "开启/关闭微信公众号支付", "code": "revenue-customer-changeEnableWxPay", "type": "BUTTON", "value": "/revenue/customer/configMeters/changeEnableWxPay**"}, {"name": "开启/关闭自动开关阀", "code": "revenue-customer-changeAutoSwitchValve", "type": "BUTTON", "value": "/revenue/customer/configMeters/changeAutoSwitchValve**"}, {"name": "修改起始收费读数", "code": "revenue-customer-changeChargeValue", "type": "BUTTON", "value": "/revenue/customer/configMeters/changeChargeValue**"}, {"name": "设置计量点延迟关阀天数", "code": "revenue-customer-changeDelayCloseValveDays", "type": "BUTTON", "value": "/revenue/customer/configMeters/changeDelayCloseValveDays**"}, {"name": "打印水费报表", "code": "revenue-customer-printWaterFee", "type": "BUTTON", "value": "/revenue/customer/configMeters/printWaterFee**"}, {"name": "导入", "code": "revenue-customer-upload", "type": "BUTTON", "value": "/revenue/customer/upload"}, {"name": "导出", "code": "revenue-customer-export", "type": "BUTTON", "value": "/revenue/customer/export"}]}, {"name": "计量点管理", "code": "revenue-measuringPoint", "type": "MENU", "value": "/revenue/customer/configMeters/measuringPointManage", "children": [{"name": "查询", "code": "revenue-measuringPoint-query", "type": "BUTTON", "value": "/revenue/customer/configMeters/pageMeasuringPoints"}, {"name": "导出", "code": "revenue-measuringPoint-export", "type": "BUTTON", "value": "/revenue/customer/configMeters/exportMeasuringPoints"}, {"name": "批量结算账单", "code": "revenue-measuringPoint-calcBill", "type": "BUTTON", "value": "/revenue/customer/configMeters/batchCalcBills"}]}, {"name": "现金预存", "code": "revenue-reserve", "type": "MENU", "value": "/revenue/reserve/list", "children": [{"name": "查询", "code": "revenue-reserve-query", "type": "BUTTON", "value": "/revenue/reserve/page"}, {"name": "导出Excel", "code": "revenue-reserve-export", "type": "BUTTON", "value": "/revenue/reserve/export**"}, {"name": "现金预存详情", "code": "revenue-reserve-detail", "type": "BUTTON", "value": "/revenue/reserve/detail**", "children": [{"name": "现金预存", "code": "revenue-reserve-detail-cashReserve", "type": "BUTTON", "value": "/revenue/reserve/cashReserve**"}, {"name": "微信扫码预存", "code": "revenue-reserve-detail-wechatReserve", "type": "BUTTON", "value": "/revenue/reserve/wechatReserve**"}, {"name": "支付宝扫码预存", "code": "revenue-reserve-detail-aliPayReserve", "type": "BUTTON", "value": "/revenue/reserve/aliPayReserve**"}, {"name": "结算账单列表", "code": "revenue-reserve-detail-pageBill", "type": "BUTTON", "value": "/revenue/reserve/pageBill"}, {"name": "缴费记录列表", "code": "revenue-reserve-detail-pagePayIncome", "type": "BUTTON", "value": "/revenue/reserve/pagePayIncome"}, {"name": "换表记录列表", "code": "revenue-reserve-detail-pageChangeMeterLog", "type": "BUTTON", "value": "/revenue/reserve/pageChangeMeterLog"}]}]}, {"name": "营收业务查询", "code": "revenue-businessQuery", "type": "MENU", "children": [{"name": "结算账单查询", "code": "revenue-bill", "type": "MENU", "value": "/revenue/bill/list", "children": [{"name": "查询", "code": "revenue-bill-query", "type": "BUTTON", "value": "/revenue/bill/page"}, {"name": "导出Excel", "code": "revenue-bill-export", "type": "BUTTON", "value": "/revenue/bill/export**"}, {"name": "减免吨数", "code": "revenue-bill-reduceBill", "type": "BUTTON", "value": "/revenue/bill/reduceBill**"}, {"name": "账单撤消", "code": "revenue-bill-cancelBill", "type": "BUTTON", "value": "/revenue/bill/cancelBill**"}, {"name": "打印账单", "code": "revenue-bill-print", "type": "BUTTON", "value": "/revenue/bill/printBill**"}, {"name": "申请账单核销", "code": "revenue-bill-verify", "type": "BUTTON", "value": "/revenue/bill/applyVerifyBill**"}, {"name": "重新计算账单", "code": "revenue-bill-reCalcBill", "type": "BUTTON", "value": "/revenue/bill/reCalculateBill**"}]}, {"name": "缴费记录查询", "code": "revenue-payIncome", "type": "MENU", "value": "/revenue/payIncome/list", "children": [{"name": "查询", "code": "revenue-payIncome-query", "type": "BUTTON", "value": "/revenue/payIncome/page"}, {"name": "导出Excel", "code": "revenue-payIncome-export", "type": "BUTTON", "value": "/revenue/payIncome/export**"}, {"name": "打印票据", "code": "revenue-payIncome-print", "type": "BUTTON", "value": "/revenue/payIncome/printPayIncome**"}, {"name": "缴费退款", "code": "revenue-payIncome-refundPayIncome", "type": "BUTTON", "value": "/revenue/payIncome/refundPayIncome**"}]}, {"name": "销户退费查询", "code": "revenue-refund", "type": "MENU", "value": "/revenue/refund/list", "children": [{"name": "查询", "code": "revenue-refund-query", "type": "BUTTON", "value": "/revenue/refund/page"}]}, {"name": "欠费计量点查询", "code": "revenue-oweMeasuringPoint", "type": "MENU", "value": "/revenue/oweMeasuringPoint/list", "children": [{"name": "查询", "code": "revenue-oweMeasuringPoint-query", "type": "BUTTON", "value": "/revenue/oweMeasuringPoint/page"}, {"name": "导出", "code": "revenue-oweMeasuringPoint-export", "type": "BUTTON", "value": "/revenue/oweMeasuringPoint/export"}]}]}, {"name": "营收业务审核", "code": "revenue-businessAudit", "type": "MENU", "children": [{"name": "开户审核", "code": "revenue-applyOpenAccount", "type": "MENU", "value": "/revenue/applyOpenAccount/list", "children": [{"name": "查询", "code": "revenue-applyOpenAccount-query", "type": "BUTTON", "value": "/revenue/applyOpenAccount/page"}]}, {"name": "账单核销审核", "code": "revenue-billVerify", "type": "MENU", "value": "/revenue/billVerify/list", "children": [{"name": "查询", "code": "revenue-billVerify-page", "type": "BUTTON", "value": "/revenue/billVerify/page"}, {"name": "审核", "code": "revenue-billVerify-apply", "type": "BUTTON", "value": "/revenue/billVerify/apply"}]}, {"name": "微信退费审核", "code": "revenue-applyWxRefund", "type": "MENU", "value": "/revenue/applyWxRefund/list", "children": [{"name": "查询", "code": "revenue-applyWxRefund-page", "type": "BUTTON", "value": "/revenue/applyWxRefund/page"}, {"name": "审核", "code": "revenue-applyWxRefund-apply", "type": "BUTTON", "value": "/revenue/applyWxRefund/apply"}]}]}, {"name": "微信公众平台", "code": "revenue-wechat", "type": "MENU", "children": [{"name": "微信用户查询", "code": "revenue-wxUser", "type": "MENU", "value": "/revenue/wxuser/list", "children": [{"name": "用户查询", "code": "revenue-wxUser-query", "type": "BUTTON", "value": "/revenue/wxuser/page"}, {"name": "推送记录查询", "code": "revenue-wxUser-queryWxPushMsg", "type": "BUTTON", "value": "/revenue/wxuser/pageWxPushMsg"}]}, {"name": "微信用户咨询", "code": "revenue-advisory", "type": "MENU", "value": "/revenue/advisory/list", "children": [{"name": "查询", "code": "revenue-advisory-query", "type": "BUTTON", "value": "/revenue/advisory/page"}, {"name": "咨询回复", "code": "revenue-advisory-reply", "type": "BUTTON", "value": "/revenue/advisory/toReply**"}]}]}, {"name": "营收参数设置", "code": "revenue-settings", "type": "MENU", "children": [{"name": "营收配置向导", "code": "revenue-settings-guide", "type": "MENU", "value": "/revenue/settings/guide/index"}, {"name": "营收能力设置", "code": "revenue-settings-revenueConfig", "type": "MENU", "value": "/revenue/settings/revenueConfig/list", "children": [{"name": "查询", "code": "revenue-settings-revenueConfig-page", "type": "BUTTON", "value": "/revenue/settings/revenueConfig/page"}, {"name": "新增", "code": "revenue-settings-revenueConfig-add", "type": "BUTTON", "value": "/revenue/settings/revenueConfig/add**"}, {"name": "编辑", "code": "revenue-settings-revenueConfig-edit", "type": "BUTTON", "value": "/revenue/settings/revenueConfig/edit**"}, {"name": "删除", "code": "revenue-settings-revenueConfig-delete", "type": "BUTTON", "value": "/revenue/settings/revenueConfig/delete**"}]}, {"name": "支付参数设置", "code": "revenue-settings-pay", "type": "MENU", "value": "/revenue/settings/pay/list", "children": [{"name": "微信支付-查询", "code": "revenue-settings-pay-pageWxConfig", "type": "BUTTON", "value": "/revenue/settings/pay/pageWxConfig"}, {"name": "微信支付-新增", "code": "revenue-settings-pay-addWxConfig", "type": "BUTTON", "value": "/revenue/settings/pay/addWxConfig**"}, {"name": "微信支付-编辑", "code": "revenue-settings-pay-editWxConfig", "type": "BUTTON", "value": "/revenue/settings/pay/editWxConfig**"}, {"name": "微信支付-删除", "code": "revenue-settings-pay-deleteWxConfig", "type": "BUTTON", "value": "/revenue/settings/pay/deleteWxConfig**"}, {"name": "支付宝支付-查询", "code": "revenue-settings-pay-pageAliConfig", "type": "BUTTON", "value": "/revenue/settings/pay/pageAliConfig"}, {"name": "支付宝支付-新增", "code": "revenue-settings-pay-addAliConfig", "type": "BUTTON", "value": "/revenue/settings/pay/addAliConfig**"}, {"name": "支付宝支付-编辑", "code": "revenue-settings-pay-editAliConfig", "type": "BUTTON", "value": "/revenue/settings/pay/editAliConfig**"}, {"name": "支付宝支付-删除", "code": "revenue-settings-pay-deleteAliConfig", "type": "BUTTON", "value": "/revenue/settings/pay/deleteAliConfig**"}, {"name": "农行支付-查询", "code": "revenue-settings-pay-pageAbchinaPayConfig", "type": "BUTTON", "value": "/revenue/settings/pay/pageAbchinaPayConfig"}, {"name": "农行支付-新增", "code": "revenue-settings-pay-addAbchinaPayConfig", "type": "BUTTON", "value": "/revenue/settings/pay/addAbchinaPayConfig**"}, {"name": "农行支付-编辑", "code": "revenue-settings-pay-editAbchinaPayConfig", "type": "BUTTON", "value": "/revenue/settings/pay/editAbchinaPayConfig**"}, {"name": "农行支付-删除", "code": "revenue-settings-pay-deleteAbchinaPayConfig", "type": "BUTTON", "value": "/revenue/settings/pay/deleteAbchinaPayConfig**"}, {"name": "首信易支付-查询", "code": "revenue-settings-pay-pageSxyPayConfig", "type": "BUTTON", "value": "/revenue/settings/pay/pageSxyPayConfig"}, {"name": "首信易支付-新增", "code": "revenue-settings-pay-addSxyPayConfig", "type": "BUTTON", "value": "/revenue/settings/pay/addSxyPayConfig**"}, {"name": "首信易支付-编辑", "code": "revenue-settings-pay-editSxyPayConfig", "type": "BUTTON", "value": "/revenue/settings/pay/editSxyPayConfig**"}, {"name": "首信易支付-删除", "code": "revenue-settings-pay-deleteSxyPayConfig", "type": "BUTTON", "value": "/revenue/settings/pay/deleteSxyPayConfig**"}, {"name": "首信易支付-商户入网状态同步", "code": "revenue-settings-pay-queryAndSyncSubMerchantStatus", "type": "BUTTON", "value": "/revenue/settings/pay/queryAndSyncSubMerchantStatus**"}, {"name": "首信易支付-商户提现", "code": "revenue-settings-pay-sxyWithdraw", "type": "BUTTON", "value": "/revenue/settings/pay/sxyWithdraw**"}, {"name": "首信易支付-查询易支付分成商户配置", "code": "revenue-settings-pay-sxy-pageSxySplitMerchantConfig", "type": "BUTTON", "value": "/revenue/settings/pay/sxy/pageSxySplitMerchantConfig"}, {"name": "首信易支付-新增易支付分成商户配置", "code": "revenue-settings-pay-sxy-addSxySplitMerchantConfig", "type": "BUTTON", "value": "/revenue/settings/pay/sxy/addSxySplitMerchantConfig**"}, {"name": "首信易支付-编辑易支付分成商户配置", "code": "revenue-settings-pay-sxy-editSxySplitMerchantConfig", "type": "BUTTON", "value": "/revenue/settings/pay/sxy/editSxySplitMerchantConfig**"}, {"name": "首信易支付-删除易支付分成商户配置", "code": "revenue-settings-pay-sxy-deleteSxySplitMerchantConfig", "type": "BUTTON", "value": "/revenue/settings/pay/sxy/deleteSxySplitMerchantConfig"}, {"name": "首信易支付-查询易支付分成关系配置", "code": "revenue-settings-pay-sxy-pageSxyRevenueSplitConfig", "type": "BUTTON", "value": "/revenue/settings/pay/sxy/pageSxyRevenueSplitConfig"}, {"name": "首信易支付-新增易支付分成关系配置", "code": "revenue-settings-pay-sxy-addSxyRevenueSplitConfig", "type": "BUTTON", "value": "/revenue/settings/pay/sxy/addSxyRevenueSplitConfig**"}, {"name": "首信易支付-编辑易支付分成关系配置", "code": "revenue-settings-pay-sxy-editSxyRevenueSplitConfig", "type": "BUTTON", "value": "/revenue/settings/pay/sxy/editSxyRevenueSplitConfig**"}, {"name": "首信易支付-删除易支付分成关系配置", "code": "revenue-settings-pay-sxy-deleteSxyRevenueSplitConfig", "type": "BUTTON", "value": "/revenue/settings/pay/sxy/deleteSxyRevenueSplitConfig"}]}, {"name": "结算周期设置", "code": "revenue-settings-settlement", "type": "MENU", "value": "/revenue/settings/settlement/list", "children": [{"name": "查询", "code": "revenue-settings-settlement-page", "type": "BUTTON", "value": "/revenue/settings/settlement/page"}, {"name": "新增", "code": "revenue-settings-settlement-add", "type": "BUTTON", "value": "/revenue/settings/settlement/add**"}, {"name": "编辑", "code": "revenue-settings-settlement-edit", "type": "BUTTON", "value": "/revenue/settings/settlement/edit**"}, {"name": "删除", "code": "revenue-settings-settlement-delete", "type": "BUTTON", "value": "/revenue/settings/settlement/delete**"}]}, {"name": "水价管理", "code": "revenue-settings-price", "type": "MENU", "value": "/revenue/settings/price/list", "children": [{"name": "查询", "code": "revenue-settings-price-waterClassifications", "type": "BUTTON", "value": "/revenue/settings/price/waterClassifications"}, {"name": "新增", "code": "revenue-settings-price-add", "type": "BUTTON", "value": "/revenue/settings/price/add**"}, {"name": "编辑", "code": "revenue-settings-price-edit", "type": "BUTTON", "value": "/revenue/settings/price/edit**"}, {"name": "删除", "code": "revenue-settings-price-delete", "type": "BUTTON", "value": "/revenue/settings/price/delete**"}]}, {"name": "小区结算设置", "code": "revenue-settings-community", "type": "MENU", "value": "/revenue/settings/community/list", "children": [{"name": "查询", "code": "revenue-settings-community-page", "type": "BUTTON", "value": "/revenue/settings/community/page"}, {"name": "启用/禁用小区结算", "code": "revenue-settings-community-changeSettlementStatus", "type": "BUTTON", "value": "/revenue/settings/community/changeSettlementStatus**"}, {"name": "设置小区结算类型", "code": "revenue-settings-community-changeSettlementType", "type": "BUTTON", "value": "/revenue/settings/community/changeSettlementType**"}, {"name": "设置小区自动开关阀", "code": "revenue-settings-community-changeAutoSwitchValve", "type": "BUTTON", "value": "/revenue/settings/community/changeAutoSwitchValve**"}, {"name": "设置业主支付手续费", "code": "revenue-settings-community-changeOwnerPayPoundage", "type": "BUTTON", "value": "/revenue/settings/community/changeOwnerPayPoundage**"}, {"name": "设置/修改小区用水类型", "code": "revenue-settings-community-configCommunityClassification", "type": "BUTTON", "value": "/revenue/settings/community/configCommunityClassification**"}, {"name": "设置小区异常流量阈值", "code": "revenue-settings-community-configFlowLimit", "type": "BUTTON", "value": "/revenue/settings/community/configFlowLimit**"}, {"name": "设置小区最低消费限额", "code": "revenue-settings-community-configMinChargeLimit", "type": "BUTTON", "value": "/revenue/settings/community/configMinChargeLimit**"}, {"name": "设置小区服务费", "code": "revenue-settings-community-configServiceFee", "type": "BUTTON", "value": "/revenue/settings/community/configServiceFee**"}, {"name": "设置小区延迟关阀天数", "code": "revenue-settings-community-changeDelayCloseValveDays", "type": "BUTTON", "value": "/revenue/settings/community/changeDelayCloseValveDays**"}, {"name": "修改小区结算日期", "code": "revenue-settings-community-changeCommunityEnableDate", "type": "BUTTON", "value": "/revenue/settings/community/changeCommunityEnableDate**"}]}, {"name": "小区保水", "code": "revenue-settings-waterRetention", "type": "MENU", "value": "/revenue/settings/waterRetention/list", "children": [{"name": "查询", "code": "revenue-settings-waterRetention-page", "type": "BUTTON", "value": "/revenue/settings/waterRetention/page"}, {"name": "新增", "code": "revenue-settings-waterRetention-add", "type": "BUTTON", "value": "/revenue/settings/waterRetention/add**"}, {"name": "编辑", "code": "revenue-settings-waterRetention-edit", "type": "BUTTON", "value": "/revenue/settings/waterRetention/edit**"}, {"name": "删除", "code": "revenue-settings-waterRetention-delete", "type": "BUTTON", "value": "/revenue/settings/waterRetention/delete**"}]}, {"name": "票据模板管理", "code": "revenue-settings-receiptTemplate", "type": "MENU", "value": "/revenue/receipt/list", "children": [{"name": "查询", "code": "revenue-settings-receiptTemplate-findAllReceiptTemplate", "type": "BUTTON", "value": "/revenue/receipt/findAllReceiptTemplate"}, {"name": "新增", "code": "revenue-settings-receiptTemplate-add", "type": "BUTTON", "value": "/revenue/receipt/add**"}, {"name": "编辑", "code": "revenue-settings-receiptTemplate-edit", "type": "BUTTON", "value": "/revenue/receipt/editBaseInfo"}, {"name": "设计", "code": "revenue-settings-receiptTemplate-design", "type": "BUTTON", "value": "/revenue/receipt/edit"}, {"name": "删除", "code": "revenue-settings-receiptTemplate-delete", "type": "BUTTON", "value": "/revenue/receipt/delete**"}, {"name": "复制", "code": "revenue-settings-receiptTemplate-copy", "type": "BUTTON", "value": "/revenue/receipt/copy**"}]}]}]}, {"name": "档案管理", "code": "archive", "type": "MENU", "cssClass": "mdi mdi-archive", "children": [{"name": "小区管理", "code": "archive-community", "type": "MENU", "value": "/archive/community/list", "children": [{"name": "查询", "code": "archive-community-page", "type": "BUTTON", "value": "/archive/community/page"}, {"name": "新增", "code": "archive-community-add", "type": "BUTTON", "value": "/archive/community/add"}, {"name": "批量删除", "code": "archive-community-batch<PERSON>el", "type": "BUTTON", "value": "/archive/community/batchDel**"}, {"name": "编辑", "code": "archive-community-edit", "type": "BUTTON", "value": "/archive/community/edit**"}, {"name": "单个删除", "code": "archive-community-delete", "type": "BUTTON", "value": "/archive/community/delete**"}, {"name": "配置下级区域", "code": "archive-community-configLowerCommunity", "type": "BUTTON", "value": "/archive/community/configLowerCommunity"}, {"name": "新增下级区域", "code": "archive-community-addLowerCommunity", "type": "BUTTON", "value": "/archive/community/addLowerCommunity"}, {"name": "批量删除下级区域", "code": "archive-community-batchDeleteLowerCommunity", "type": "BUTTON", "value": "/archive/community/batchDeleteLowerCommunity"}, {"name": "编辑下级区域", "code": "archive-community-editLowerCommunity", "type": "BUTTON", "value": "/archive/community/editLowerCommunity"}, {"name": "单个删除下级区域", "code": "archive-community-deleteLowerCommunity", "type": "BUTTON", "value": "/archive/community/deleteLowerCommunity"}]}, {"name": "集中器管理", "code": "archive-concentrator", "type": "MENU", "value": "/archive/concentrator/list", "children": [{"name": "查询", "code": "archive-concentrator-page", "type": "BUTTON", "value": "/archive/concentrator/page"}, {"name": "新增", "code": "archive-concentrator-add", "type": "BUTTON", "value": "/archive/concentrator/add"}, {"name": "批量删除", "code": "archive-concentrator-batchDel", "type": "BUTTON", "value": "/archive/concentrator/batchDel**"}, {"name": "批量导入", "code": "archive-concentrator-upload", "type": "BUTTON", "value": "/archive/concentrator/upload**"}, {"name": "导出Excel", "code": "archive-concentrator-export", "type": "BUTTON", "value": "/archive/concentrator/export**"}, {"name": "编辑", "code": "archive-concentrator-edit", "type": "BUTTON", "value": "/archive/concentrator/edit**"}, {"name": "单个删除", "code": "archive-concentrator-delete", "type": "BUTTON", "value": "/archive/concentrator/delete**"}, {"name": "更换集中器", "code": "archive-concentrator-change", "type": "BUTTON", "value": "/archive/concentrator/change**"}]}, {"name": "NB采集器管理", "code": "archive-nbcollector", "type": "MENU", "value": "/archive/nbcollector/list", "children": [{"name": "查询", "code": "archive-nbcollector-page", "type": "BUTTON", "value": "/archive/nbcollector/page"}, {"name": "新增", "code": "archive-nbcollector-add", "type": "BUTTON", "value": "/archive/nbcollector/add"}, {"name": "批量删除", "code": "archive-nbcollector-batchDel", "type": "BUTTON", "value": "/archive/nbcollector/batchDel**"}, {"name": "同步状态", "code": "archive-nbcollector-syncStatus", "type": "BUTTON", "value": "/archive/nbcollector/syncStatus**"}, {"name": "编辑", "code": "archive-nbcollector-edit", "type": "BUTTON", "value": "/archive/nbcollector/edit**"}, {"name": "单个删除", "code": "archive-nbcollector-delete", "type": "BUTTON", "value": "/archive/nbcollector/delete**"}]}, {"name": "基表管理", "code": "archive-basemeter", "type": "MENU", "value": "/archive/basemeter/list", "children": [{"name": "查询", "code": "archive-basemeter-page", "type": "BUTTON", "value": "/archive/basemeter/page"}, {"name": "新增", "code": "archive-basemeter-add", "type": "BUTTON", "value": "/archive/basemeter/add"}, {"name": "批量删除", "code": "archive-basemeter-batchDel", "type": "BUTTON", "value": "/archive/basemeter/batchDel**"}, {"name": "编辑", "code": "archive-basemeter-edit", "type": "BUTTON", "value": "/archive/basemeter/edit**"}, {"name": "单个删除", "code": "archive-basemeter-delete", "type": "BUTTON", "value": "/archive/basemeter/delete**"}]}, {"name": "水表型号管理", "code": "archive-model", "type": "MENU", "value": "/archive/model/list", "children": [{"name": "查询", "code": "archive-model-page", "type": "BUTTON", "value": "/archive/model/page"}, {"name": "新增", "code": "archive-model-add", "type": "BUTTON", "value": "/archive/model/add"}, {"name": "批量删除", "code": "archive-model-batchDel", "type": "BUTTON", "value": "/archive/model/batchDel**"}, {"name": "编辑", "code": "archive-model-edit", "type": "BUTTON", "value": "/archive/model/edit**"}, {"name": "单个删除", "code": "archive-model-delete", "type": "BUTTON", "value": "/archive/model/delete**"}]}, {"name": "NB模组程序管理", "code": "archive-nbWmProgram", "type": "MENU", "value": "/archive/nbWmProgram/list", "children": [{"name": "查询", "code": "archive-nbWmProgram-page", "type": "BUTTON", "value": "/archive/nbWmProgram/page"}, {"name": "新增", "code": "archive-nbWmProgram-add", "type": "BUTTON", "value": "/archive/nbWmProgram/add"}, {"name": "编辑", "code": "archive-nbWmProgram-edit", "type": "BUTTON", "value": "/archive/nbWmProgram/edit**"}, {"name": "删除", "code": "archive-nbWmProgram-delete", "type": "BUTTON", "value": "/archive/nbWmProgram/delete**"}]}, {"name": "NB水表预导入", "code": "archive-preimport", "type": "MENU", "value": "/archive/preImport/list", "children": [{"name": "查询", "code": "archive-preimport-page", "type": "BUTTON", "value": "/archive/preImport/page"}, {"name": "导入", "code": "archive-preimport-import", "type": "BUTTON", "value": "/archive/preImport/import"}]}, {"name": "抄表册管理", "code": "archive-meterBook", "type": "MENU", "value": "/archive/meterBook/list", "children": [{"name": "查询", "code": "archive-meterBook-page", "type": "BUTTON", "value": "/archive/meterBook/page"}, {"name": "新增", "code": "archive-meterBook-add", "type": "BUTTON", "value": "/archive/meterBook/add"}, {"name": "批量删除", "code": "archive-meterBook-batchDel", "type": "BUTTON", "value": "/archive/meterBook/batchDel**"}, {"name": "编辑", "code": "archive-meterBook-edit", "type": "BUTTON", "value": "/archive/meterBook/edit**"}, {"name": "单个删除", "code": "archive-meterBook-delete", "type": "BUTTON", "value": "/archive/meterBook/delete**"}, {"name": "配置", "code": "archive-meterBook-settings", "type": "BUTTON", "value": "/archive/meterBook/settings"}]}, {"name": "水表(计量点)管理", "code": "archive-meter", "type": "MENU", "children": [{"name": "集中器水表管理", "code": "archive-meter-concentrator", "type": "MENU", "value": "/archive/meter/concentrator/list", "children": [{"name": "查询", "code": "archive-meter-concentrator-page", "type": "BUTTON", "value": "/archive/meter/concentrator/page"}, {"name": "新增", "code": "archive-meter-concentrator-add", "type": "BUTTON", "value": "/archive/meter/concentrator/add"}, {"name": "批量删除", "code": "archive-meter-concentrator-batchDel", "type": "BUTTON", "value": "/archive/meter/concentrator/batchDel**"}, {"name": "批量导入", "code": "archive-meter-concentrator-upload", "type": "BUTTON", "value": "/archive/meter/concentrator/upload**"}, {"name": "批量编辑", "code": "archive-meter-concentrator-batchEdit", "type": "BUTTON", "value": "/archive/meter/concentrator/batchEdit"}, {"name": "编辑", "code": "archive-meter-concentrator-edit", "type": "BUTTON", "value": "/archive/meter/concentrator/edit**"}, {"name": "单个删除", "code": "archive-meter-concentrator-delete", "type": "BUTTON", "value": "/archive/meter/concentrator/delete**"}, {"name": "更换水表", "code": "archive-meter-concentrator-change", "type": "BUTTON", "value": "/archive/meter/concentrator/change"}, {"name": "修改表号", "code": "archive-meter-concentrator-changeMeterCode", "type": "BUTTON", "value": "/archive/meter/concentrator/changeMeterCode"}, {"name": "设置倍率", "code": "archive-meter-concentrator-configMeterRate", "type": "BUTTON", "value": "/archive/meter/concentrator/configMeterRate"}, {"name": "绑定父表", "code": "archive-meter-concentrator-boundParentMeter", "type": "BUTTON", "value": "/archive/meter/concentrator/boundParentMeter"}]}, {"name": "NB水表管理", "code": "archive-meter-nbiot", "type": "MENU", "value": "/archive/meter/nbiot/list", "children": [{"name": "查询", "code": "archive-meter-nbiot-page", "type": "BUTTON", "value": "/archive/meter/nbiot/page"}, {"name": "新增", "code": "archive-meter-nbiot-add", "type": "BUTTON", "value": "/archive/meter/nbiot/add"}, {"name": "批量删除", "code": "archive-meter-nbiot-batchDel", "type": "BUTTON", "value": "/archive/meter/nbiot/batchDel**"}, {"name": "批量导入", "code": "archive-meter-nbiot-upload", "type": "BUTTON", "value": "/archive/meter/nbiot/upload**"}, {"name": "批量编辑", "code": "archive-meter-nbiot-batchEdit", "type": "BUTTON", "value": "/archive/meter/nbiot/batchEdit"}, {"name": "同步状态", "code": "archive-meter-nbiot-syncStatus", "type": "BUTTON", "value": "/archive/meter/nbiot/syncStatus**"}, {"name": "编辑", "code": "archive-meter-nbiot-edit", "type": "BUTTON", "value": "/archive/meter/nbiot/edit**"}, {"name": "单个删除", "code": "archive-meter-nbiot-delete", "type": "BUTTON", "value": "/archive/meter/nbiot/delete**"}, {"name": "更换水表", "code": "archive-meter-nbiot-change", "type": "BUTTON", "value": "/archive/meter/nbiot/change"}, {"name": "更换水表模块", "code": "archive-meter-nbiot-changeModule", "type": "BUTTON", "value": "/archive/meter/nbiot/changeModule"}, {"name": "修改表号", "code": "archive-meter-nbiot-changeMeterCode", "type": "BUTTON", "value": "/archive/meter/nbiot/changeMeterCode"}, {"name": "设置倍率", "code": "archive-meter-nbiot-configMeterRate", "type": "BUTTON", "value": "/archive/meter/nbiot/configMeterRate"}, {"name": "绑定父表", "code": "archive-meter-nbiot-boundParentMeter", "type": "BUTTON", "value": "/archive/meter/nbiot/boundParentMeter"}]}, {"name": "机械水表管理", "code": "archive-meter-machine", "type": "MENU", "value": "/archive/meter/machine/list", "children": [{"name": "查询", "code": "archive-meter-machine-page", "type": "BUTTON", "value": "/archive/meter/machine/page"}, {"name": "新增", "code": "archive-meter-machine-add", "type": "BUTTON", "value": "/archive/meter/machine/add"}, {"name": "批量删除", "code": "archive-meter-machine-batchDel", "type": "BUTTON", "value": "/archive/meter/machine/batchDel**"}, {"name": "批量导入", "code": "archive-meter-machine-upload", "type": "BUTTON", "value": "/archive/meter/machine/upload**"}, {"name": "批量编辑", "code": "archive-meter-machine-batchEdit", "type": "BUTTON", "value": "/archive/meter/machine/batchEdit"}, {"name": "编辑", "code": "archive-meter-machine-edit", "type": "BUTTON", "value": "/archive/meter/machine/edit**"}, {"name": "单个删除", "code": "archive-meter-machine-delete", "type": "BUTTON", "value": "/archive/meter/machine/delete**"}, {"name": "更换水表", "code": "archive-meter-machine-change", "type": "BUTTON", "value": "/archive/meter/machine/change"}, {"name": "修改表号", "code": "archive-meter-machine-changeMeterCode", "type": "BUTTON", "value": "/archive/meter/machine/changeMeterCode"}, {"name": "绑定父表", "code": "archive-meter-machine-boundParentMeter", "type": "BUTTON", "value": "/archive/meter/machine/boundParentMeter"}, {"name": "修改水表使用状态", "code": "archive-meter-machine-changeMeterUseState", "type": "BUTTON", "value": "/archive/meter/machine/changeMeterUseState"}]}, {"name": "UDP直连水表管理", "code": "archive-meter-cat1", "type": "MENU", "value": "/archive/meter/cat1/list", "children": [{"name": "查询", "code": "archive-meter-cat1-page", "type": "BUTTON", "value": "/archive/meter/cat1/page"}, {"name": "新增", "code": "archive-meter-cat1-add", "type": "BUTTON", "value": "/archive/meter/cat1/add"}, {"name": "批量删除", "code": "archive-meter-cat1-batchDel", "type": "BUTTON", "value": "/archive/meter/cat1/batchDel**"}, {"name": "批量导入", "code": "archive-meter-cat1-upload", "type": "BUTTON", "value": "/archive/meter/cat1/upload**"}, {"name": "批量编辑", "code": "archive-meter-cat1-batchEdit", "type": "BUTTON", "value": "/archive/meter/cat1/batchEdit"}, {"name": "同步状态", "code": "archive-meter-cat1-syncStatus", "type": "BUTTON", "value": "/archive/meter/cat1/syncStatus**"}, {"name": "编辑", "code": "archive-meter-cat1-edit", "type": "BUTTON", "value": "/archive/meter/cat1/edit**"}, {"name": "单个删除", "code": "archive-meter-cat1-delete", "type": "BUTTON", "value": "/archive/meter/cat1/delete**"}, {"name": "更换水表", "code": "archive-meter-cat1-change", "type": "BUTTON", "value": "/archive/meter/cat1/change"}, {"name": "更换水表模块", "code": "archive-meter-cat1-changeModule", "type": "BUTTON", "value": "/archive/meter/cat1/changeModule"}, {"name": "修改表号", "code": "archive-meter-cat1-changeMeterCode", "type": "BUTTON", "value": "/archive/meter/cat1/changeMeterCode"}, {"name": "绑定父表", "code": "archive-meter-cat1-boundParentMeter", "type": "BUTTON", "value": "/archive/meter/cat1/boundParentMeter"}]}, {"name": "水表批量更换小区", "code": "archive-meter-changeCommunity", "type": "BUTTON", "value": "/archive/meter/waterMeter/changeCommunity"}]}, {"name": "换表(模块)记录查询", "code": "archive-changeMeterLog", "type": "MENU", "value": "/archive/changeMeterLog/list", "children": [{"name": "查询", "code": "archive-changeMeterLog-page", "type": "BUTTON", "value": "/archive/changeMeterLog/page"}]}, {"name": "装箱批次管理", "code": "archive-packBoxBatch", "type": "MENU", "value": "/archive/packBoxBatch/list", "children": [{"name": "查询", "code": "archive-packBoxBatch-page", "type": "BUTTON", "value": "/archive/packBoxBatch/page"}, {"name": "新增", "code": "archive-packBoxBatch-add", "type": "BUTTON", "value": "/archive/packBoxBatch/add"}, {"name": "编辑", "code": "archive-packBoxBatch-edit", "type": "BUTTON", "value": "/archive/packBoxBatch/edit"}, {"name": "删除", "code": "archive-packBoxBatch-delete", "type": "BUTTON", "value": "/archive/packBoxBatch/delete"}, {"name": "查看装箱", "code": "archive-packBoxBatch-getPackBoxes", "type": "BUTTON", "value": "/archive/packBoxBatch/getPackBoxes"}]}, {"name": "装箱管理", "code": "archive-packBox", "type": "MENU", "value": "/archive/packBox/list", "children": [{"name": "查询", "code": "archive-packBox-page", "type": "BUTTON", "value": "/archive/packBox/page"}, {"name": "新增", "code": "archive-packBox-add", "type": "BUTTON", "value": "/archive/packBox/add"}, {"name": "编辑", "code": "archive-packBox-edit", "type": "BUTTON", "value": "/archive/packBox/edit"}, {"name": "删除", "code": "archive-packBox-delete", "type": "BUTTON", "value": "/archive/packBox/delete"}, {"name": "查看已装箱水表", "code": "archive-packBox-getWaterMeters", "type": "BUTTON", "value": "/archive/packBox/getWaterMeters"}, {"name": "确认出货", "code": "archive-packBox-shipment", "type": "BUTTON", "value": "/archive/packBox/shipment"}, {"name": "打印装箱单", "code": "archive-packBox-print", "type": "BUTTON", "value": "/archive/packBox/print"}, {"name": "快速装箱", "code": "archive-packBox-fastPack", "type": "BUTTON", "value": "/archive/packBox/fastPack"}]}, {"name": "设备档案", "code": "archive-deviceArchive", "type": "MENU", "value": "/archive/deviceArchive/list", "children": [{"name": "查询", "code": "archive-deviceArchive-page", "type": "BUTTON", "value": "/archive/deviceArchive/page"}, {"name": "新增", "code": "archive-deviceArchive-add", "type": "BUTTON", "value": "/archive/deviceArchive/add**"}, {"name": "编辑", "code": "archive-deviceArchive-edit", "type": "BUTTON", "value": "/archive/deviceArchive/edit**"}, {"name": "删除", "code": "archive-deviceArchive-delete", "type": "BUTTON", "value": "/archive/deviceArchive/delete**"}, {"name": "批量删除", "code": "archive-deviceArchive-batchDel", "type": "BUTTON", "value": "/archive/deviceArchive/batchDel**"}, {"name": "批量导入", "code": "archive-deviceArchive-upload", "type": "BUTTON", "value": "/archive/deviceArchive/upload**"}, {"name": "批量修改表号", "code": "archive-deviceArchive-batchUpdateMeterCode", "type": "BUTTON", "value": "/archive/deviceArchive/batchUpdateMeterCode**"}, {"name": "导出", "code": "archive-deviceArchive-export", "type": "BUTTON", "value": "/archive/deviceArchive/export**"}, {"name": "批量删除注册信息", "code": "archive-deviceArchive-deleteDeviceInfos", "type": "BUTTON", "value": "/archive/deviceArchive/deleteDeviceInfos**"}, {"name": "同步状态(注册信息)", "code": "archive-deviceArchive-syncStatus", "type": "BUTTON", "value": "/archive/deviceArchive/syncStatus**"}, {"name": "切换归属公司", "code": "archive-deviceArchive-changeDeviceArchiveCompany", "type": "BUTTON", "value": "/archive/deviceArchive/changeDeviceArchiveCompany**"}, {"name": "表号置空", "code": "archive-deviceArchive-setCodeNull", "type": "BUTTON", "value": "/archive/deviceArchive/setCodeNull"}]}]}, {"name": "报表统计", "code": "chart", "type": "MENU", "cssClass": "mdi mdi-chart-bar", "children": [{"name": "抄表数据统计", "code": "chart-monitor", "type": "MENU", "children": [{"name": "小区用水量统计", "code": "chart-monitor-communityConsumption", "type": "MENU", "value": "/chart/communityConsumption/show", "children": [{"name": "导出年度用水量", "code": "chart-monitor-communityConsumption-exportCommunityConsumptionByMonth", "type": "BUTTON", "value": "/chart/communityConsumption/exportCommunityConsumptionByMonth"}, {"name": "导出时间范围内用水量", "code": "chart-monitor-communityConsumption-exportCommunityConsumptionByDay", "type": "BUTTON", "value": "/chart/communityConsumption/exportCommunityConsumptionByDay"}]}, {"name": "客户用水量统计", "code": "chart-monitor-customerConsumption", "type": "MENU", "value": "/chart/customerConsumption/show", "children": [{"name": "导出年度用水量", "code": "chart-monitor-customerConsumption-exportConsumptionByMonth", "type": "BUTTON", "value": "/chart/customerConsumption/exportConsumptionByMonth"}, {"name": "导出时间范围内用水量", "code": "chart-monitor-customerConsumption-exportConsumptionByDay", "type": "BUTTON", "value": "/chart/customerConsumption/exportConsumptionByDay"}]}, {"name": "区间用水量统计", "code": "chart-monitor-rangeConsumption", "type": "MENU", "value": "/chart/rangeConsumption/show", "children": [{"name": "导出区间用水量", "code": "chart-monitor-rangeConsumption-export", "type": "BUTTON", "value": "/chart/rangeConsumption/export"}]}, {"name": "异常流量统计", "code": "chart-monitor-withoutWaterDevice", "type": "MENU", "value": "/chart/withoutWaterDevice/show", "children": [{"name": "查询", "code": "chart-monitor-withoutWaterDevice-page", "type": "BUTTON", "value": "/chart/withoutWaterDevice/pageMonthFreezeAmounts"}, {"name": "导出", "code": "chart-monitor-withoutWaterDevice-export", "type": "BUTTON", "value": "/chart/withoutWaterDevice/exportByMonth"}, {"name": "设置计量点异常流量阈值", "code": "chart-monitor-withoutWaterDevice-configFlowLimit", "type": "BUTTON", "value": "/chart/withoutWaterDevice/configFlowLimit**"}]}, {"name": "水表状态统计", "code": "chart-monitor-waterMeterState", "type": "MENU", "value": "/chart/waterMeterState/show"}, {"name": "在线设备统计", "code": "chart-monitor-onlineDevice", "type": "MENU", "value": "/chart/onlineDevice/show"}, {"name": "水表类型统计", "code": "chart-monitor-waterMeterType", "type": "MENU", "value": "/chart/waterMeterType/show"}, {"name": "水表增量统计", "code": "chart-monitor-waterMeterIncrement", "type": "MENU", "value": "/chart/waterMeterIncrement/show"}]}, {"name": "营收数据统计", "code": "chart-revenue", "type": "MENU", "children": [{"name": "营收汇总统计", "code": "chart-revenue-owe", "type": "MENU", "value": "/chart/owe/show"}, {"name": "营收明细统计", "code": "chart-revenue-detail", "type": "MENU", "value": "/chart/revenue/show", "children": [{"name": "导出年度账单", "code": "chart-revenue-detail-exportCommunityRevenueByMonth", "type": "BUTTON", "value": "/chart/revenue/exportCommunityRevenueByMonth"}, {"name": "导出时间范围内账单", "code": "chart-revenue-detail-exportCommunityRevenueByDay", "type": "BUTTON", "value": "/chart/revenue/exportCommunityRevenueByDay"}]}, {"name": "现金预存统计", "code": "chart-revenue-reserve", "type": "MENU", "value": "/chart/reserve/show"}, {"name": "缴费占比统计", "code": "chart-revenue-base", "type": "MENU", "value": "/chart/income/show"}, {"name": "销户退费统计", "code": "chart-revenue-refund", "type": "MENU", "value": "/chart/refund/show"}, {"name": "结算账单统计", "code": "chart-revenue-bill", "type": "MENU", "value": "/chart/bill/show"}, {"name": "账单明细统计", "code": "chart-revenue-billDetail", "type": "MENU", "value": "/chart/billDetail/show"}, {"name": "用水金额统计", "code": "chart-revenue-customerFreezeAmount", "type": "MENU", "value": "/chart/customerFreezeAmount/show", "children": [{"name": "导出用水金额", "code": "chart-revenue-customerFreezeAmount-exportAmountByMonth", "type": "BUTTON", "value": "/chart/customerFreezeAmount/exportAmountByMonth"}]}]}, {"name": "短信数据统计", "code": "chart-sms", "type": "MENU", "children": [{"name": "短信发送统计", "code": "chart-sms-send", "type": "MENU", "value": "/chart/sms/show"}]}]}, {"name": "短信管理", "code": "sms", "type": "MENU", "cssClass": "mdi mdi-message-processing", "children": [{"name": "短信参数", "code": "sms-config", "type": "MENU", "value": "/sms/settings/config", "children": [{"name": "查询", "code": "sms-config-page", "type": "BUTTON", "value": "/sms/settings/page"}, {"name": "新增", "code": "sms-config-add", "type": "BUTTON", "value": "/sms/settings/add**"}, {"name": "编辑", "code": "sms-config-edit", "type": "BUTTON", "value": "/sms/settings/edit**"}, {"name": "删除", "code": "sms-config-delete", "type": "BUTTON", "value": "/sms/settings/delete**"}]}, {"name": "短信记录", "code": "sms-record", "type": "MENU", "value": "/sms/smsRecord/list", "children": [{"name": "查询", "code": "sms-record-page", "type": "BUTTON", "value": "/sms/smsRecord/page"}]}]}, {"name": "系统管理", "code": "sys", "type": "MENU", "cssClass": "mdi mdi-security", "children": [{"name": "公司管理", "code": "sys-company", "type": "MENU", "value": "/sys/company/list", "children": [{"name": "查询", "code": "sys-company-page", "type": "BUTTON", "value": "/sys/company/page"}, {"name": "新增", "code": "sys-company-add", "type": "BUTTON", "value": "/sys/company/add**"}, {"name": "编辑", "code": "sys-company-edit", "type": "BUTTON", "value": "/sys/company/edit**"}, {"name": "删除", "code": "sys-company-delete", "type": "BUTTON", "value": "/sys/company/delete**"}, {"name": "启用", "code": "sys-company-enabled", "type": "BUTTON", "value": "/sys/company/enable?id=**"}, {"name": "禁用", "code": "sys-company-disabled", "type": "BUTTON", "value": "/sys/company/disable?id=**"}]}, {"name": "管辖区域管理", "code": "sys-region", "type": "MENU", "value": "/sys/region/list", "children": [{"name": "查询", "code": "sys-region-page", "type": "BUTTON", "value": "/sys/region/page"}, {"name": "新增", "code": "sys-region-add", "type": "BUTTON", "value": "/sys/region/add**"}, {"name": "批量删除", "code": "sys-region-batchDel", "type": "BUTTON", "value": "/sys/region/batchDel**"}, {"name": "编辑", "code": "sys-region-edit", "type": "BUTTON", "value": "/sys/region/edit**"}, {"name": "删除", "code": "sys-region-delete", "type": "BUTTON", "value": "/sys/region/delete**"}]}, {"name": "后台管理员", "code": "sys-user", "type": "MENU", "value": "/sys/user/list", "children": [{"name": "查询", "code": "sys-user-page", "type": "BUTTON", "value": "/sys/user/page"}, {"name": "新增", "code": "sys-user-add", "type": "BUTTON", "value": "/sys/user/add"}, {"name": "批量删除", "code": "sys-user-batchDel", "type": "BUTTON", "value": "/sys/user/batchDel**"}, {"name": "编辑", "code": "sys-user-edit", "type": "BUTTON", "value": "/sys/user/edit**"}, {"name": "删除", "code": "sys-user-delete", "type": "BUTTON", "value": "/sys/user/delete**"}, {"name": "启用", "code": "sys-user-enabled", "type": "BUTTON", "value": "/sys/user?enabled=true&ids=**"}, {"name": "禁用", "code": "sys-user-disabled", "type": "BUTTON", "value": "/sys/user?enabled=false&ids=**"}, {"name": "重置密码", "code": "sys-user-resetPwd", "type": "BUTTON", "value": "/sys/user/resetPwd**"}]}, {"name": "管理员查询", "code": "sys-userQuery", "type": "MENU", "value": "/sys/userQuery/list", "children": [{"name": "查询", "code": "sys-userQuery-page", "type": "BUTTON", "value": "/sys/userQuery/page"}]}, {"name": "角色管理", "code": "sys-role", "type": "MENU", "value": "/sys/role/list", "children": [{"name": "查询", "code": "sys-role-page", "type": "BUTTON", "value": "/sys/role/page"}, {"name": "新增", "code": "sys-role-add", "type": "BUTTON", "value": "/sys/role/add"}, {"name": "批量删除", "code": "sys-role-batchDel", "type": "BUTTON", "value": "/sys/role/batchDel**"}, {"name": "编辑", "code": "sys-role-edit", "type": "BUTTON", "value": "/sys/role/edit**"}, {"name": "删除", "code": "sys-role-delete", "type": "BUTTON", "value": "/sys/role/delete**"}]}, {"name": "操作日志", "code": "sys-logInfo", "type": "MENU", "value": "/sys/log/info/list", "children": [{"name": "查询", "code": "sys-logInfo-page", "type": "BUTTON", "value": "/sys/log/info/page"}]}, {"name": "异常日志", "code": "sys-logError", "type": "MENU", "value": "/sys/log/error/list", "children": [{"name": "查询", "code": "sys-logError-page", "type": "BUTTON", "value": "/sys/log/error/page"}]}, {"name": "小程序日志", "code": "sys-miniOperateLog", "type": "MENU", "value": "/sys/miniOperateLog/list"}, {"name": "系统通知", "code": "sys-notification", "type": "MENU", "value": "/sys/notification/list", "children": [{"name": "查询", "code": "sys-notification-page", "type": "BUTTON", "value": "/sys/notification/page"}, {"name": "新增", "code": "sys-notification-add", "type": "BUTTON", "value": "/sys/notification/add"}, {"name": "编辑", "code": "sys-notification-edit", "type": "BUTTON", "value": "/sys/notification/edit**"}, {"name": "删除", "code": "sys-notification-delete", "type": "BUTTON", "value": "/sys/notification/delete**"}]}, {"name": "第三方对接管理", "code": "sys-thirdDeviceDocking", "type": "MENU", "value": "/sys/thirdDeviceDocking/list", "children": [{"name": "查询", "code": "sys-thirdDeviceDocking-page", "type": "BUTTON", "value": "/sys/thirdDeviceDocking/page"}, {"name": "新增", "code": "sys-thirdDeviceDocking-add", "type": "BUTTON", "value": "/sys/thirdDeviceDocking/add"}, {"name": "编辑", "code": "sys-thirdDeviceDocking-edit", "type": "BUTTON", "value": "/sys/thirdDeviceDocking/edit**"}, {"name": "删除", "code": "sys-thirdDeviceDocking-delete", "type": "BUTTON", "value": "/sys/thirdDeviceDocking/delete**"}]}]}, {"name": "系统设置", "code": "settings", "type": "MENU", "cssClass": "mdi mdi-settings", "children": [{"name": "基本设置", "code": "settings-sys", "type": "MENU", "children": [{"name": "网站设置", "code": "settings-sys-website", "type": "MENU", "value": "/settings/sys/website"}]}, {"name": "我的设置", "code": "settings-mine", "type": "MENU", "children": [{"name": "个人信息", "code": "settings-mine-info", "type": "MENU", "value": "/settings/mine/info"}, {"name": "修改密码", "code": "settings-mine-password", "type": "MENU", "value": "/settings/mine/password"}]}]}, {"name": "生产测试", "code": "productionTest", "type": "MENU", "cssClass": "mdi mdi-test-tube", "children": [{"name": "批次管理", "code": "productionTest-testBatch", "type": "MENU", "value": "/productionTest/testBatch/list", "children": [{"name": "查询", "code": "productionTest-testBatch-page", "type": "BUTTON", "value": "/productionTest/testBatch/page"}, {"name": "新增", "code": "productionTest-testBatch-add", "type": "BUTTON", "value": "/productionTest/testBatch/add**"}, {"name": "编辑", "code": "productionTest-testBatch-edit", "type": "BUTTON", "value": "/productionTest/testBatch/edit**"}, {"name": "删除", "code": "productionTest-testBatch-delete", "type": "BUTTON", "value": "/productionTest/testBatch/delete**"}, {"name": "批量删除", "code": "productionTest-testBatch-batchDel", "type": "BUTTON", "value": "/productionTest/testBatch/batchDel**"}]}, {"name": "成表产测", "code": "productionTest-testWaterMeter", "type": "MENU", "value": "/productionTest/testWaterMeter/list", "children": [{"name": "查询", "code": "productionTest-testWaterMeter-page", "type": "BUTTON", "value": "/productionTest/testWaterMeter/page"}, {"name": "新增", "code": "productionTest-testWaterMeter-add", "type": "BUTTON", "value": "/productionTest/testWaterMeter/add"}, {"name": "导出待测水表", "code": "productionTest-testWaterMeter-export", "type": "BUTTON", "value": "/productionTest/testWaterMeter/export**"}, {"name": "批量标记成功", "code": "productionTest-testWaterMeter-batchMarkSuccess", "type": "BUTTON", "value": "/productionTest/testWaterMeter/batchMarkSuccess**"}, {"name": "删除", "code": "productionTest-testWaterMeter-delete", "type": "BUTTON", "value": "/productionTest/testWaterMeter/delete**"}, {"name": "批量删除", "code": "productionTest-testWaterMeter-batchDel", "type": "BUTTON", "value": "/productionTest/testWaterMeter/batchDel**"}, {"name": "下发预置产测命令", "code": "productionTest-testWaterMeter-sendProductionTestPrepareCmd", "type": "BUTTON", "value": "/productionTest/testWaterMeter/sendProductionTestPrepareCmd"}, {"name": "下发预置阀控命令", "code": "productionTest-testWaterMeter-sendValveCtrlPrepareCmd", "type": "BUTTON", "value": "/productionTest/testWaterMeter/sendValveCtrlPrepareCmd"}, {"name": "下发预置IP命令", "code": "productionTest-testWaterMeter-sendSetIpPrepareCmd", "type": "BUTTON", "value": "/productionTest/testWaterMeter/sendSetIpPrepareCmd"}, {"name": "下发预置表底数命令", "code": "productionTest-testWaterMeter-sendSetBaseValue", "type": "BUTTON", "value": "/productionTest/testWaterMeter/sendSetBaseValue"}, {"name": "下发写入表号命令", "code": "productionTest-testWaterMeter-sendSetCodeCmd", "type": "BUTTON", "value": "/productionTest/testWaterMeter/sendSetCodeCmd"}, {"name": "取消命令", "code": "productionTest-testWaterMeter-cancelCmd", "type": "BUTTON", "value": "/productionTest/testWaterMeter/cancelCmd"}]}, {"name": "抄表数据", "code": "productionTest-testWaterMeterData", "type": "MENU", "value": "/productionTest/testWaterMeterData/list", "children": [{"name": "查询", "code": "productionTest-testWaterMeterData-query", "type": "BUTTON", "value": "/productionTest/testWaterMeterData/page"}]}, {"name": "预置命令", "code": "productionTest-testPrepareCmd", "type": "MENU", "value": "/productionTest/testPrepareCmd/list", "children": [{"name": "查询", "code": "productionTest-testPrepareCmd-query", "type": "BUTTON", "value": "/productionTest/testPrepareCmd/page"}, {"name": "删除", "code": "productionTest-testPrepareCmd-delete", "type": "BUTTON", "value": "/productionTest/testPrepareCmd/delete"}]}]}, {"name": "平台运维", "code": "maintenance", "type": "MENU", "cssClass": "mdi mdi-layers", "children": [{"name": "归档数据查询", "code": "maintenance-archive", "type": "MENU", "children": [{"name": "缴费记录", "code": "maintenance-archive-payIncome", "value": "/maintenance/archive/payIncome/list", "type": "MENU", "children": [{"name": "查询", "code": "maintenance-archive-payIncome-page", "type": "BUTTON", "value": "/maintenance/archive/payIncome/page"}]}, {"name": "结算账单", "code": "maintenance-archive-bill", "value": "/maintenance/archive/bill/list", "type": "MENU", "children": [{"name": "查询", "code": "maintenance-archive-bill-page", "type": "BUTTON", "value": "/maintenance/archive/bill/page"}]}, {"name": "换表记录", "code": "maintenance-archive-changeMeter", "value": "/maintenance/archive/changeMeter/list", "type": "MENU", "children": [{"name": "查询", "code": "maintenance-archive-changeMeter-page", "type": "BUTTON", "value": "/maintenance/archive/changeMeter/page"}]}, {"name": "退款记录", "code": "maintenance-archive-refund", "value": "/maintenance/archive/refund/list", "type": "MENU", "children": [{"name": "查询", "code": "maintenance-archive-refund-page", "type": "BUTTON", "value": "/maintenance/archive/refund/page"}]}]}]}, {"name": "智慧水务小程序", "code": "miniprogram", "type": "BUTTON", "children": [{"name": "管理", "code": "miniprogram-manage", "type": "BUTTON", "children": [{"name": "计量点监测", "code": "miniprogram-manage-monitor", "type": "BUTTON", "children": [{"name": "计量点查询", "code": "miniprogram-manage-monitor-page", "type": "BUTTON", "value": "/miniprogram/auth/monitor/page"}, {"name": "远程抄表", "code": "miniprogram-manage-monitor-meteringData", "type": "BUTTON", "value": "/miniprogram/auth/monitor/meteringData**"}, {"name": "远程开阀", "code": "miniprogram-manage-monitor-openValue", "type": "BUTTON", "value": "/miniprogram/auth/monitor/valve/open**"}, {"name": "远程关阀", "code": "miniprogram-manage-monitor-closeValve", "type": "BUTTON", "value": "/miniprogram/auth/monitor/valve/close**"}, {"name": "人工抄表", "code": "miniprogram-manage-monitor-manualReadingMeter", "type": "BUTTON", "value": "/miniprogram/auth/manualReadingMeter/manualReadingMeterInfoReport"}, {"name": "计量点详细信息", "code": "miniprogram-manage-monitor-detail", "type": "BUTTON", "value": "/miniprogram/auth/monitorDetail/findMeasuringPointById**", "children": [{"name": "水表详情", "code": "miniprogram-manage-monitor-detail-meterDetail", "type": "BUTTON", "value": "/miniprogram/auth/monitorDetail/findWaterMeterByMeasuringPointId**", "children": [{"name": "水表详情查询", "code": "miniprogram-manage-monitor-detail-meterDetail-query", "type": "BUTTON", "value": "/miniprogram/auth/monitorDetail/findWaterMeterByMeasuringPointId**"}, {"name": "远程设置表底数(卓正)", "code": "miniprogram-manage-monitor-detail-meterDetail-setValueZZMBusSync", "type": "BUTTON", "value": "/miniprogram/auth/monitor/setValueZZMBusSync**"}, {"name": "远程设置表底数(航天中电)", "code": "miniprogram-manage-monitor-detail-meterDetail-setValueWiredSync", "type": "BUTTON", "value": "/miniprogram/auth/monitor/setValueWiredSync**"}, {"name": "远程读表数据(卓正)", "code": "miniprogram-manage-monitor-detail-meterDetail-readMeterZZMBusSync", "type": "BUTTON", "value": "/miniprogram/auth/monitor/readMeterZZMBusSync**"}, {"name": "远程读表数据(航天中电)", "code": "miniprogram-manage-monitor-detail-meterDetail-readMeterWiredSync", "type": "BUTTON", "value": "/miniprogram/auth/monitor/readMeterWiredSync**"}, {"name": "远程设置表底数(NB水表)", "code": "miniprogram-manage-monitor-detail-meterDetail-setValueNB", "type": "BUTTON", "value": "/miniprogram/auth/monitor/setValueNB**"}]}, {"name": "抄表数据", "code": "miniprogram-manage-monitor-detail-meterData", "type": "BUTTON", "value": "/miniprogram/auth/monitorDetail/pagedMeterData**"}, {"name": "水表事件", "code": "miniprogram-manage-monitor-detail-meterEvents", "type": "BUTTON", "value": "/miniprogram/auth/monitorDetail/pagedMeterEvents**"}, {"name": "历史命令", "code": "miniprogram-manage-monitor-detail-meterCmdLogs", "type": "BUTTON", "value": "/miniprogram/auth/monitorDetail/pagedMeterCmdLogs**"}, {"name": "业务阀控记录", "code": "miniprogram-manage-monitor-detail-serviceValveCtrlRecord", "type": "BUTTON", "value": "/miniprogram/auth/monitorDetail/pagedServiceValveCtrlRecord**"}]}]}, {"name": "集中器维护", "code": "miniprogram-manage-monitorConcentratorSettings", "type": "BUTTON", "children": [{"name": "集中器查询", "code": "miniprogram-manage-monitorConcentratorSettings-page", "type": "BUTTON", "value": "/miniprogram/auth/concentrator/settings/page"}, {"name": "档案维护", "code": "miniprogram-manage-monitorConcentratorSettings-archive", "type": "BUTTON", "value": "/miniprogram/auth/concentrator/settings/archives"}, {"name": "其他操作", "code": "miniprogram-manage-monitorConcentratorSettings-other", "type": "BUTTON", "value": "/miniprogram/auth/concentrator/settings/meteringRealtimeData"}, {"name": "集中器事件", "code": "miniprogram-manage-monitorConcentratorSettings-event", "type": "BUTTON", "value": "/miniprogram/auth/concentrator/settings/concentratorEvents"}]}, {"name": "抄表成功率", "code": "miniprogram-manage-readMeterRate", "type": "BUTTON", "children": [{"name": "小区抄表成功率", "code": "miniprogram-manage-readMeterRate-community", "type": "BUTTON", "value": "/miniprogram/auth/successRate/pageCommunityReadingSuccessRate"}, {"name": "集中器抄表成功率", "code": "miniprogram-manage-readMeterRate-concentrator", "type": "BUTTON", "value": "/miniprogram/auth/successRate/pageConcentratorReadingSuccessRate"}]}, {"name": "冻结数据查询", "code": "miniprogram-manage-freezeData", "type": "BUTTON", "value": "/miniprogram/auth/freezeData/page"}, {"name": "历史命令查询", "code": "miniprogram-manage-meterCmdLog", "type": "BUTTON", "value": "/miniprogram/auth/cmdlog/pageMeterCmdLogs"}, {"name": "客户管理", "code": "miniprogram-manage-customer", "type": "BUTTON", "children": [{"name": "客户查询", "code": "miniprogram-manage-customer-page", "type": "BUTTON", "value": "/miniprogram/auth/customer/page"}, {"name": "新增客户", "code": "miniprogram-manage-customer-addCustomer", "type": "BUTTON", "value": "/miniprogram/auth/customer/addCustomer**"}, {"name": "编辑客户", "code": "miniprogram-manage-customer-editCustomer", "type": "BUTTON", "value": "/miniprogram/auth/customer/editCustomer**"}, {"name": "删除客户", "code": "miniprogram-manage-customer-delete", "type": "BUTTON", "value": "/miniprogram/auth/customer/delete**"}, {"name": "绑定计量点(开户)", "code": "miniprogram-manage-customer-bindMeasuringPoint", "type": "BUTTON", "value": "/miniprogram/auth/customer/bindMeasuringPoint**"}]}, {"name": "现金预存", "code": "miniprogram-manage-reserve", "type": "BUTTON", "value": "/miniprogram/auth/reserve/cashReserve"}, {"name": "欠费计量点", "code": "miniprogram-manage-oweMeasuringPoint", "type": "BUTTON", "value": "/miniprogram/auth/oweMeasuringPoint/page"}, {"name": "账单查询", "code": "miniprogram-manage-bill", "type": "BUTTON", "value": "/miniprogram/auth/bill/page"}, {"name": "缴费记录", "code": "miniprogram-manage-payIncome", "type": "BUTTON", "value": "/miniprogram/auth/payIncome/page"}, {"name": "小区管理", "code": "miniprogram-manage-community", "type": "BUTTON", "children": [{"name": "查询", "code": "miniprogram-manage-community-page", "type": "BUTTON", "value": "/miniprogram/auth/community/page"}, {"name": "新增", "code": "miniprogram-manage-community-add", "type": "BUTTON", "value": "/miniprogram/auth/community/add"}, {"name": "编辑", "code": "miniprogram-manage-community-edit", "type": "BUTTON", "value": "/miniprogram/auth/community/edit"}, {"name": "删除", "code": "miniprogram-manage-community-delete", "type": "BUTTON", "value": "/miniprogram/auth/community/delete"}]}, {"name": "集中器管理", "code": "miniprogram-manage-concentrator", "type": "BUTTON", "children": [{"name": "查询", "code": "miniprogram-manage-concentrator-page", "type": "BUTTON", "value": "/miniprogram/auth/concentrator/page"}, {"name": "新增", "code": "miniprogram-manage-concentrator-add", "type": "BUTTON", "value": "/miniprogram/auth/concentrator/add"}, {"name": "编辑", "code": "miniprogram-manage-concentrator-edit", "type": "BUTTON", "value": "/miniprogram/auth/concentrator/edit"}, {"name": "删除", "code": "miniprogram-manage-concentrator-delete", "type": "BUTTON", "value": "/miniprogram/auth/concentrator/delete"}]}, {"name": "NB水表", "code": "miniprogram-manage-nbWaterMeter", "type": "BUTTON", "children": [{"name": "查询", "code": "miniprogram-manage-nbWaterMeter-page", "type": "BUTTON", "value": "/miniprogram/auth/nbWaterMeter/page"}, {"name": "新增", "code": "miniprogram-manage-nbWaterMeter-add", "type": "BUTTON", "value": "/miniprogram/auth/nbWaterMeter/add"}, {"name": "编辑", "code": "miniprogram-manage-nbWaterMeter-edit", "type": "BUTTON", "value": "/miniprogram/auth/nbWaterMeter/edit"}, {"name": "删除", "code": "miniprogram-manage-nbWaterMeter-delete", "type": "BUTTON", "value": "/miniprogram/auth/nbWaterMeter/delete"}]}, {"name": "集中器水表", "code": "miniprogram-manage-concentratorWaterMeter", "type": "BUTTON", "children": [{"name": "查询", "code": "miniprogram-manage-concentratorWaterMeter-page", "type": "BUTTON", "value": "/miniprogram/auth/concentratorWaterMeter/page"}, {"name": "新增", "code": "miniprogram-manage-concentratorWaterMeter-add", "type": "BUTTON", "value": "/miniprogram/auth/concentratorWaterMeter/add"}, {"name": "编辑", "code": "miniprogram-manage-concentratorWaterMeter-edit", "type": "BUTTON", "value": "/miniprogram/auth/concentratorWaterMeter/edit"}, {"name": "删除", "code": "miniprogram-manage-concentratorWaterMeter-delete", "type": "BUTTON", "value": "/miniprogram/auth/concentratorWaterMeter/delete"}]}, {"name": "机械水表", "code": "miniprogram-manage-machineWaterMeter", "type": "BUTTON", "children": [{"name": "查询", "code": "miniprogram-manage-machineWaterMeter-page", "type": "BUTTON", "value": "/miniprogram/auth/machineWaterMeter/page"}, {"name": "新增", "code": "miniprogram-manage-machineWaterMeter-add", "type": "BUTTON", "value": "/miniprogram/auth/machineWaterMeter/add"}, {"name": "编辑", "code": "miniprogram-manage-machineWaterMeter-edit", "type": "BUTTON", "value": "/miniprogram/auth/machineWaterMeter/edit"}, {"name": "删除", "code": "miniprogram-manage-machineWaterMeter-delete", "type": "BUTTON", "value": "/miniprogram/auth/machineWaterMeter/delete"}, {"name": "更换机械水表", "code": "miniprogram-manage-machineWaterMeter-changeMachineWaterMeter", "type": "BUTTON", "value": "/miniprogram/auth/machineWaterMeter/changeMachineWaterMeter"}]}, {"name": "UDP直连水表", "code": "miniprogram-manage-cat1WaterMeter", "type": "BUTTON", "children": [{"name": "查询", "code": "miniprogram-manage-cat1WaterMeter-page", "type": "BUTTON", "value": "/miniprogram/auth/cat1WaterMeter/page"}, {"name": "新增", "code": "miniprogram-manage-cat1WaterMeter-add", "type": "BUTTON", "value": "/miniprogram/auth/cat1WaterMeter/add"}, {"name": "编辑", "code": "miniprogram-manage-cat1WaterMeter-edit", "type": "BUTTON", "value": "/miniprogram/auth/cat1WaterMeter/edit"}, {"name": "删除", "code": "miniprogram-manage-cat1WaterMeter-delete", "type": "BUTTON", "value": "/miniprogram/auth/cat1WaterMeter/delete"}]}, {"name": "小区用水量统计", "code": "miniprogram-manage-communityConsumption", "type": "BUTTON", "value": "/miniprogram/auth/communityConsumption/getCommunityConsumptionDataByMonth"}, {"name": "客户用水量统计", "code": "miniprogram-manage-customerConsumption", "type": "BUTTON", "value": "/miniprogram/auth/customerConsumption/getCustomerConsumptionDataByMonth"}, {"name": "营收明细统计", "code": "miniprogram-manage-revenueDetail", "type": "BUTTON", "value": "/miniprogram/auth/revenueDetail/getRevenueDataByMonth"}, {"name": "现金预存统计", "code": "miniprogram-manage-reserveStatistical", "type": "BUTTON", "value": "/miniprogram/auth/reserveStatistical/getReserveDataByDay"}, {"name": "结算账单统计", "code": "miniprogram-manage-billStatistical", "type": "BUTTON", "value": "/miniprogram/auth/billStatistical/getBillDataByMonth"}, {"name": "营收汇总统计", "code": "miniprogram-manage-revenueSummaryStatistical", "type": "BUTTON", "value": "/miniprogram/auth/revenueSummaryStatistical/getRevenueSummary"}]}, {"name": "抄表", "code": "miniprogram-readmeter", "type": "BUTTON", "children": [{"name": "本地抄表", "code": "miniprogram-readmeter-wireless", "type": "BUTTON", "children": [{"name": "同步数据", "code": "miniprogram-readmeter-wireless-reportReadMeterData", "type": "BUTTON", "value": "/miniprogram/auth/native/wireless/reportReadMeterData"}, {"name": "一键读取", "code": "miniprogram-readmeter-wireless-allRead", "type": "BUTTON", "value": "###"}, {"name": "读表号", "code": "miniprogram-readmeter-wireless-readMeterCode", "type": "BUTTON", "value": "###"}, {"name": "写表号", "code": "miniprogram-readmeter-wireless-writeMeterCode", "type": "BUTTON", "value": "###"}, {"name": "读水表数据", "code": "miniprogram-readmeter-wireless-readMeterData", "type": "BUTTON", "value": "###"}, {"name": "读水表整数位数", "code": "miniprogram-readmeter-wireless-readInteger", "type": "BUTTON", "value": "###"}, {"name": "水表MCU复位", "code": "miniprogram-readmeter-wireless-resetMCU", "type": "BUTTON", "value": "###"}, {"name": "写水表数据", "code": "miniprogram-readmeter-wireless-writeMeterData", "type": "BUTTON", "value": "###"}, {"name": "开阀", "code": "miniprogram-readmeter-wireless-openValve", "type": "BUTTON", "value": "###"}, {"name": "关阀", "code": "miniprogram-readmeter-wireless-closeValve", "type": "BUTTON", "value": "###"}, {"name": "洗阀", "code": "miniprogram-readmeter-wireless-washValve", "type": "BUTTON", "value": "###"}, {"name": "读NB-IoT信号", "code": "miniprogram-readmeter-wireless-readNbInfo", "type": "BUTTON", "value": "###"}, {"name": "读设备信息", "code": "miniprogram-readmeter-wireless-readDeviceInfo", "type": "BUTTON", "value": "###"}, {"name": "读设备参数", "code": "miniprogram-readmeter-wireless-readDeviceParam", "type": "BUTTON", "value": "###"}, {"name": "写设备参数", "code": "miniprogram-readmeter-wireless-writeDeviceParam", "type": "BUTTON", "value": "###"}, {"name": "读计量配置", "code": "miniprogram-readmeter-wireless-readMeasuringConfig", "type": "BUTTON", "value": "###"}, {"name": "写计量配置", "code": "miniprogram-readmeter-wireless-writeMeasuringConfig", "type": "BUTTON", "value": "###"}, {"name": "读功能选项", "code": "miniprogram-readmeter-wireless-readFunctionOptional", "type": "BUTTON", "value": "###"}, {"name": "写功能选项", "code": "miniprogram-readmeter-wireless-writeFunctionOptional", "type": "BUTTON", "value": "###"}, {"name": "读水表状态", "code": "miniprogram-readmeter-wireless-readMeterState", "type": "BUTTON", "value": "###"}, {"name": "读无磁信号", "code": "miniprogram-readmeter-wireless-readNoMagnetism", "type": "BUTTON", "value": "###"}, {"name": "复位NB", "code": "miniprogram-readmeter-wireless-resetNb", "type": "BUTTON", "value": "###"}, {"name": "启用NB", "code": "miniprogram-readmeter-wireless-enableNb", "type": "BUTTON", "value": "###"}, {"name": "禁用NB", "code": "miniprogram-readmeter-wireless-disabledNb", "type": "BUTTON", "value": "###"}, {"name": "设置工作模式", "code": "miniprogram-readmeter-wireless-setWorkMode", "type": "BUTTON", "value": "###"}, {"name": "设置产测模式", "code": "miniprogram-readmeter-wireless-setProductionMode", "type": "BUTTON", "value": "###"}, {"name": "读无磁信号", "code": "miniprogram-readmeter-wireless-readNonmagneticSignal", "type": "BUTTON", "value": "###"}, {"name": "配置水表运行模式", "code": "miniprogram-readmeter-wireless-configMeterMode", "type": "BUTTON", "value": "###"}, {"name": "水表添加中继", "code": "miniprogram-readmeter-wireless-meterAddRelay", "type": "BUTTON", "value": "###"}, {"name": "水表删除中继", "code": "miniprogram-readmeter-wireless-meterRemoveRelay", "type": "BUTTON", "value": "###"}, {"name": "读取中继地址", "code": "miniprogram-readmeter-wireless-readRelayLocation", "type": "BUTTON", "value": "###"}, {"name": "流量清零", "code": "miniprogram-readmeter-wireless-resetBaseValue", "type": "BUTTON", "value": "###"}, {"name": "读流量统计信息", "code": "miniprogram-readmeter-wireless-readFlowStatisticInfo", "type": "BUTTON", "value": "###"}, {"name": "写每月结算日", "code": "miniprogram-readmeter-wireless-writeBillDate", "type": "BUTTON", "value": "###"}, {"name": "中继添加水表", "code": "miniprogram-readmeter-wireless-relayAddMeters", "type": "BUTTON", "value": "###"}, {"name": "中继删除水表", "code": "miniprogram-readmeter-wireless-relayDeleteMeters", "type": "BUTTON", "value": "###"}, {"name": "中继读取水表", "code": "miniprogram-readmeter-wireless-relayReadMeters", "type": "BUTTON", "value": "###"}, {"name": "读中继器版本信息", "code": "miniprogram-readmeter-wireless-readRelayVersionInfo", "type": "BUTTON", "value": "###"}, {"name": "读中继器信号信息", "code": "miniprogram-readmeter-wireless-readRelaySignalInfo", "type": "BUTTON", "value": "###"}, {"name": "读水表洗阀信息", "code": "miniprogram-readmeter-wireless-readMeterWashValveInfo", "type": "BUTTON", "value": "###"}, {"name": "写水表洗阀信息", "code": "miniprogram-readmeter-wireless-writeMeterWashValveInfo", "type": "BUTTON", "value": "###"}, {"name": "UDP直连水表写升级网络地址", "code": "miniprogram-readmeter-cat1WaterMeter-setUpgradeIpAndPort", "type": "BUTTON", "value": "###"}]}]}]}]