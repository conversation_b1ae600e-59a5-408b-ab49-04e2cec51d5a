package com.acpiot.wsd.admin.mvc.revenue.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 批量预存对象
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchReserveDto {

    private long id;

    /**
     * 预存金额
     */
    @DecimalMin(value = "0.01", message = "预存金额必须是0.01-300000之间")
    @DecimalMax(value = "300000", message = "预存金额必须是0.01-300000之间")
    private BigDecimal amount;

    /**
     * 预存备注
     */
    private String remark;
}
