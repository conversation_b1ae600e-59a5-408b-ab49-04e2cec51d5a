package com.acpiot.wsd.admin.mvc.monitor.controller;

import com.acpiot.wsd.admin.mvc.monitor.projections.MonitorConcentratorProjections;
import com.acpiot.wsd.admin.mvc.monitor.service.MonitorConcentratorService;
import com.acpiot.wsd.data.archive.entity.Concentrator;
import com.acpiot.wsd.data.archive.enums.ConcentratorType;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import pers.mx.jupiter.jpa.PagingQueryParams;

/**
 * Created by moxin on 2019-11-28-0028
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Controller
@RequestMapping("/monitor/concentrator")
public class MonitorConcentratorController {

    private final MonitorConcentratorService monitorConcentratorService;

    @GetMapping("list")
    public String list(ModelMap map) {
        map.addAttribute("concentratorTypes", ConcentratorType.values());
        return "monitor/concentrator/list";
    }

    @PostMapping("page")
    @ResponseBody
    public Page<MonitorConcentratorProjections.Paged> page(@RequestBody PagingQueryParams<Concentrator> params) {
        return monitorConcentratorService.pagedConcentrator(params);
    }
}
