package com.acpiot.wsd.admin.mvc.chart.service.impl;

import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.admin.constant.AdminConstant;
import com.acpiot.wsd.admin.mvc.chart.service.AbstractChartService;
import com.acpiot.wsd.admin.mvc.chart.service.FreezeAmountChartService;
import com.acpiot.wsd.data.archive.entity.Community_;
import com.acpiot.wsd.data.archive.entity.MeasuringPoint_;
import com.acpiot.wsd.data.monitor.entity.MonthFreezeAmount;
import com.acpiot.wsd.data.monitor.entity.MonthFreezeAmount_;
import com.acpiot.wsd.data.monitor.repository.MonthFreezeAmountRepository;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.util.Arrays;
import java.util.List;

import static com.acpiot.wsd.common.security.util.SecurityContextUtils.getGlobalFilter;
import static com.acpiot.wsd.common.security.util.SecurityContextUtils.getRegionCommunityFilter;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class FreezeAmountChartServiceImpl extends AbstractChartService implements FreezeAmountChartService {

    private final MonthFreezeAmountRepository monthFreezeAmountRepository;

    @Override
    public Page<MonthFreezeAmount> pageCustomerAmountByMonth(PagingQueryParams<MonthFreezeAmount> params) {
        String companyId = StrUtil.format("{}.{}", MonthFreezeAmount_.MEASURING_POINT, MeasuringPoint_.COMPANY_ID);
        String communityId = StrUtil.format("{}.{}.{}", MonthFreezeAmount_.MEASURING_POINT, MeasuringPoint_.COMMUNITY, Community_.ID);
        getGlobalFilter(companyId, communityId).ifPresent(p -> params.getAndFilters().addAll(Arrays.asList(p)));
        boolean continueQuery = getRegionCommunityFilter(params, communityId);
        if (!continueQuery) {
            return new PageImpl<>(Lists.newArrayList());
        }
        params.fetch(String.format("%s.%s", MonthFreezeAmount_.MEASURING_POINT, MeasuringPoint_.COMMUNITY));
        return monthFreezeAmountRepository.findAll(params);
    }

    @Override
    public List<MonthFreezeAmount> getExportCustomerFreezeAmountByMonth(PagingQueryParams<MonthFreezeAmount> params) {
        params.fetch(String.format("%s.%s", MonthFreezeAmount_.MEASURING_POINT, MeasuringPoint_.COMMUNITY));
        String companyId = StrUtil.format("{}.{}", MonthFreezeAmount_.MEASURING_POINT, MeasuringPoint_.COMPANY_ID);
        String communityId = StrUtil.format("{}.{}.{}", MonthFreezeAmount_.MEASURING_POINT, MeasuringPoint_.COMMUNITY, Community_.ID);
        getGlobalFilter(companyId, communityId).ifPresent(p -> params.getAndFilters().addAll(Arrays.asList(p)));
        boolean continueQuery = getRegionCommunityFilter(params, communityId);
        if (!continueQuery) {
            return Lists.newArrayList();
        }
        params.setLimit(AdminConstant.EXPORT_LIMIT);
        return monthFreezeAmountRepository.findAll(params).getContent();
    }
}
