package com.acpiot.wsd.admin.mvc.archive.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.acpiot.wsd.admin.mvc.archive.dto.FastPackDto;
import com.acpiot.wsd.admin.mvc.archive.service.PackBoxBatchService;
import com.acpiot.wsd.admin.mvc.archive.service.PackBoxService;
import com.acpiot.wsd.common.aop.annotation.NoCompanyFilter;
import com.acpiot.wsd.common.aop.annotation.NoRepeatSubmit;
import com.acpiot.wsd.common.aop.annotation.SystemLog;
import com.acpiot.wsd.data.archive.entity.PackBox;
import com.acpiot.wsd.data.archive.entity.PackBoxBatch;
import com.acpiot.wsd.data.archive.entity.PackBox_;
import com.acpiot.wsd.data.archive.entity.WaterMeterPackBox;
import com.acpiot.wsd.core.cache.CompanyNameCache;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import pers.mx.jupiter.jpa.PagingQueryParams;
import pers.mx.jupiter.web.util.ResponseUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by moxin on 2019-11-25-0025
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Controller
@RequestMapping("/archive/packBox")
public class PackBoxController {

    private final PackBoxService packBoxService;
    private final PackBoxBatchService packBoxBatchService;
    private final CompanyNameCache companyNameCache;

    @GetMapping("list")
    public String list(ModelMap map) {
        map.addAttribute("packBoxBatches", packBoxBatchService.getPackBoxBatches());
        return "archive/pack-box/list";
    }

    @PostMapping("page")
    @ResponseBody
    public Page<PackBox> page(@RequestBody PagingQueryParams<PackBox> params) {
        params.fetch(PackBox_.PACK_BOX_BATCH);
        return packBoxService.page(params);
    }

    @GetMapping("add")
    public String toAdd(@ModelAttribute("packBox") PackBox packBox) {
        return "archive/pack-box/add";
    }

    @NoCompanyFilter
    @GetMapping("packBoxBatches")
    @ResponseBody
    public List<PackBoxBatch> packBoxBatches(@RequestParam("companyId") String companyId) {
        return packBoxBatchService.getPackBoxBatches(companyId);
    }

    @NoCompanyFilter
    @NoRepeatSubmit
    @SystemLog(description = "新增装箱")
    @PostMapping("add")
    @ResponseBody
    public ResponseEntity<?> add(@Validated @ModelAttribute("packBox") PackBox packBox) {
        packBoxService.addPackBox(packBox);
        return ResponseUtils.ok(true);
    }

    @GetMapping("fastPack")
    public String toFastPack(@ModelAttribute("fastPackDto") FastPackDto fastPackDto) {
        return "archive/pack-box/fast-pack";
    }

    @NoCompanyFilter
    @NoRepeatSubmit
    @SystemLog(description = "快速装箱")
    @PostMapping("fastPack")
    @ResponseBody
    public ResponseEntity<?> fastPack(@Validated @ModelAttribute("fastPackDto") FastPackDto fastPackDto) {
        packBoxService.fastPack(fastPackDto);
        return ResponseUtils.ok(true);
    }

    @GetMapping("edit")
    public String toEdit(@RequestParam("id") long id, ModelMap map) {
        return packBoxService.getPackBoxById(id).map(packBox -> toEdit(map, packBox)).orElse("redirect:list");
    }

    private String toEdit(ModelMap map, PackBox packBox) {
        map.addAttribute("packBox", packBox);
        map.addAttribute("companyName", companyNameCache.get(packBox.getCompanyId()));
        return "archive/pack-box/edit";
    }

    @NoCompanyFilter
    @NoRepeatSubmit
    @SystemLog(description = "编辑装箱")
    @PostMapping("edit")
    @ResponseBody
    public ResponseEntity<?> edit(@Validated @ModelAttribute("packBox") PackBox packBox) {
        packBoxService.updatePackBox(packBox);
        return ResponseUtils.ok("编辑成功");
    }

    @SystemLog(description = "删除装箱")
    @DeleteMapping("delete")
    @ResponseBody
    public ResponseEntity<?> delete(@RequestParam("id") long id) {
        packBoxService.removePackBox(id);
        return ResponseUtils.ok("删除成功");
    }

    @GetMapping("getBindWaterMeters")
    @ResponseBody
    public List<WaterMeterPackBox> getBindWaterMeters(@RequestParam("packBoxId") long packBoxId) {
        return packBoxService.getBindWaterMeters(packBoxId);
    }

    @GetMapping("getPackBoxMeterCodes")
    @ResponseBody
    public String getPackBoxMeterCodes(@RequestParam("packBoxId") long packBoxId) {
        List<WaterMeterPackBox> bindWaterMeters = packBoxService.getBindWaterMeters(packBoxId);
        if (CollectionUtil.isNotEmpty(bindWaterMeters)) {
            return bindWaterMeters.stream().map(WaterMeterPackBox::getMeterCode).collect(Collectors.joining(","));
        }
        return null;
    }

    @GetMapping("toWaterMeterPackBox")
    public String toWaterMeterPackBox(@RequestParam("packBoxId") long packBoxId, @RequestParam("shipment") boolean shipment, ModelMap map) {
        map.addAttribute("packBoxId", packBoxId);
        map.addAttribute("shipment", shipment);
        return "archive/pack-box/water-meter-pack-box";
    }

    @SystemLog(description = "删除某个已装箱水表")
    @DeleteMapping("deleteWaterMeterPackBox")
    @ResponseBody
    public ResponseEntity<?> deleteWaterMeterPackBox(@RequestParam("waterMeterPackBoxId") long waterMeterPackBoxId) {
        packBoxService.deleteWaterMeterPackBox(waterMeterPackBoxId);
        return ResponseUtils.ok("删除成功");
    }

    @NoRepeatSubmit
    @SystemLog(description = "确认出货")
    @PostMapping("shipment")
    public ResponseEntity<?> shipment(@RequestParam("packBoxId") long packBoxId) {
        packBoxService.shipment(packBoxId);
        return ResponseUtils.ok("出货成功");
    }
}
