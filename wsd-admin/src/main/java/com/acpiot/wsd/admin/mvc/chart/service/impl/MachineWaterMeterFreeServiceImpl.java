package com.acpiot.wsd.admin.mvc.chart.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.admin.mvc.chart.dto.MachineWaterMeterFeeChartDto;
import com.acpiot.wsd.admin.mvc.chart.service.MachineWaterMeterFreeService;
import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.data.monitor.entity.QMachineWaterMeterReport;
import com.acpiot.wsd.data.monitor.entity.QMachineWaterMeterReportData;
import com.acpiot.wsd.data.revenue.entity.QWaterClassification;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 机械水表水费分流数据统计服务
 */
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class MachineWaterMeterFreeServiceImpl implements MachineWaterMeterFreeService {

    private final JPAQueryFactory jpaQueryFactory;

    @Override
    public List<MachineWaterMeterFeeChartDto> getMachineWaterFeeReportByMonth(String dimension, String yearMonth, Long communityId, Long[] meterBookIds,
                                                                              Long[] userIds, String pointName, String mobile, String meterCode,
                                                                              String accountNo, String customerName, String idCard) {
        // 检查维度选项是否合法
        if (!List.of("ladderPrice", "waterClassification", "meterBook", "user", "samePrice").contains(dimension)) {
            throw new BusinessException("统计维度选项值非法");
        }
        if (StrUtil.isBlank(yearMonth)) {
            throw new BusinessException("必须提供月份参数");
        }

        // 存储最终返回的数据结构
        Map<String, MachineWaterMeterFeeChartDto> results = new HashMap<>();

        // 用水金额查询条件
        QMachineWaterMeterReportData reportData = QMachineWaterMeterReportData.machineWaterMeterReportData;
        QMachineWaterMeterReport dataReport = reportData.machineWaterMeterReport;
        List<Predicate> amountPredicates = getPredicates(dataReport, yearMonth, communityId, meterBookIds, userIds, pointName, mobile, meterCode, accountNo, customerName, idCard);
        QWaterClassification amountPrice = dataReport.waterClassification;
        // 用水量查询条件
        QMachineWaterMeterReport report = QMachineWaterMeterReport.machineWaterMeterReport;
        List<Predicate> consumptionPredicates = getPredicates(report, yearMonth, communityId, meterBookIds, userIds, pointName, mobile, meterCode, accountNo, customerName, idCard);
        QWaterClassification consumptionPrice = report.waterClassification;

        // 根据统计维度进行分组查询并处理
        switch (dimension) {
            case "ladderPrice": {
                // 查询用水金额
                List<Tuple> amounts = jpaQueryFactory.select(amountPrice.id, dataReport.ladderPriceName, dataReport.pay, reportData.chargeName, reportData.amount.sum())
                        .from(reportData).where(amountPredicates.toArray(new Predicate[0]))
                        .groupBy(amountPrice.id, dataReport.ladderPriceName, dataReport.pay, reportData.chargeName).fetch();
                if (CollUtil.isEmpty(amounts)) {
                    return new ArrayList<>();
                }
                amounts.forEach(t -> {
                    String ladderPriceName = t.get(dataReport.ladderPriceName);
                    String groupId = t.get(amountPrice.id) + "_" + ladderPriceName;
                    MachineWaterMeterFeeChartDto data = results.computeIfAbsent(groupId, key -> new MachineWaterMeterFeeChartDto(key, ladderPriceName));
                    data.updateWaterFee(t.get(dataReport.pay), t.get(reportData.chargeName), t.get(reportData.amount.sum()));
                });
                // 查询用水量、笔数
                List<Tuple> consumptions = jpaQueryFactory.select(consumptionPrice.id, report.ladderPriceName, report.consumption.sum(), report.count())
                        .from(report).where(consumptionPredicates.toArray(new Predicate[0])).groupBy(consumptionPrice.id, report.ladderPriceName).fetch();
                consumptions.forEach(t -> {
                    MachineWaterMeterFeeChartDto chartDto = results.get(t.get(consumptionPrice.id) + "_" + t.get(report.ladderPriceName));
                    chartDto.setConsumption(t.get(report.consumption.sum()));
                    chartDto.setRecordTotal(t.get(report.count()));
                });
                break;
            }
            case "waterClassification": {
                // 查询用水金额
                List<Tuple> amounts = jpaQueryFactory.select(amountPrice.id, amountPrice.name, dataReport.pay, reportData.chargeName, reportData.amount.sum())
                        .from(reportData).where(amountPredicates.toArray(new Predicate[0])).groupBy(amountPrice.id, dataReport.pay, reportData.chargeName).fetch();
                if (CollUtil.isEmpty(amounts)) {
                    return new ArrayList<>();
                }
                amounts.forEach(t -> {
                    String priceName = t.get(amountPrice.name);
                    String groupId = t.get(amountPrice.id) + "";
                    MachineWaterMeterFeeChartDto data = results.computeIfAbsent(groupId, key -> new MachineWaterMeterFeeChartDto(key, priceName));
                    data.updateWaterFee(t.get(dataReport.pay), t.get(reportData.chargeName), t.get(reportData.amount.sum()));
                });
                // 查询用水量、户数
                List<Tuple> consumptions = jpaQueryFactory.select(consumptionPrice.id, report.consumption.sum(), report.measuringPoint.countDistinct())
                        .from(report).where(consumptionPredicates.toArray(new Predicate[0])).groupBy(consumptionPrice.id).fetch();
                consumptions.forEach(t -> {
                    MachineWaterMeterFeeChartDto chartDto = results.get(t.get(consumptionPrice.id) + "");
                    chartDto.setConsumption(t.get(report.consumption.sum()));
                    chartDto.setRecordTotal(t.get(report.measuringPoint.countDistinct()));
                });
                break;
            }
            case "meterBook": {
                amountPredicates.add(dataReport.meterBook.isNotNull());
                // 查询用水金额
                List<Tuple> amounts = jpaQueryFactory.select(dataReport.meterBook.id, dataReport.meterBook.name, dataReport.pay, reportData.chargeName, reportData.amount.sum())
                        .from(reportData).where(amountPredicates.toArray(new Predicate[0])).groupBy(dataReport.meterBook.id, dataReport.pay, reportData.chargeName).fetch();
                if (CollUtil.isEmpty(amounts)) {
                    return new ArrayList<>();
                }
                amounts.forEach(t -> {
                    String meterBookName = t.get(dataReport.meterBook.name);
                    String groupId = t.get(dataReport.meterBook.id) + "";
                    MachineWaterMeterFeeChartDto data = results.computeIfAbsent(groupId, key -> new MachineWaterMeterFeeChartDto(key, meterBookName));
                    data.updateWaterFee(t.get(dataReport.pay), t.get(reportData.chargeName), t.get(reportData.amount.sum()));
                });
                consumptionPredicates.add(report.meterBook.isNotNull());
                // 查询用水量、户数
                List<Tuple> consumptions = jpaQueryFactory.select(report.meterBook.id, report.consumption.sum(), report.measuringPoint.countDistinct())
                        .from(report).where(consumptionPredicates.toArray(new Predicate[0])).groupBy(report.meterBook.id).fetch();
                consumptions.forEach(t -> {
                    MachineWaterMeterFeeChartDto chartDto = results.get(t.get(report.meterBook.id) + "");
                    chartDto.setConsumption(t.get(report.consumption.sum()));
                    chartDto.setRecordTotal(t.get(report.measuringPoint.countDistinct()));
                });
                break;
            }
            case "user": {
                // 查询用水金额
                List<Tuple> amounts = jpaQueryFactory.select(dataReport.meterBook.user.id, dataReport.meterBook.user.name, dataReport.meterBook.user.realName, dataReport.pay,
                        reportData.chargeName, reportData.amount.sum()).from(reportData).where(amountPredicates.toArray(new Predicate[0]))
                        .groupBy(dataReport.meterBook.user.id, dataReport.pay, reportData.chargeName).fetch();
                if (CollUtil.isEmpty(amounts)) {
                    return new ArrayList<>();
                }
                amounts.forEach(t -> {
                    String userName = StrUtil.isNotBlank(t.get(dataReport.meterBook.user.realName)) ? t.get(dataReport.meterBook.user.realName) : t.get(dataReport.meterBook.user.name);
                    String groupId = t.get(dataReport.meterBook.user.id) + "";
                    MachineWaterMeterFeeChartDto data = results.computeIfAbsent(groupId, key -> new MachineWaterMeterFeeChartDto(key, userName));
                    data.updateWaterFee(t.get(dataReport.pay), t.get(reportData.chargeName), t.get(reportData.amount.sum()));
                });
                // 查询用水量、户数
                List<Tuple> consumptions = jpaQueryFactory.select(report.meterBook.user.id, report.consumption.sum(), report.measuringPoint.countDistinct())
                        .from(report).where(consumptionPredicates.toArray(new Predicate[0])).groupBy(report.meterBook.user.id).fetch();
                consumptions.forEach(t -> {
                    MachineWaterMeterFeeChartDto chartDto = results.get(t.get(report.meterBook.user.id) + "");
                    chartDto.setConsumption(t.get(report.consumption.sum()));
                    chartDto.setRecordTotal(t.get(report.measuringPoint.countDistinct()));
                });
                break;
            }
            case "samePrice": {
                amountPredicates.add(reportData.chargeName.eq("污水费").or(reportData.amount.gt(0)));

                // 查询用水金额
                List<Tuple> amounts = jpaQueryFactory.select(dataReport.pay,
                                reportData.chargeName, reportData.ladderPrice, reportData.amount.sum()).from(reportData)
                        .where(amountPredicates.toArray(new Predicate[0]))
                        .groupBy(reportData.ladderPrice, dataReport.pay, reportData.chargeName).fetch();
                if (CollUtil.isEmpty(amounts)) {
                    return new ArrayList<>();
                }
                amounts.forEach(t -> {
                    String chargeNameAndPrice = t.get(reportData.chargeName)+ "(" + t.get(reportData.ladderPrice) + ")";
                    String groupId = t.get(reportData.chargeName) + "_" + t.get(reportData.ladderPrice);
                    MachineWaterMeterFeeChartDto data = results.computeIfAbsent(groupId, key -> new MachineWaterMeterFeeChartDto(key, chargeNameAndPrice));
                    data.updateWaterFee(t.get(dataReport.pay), null, t.get(reportData.amount.sum()));
                });

                consumptionPredicates.add(reportData.chargeName.eq("污水费").or(reportData.amount.gt(0)));
                // 查询用水量、笔数
                List<Tuple> consumptions = jpaQueryFactory.select(report.consumption.sum(), report.count(), reportData.chargeName, reportData.ladderPrice)
                        .from(report).join(reportData).on(report.id.eq(reportData.machineWaterMeterReport.id))
                        .where(consumptionPredicates.toArray(new Predicate[0])).groupBy(reportData.chargeName, reportData.ladderPrice).fetch();
                consumptions.forEach(t -> {
                    MachineWaterMeterFeeChartDto chartDto = results.get(t.get(reportData.chargeName) + "_" + t.get(reportData.ladderPrice));
                    chartDto.setConsumption(t.get(report.consumption.sum()));
                    chartDto.setRecordTotal(t.get(report.count()));
                });
                // 移除条件
                consumptionPredicates.remove(reportData.chargeName.eq("污水费").or(reportData.amount.gt(0)));
                break;
            }
        }

        // 计算汇总统计数据行
        final String totalGroupId = "all";
        List<MachineWaterMeterFeeChartDto> reportResults = new ArrayList<>(results.values());
        MachineWaterMeterFeeChartDto totalReport = new MachineWaterMeterFeeChartDto(totalGroupId);

        if (dimension.equals("samePrice")) {
            consumptionPredicates.add(report.ladderPriceName.like("%(0.00)%").or(report.ladderPriceName.like("%(0.000000)%")));
            consumptionPredicates.add(report.consumption.gt(0));
            // 查询出所有用水量大于0的主表id
            List<Long> reportIds = jpaQueryFactory.select(report.id)
                    .from(report)
                    .where(consumptionPredicates.toArray(new Predicate[0])).fetch();
            consumptionPredicates.remove(report.ladderPriceName.like("%(0.00)%").or(report.ladderPriceName.like("%(0.000000)%")));
            consumptionPredicates.remove(report.consumption.gt(0));
            if (CollUtil.isNotEmpty(reportIds)) {
                // 查询出这些主表对应的免费用水的用水量
                List<BigDecimal> freeConsumptions = jpaQueryFactory.select(reportData.machineWaterMeterReport.consumption)
                        .from(reportData)
                        .where(reportData.machineWaterMeterReport.id.in(reportIds), reportData.chargeName.eq("免费用水")).fetch();
                MachineWaterMeterFeeChartDto machineWaterMeterFeeChartDto = new MachineWaterMeterFeeChartDto();
                machineWaterMeterFeeChartDto.setSubject("免费用水(0.00)");
                machineWaterMeterFeeChartDto.setGroupId("免费用水_0.00");
                machineWaterMeterFeeChartDto.setConsumption(freeConsumptions.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
                machineWaterMeterFeeChartDto.setRecordTotal((long) freeConsumptions.size());
                reportResults.add(machineWaterMeterFeeChartDto);
            }
        }

        totalReport.setSubject(String.format("共有%s笔数据", reportResults.size()));
        reportResults.forEach(rp -> rp.fillToTotalReportData(totalReport));

        // 计算用水量占比
        if (totalReport.getConsumption().compareTo(BigDecimal.ZERO) > 0) {
            reportResults.forEach(rp -> {
                assert rp.getConsumption() != null;
                BigDecimal value = rp.getConsumption().divide(totalReport.getConsumption(), 4, RoundingMode.HALF_UP);
                rp.setWaterPercent(value.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
            });
        }

        if (dimension.equals("samePrice")) {
            // 减去污水费计算总用水量
            BigDecimal wasteFeeConsumption = reportResults.stream().filter(rp -> rp.getSubject().startsWith("污水费")).map(MachineWaterMeterFeeChartDto::getConsumption).reduce(BigDecimal.ZERO, BigDecimal::add);
            totalReport.setConsumption(totalReport.getConsumption().subtract(wasteFeeConsumption));
        }

        // 对返回的分组数据统计数据进行排序，避免每次显示的顺序不一致或者相同组别的数据行分散
        reportResults.sort(Comparator.comparing(MachineWaterMeterFeeChartDto::getSubject));

        // 计算完分组数据中每一条的用水量占比后，再将汇总统计加入最终数据集
        reportResults.add(totalReport);
        return reportResults;
    }


    /**
     * 构建查询条件
     *
     * @param machineWaterMeterReport
     * @param yearMonth
     * @param communityId
     * @param meterBookIds
     * @param userIds
     * @param pointName
     * @param mobile
     * @param meterCode
     * @param accountNo
     * @param customerName
     * @param idCard
     * @return
     */
    private List<Predicate> getPredicates(QMachineWaterMeterReport machineWaterMeterReport, String yearMonth, Long communityId, Long[] meterBookIds, Long[] userIds,
                                          String pointName, String mobile, String meterCode, String accountNo, String customerName, String idCard) {
        List<Predicate> predicates = new ArrayList<>();
        // 添加月份参数
        predicates.add(machineWaterMeterReport.reportMonth.eq(yearMonth));
        // 判断是否添加小区条件
        if (communityId != null && communityId > 0) {
            predicates.add(machineWaterMeterReport.community.id.eq(communityId));
        }
        // 判断是否添加抄表册条件
        if (ArrayUtil.isNotEmpty(meterBookIds)) {
            predicates.add(machineWaterMeterReport.meterBook.id.in(meterBookIds));
        }
        // 判断是否添加抄表员条件
        if (ArrayUtil.isNotEmpty(userIds)) {
            predicates.add(machineWaterMeterReport.meterBook.user.id.in(userIds));
        }
        // 判断是否添加计量点条件
        if (StrUtil.isNotBlank(pointName)) {
            predicates.add(machineWaterMeterReport.measuringPoint.name.like("%" + pointName + "%"));
        }
        // 判断是否添加手机号条件
        if (StrUtil.isNotBlank(mobile)) {
            predicates.add(machineWaterMeterReport.measuringPoint.mobile.like("%" + mobile + "%"));
        }
        // 判断是否添加水表编号条件
        if (StrUtil.isNotBlank(meterCode)) {
            predicates.add(machineWaterMeterReport.measuringPoint.meterCode.like("%" + meterCode + "%"));
        }
        // 判断是否添加户号条件
        if (StrUtil.isNotBlank(accountNo)) {
            predicates.add(machineWaterMeterReport.measuringPoint.accountNo.like("%" + accountNo + "%"));
        }
        // 添加客户姓名条件
        if (StrUtil.isNotBlank(customerName)) {
            predicates.add(machineWaterMeterReport.measuringPoint.customer.name.like("%" + customerName + "%"));
        }
        // 添加客户身份证号条件
        if (StrUtil.isNotBlank(idCard)) {
            predicates.add(machineWaterMeterReport.measuringPoint.customer.idCard.like("%" + idCard + "%"));
        }
        return predicates;
    }
}
