package com.acpiot.wsd.admin.mvc.archive.dto;

import com.acpiot.wsd.admin.mvc.archive.converter.IotPlatformConverter;
import com.acpiot.wsd.data.archive.entity.MeasuringPoint;
import com.acpiot.wsd.data.archive.entity.NbInfo;
import com.acpiot.wsd.data.archive.entity.NbWaterMeter;
import com.acpiot.wsd.data.archive.enums.IotPlatform;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * NB水表预导入档案Excel格式
 * Created by moxin on 2020-05-27-0027
 *
 * <AUTHOR> Email: <EMAIL>
 */
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@Data
public class ExcelNbMeterPreImportDto {

    @NotBlank(message = "表号不能为空")
    @Size(max = 14, message = "表号最多为14位")
    @ColumnWidth(18)
    @ExcelProperty(index = 0)
    private String meterCode;

    /**
     * IoT平台
     */
    @NotNull(message = "IoT平台不能为空")
    @ColumnWidth(18)
    @ExcelProperty(value = {"NB水表档案", "IoT平台"}, converter = IotPlatformConverter.class)
    private IotPlatform iotPlatform;

    /**
     * IMEI
     */
    @NotBlank(message = "IMEI不能为空")
    @Pattern(regexp = "\\d{15}", message = "IMEI长度为15位数字")
    @ColumnWidth(18)
    @ExcelProperty({"NB水表档案", "IMEI"})
    private String imei;

    /**
     * IMSI
     */
    @NotBlank(message = "IMSI不能为空")
    @Pattern(regexp = "\\d{15}", message = "IMSI长度为15位数字")
    @ColumnWidth(18)
    @ExcelProperty({"NB水表档案", "IMSI"})
    private String imsi;

    /**
     * 是否有阀
     */
    @NotBlank(message = "是否有阀不能为空")
    @ColumnWidth(20)
    @ExcelProperty({"NB水表档案", "是否有阀"})
    private String valve;

    /**
     * ICCID
     */
    @Size(max = 20, message = "ICCID最多可输入20个字符")
    @ColumnWidth(20)
    @ExcelProperty({"NB水表档案", "ICCID"})
    private String iccid;

    /**
     * 构造预导入水表信息
     * @return
     */
    public NbWaterMeter toNbWaterMeter() {
        NbWaterMeter nbWaterMeter = new NbWaterMeter();
        nbWaterMeter.setCode(this.meterCode);

        MeasuringPoint measuringPoint = new MeasuringPoint();
        measuringPoint.setMeterCode(this.meterCode);
        measuringPoint.setName(this.meterCode);
        nbWaterMeter.setMeasuringPoint(measuringPoint);

        NbInfo nbInfo = new NbInfo();
        nbInfo.setIotPlatform(this.iotPlatform);
        nbInfo.setImei(this.imei);
        nbInfo.setImsi(this.imsi);
        nbInfo.setIccid(this.iccid);
        nbWaterMeter.setNbInfo(nbInfo);

        nbWaterMeter.setPreImport(true);
        return nbWaterMeter;
    }
}
