package com.acpiot.wsd.admin.mvc.screen.service;

import com.acpiot.wsd.admin.mvc.screen.dto.CurrentMonthCommunityConsumptionDto;
import com.acpiot.wsd.admin.mvc.screen.dto.MonitorDataStatistics;
import com.acpiot.wsd.admin.mvc.screen.dto.RevenueDataStatistics;
import com.acpiot.wsd.common.dto.CommonKeyValue;
import org.springframework.data.domain.Page;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.util.List;

/**
 * 营收大屏服务
 * Created by zc on 2021/6/30 17:43
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface RevenueScreenService {

    /**
     * 获取近30天的营收统计数据
     *
     * @return
     */
    List<CommonKeyValue> getLast30DaysRevenueData();

    /**
     * 获取近12个月的营收统计数据
     *
     * @return
     */
    List<CommonKeyValue> getLast12MonthsRevenueData();

    /**
     * 分页查询近30天各小区的营收统计数据
     *
     * @param params
     * @return
     */
    Page<CommonKeyValue> pageLast30DaysCommunityRevenueData(PagingQueryParams<CommonKeyValue> params);

    /**
     * 获取营收汇总统计数据
     *
     * @return
     */
    RevenueDataStatistics getRevenueDataStatistics();

    /**
     * 获取近12个月的结算统计数据
     *
     * @return
     */
    List<CommonKeyValue> getLast12MonthsSettleData();

    /**
     * 获取近12个月各缴费渠道缴费金额占比统计数据
     *
     * @return
     */
    List<CommonKeyValue> getLast12MonthsPayIncomeTypeProportion();

    /**
     * 获取当前抄表汇总统计数据项
     *
     * @return
     */
    MonitorDataStatistics getMonitorDataStatistics();

    /**
     * 分页查询当月各小区零用水户数情况统计
     *
     * @param params
     * @return
     */
    Page<CurrentMonthCommunityConsumptionDto> pageCurrentMonthWaterMeterUseDataStatistics(PagingQueryParams<CurrentMonthCommunityConsumptionDto> params);

    /**
     * 获取近12个月用水总量统计
     *
     * @return
     */
    List<CommonKeyValue> getLast12MonthsUseWaterTotalStatistics();
}
