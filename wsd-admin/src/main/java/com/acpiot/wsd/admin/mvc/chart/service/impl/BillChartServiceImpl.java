package com.acpiot.wsd.admin.mvc.chart.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.acpiot.wsd.admin.mvc.chart.dto.MeasuringPointStatisticsDto;
import com.acpiot.wsd.admin.mvc.chart.service.BillChartService;
import com.acpiot.wsd.admin.mvc.common.projections.BaseProjection;
import com.acpiot.wsd.common.utils.StatisticsDataFillUtil;
import com.acpiot.wsd.admin.mvc.sys.projections.RegionProjections;
import com.acpiot.wsd.common.dto.CommonKeyValue;
import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.data.archive.entity.Community_;
import com.acpiot.wsd.data.archive.repository.CommunityRepository;
import com.acpiot.wsd.data.revenue.entity.QBill;
import com.google.common.collect.Lists;
import com.querydsl.core.QueryResults;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.StringTemplate;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.Filter;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.acpiot.wsd.common.utils.StatisticsDataFillUtil.fillMapMonth;
import static com.acpiot.wsd.common.utils.StatisticsDataFillUtil.fillZeroKeyValue;
import static com.acpiot.wsd.common.security.util.SecurityContextUtils.getGlobalFilter;
import static com.acpiot.wsd.core.query.CommunityFilters.byRegionId;

/**
 * 账单相关统计接口
 * Created by hsq on 2021-07-09-0000
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class BillChartServiceImpl implements BillChartService {

    private final JPAQueryFactory jpaQueryFactory;
    private final CommunityRepository communityRepository;

    @Override
    public List<CommonKeyValue> getBillDataByMonth(Long communityId, Long regionId, String pointName, String mobile, String meterCode, String accountNo, int year) {
        QBill bill = QBill.bill;
        List<Predicate> predicates = new ArrayList<>();
        Date firstDayStart = DateUtil.parse(year + "-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        Date lastDayEnd = DateUtil.parse(year + "-12-31 23:59:59", "yyyy-MM-dd HH:mm:ss");
        predicates.add(bill.endDate.between(firstDayStart, lastDayEnd));
        getGlobalFilter(bill.measuringPoint.companyId, bill.measuringPoint.community.id).ifPresent(predicates::addAll);
        if (communityId != null) {
            predicates.add(bill.measuringPoint.community.id.eq(communityId));
        }
        if (regionId != null) {
            List<RegionProjections.Community> communityList = communityRepository.findAll(RegionProjections.Community.class,
                    new Filter[]{byRegionId(regionId), Filter.isNull(Community_.PARENT)});
            if (CollUtil.isEmpty(communityList)) {
                predicates.add(bill.measuringPoint.community.id.eq(0L));
            } else {
                List<Long> communityIds = communityList.stream().map(BaseProjection::getId).collect(Collectors.toList());
                predicates.add(bill.measuringPoint.community.id.in(communityIds));
            }
        }
        if (StringUtils.isNotBlank(pointName)) {
            predicates.add(bill.measuringPoint.name.like(pointName));
        }
        if (StringUtils.isNotBlank(mobile)) {
            predicates.add(bill.customer.mobile.like(mobile));
        }
        if (StringUtils.isNotBlank(meterCode)) {
            predicates.add(bill.measuringPoint.meterCode.like(meterCode));
        }
        if (StringUtils.isNotBlank(accountNo)) {
            predicates.add(bill.measuringPoint.accountNo.like(accountNo));
        }
        StringTemplate monthExpr = Expressions.stringTemplate("DATE_FORMAT({0},'%Y-%m')", bill.endDate);
        List<CommonKeyValue> commonKeyValues = jpaQueryFactory.select(monthExpr, bill.amount.sum())
                .from(bill)
                .where(predicates.toArray(new Predicate[0]))
                .groupBy(monthExpr)
                .fetch().stream()
                .map(tuple -> new CommonKeyValue(
                        tuple.get(monthExpr),
                        tuple.get(bill.amount.sum())
                )).collect(Collectors.toList());
        LocalDate start = LocalDate.of(year, 1, 1);
        LocalDate end = LocalDate.of(year, 12, 31);
        return fillZeroKeyValue(commonKeyValues, Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant()),
                Date.from(end.atStartOfDay(ZoneId.systemDefault()).toInstant()), StatisticsDataFillUtil.KeyType.MONTH);
    }

    @Override
    public Page<MeasuringPointStatisticsDto> pageMeasuringPointBillDataByMonth(PagingQueryParams<CommonKeyValue> params) {
        QBill bill = QBill.bill;
        List<Predicate> predicates = new ArrayList<>();
        getGlobalFilter(bill.measuringPoint.companyId, bill.measuringPoint.community.id).ifPresent(predicates::addAll);
        List<Filter> andFilters = params.getAndFilters();
        String year = "";
        Date firstDayStart = null;
        Date lastDayEnd = null;
        for (Filter filter : andFilters) {
            if (filter.getValue() == null || StringUtils.isBlank(filter.getValue().toString())) {
                continue;
            }
            if (filter.getProperty().equalsIgnoreCase("year")) {
                year = filter.getValue().toString();
                firstDayStart = DateUtil.parse(year + "-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
                lastDayEnd = DateUtil.parse(year + "-12-31 23:59:59", "yyyy-MM-dd HH:mm:ss");
                predicates.add(bill.endDate.between(firstDayStart, lastDayEnd));
            } else if (filter.getProperty().equalsIgnoreCase("communityId")) {
                predicates.add(bill.measuringPoint.community.id.eq(Long.parseLong(filter.getValue().toString())));
            } else if (filter.getProperty().equalsIgnoreCase("regionId")) {
                long regionId = Long.parseLong(filter.getValue().toString());
                List<RegionProjections.Community> communityList = communityRepository.findAll(RegionProjections.Community.class,
                        new Filter[]{byRegionId(regionId), Filter.isNull(Community_.PARENT)});
                if (CollUtil.isEmpty(communityList)) {
                    predicates.add(bill.measuringPoint.community.id.eq(0L));
                } else {
                    List<Long> communityIds = communityList.stream().map(BaseProjection::getId).collect(Collectors.toList());
                    predicates.add(bill.measuringPoint.community.id.in(communityIds));
                }
            } else if (filter.getProperty().equalsIgnoreCase("pointName")) {
                predicates.add(bill.measuringPoint.name.like(filter.getValue().toString()));
            } else if (filter.getProperty().equalsIgnoreCase("mobile")) {
                predicates.add(bill.customer.mobile.like(filter.getValue().toString()));
            } else if (filter.getProperty().equalsIgnoreCase("meterCode")) {
                predicates.add(bill.measuringPoint.meterCode.like(filter.getValue().toString()));
            } else if (filter.getProperty().equalsIgnoreCase("accountNo")) {
                predicates.add(bill.measuringPoint.accountNo.like(filter.getValue().toString()));
            }
        }
        // 必须提供查询年份
        if (StringUtils.isBlank(year)) {
            throw new BusinessException("请选择查询年份");
        }

        // 先按照计量点分组，账单总额倒序排序，查询出本页的数据
        QueryResults<Tuple> tupleQueryResults = jpaQueryFactory.select(bill.measuringPoint.id, bill.measuringPoint.name,
                bill.customer.mobile, bill.measuringPoint.meterCode, bill.measuringPoint.accountNo,
                bill.measuringPoint.community.name, bill.amount.sum(), bill.measuringPoint.companyId)
                .from(bill)
                .where(predicates.toArray(new Predicate[0]))
                .groupBy(bill.measuringPoint.id)
                .orderBy(bill.amount.sum().desc())
                .offset(params.getOffset())
                .limit(params.getLimit())
                .fetchResults();
        // 获取总数
        long total = tupleQueryResults.getTotal();
        // 获取当页查询结果
        List<Tuple> results = tupleQueryResults.getResults();
        if (CollectionUtils.isEmpty(results)) {
            return new PageImpl<>(Lists.newArrayList());
        }

        List<MeasuringPointStatisticsDto> measuringPointStatisticsDtoList = new ArrayList<>();
        // 取出计量点id的集合
        List<Long> measuringPointIds = results.stream().map(tuple -> tuple.get(bill.measuringPoint.id)).collect(Collectors.toList());
        List<Predicate> measuringPointPredicates = new ArrayList<>();
        if (firstDayStart != null) {
            measuringPointPredicates.add(bill.endDate.between(firstDayStart, lastDayEnd));
        }
        measuringPointPredicates.add(bill.measuringPoint.id.in(measuringPointIds));
        // 根据计量点id和月份分组
        StringTemplate monthExpr = Expressions.stringTemplate("DATE_FORMAT({0},'%Y-%m')", bill.endDate);
        List<Tuple> tupleList = jpaQueryFactory.select(bill.measuringPoint.id, monthExpr, bill.amount.sum())
                .from(bill)
                .where(measuringPointPredicates.toArray(new Predicate[0]))
                .groupBy(bill.measuringPoint.id, monthExpr)
                .fetch();
        Map<Long, List<Tuple>> measuringPointMap = tupleList.stream().collect(Collectors.groupingBy(tuple -> tuple.get(bill.measuringPoint.id)));
        String finalYear = year;
        // 循环分页结果，以免数据顺序被打乱
        results.forEach(tuple -> {
            MeasuringPointStatisticsDto measuringPointStatisticsDto = new MeasuringPointStatisticsDto();
            measuringPointStatisticsDto.setMeasuringPointName(tuple.get(bill.measuringPoint.name));
            measuringPointStatisticsDto.setMobile(tuple.get(bill.customer.mobile));
            measuringPointStatisticsDto.setMeterCode(tuple.get(bill.measuringPoint.meterCode));
            measuringPointStatisticsDto.setAccountNo(tuple.get(bill.measuringPoint.accountNo));
            measuringPointStatisticsDto.setCommunityName(tuple.get(bill.measuringPoint.community.name));
            measuringPointStatisticsDto.setValue(tuple.get(bill.amount.sum()));
            measuringPointStatisticsDto.setCompanyId(tuple.get(bill.measuringPoint.companyId));
            List<Tuple> monthData = measuringPointMap.get(tuple.get(bill.measuringPoint.id));
            // 处理每月的数据
            Map<String, Object> data = new HashMap<>();
            monthData.forEach(item -> data.put(item.get(monthExpr), item.get(bill.amount.sum())));
            // 补全缺失的月份，将值设为0
            fillMapMonth(data, finalYear);
            measuringPointStatisticsDto.setData(data);
            measuringPointStatisticsDtoList.add(measuringPointStatisticsDto);
        });
        return new PageImpl<>(measuringPointStatisticsDtoList, params.getPageRequest(), total);
    }
}
