package com.acpiot.wsd.admin.mvc.sys.serivce;

import com.acpiot.wsd.data.sys.entity.Notification;
import org.springframework.data.domain.Page;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.util.Optional;

/**
 * 通知(Notification)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
public interface NotificationService {

    Page<Notification> pagedNotification(PagingQueryParams<Notification> params);

    void addNotification(Notification notification);

    Optional<Notification> getNotificationById(long id);

    void updateNotification(Notification notification);

    Notification getLastNotification();

    void deleteNotification(long id);

    void markNotificationRead();
}