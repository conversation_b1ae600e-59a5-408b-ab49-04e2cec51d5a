package com.acpiot.wsd.admin.mvc.chart.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.admin.mvc.chart.service.WaterMeterTypeChartService;
import com.acpiot.wsd.admin.mvc.common.projections.BaseProjection;
import com.acpiot.wsd.admin.mvc.sys.projections.RegionProjections;
import com.acpiot.wsd.data.archive.entity.Community_;
import com.acpiot.wsd.data.archive.entity.QCommunity;
import com.acpiot.wsd.data.archive.entity.QWaterMeter;
import com.acpiot.wsd.data.archive.enums.MeterType;
import com.acpiot.wsd.data.archive.repository.CommunityRepository;
import com.acpiot.wsd.admin.mvc.chart.dto.WaterMeterTypeDto;
import com.querydsl.core.QueryResults;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.Filter;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.hutool.core.util.ObjectUtil.defaultIfNull;
import static com.acpiot.wsd.core.query.CommunityFilters.byRegionId;

/**
 * 水表设备类型占比统计服务
 * Created by hsq on 2021/7/22 9:37
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class WaterMeterTypeChartServiceImpl implements WaterMeterTypeChartService {

    private final JPAQueryFactory jpaQueryFactory;
    private final CommunityRepository communityRepository;

    @Override
    public WaterMeterTypeDto getWaterMeterTypeTotal(Long communityId, Long regionId) {
        WaterMeterTypeDto result = new WaterMeterTypeDto();
        QWaterMeter waterMeter = QWaterMeter.waterMeter;
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(waterMeter.community.companyId.isNotNull());
        if (communityId != null && communityId > 0) {
            predicates.add(waterMeter.community.id.eq(communityId));
        }
        if (regionId != null && regionId > 0) {
            List<RegionProjections.Community> communityList = communityRepository.findAll(RegionProjections.Community.class,
                    new Filter[]{byRegionId(regionId), Filter.isNull(Community_.PARENT)});
            if (CollUtil.isEmpty(communityList)) {
                return result;
            }
            List<Long> communityIds = communityList.stream().map(BaseProjection::getId).collect(Collectors.toList());
            predicates.add(waterMeter.community.id.in(communityIds));
        }
        Tuple tuple = jpaQueryFactory.select(
                        waterMeter.count(),
                        new CaseBuilder()
                                .when(waterMeter.model.meterType.eq(MeterType.NB_IOT))
                                .then(1)
                                .otherwise(0)
                                .sum(),
                        new CaseBuilder()
                                .when(waterMeter.model.meterType.eq(MeterType.CONCENTRATOR))
                                .then(1)
                                .otherwise(0)
                                .sum(),
                        new CaseBuilder()
                                .when(waterMeter.model.meterType.eq(MeterType.CAT1))
                                .then(1)
                                .otherwise(0)
                                .sum(),
                        new CaseBuilder()
                                .when(waterMeter.model.meterType.eq(MeterType.MACHINE))
                                .then(1)
                                .otherwise(0)
                                .sum(),
                        new CaseBuilder()
                                .when(waterMeter.valve.eq(true))
                                .then(1)
                                .otherwise(0)
                                .sum(),
                        new CaseBuilder()
                                .when(waterMeter.valve.eq(false))
                                .then(1)
                                .otherwise(0)
                                .sum()
                )
                .from(waterMeter)
                .where(predicates.toArray(new Predicate[0]))
                .fetchOne();
        if (tuple == null) {
            return result;
        }
        result.setWaterMeterTotal(defaultIfNull(tuple.get(0, Number.class), 0).intValue());
        result.setNbWaterMeterTotal(defaultIfNull(tuple.get(1, Number.class), 0).intValue());
        result.setConWaterMeterTotal(defaultIfNull(tuple.get(2, Number.class), 0).intValue());
        result.setUdpWaterMeterTotal(defaultIfNull(tuple.get(3, Number.class), 0).intValue());
        result.setMachineWaterMeterTotal(defaultIfNull(tuple.get(4, Number.class), 0).intValue());
        result.setHasValveWaterMeterTotal(defaultIfNull(tuple.get(5, Number.class), 0).intValue());
        result.setNoValveWaterMeterTotal(defaultIfNull(tuple.get(6, Number.class), 0).intValue());
        return result;
    }

    @Override
    public Page<WaterMeterTypeDto> pageCommunityWaterMeterTypes(PagingQueryParams<WaterMeterTypeDto> params) {
        QWaterMeter waterMeter = QWaterMeter.waterMeter;
        QCommunity community = waterMeter.community;

        // 构建条件列表
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(community.parent.isNull());
        predicates.add(community.companyId.isNotNull());
        for (Filter filter : params.getAndFilters()) {
            if (filter.getValue() == null || StrUtil.isBlank(filter.getValue().toString())) continue;
            switch (filter.getProperty()) {
                case "communityId":
                    Long communityId = Long.parseLong(filter.getValue().toString());
                    predicates.add(community.id.eq(communityId));
                    break;
                case "regionId":
                    long regionId = Long.parseLong(filter.getValue().toString());
                    List<RegionProjections.Community> communities = communityRepository.findAll(
                            RegionProjections.Community.class, new Filter[]{byRegionId(regionId), Filter.isNull(Community_.PARENT)});
                    if (CollUtil.isEmpty(communities)) {
                        return new PageImpl<>(List.of());
                    }
                    List<Long> communityIds = communities.stream().map(BaseProjection::getId).collect(Collectors.toList());
                    predicates.add(community.id.in(communityIds));
                    break;
            }
        }

        // 分页查询加载数据
        QueryResults<Tuple> results = jpaQueryFactory.select(
                        community.id,
                        community.name,
                        community.companyId,
                        waterMeter.count(),
                        new CaseBuilder()
                                .when(waterMeter.model.meterType.eq(MeterType.NB_IOT))
                                .then(1)
                                .otherwise(0)
                                .sum(),
                        new CaseBuilder()
                                .when(waterMeter.model.meterType.eq(MeterType.CONCENTRATOR))
                                .then(1)
                                .otherwise(0)
                                .sum(),
                        new CaseBuilder()
                                .when(waterMeter.model.meterType.eq(MeterType.CAT1))
                                .then(1)
                                .otherwise(0)
                                .sum(),
                        new CaseBuilder()
                                .when(waterMeter.model.meterType.eq(MeterType.MACHINE))
                                .then(1)
                                .otherwise(0)
                                .sum(),
                        new CaseBuilder()
                                .when(waterMeter.valve.eq(true))
                                .then(1)
                                .otherwise(0)
                                .sum(),
                        new CaseBuilder()
                                .when(waterMeter.valve.eq(false))
                                .then(1)
                                .otherwise(0)
                                .sum()
                )
                .from(waterMeter)
                .where(predicates.toArray(new Predicate[0]))
                .groupBy(community.id)
                .orderBy(waterMeter.count().desc())
                .limit(params.getLimit())
                .offset(params.getOffset())
                .fetchResults();

        // 转换数据结构
        List<WaterMeterTypeDto> dataList = new ArrayList<>();
        for (Tuple tuple : results.getResults()) {
            WaterMeterTypeDto dto = new WaterMeterTypeDto();
            dto.setCommunityId(tuple.get(0, Long.class));
            dto.setCommunityName(tuple.get(1, String.class));
            dto.setCompanyId(tuple.get(2, String.class));
            dto.setWaterMeterTotal(defaultIfNull(tuple.get(3, Number.class), 0).intValue());
            dto.setNbWaterMeterTotal(defaultIfNull(tuple.get(4, Number.class), 0).intValue());
            dto.setConWaterMeterTotal(defaultIfNull(tuple.get(5, Number.class), 0).intValue());
            dto.setUdpWaterMeterTotal(defaultIfNull(tuple.get(6, Number.class), 0).intValue());
            dto.setMachineWaterMeterTotal(defaultIfNull(tuple.get(7, Number.class), 0).intValue());
            dto.setHasValveWaterMeterTotal(defaultIfNull(tuple.get(8, Number.class), 0).intValue());
            dto.setNoValveWaterMeterTotal(defaultIfNull(tuple.get(9, Number.class), 0).intValue());
            dataList.add(dto);
        }
        return new PageImpl<>(dataList, params.getPageRequest(), results.getTotal());
    }
}
