package com.acpiot.wsd.admin.mvc.monitor.service;

import com.acpiot.wsd.admin.mvc.monitor.query.ServiceValveCtrlQuery;
import com.acpiot.wsd.data.monitor.entity.ServiceValveCtrlRecord;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Optional;

/**
 * 业务开关阀记录服务
 * Created by zc on 2022/1/6 15:19
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface ServiceValveCtrlRecordService {

    void saveServiceValveCtrlRecord(ServiceValveCtrlRecord serviceValveCtrlRecord);

    List<ServiceValveCtrlRecord> findByExecutionStatus(ServiceValveCtrlRecord.CommandStatus commandStatus);

    void retryServiceValveCtrl(Long serviceValveCtrlRecordId);

    void updateRetryData(long serviceValveCtrlId, String cmdLogId, String errorMsg);

    void handleValveCtrlResponse(String commandId, boolean valveCtrlResult);

    Page<ServiceValveCtrlRecord> pagedServiceValveCtrlRecord(ServiceValveCtrlQuery params);

    Optional<ServiceValveCtrlRecord> getNewestServiceValveCtrlRecord(long meterId);

    Optional<ServiceValveCtrlRecord> findByIdFetchWaterMeter(long id);

    void clearServiceValveCtrlRecord(Long meterId);
}
