package com.acpiot.wsd.admin.mvc.revenue.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.admin.constant.AdminConstant;
import com.acpiot.wsd.admin.mvc.archive.service.CommunityService;
import com.acpiot.wsd.admin.mvc.revenue.dto.BillBalanceDto;
import com.acpiot.wsd.admin.mvc.revenue.dto.ExcelBillDto;
import com.acpiot.wsd.admin.mvc.revenue.query.BillQuery;
import com.acpiot.wsd.admin.mvc.revenue.service.BillService;
import com.acpiot.wsd.admin.mvc.revenue.service.WxUserService;
import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.common.listener.event.BillAutoSettlementEvent;
import com.acpiot.wsd.common.listener.event.BillChangeEvent;
import com.acpiot.wsd.common.listener.event.BillReCalculateEvent;
import com.acpiot.wsd.common.listener.event.BillSavedEvent;
import com.acpiot.wsd.common.provider.BalanceCalculateService;
import com.acpiot.wsd.common.provider.OweCheckProvider;
import com.acpiot.wsd.common.provider.OweCheckService;
import com.acpiot.wsd.common.security.util.SecurityContextUtils;
import com.acpiot.wsd.core.sms.adapter.SmsServiceAdapter;
import com.acpiot.wsd.core.wxmp.adapter.WxMpMsgAdapter;
import com.acpiot.wsd.data.archive.entity.*;
import com.acpiot.wsd.data.archive.enums.MeterType;
import com.acpiot.wsd.data.archive.repository.CommunityRepository;
import com.acpiot.wsd.data.archive.repository.MachineWaterMeterRepository;
import com.acpiot.wsd.data.archive.repository.MeasuringPointRepository;
import com.acpiot.wsd.data.archive.repository.WaterMeterRepository;
import com.acpiot.wsd.data.monitor.entity.QDayFreezeData;
import com.acpiot.wsd.data.revenue.bean.ChargeDetail;
import com.acpiot.wsd.data.revenue.bean.WaterPrice;
import com.acpiot.wsd.data.revenue.entity.*;
import com.acpiot.wsd.data.revenue.enums.ApplyStatus;
import com.acpiot.wsd.data.revenue.repository.*;
import com.acpiot.wsd.miniprogram.res.model.WaterPriceDetail;
import com.google.common.collect.Lists;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.Filter;
import pers.mx.jupiter.jpa.PagingQueryParams;

import javax.persistence.criteria.Fetch;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cn.hutool.core.date.DateField.HOUR_OF_DAY;
import static cn.hutool.core.util.ObjectUtil.defaultIfNull;
import static com.acpiot.wsd.common.security.util.SecurityContextUtils.getGlobalFilter;
import static com.acpiot.wsd.common.security.util.SecurityContextUtils.getRegionCommunityFilter;

/**
 * Created by moxin on 2020-03-17-0017
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class BillServiceImpl implements BillService {

    private final BillRepository billRepository;
    private final WaterMeterRepository waterMeterRepository;
    private final MachineWaterMeterRepository machineWaterMeterRepository;
    private final CommunityRepository communityRepository;
    private final JPAQueryFactory jpaQueryFactory;
    private final PayIncomeRepository payIncomeRepository;
    private final RevenueConfigRepository revenueConfigRepository;
    private final ApplicationContext applicationContext;
    private final CommunityService communityService;
    private final BalanceCalculateService balanceCalculateService;
    private final WxUserService wxUserService;
    private final LateFeesRepository lateFeesRepository;
    private final ApplyBillVerificationRepository applyBillVerificationRepository;
    private final SettlementConfigRepository settlementConfigRepository;
    private final SmsServiceAdapter smsServiceAdapter;
    private final WxMpMsgAdapter wxMpMsgAdapter;
    private final MeasuringPointRepository measuringPointRepository;
    private final OweCheckProvider oweCheckProvider;

    @Value("#{'${application.onlyWxMapMsg}'.split(',')}")
    private List<String> onlyWxMapMsg;
    @Value("#{'${application.billUsedTonToInteger}'.split(',')}")
    private List<String> billUsedTonToInteger;

    @Override
    @Transactional(readOnly = true)
    public Page<Bill> pagedBill(BillQuery params) {
        boolean continueQuery = getRegionCommunityFilter(params, String.format("%s.%s.%s", Bill_.MEASURING_POINT, MeasuringPoint_.COMMUNITY, Community_.ID));
        if (!continueQuery) {
            return new PageImpl<>(Lists.newArrayList());
        }
        params.fetch(String.format("%s.%s", Bill_.MEASURING_POINT, MeasuringPoint_.COMMUNITY));
        params.fetch(String.format("%s.%s", Bill_.MEASURING_POINT, MeasuringPoint_.LOWER_COMMUNITY));
        params.fetch(Bill_.CUSTOMER);
        // 加上过滤条件
        getGlobalFilter(
                String.format("%s.%s", Bill_.MEASURING_POINT, MeasuringPoint_.COMPANY_ID),
                String.format("%s.%s.%s", Bill_.MEASURING_POINT, MeasuringPoint_.COMMUNITY, Community_.ID)
        ).ifPresent(params::and);
        return billRepository.findAll(params);
    }

    /**
     * 获取公司下计算账单的小区
     *
     * @param billDate
     * @param companyId
     * @return
     */
    @Override
    @Transactional(readOnly = true)
    public List<Community> getCalcBillCommunities(Date billDate, String companyId) {
        // 判断当前公司是否开启营收能力
        boolean enableRevenueAbility = revenueConfigRepository.isEnableRevenueAbility(companyId);
        if (!enableRevenueAbility) {
            log.info("对应公司未开启营收能力，忽略执行账单生成任务，companyId={}", companyId);
            return List.of();
        }
        // 排除未设置水价以及未启用结算的小区
        Filter[] filters = {
                Filter.isNotNull(Community_.ENABLED_BILL_DATE),
                Filter.lt(Community_.ENABLED_BILL_DATE, billDate),
                Filter.eq(Community_.COMPANY_ID, companyId),
                Filter.isNull(Community_.PARENT)
        };
        return communityRepository.findAll(filters, null, Community_.WATER_CLASSIFICATION);
    }

    /**
     * 获取小区下计算账单的水表（计量点）
     *
     * @param community
     * @return
     */
    @Override
    @Transactional(readOnly = true)
    public List<WaterMeter> getCalcBillWaterMeters(Community community) {
        SettlementConfig settlementConfig = settlementConfigRepository.findByCompanyId(community.getCompanyId());
        return waterMeterRepository.findAll((root, query, cb) -> {
            Fetch<Object, Object> fetch = root.fetch(WaterMeter_.MEASURING_POINT, JoinType.LEFT);
            fetch.fetch(MeasuringPoint_.WATER_CLASSIFICATION, JoinType.LEFT);
            fetch.fetch(MeasuringPoint_.CUSTOMER, JoinType.LEFT);
            Predicate predicate = cb.equal(root.get(WaterMeter_.community).get(Community_.id), community.getId());
            predicate = cb.and(predicate, cb.equal(root.get(WaterMeter_.MEASURING_POINT).get(MeasuringPoint_.CANCELLATION), false));
            predicate = cb.and(predicate, cb.isNotNull(root.get(WaterMeter_.measuringPoint).get(MeasuringPoint_.accountNo)));
            if (settlementConfig.isArtificialMeterReadingSettlement()) {
                predicate = cb.and(predicate, cb.notEqual(root.get(WaterMeter_.MODEL).get(MeterModel_.METER_TYPE), MeterType.MACHINE));
            }
            Predicate predicate1 = cb.isNotNull(root.get(WaterMeter_.measuringPoint).get(MeasuringPoint_.waterClassification));
            Predicate predicate2 = cb.isNotNull(root.get(WaterMeter_.community).get(Community_.waterClassification));
            return cb.and(predicate, cb.or(predicate1, predicate2));
        });
    }

    /**
     * 计算水表的账单
     *
     * @param waterMeter
     * @param currentBillDate
     * @param community
     */
    @Override
    public Bill calcBill(Date currentBillDate, WaterMeter waterMeter, Community community) {
        // 获取上次账单日期
        Date lastBillDate = waterMeter.getLastBillDate();
        // 如果当前要结算的日期以及之后有结算过了，则不再结算
        if (lastBillDate != null && currentBillDate.compareTo(lastBillDate) <= 0) {
            log.warn("此日期：{}内已经结算过账单，不能重复结算，表号：{}", lastBillDate, waterMeter.getCode());
            return null;
        }
        // 获取启用结算日期，未到启用结算日期及以后不结算
        Date enabledBillDate = community.getEnabledBillDate();
        if (currentBillDate.before(enabledBillDate)) {
            log.warn("当前日期：{}未到启用结算日期：{}，不能结算，表号：{}", currentBillDate, enabledBillDate, waterMeter.getCode());
            return null;
        }
        // 获取对应计量点对象
        MeasuringPoint measuringPoint = waterMeter.getMeasuringPoint();

        // 上次结算读数
        BigDecimal lastValue;
        boolean isUseEnabledBillDate = false;
        QDayFreezeData dayFreezeData = QDayFreezeData.dayFreezeData;
        // 优先取计量点中的水价
        WaterClassification waterClassification = measuringPoint.getWaterClassification();
        WaterPrice realWaterPrice = waterClassification != null ? waterClassification.getWaterPrice() : community.getWaterClassification().getWaterPrice();
        if (lastBillDate == null || enabledBillDate.after(lastBillDate)) {
            // 如果水表上次计算账单日期为null，或者启用计算账单日期在上次账单计算之后，则设置上次计算账单日期为启用账单计算日期
            lastBillDate = enabledBillDate;
            isUseEnabledBillDate = true;
            // 获取前次结算日抄表读数作为结算读数
            lastValue = billRepository.getMeteringValue(lastBillDate, waterMeter, false);
        } else {
            // 这里说明该水表有进行过账单计算，直接使用上次结算读数
            lastValue = waterMeter.getLastValue() != null ? waterMeter.getLastValue() : waterMeter.getStartValue();
        }

        // 本次结算抄表读数
        BigDecimal value = billRepository.getMeteringValue(currentBillDate, waterMeter, true);

        // 统计账单周期范围内的换表前的用水量，此处需要使用换表前总表的总流量计算换表前用水量，因为下方专门计算了所有子表的用水量进行了减除，此处不能使用总表的用水量计算否则会重复减除子表用水量
        BigDecimal preUsedTon = jpaQueryFactory.select(dayFreezeData.dayFlow.sum())
                .from(dayFreezeData)
                .where(dayFreezeData.measuringPoint.eq(measuringPoint),
                        dayFreezeData.waterMeter.ne(waterMeter).or(dayFreezeData.waterMeter.id.isNull()),
                        (isUseEnabledBillDate ? dayFreezeData.freezeDate.goe(lastBillDate) : dayFreezeData.freezeDate.gt(lastBillDate))
                                .and(dayFreezeData.freezeDate.loe(currentBillDate)))
                .fetchFirst();
        preUsedTon = preUsedTon != null ? preUsedTon : BigDecimal.ZERO;

        // 查询所有子表用水量
        BigDecimal childValues = balanceCalculateService.calculateChildMeterValues(waterMeter.getId(), currentBillDate);

        // 判断是否需要取整
        if (billUsedTonToInteger.contains(measuringPoint.getCompanyId())) {
            lastValue = lastValue.setScale(0, RoundingMode.HALF_UP);
            value = value.setScale(0, RoundingMode.HALF_UP);
            preUsedTon = preUsedTon.setScale(0, RoundingMode.HALF_UP);
            childValues = childValues.setScale(0, RoundingMode.HALF_UP);
        }

        // 创建账单
        Bill bill = new Bill();
        bill.setMeasuringPoint(measuringPoint);
        bill.setCustomer(measuringPoint.getCustomer());
        bill.setWaterPrice(realWaterPrice);
        bill.setStartDate(isUseEnabledBillDate ? lastBillDate : DateUtil.offsetDay(lastBillDate, 1));
        bill.setEndDate(currentBillDate);
        bill.setLastValue(lastValue);
        bill.setValue(value);
        bill.setPreUsedTon(preUsedTon);
        bill.setChildUsedTon(childValues);
        bill.setRealBalance(measuringPoint.getRealBalance());
        // 计算账单金额
        calculateBillOriginAmount(bill, measuringPoint, community);

        // 设置滞纳金计算日期
        RevenueConfig revenueConfig = revenueConfigRepository.findOne((root, query, cb)
                -> cb.equal(root.get(RevenueConfig_.companyId), community.getCompanyId())).orElse(null);
        if (revenueConfig != null) {
            // 判断其公司配置是否为开启了滞纳金，开启了滞纳金需要计算滞纳金下次收取时间
            if (revenueConfig.isEnableLateFees()) {
                // 若账单状态为未支付且金额大于0，需要根据配置计算滞纳金下次收取时间
                if (bill.getBillStatus() == Bill.BillStatus.UNPAID && bill.getOriginAmount().compareTo(BigDecimal.ZERO) > 0) {
                    Integer lateDays = revenueConfig.getLateDays();
                    Date endDate = bill.getEndDate();
                    // 获取下次收取滞纳金时间
                    DateTime lateFeeNextDate = DateUtil.offsetDay(endDate, lateDays);
                    bill.setLateFeeNextDate(lateFeeNextDate);
                }
            }
        }

        // 保存账单
        billRepository.save(bill);
        // 更新水表中的冗余字段
        waterMeterRepository.updateLastBillDateAndLastValueById(currentBillDate, value, waterMeter.getId());

        // 如果水表类型为机械水表，则重置本次周期是否已抄表字段
        if (waterMeter instanceof MachineWaterMeter) {
            Optional<MachineWaterMeter> optional = machineWaterMeterRepository.findMachineWaterMeterByMeasuringPoint_Id(measuringPoint.getId());
            optional.ifPresent(machineWaterMeter -> {
                // 判断公司是否设置立即结算账单，为true则不需要在账单中修改“已抄表”标记，在公司生成账单的定时任务中统一设置，为false则需要在账单中重置标记
                SettlementConfig settlementConfig = settlementConfigRepository.findByCompanyId(waterMeter.getCompanyId());
                if(settlementConfig == null) {
                    throw new BusinessException("当前表计所属公司没有配置结算周期");
                }
                boolean artificialMeterReadingSettlement = settlementConfig.isArtificialMeterReadingSettlement();
                if (!artificialMeterReadingSettlement) {
                    machineWaterMeter.setMetering(false);
                }
                machineWaterMeter.setNotify(false);
            });
        }

        // 发布账单保存事件
        applicationContext.publishEvent(new BillSavedEvent(this, bill, waterMeter));

        // 发布账单自动结算事件，这里不管账单是否需要支付，都需要发布此事件，因为需要在创建账单后冻结本期的预存和欠费
        log.info("水表账单已创建，进入自动结算流程，公司ID：{}， 小区：{}， 水表：{}", waterMeter.getCompanyId(), community.getName(), waterMeter.getCode());
        applicationContext.publishEvent(new BillAutoSettlementEvent(this, measuringPoint.getAccountNo(), List.of(Objects.requireNonNull(bill.getId())), bill.getId()));

        return bill;
    }

    /**
     * 计算账单金额、污水费账单金额、正常水费账单金额
     *
     * @param bill
     * @param measuringPoint
     * @param community
     */
    private void calculateBillOriginAmount(Bill bill, MeasuringPoint measuringPoint, Community community) {
        // 计算账单金额、污水费账单金额、正常水费账单金额
        bill.setAmount(bill.calcAmount());
        bill.setWasteWaterFee(bill.calcWasteAmount());
        bill.setNormalWaterFee(bill.getAmount().subtract(bill.getWasteWaterFee()));

        // 提取起始读数和本次读数
        BigDecimal lastValue = bill.getLastValue();
        BigDecimal value = bill.getValue();

        // 如果起始收费读数不为空，则需要应用到计费中
        if (measuringPoint.getChargeValue() != null) {
            // 当前读数小于收费读数，则本次账单不计费
            if (value.compareTo(measuringPoint.getChargeValue()) <= 0) {
                bill.setRemark(String.format("抄表读数:%s小于等于计量点起始收费读数:%s", value, measuringPoint.getChargeValue()));
                bill.setBillStatus(Bill.BillStatus.UNWANTED_PAID);
                bill.setAmount(BigDecimal.ZERO);
                bill.setWasteWaterFee(BigDecimal.ZERO);
                bill.setNormalWaterFee(BigDecimal.ZERO);
            }
            // 起始收费读数大于上次结算读数，则需要基于收费读数计算用水量
            else if (measuringPoint.getChargeValue().compareTo(lastValue) > 0) {
                bill.setLastValue(measuringPoint.getChargeValue());
                bill.setAmount(bill.calcAmount());
                bill.setWasteWaterFee(bill.calcWasteAmount());
                bill.setNormalWaterFee(bill.getAmount().subtract(bill.getWasteWaterFee()));
                bill.setLastValue(lastValue);
                bill.setRemark(String.format("上次结算读数:%s小于计量点起始收费读数:%s，实际使用起始收费读数计算用水量", lastValue, measuringPoint.getChargeValue()));
            }
        }

        // 若小区设置了最低消费限额，且账单金额小于最低消费限额，将账单金额设置为最低消费限额
        if (community.getMinChargeLimit() != null && (community.getMinChargeLimit().compareTo(bill.getAmount()) > 0)) {
            String remark = bill.getRemark() != null ? bill.getRemark() + "；" : "";
            bill.setRemark(remark + String.format("账单实际金额：%s，因低于最低消费限额：%s，账单按最低消费限额计费", bill.getAmount(), community.getMinChargeLimit()));
            bill.setBillStatus(Bill.BillStatus.UNPAID);
            bill.setAmount(community.getMinChargeLimit());
        }

        // 若小区设置了服务费，需要追加服务费
        if (community.getServiceFee() != null && community.getServiceFee().compareTo(BigDecimal.ZERO) > 0) {
            String remark = bill.getRemark() != null ? bill.getRemark() + "；" : "";
            bill.setRemark(remark + String.format("账单水费实际金额：%s，服务费：%s", bill.getAmount(), community.getServiceFee()));
            bill.setAmount(bill.getAmount().add(community.getServiceFee()));
        }

        // 如果账单金额为0，则状态置为无需支付
        boolean isInvalid = bill.getAmount().compareTo(BigDecimal.ZERO) <= 0;
        if (isInvalid) {
            bill.setBillStatus(Bill.BillStatus.UNWANTED_PAID);
        }
        // 初始状态时账单金额固定为账单原始金额
        bill.setOriginAmount(bill.getAmount());
    }

    @Override
    public void calcBillWithPay(Date currentBillDate, WaterMeter waterMeter, Community community, boolean pay) {
        Bill bill = calcBill(currentBillDate, waterMeter, community);
        if (bill != null) {
            boolean enableRevenueAbility = revenueConfigRepository.isEnableRevenueAbility(waterMeter.getCompanyId());
            if (!pay || !enableRevenueAbility) {
                bill.setBillStatus(Bill.BillStatus.UNWANTED_PAID);
                bill.setLateFeeNextDate(null);
            }
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ExcelBillDto> getExportBillDtoList(BillQuery params) {
        boolean continueQuery = getRegionCommunityFilter(params, String.format("%s.%s.%s", Bill_.MEASURING_POINT, MeasuringPoint_.COMMUNITY, Community_.ID));
        if (!continueQuery) {
            return Lists.newArrayList();
        }
        // 加上过滤条件
        getGlobalFilter(
                String.format("%s.%s", Bill_.MEASURING_POINT, MeasuringPoint_.COMPANY_ID),
                String.format("%s.%s.%s", Bill_.MEASURING_POINT, MeasuringPoint_.COMMUNITY, Community_.ID)
        ).ifPresent(params::and);
        params.fetch(String.format("%s.%s", Bill_.MEASURING_POINT, MeasuringPoint_.COMMUNITY));
        params.fetch(String.format("%s.%s", Bill_.MEASURING_POINT, MeasuringPoint_.LOWER_COMMUNITY));
        params.fetch(String.format("%s.%s", Bill_.MEASURING_POINT, MeasuringPoint_.CUSTOMER));
        params.setLimit(AdminConstant.EXPORT_LIMIT);
        Page<Bill> bills = billRepository.findAll(params);
        communityService.buildBuildingUnitName(bills.getContent());
        return bills.stream().map(ExcelBillDto::new).collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Bill findBillById(long billId) {
        // 增加公司过滤条件
        if (SecurityContextUtils.isCompanyUser()) {
            String[] companyIds = SecurityContextUtils.getCompanyIds();
            return billRepository.getBillByBillIdAndCompanyId(billId, companyIds).orElseThrow(() -> new BusinessException("该账单不存在"));
        }
        return billRepository.findOne(new Filter[] {Filter.eq(Bill_.ID, billId)}, Bill_.MEASURING_POINT).orElseThrow(() -> new BusinessException("该账单不存在"));
    }

    @Override
    public void cancelBill(long billId) {
        Bill dbBill = this.findBillById(billId);
        switch (dbBill.getBillStatus()) {
            case UNPAID:
                dbBill.setBillStatus(Bill.BillStatus.CANCELED);
                dbBill.setLateFeeNextDate(null);
                // 撤销账单会影响到账单关联的计量点的可用余额，发布账单变更事件
                applicationContext.publishEvent(new BillChangeEvent(this, billId));
                break;
            case CANCELED:
                throw new BusinessException("账单状态为已撤销，不能重复撤销");
            default:
                throw new BusinessException("账单状态为已支付，不能撤销");
        }
    }

    @Override
    public void reduceBill(long billId, BigDecimal reduceTon) {
        // 查询待减免的账单，必须是已支付或未支付状态，减免的吨数必须是账单总用水吨数或小于账单总用水吨数
        Bill dbBill = billRepository.findOne((root, query, cb) -> {
            root.fetch(Bill_.MEASURING_POINT).fetch(MeasuringPoint_.COMMUNITY);
            return cb.equal(root.get(Bill_.id), billId);
        }).orElseThrow(() -> new BusinessException("当前账单不存在"));
        if (dbBill.getBillStatus() != Bill.BillStatus.UNPAID && dbBill.getBillStatus() != Bill.BillStatus.PAID) {
            throw new BusinessException("该账单已执行撤销/无需支付/核销操作，不能减免");
        }
        if (dbBill.getTotalUsedTon().add(dbBill.getReduce()).compareTo(reduceTon) < 0) {
            throw new BusinessException("减免吨数不能大于总用水吨数");
        }

        // 如果存在未过期的微信或支付宝的codeUrl，需要清除掉，下次扫码时重新生成新的codeUrl
        DateTime dateTime = DateTime.now();
        dateTime = dateTime.offsetNew(HOUR_OF_DAY, -2);
        List<PayIncome> dbPayIncomes = payIncomeRepository.findByBill_IdAndIncomeStatusAndCodeUrlTimeGreaterThanEqualAndCodeUrlIsNotNull(billId, PayIncome.IncomeStatus.UNPAID, dateTime);
        if (CollectionUtils.isNotEmpty(dbPayIncomes)) {
            dbPayIncomes.forEach(payIncome -> {
                payIncome.setCodeUrl(null);
                payIncome.setCodeUrlTime(null);
            });
        }

        // 重新计算账单金额之前，提取旧的账单金额和状态
        BigDecimal oldAmount = dbBill.getAmount();
        Bill.BillStatus billStatus = dbBill.getBillStatus();

        // 重设减免吨数后重新计算账单金额
        dbBill.setReduce(reduceTon);
        calculateBillOriginAmount(dbBill, dbBill.getMeasuringPoint(), dbBill.getMeasuringPoint().getCommunity());
        // 账单金额更新为减免后的金额加上已有滞纳金
        BigDecimal sumOfLateFeeAmount = lateFeesRepository.getSumOfLateFeeAmountByBillId(billId);
        if (sumOfLateFeeAmount != null && sumOfLateFeeAmount.compareTo(BigDecimal.ZERO) > 0) {
            dbBill.setAmount(dbBill.getOriginAmount().add(sumOfLateFeeAmount));
        }

        // 如果原账单已支付，则将旧的支付金额退回计量点，并将账单状态置为未支付状态重新触发账单结算
        if (billStatus == Bill.BillStatus.PAID) {
            MeasuringPoint measuringPoint = dbBill.getMeasuringPoint();
            measuringPoint.setBalance(measuringPoint.getBalance().add(oldAmount));
            if (dbBill.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                dbBill.setBillStatus(Bill.BillStatus.UNPAID);
            }
        }

        // 减免账单会影响到账单关联的计量点的可用余额，发布账单变更事件
        applicationContext.publishEvent(new BillChangeEvent(this, billId));
        // 发布账单扣减事件
        applicationContext.publishEvent(new BillAutoSettlementEvent(this, dbBill.getMeasuringPoint().getAccountNo(), List.of(billId), billId));
    }

    @Override
    @Transactional(readOnly = true)
    public Bill findBillByIdFetchMeasuringPointAndCommunityAndCustomer(Long billId) {
        return billRepository.findOne((root, query, cb) -> {
            root.fetch(Bill_.MEASURING_POINT).fetch(MeasuringPoint_.COMMUNITY);
            root.fetch(Bill_.CUSTOMER);
            return cb.equal(root.get(Bill_.id), billId);
        }).orElseThrow(() -> new BusinessException("该账单不存在"));
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getTotalBillAmount(BillQuery queryParams) {
        List<Filter> billFilters = queryParams.getAndFilters().stream().filter(predicate -> predicate.getValue() != null).collect(Collectors.toList());

        QBill bill = QBill.bill;
        QMeasuringPoint measuringPoint = bill.measuringPoint;
        QCustomer customer = measuringPoint.customer;
        QCommunity community = measuringPoint.community;

        List<com.querydsl.core.types.Predicate> predicates = new ArrayList<>();
        billFilters.forEach(filter -> {
            if (filter.getValue() != null) {
                switch (filter.getProperty()) {
                    case "measuringPoint.customer.name": {
                        predicates.add(customer.name.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.customer.mobile": {
                        predicates.add(customer.mobile.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.customer.idCard": {
                        predicates.add(customer.idCard.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.accountNo": {
                        predicates.add(measuringPoint.accountNo.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.name": {
                        predicates.add(measuringPoint.name.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.meterCode": {
                        predicates.add(measuringPoint.meterCode.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "billStatus": {
                        predicates.add(bill.billStatus.eq(Bill.BillStatus.valueOf(filter.getValue().toString())));
                        break;
                    }
                    case "measuringPoint.community.id": {
                        predicates.add(community.id.eq(Long.valueOf((String) filter.getValue())));
                        break;
                    }
                    case "measuringPoint.linkCode": {
                        predicates.add(measuringPoint.linkCode.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.lowerCommunity.layerCode" : {
                        predicates.add(measuringPoint.lowerCommunity.layerCode.like(filter.getValue() + "%"));
                    }
                }
            }
        });

        if (StrUtil.isNotBlank(queryParams.getBillPayTimeDatetimes())) {
            String billPayTimeDatetimes = queryParams.getBillPayTimeDatetimes();
            String[] range = billPayTimeDatetimes.split(" - ");
            DateTime endTime = DateUtil.parse(range[1], "yyyy-MM-dd HH:mm");
            endTime.setField(Calendar.SECOND, 59);
            endTime.setField(Calendar.MILLISECOND, 999);
            predicates.add(bill.payTime.between(DateUtil.parse(range[0], "yyyy-MM-dd HH:mm"), endTime));
        }
        if (StrUtil.isNotBlank(queryParams.getBillEndDatetimes())) {
            String billEndDatetimes = queryParams.getBillEndDatetimes();
            String[] range = billEndDatetimes.split(" - ");
            DateTime endTime = DateUtil.parse(range[1], "yyyy-MM-dd HH:mm");
            endTime.setField(Calendar.SECOND, 59);
            endTime.setField(Calendar.MILLISECOND, 999);
            predicates.add(bill.endDate.between(DateUtil.parse(range[0], "yyyy-MM-dd HH:mm"), endTime));
        }
        Boolean balance = queryParams.getBalance();
        if (balance != null) {
            if (balance) {
                predicates.add(bill.balance.gt(BigDecimal.ZERO));
            } else {
                predicates.add(bill.balance.eq(BigDecimal.ZERO));
            }
        }
        Boolean oweAmount = queryParams.getOweAmount();
        if (oweAmount != null) {
            if (oweAmount) {
                predicates.add(bill.oweAmount.gt(BigDecimal.ZERO));
            } else {
                predicates.add(bill.oweAmount.eq(BigDecimal.ZERO));
            }
        }
        Boolean hasLateFees = queryParams.getHasLateFees();
        if (hasLateFees != null && hasLateFees) {
            predicates.add(bill.lateFeeAmount.isNotNull());
        }

        // 添加默认条件
        getGlobalFilter(measuringPoint.companyId, community.id).ifPresent(predicates::addAll);

        return jpaQueryFactory.select(bill.amount.sum()).from(bill)
                .where(predicates.toArray(new com.querydsl.core.types.Predicate[0])).fetchOne();
    }

    @Override
    @Transactional(readOnly = true)
    public BillBalanceDto getBillBalanceDto(BillQuery queryParams) {
        BillBalanceDto billBalanceDto = new BillBalanceDto();
        if (StrUtil.isBlank(queryParams.getBillEndDatetimes())) {
            return billBalanceDto;
        }
        QBill bill = QBill.bill;
        QMeasuringPoint measuringPoint = bill.measuringPoint;
        QCustomer customer = measuringPoint.customer;
        QCommunity community = measuringPoint.community;
        List<Filter> billFilters = queryParams.getAndFilters().stream().filter(predicate -> predicate.getValue() != null).collect(Collectors.toList());
        List<com.querydsl.core.types.Predicate> predicates = new ArrayList<>();
        getGlobalFilter(measuringPoint.companyId, measuringPoint.community.id).ifPresent(predicates::addAll);
        billFilters.forEach(filter -> {
            if (filter.getValue() != null) {
                switch (filter.getProperty()) {
                    case "measuringPoint.customer.name": {
                        predicates.add(customer.name.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.customer.mobile": {
                        predicates.add(customer.mobile.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.customer.idCard": {
                        predicates.add(customer.idCard.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.accountNo": {
                        predicates.add(measuringPoint.accountNo.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.name": {
                        predicates.add(measuringPoint.name.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.meterCode": {
                        predicates.add(measuringPoint.meterCode.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "billStatus": {
                        predicates.add(bill.billStatus.eq(Bill.BillStatus.valueOf(filter.getValue().toString())));
                        break;
                    }
                    case "measuringPoint.community.id": {
                        predicates.add(community.id.eq(Long.valueOf((String) filter.getValue())));
                        break;
                    }
                    case "measuringPoint.linkCode": {
                        predicates.add(measuringPoint.linkCode.like("%" + filter.getValue() + "%"));
                        break;
                    }
                }
            }
        });

        String billEndDatetimes = queryParams.getBillEndDatetimes();
        String[] range = billEndDatetimes.split(" - ");
        DateTime endTime = DateUtil.parse(range[1], "yyyy-MM-dd HH:mm");
        endTime.setField(Calendar.SECOND, 59);
        endTime.setField(Calendar.MILLISECOND, 999);
        predicates.add(bill.endDate.between(DateUtil.parse(range[0], "yyyy-MM-dd HH:mm"), endTime));

        Boolean balance = queryParams.getBalance();
        if (balance != null) {
            if (balance) {
                predicates.add(bill.balance.gt(BigDecimal.ZERO));
            } else {
                predicates.add(bill.balance.eq(BigDecimal.ZERO));
            }
        }
        Boolean oweAmount = queryParams.getOweAmount();
        if (oweAmount != null) {
            if (oweAmount) {
                predicates.add(bill.oweAmount.gt(BigDecimal.ZERO));
            } else {
                predicates.add(bill.oweAmount.eq(BigDecimal.ZERO));
            }
        }
        Boolean hasLateFees = queryParams.getHasLateFees();
        if (hasLateFees != null && hasLateFees) {
            predicates.add(bill.lateFeeAmount.isNotNull());
        }

        // 添加默认条件
        getGlobalFilter(measuringPoint.companyId, community.id).ifPresent(predicates::addAll);

        Tuple tuple = jpaQueryFactory.select((bill.value.subtract(bill.lastValue)).sum(), bill.preUsedTon.sum(),
                        bill.reduce.sum(), bill.childUsedTon.sum()).from(bill)
                .where(predicates.toArray(new com.querydsl.core.types.Predicate[0])).fetchOne();
        if (tuple != null) {
            BigDecimal usedTon = defaultIfNull(tuple.get((bill.value.subtract(bill.lastValue)).sum()), BigDecimal.ZERO);
            BigDecimal preUsedTon = defaultIfNull(tuple.get(bill.preUsedTon.sum()), BigDecimal.ZERO);
            BigDecimal reduce = defaultIfNull(tuple.get(bill.reduce.sum()), BigDecimal.ZERO);
            BigDecimal childUsedTon = defaultIfNull(tuple.get(bill.childUsedTon.sum()), BigDecimal.ZERO);
            BigDecimal realUsedTon = usedTon.add(preUsedTon).subtract(reduce).subtract(childUsedTon);
            billBalanceDto.setTotalConsumption(realUsedTon);
        }

        // 账单日当天冻结的预存金额和欠费金额
        Tuple tupleAmount = jpaQueryFactory.select(bill.balance.sum(), bill.oweAmount.sum()).from(bill)
                .where(predicates.toArray(new com.querydsl.core.types.Predicate[0])).fetchOne();
        if (tupleAmount != null) {
            billBalanceDto.setBalance(tupleAmount.get(bill.balance.sum()));
            billBalanceDto.setOweAmount(tupleAmount.get(bill.oweAmount.sum()));
        }

        return billBalanceDto;
    }

    @Override
    public BigDecimal refreshAndReturnRealBalance(Long measuringPointId) {
        MeasuringPoint dbMeasuringPointBefore = measuringPointRepository.findById(measuringPointId).orElseThrow(() -> new BusinessException("该账单对应计量点不存在"));
        oweCheckProvider.executeCheck(dbMeasuringPointBefore.getAccountNo(), OweCheckService.CheckTiming.REFRESH_REAL_BALANCE_RECHECK);
        MeasuringPoint dbMeasuringPointAfter = measuringPointRepository.findById(measuringPointId).orElseThrow(() -> new BusinessException("该计量点不存在"));
        return dbMeasuringPointAfter.getRealBalance();
    }

    @Override
    public void calcLateFees() {
        List<RevenueConfig> enableLateFeesRevenueConfigs = revenueConfigRepository.findAll((root, query, cb) -> cb.and(cb.equal(root.get(RevenueConfig_.enabled), true), cb.equal(root.get(RevenueConfig_.enableLateFees), true)));
        if (CollUtil.isEmpty(enableLateFeesRevenueConfigs)) {
            return;
        }
        // 获取当前时间
        DateTime now = DateUtil.beginOfDay(new Date());
        Set<Long> billIds = new HashSet<>();
        enableLateFeesRevenueConfigs.forEach(revenueConfig -> {
            String companyId = revenueConfig.getCompanyId();
            // 滞纳金收取比例
            BigDecimal lateFeeRate = revenueConfig.getLateFeeRate();
            // 允许欠费天数
            Integer lateDays = revenueConfig.getLateDays();
            log.info("{}公司：滞纳金计算开始，滞纳金收取比例：{}%，允许欠费天数：{}", companyId, lateFeeRate, lateDays);
            // 获取所有需要计算滞纳金的账单，处理滞纳金
            List<Bill> needGenerateLateFeesBills = billRepository.findNeedGenerateLateFeesBills(companyId, now);

            needGenerateLateFeesBills.forEach(bill -> {
                BigDecimal originAmount = bill.getOriginAmount();
                Date periodStartDate = DateUtil.offsetDay(bill.getLateFeeNextDate(), -lateDays + 1);
                // 上次收取滞纳金的时间
                Date lateFeeLastDate = bill.getLateFeeLastDate();
                // 下次收取滞纳金的时间
                Date lateFeeNextDate = bill.getLateFeeNextDate();
                // 本次产生收取滞纳金总金额，方便日志打印
                BigDecimal thisTimeLateFeeAmount = BigDecimal.ZERO;
                // 需要循环，如果下次收取的时间还在当天及当天之前，需要继续生成
                while (!lateFeeNextDate.after(now)) {
                    // 计算并保存本次产生滞纳金
                    BigDecimal lateFeeAmount = originAmount.multiply(lateFeeRate).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                    LateFees lateFee = new LateFees();
                    lateFee.setBill(bill);
                    lateFee.setLateFeeAmount(lateFeeAmount);
                    lateFee.setLateFeeRate(lateFeeRate);
                    lateFee.setStartDate(periodStartDate);
                    lateFee.setEndDate(lateFeeNextDate);
                    lateFeesRepository.save(lateFee);
                    thisTimeLateFeeAmount = thisTimeLateFeeAmount.add(lateFee.getLateFeeAmount());
                    lateFeeLastDate = lateFeeNextDate;
                    // 更新账期起始时间
                    periodStartDate = DateUtil.offsetDay(lateFeeNextDate, 1);
                    // 更新下次收取滞纳金的时间
                    lateFeeNextDate = DateUtil.offsetDay(lateFeeNextDate, lateDays);
                }
                // 更新上次滞纳金收取日期
                bill.setLateFeeLastDate(lateFeeLastDate);
                // 更新下次滞纳金收取日期
                bill.setLateFeeNextDate(lateFeeNextDate);
                // 更新账单下滞纳金金额总和和总金额
                BigDecimal sumOfLateFeeAmount = lateFeesRepository.getSumOfLateFeeAmountByBillId(bill.getId());
                bill.setLateFeeAmount(sumOfLateFeeAmount);
                BigDecimal billOldAmount = bill.getAmount();
                bill.setAmount(bill.getOriginAmount().add(sumOfLateFeeAmount));
                log.info("{}公司，滞纳金收取对应账单ID：{}， 原账单金额：{}，滞纳金累计总和：{}，本次产生滞纳金金额：{}，下次滞纳金收取日期：{}",
                        companyId, bill.getId(), bill.getOriginAmount(), bill.getLateFeeAmount(), thisTimeLateFeeAmount, DateUtil.format(lateFeeNextDate, "yyyy-MM-dd"));
                // 只有账单金额发生变更，才去发送账单变化事件
                if (billOldAmount.compareTo(bill.getAmount()) != 0) {
                    billIds.add(bill.getId());
                }
            });
            log.info("公司：{}滞纳金计算结束", companyId);
        });

        if (!billIds.isEmpty()) {
            applicationContext.publishEvent(new BillChangeEvent(this, new ArrayList<>(billIds)));
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Page<LateFees> pageLateFees(PagingQueryParams<LateFees> params) {
        if (params.getAndFilters().stream().noneMatch(param -> param.getProperty().equals("bill.id"))) {
            throw new BusinessException("查询滞纳金必须指定账单id！");
        }
        return lateFeesRepository.findAll(params);
    }

    @Override
    public void applyVerifyBill(long billId, String reason) {
        Bill dbBill = billRepository.findById(billId).orElseThrow(() -> new BusinessException("未找到账单记录"));
        if (StrUtil.isBlank(reason)) {
            throw new BusinessException("申请原因不能为空！");
        }
        // 判断账单状态，账单状态为未支付，才能申请核销
        if (dbBill.getBillStatus() != Bill.BillStatus.UNPAID) {
            throw new BusinessException("当前账单记录不处于未支付状态，不允许申请核销！");
        }
        // 判断是否已经在核销中了
        if (dbBill.getVerifyApplyStatus() == ApplyStatus.WAIT_AUDIT) {
            throw new BusinessException("当前账单记录已在核销审核中，无需重复提交申请！");
        }
        // 保存申请记录
        ApplyBillVerification applyBillVerification = new ApplyBillVerification();
        applyBillVerification.setVerifyType(ApplyBillVerification.VerifyType.CHARGE_LESS);
        applyBillVerification.setBill(dbBill);
        applyBillVerification.setReason(reason);
        applyBillVerification.setCompanyId(dbBill.getMeasuringPoint().getCompanyId());
        applyBillVerificationRepository.save(applyBillVerification);
        // 更新账单核销状态
        dbBill.setVerifyApplyStatus(ApplyStatus.WAIT_AUDIT);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ApplyBillVerification> pageApplyBillVerification(PagingQueryParams<ApplyBillVerification> params) {
        if (params.getAndFilters().stream().noneMatch(param -> param.getProperty().equals("bill.id"))) {
            throw new BusinessException("查询核销记录必须指定账单id！");
        }
        return applyBillVerificationRepository.findAll(params);
    }

    @Override
    public void machineMeterDelaySendBillNotify() {
        // 查询所有机械表生成账单延迟天数不为空的公司
        List<SettlementConfig> settlementConfigs = settlementConfigRepository.findByMachineMeterDelayIsNotNull();
        if (CollUtil.isEmpty(settlementConfigs)) {
            return;
        }
        settlementConfigs.forEach(settlementConfig -> {
            int machineMeterDelay = settlementConfig.getMachineMeterDelay().intValue();
            // 查询出当前公司的机械表，并且是抄表时间为machineMeterDelay天前的
            String companyId = settlementConfig.getCompanyId();
            DateTime dateTime = DateUtil.endOfDay(DateUtil.offsetDay(new Date(), -machineMeterDelay));
            List<MachineWaterMeter> waterMeters = machineWaterMeterRepository.findAll((root, query, cb) -> {
                Fetch<Object, Object> fetch = root.fetch(MachineWaterMeter_.MEASURING_POINT, JoinType.LEFT);
                fetch.fetch(MeasuringPoint_.WATER_CLASSIFICATION, JoinType.LEFT);
                fetch.fetch(MeasuringPoint_.CUSTOMER, JoinType.LEFT);
                Predicate predicate = cb.equal(root.get(MachineWaterMeter_.companyId), companyId);
                predicate = cb.and(predicate, cb.equal(root.get(MachineWaterMeter_.MEASURING_POINT).get(MeasuringPoint_.CANCELLATION), false));
                predicate = cb.and(predicate, cb.isNotNull(root.get(MachineWaterMeter_.measuringPoint).get(MeasuringPoint_.accountNo)));
                // 本期已抄表
                predicate = cb.and(predicate, cb.equal(root.get(MachineWaterMeter_.metering), true));
                // 本期未通知
                predicate = cb.and(predicate, cb.equal(root.get(MachineWaterMeter_.notify), false));
                // 如果计量点不欠费，则不发送账单通知
                predicate = cb.and(predicate, cb.lessThan(root.get(MachineWaterMeter_.MEASURING_POINT).get(MeasuringPoint_.REAL_BALANCE), BigDecimal.ZERO));
                // 抄表时间+machineMeterDelay天 < 当前时间
                predicate = cb.and(predicate, cb.lessThanOrEqualTo(root.get(MachineWaterMeter_.latestData).get(LatestData_.meteringTime), dateTime));
                Predicate predicate1 = cb.isNotNull(root.get(MachineWaterMeter_.measuringPoint).get(MeasuringPoint_.waterClassification));
                Predicate predicate2 = cb.isNotNull(root.get(MachineWaterMeter_.community).get(Community_.waterClassification));
                return cb.and(predicate, cb.or(predicate1, predicate2));
            });
            if (CollUtil.isEmpty(waterMeters)) {
                return;
            }
            // 发送通知
            waterMeters.forEach(waterMeter -> {
                MeasuringPoint measuringPoint = waterMeter.getMeasuringPoint();
                // 查询绑定的openid，只查询关注了公众号的微信用户
                List<String> openIds = wxUserService.findOpenidByMeasuringPointId(measuringPoint.getId());
                // 如果不存在openid，直接发送短信
                if (CollUtil.isEmpty(openIds)) {
                    // 发送短信通知
                    smsServiceAdapter.billPaymentRemind(measuringPoint.getCustomer(), null, waterMeter.getId(), measuringPoint.getAccountNo());
                    // 更新机械表本期通知状态
                    waterMeter.setNotify(true);
                    return;
                }
                boolean success = wxMpMsgAdapter.billPaymentRemind(openIds, null, waterMeter.getId());
                // 如果微信模板消息发送不成功，或者当前公司不在onlyWxMapMsg内，则继续发送短信
                if (!success || !onlyWxMapMsg.contains(companyId)) {
                    // 调用阿里云短信服务，发送缴费成功短信
                    smsServiceAdapter.billPaymentRemind(measuringPoint.getCustomer(), null, waterMeter.getId(), measuringPoint.getAccountNo());
                }
                // 更新机械表本期通知状态
                waterMeter.setNotify(true);
            });
        });
    }

    @Override
    @Transactional(readOnly = true)
    public List<WaterPriceDetail.WaterPriceItem> getBillWaterPriceDetail(Long billId) {
        Bill bill = billRepository.findById(billId).orElseThrow(() -> new BusinessException("当前账单不存在"));
        BigDecimal totalUsedTon = bill.getTotalUsedTon();
        WaterPrice waterPrice = bill.getWaterPrice();
        // 计算明细
        List<BigDecimal> ladders = IntStream.of(waterPrice.getLadders()).mapToObj(BigDecimal::valueOf).collect(Collectors.toList());
        ladders.add(totalUsedTon);
        Collections.sort(ladders);
        int pos = ladders.lastIndexOf(totalUsedTon);
        // 费用详情
        List<WaterPriceDetail.WaterPriceItem> waterPriceItemList = new ArrayList<>();
        List<ChargeDetail> chargeDetails = waterPrice.getChargeDetails();
        for (ChargeDetail detail : chargeDetails) {
            // 获取当前收费项目价格明细
            BigDecimal[] ladderPrices = detail.getLadderCharges();
            // 计算当前收费项下每个阶梯的用水量和金额
            for (int i = pos; i > 0; i--) {
                // 构建当前阶梯费用明细
                WaterPriceDetail.WaterPriceItem waterPriceItem = buildWaterPriceItem(detail, ladderPrices, i - 1, waterPrice.getLadders().length == 1);
                // 当前阶梯内用水量
                waterPriceItem.setConsumption(ladders.get(i).subtract(ladders.get(i - 1)));
                // 当前阶梯内用水金额
                waterPriceItem.setAmount(waterPriceItem.getConsumption().multiply(ladderPrices[i - 1]));
                // 添加费用明细
                waterPriceItemList.add(waterPriceItem);
            }
        }
        waterPriceItemList.sort(Comparator.comparing(WaterPriceDetail.WaterPriceItem::getItemName));
        return waterPriceItemList;
    }

    @Override
    public void billPaymentRemind(Long billId) {
        Bill bill = billRepository.findById(billId).orElseThrow(() -> new BusinessException("当前账单不存在"));
        Bill.BillStatus billStatus = bill.getBillStatus();
        if (billStatus != Bill.BillStatus.UNPAID) {
            throw new BusinessException("当前账单不处于未缴费状态，无法发送短信");
        }
        MeasuringPoint measuringPoint = bill.getMeasuringPoint();
        WaterMeter waterMeter = waterMeterRepository.findByMeasuringPoint_Id(measuringPoint.getId());
        smsServiceAdapter.activeBillPaymentRemind(measuringPoint.getCustomer(), billId, waterMeter.getId());
    }

    private WaterPriceDetail.WaterPriceItem buildWaterPriceItem(ChargeDetail detail, BigDecimal[] ladderCharges, int index, boolean singlePrice) {
        // 每个费用项目的每个阶梯均要单独计算
        WaterPriceDetail.WaterPriceItem waterPriceItem = new WaterPriceDetail.WaterPriceItem();
        // 设置当前第几阶
        waterPriceItem.setItemName(detail.getName() + (!singlePrice ? "(" + (index + 1) + "阶)" : ""));
        // 设置当前阶梯价格
        waterPriceItem.setItemPrice(ladderCharges[index].toString());
        // 返回当前明细
        return waterPriceItem;
    }

    @Override
    public void reCalculateBill(Long billId) {
        // 查询账单，判断计算条件
        Bill bill = billRepository.findById(billId).orElseThrow(() -> new BusinessException("当前账单不存在"));
        Bill.BillStatus billStatus = bill.getBillStatus();
        if (billStatus != Bill.BillStatus.UNPAID && billStatus != Bill.BillStatus.PAID) {
            throw new BusinessException("当前账单不支持重新计算，仅支持（未缴费、已缴费）");
        }

        // 获取最新水价
        MeasuringPoint measuringPoint = bill.getMeasuringPoint();
        WaterClassification waterClassification = measuringPoint.getWaterClassification();
        if (waterClassification == null) {
            waterClassification = measuringPoint.getCommunity().getWaterClassification();
        }
        if (waterClassification == null) {
            throw new BusinessException("当前水表未配置水价信息，无法计算");
        }
        WaterPrice waterPrice = waterClassification.getWaterPrice();

        // 获取重新计算之前的账单金额
        BigDecimal oldAmount = bill.getAmount();
        // 设置新的水价
        bill.setWaterPrice(waterPrice);
        // 基于最新水价重新计算账单金额
        calculateBillOriginAmount(bill, measuringPoint, measuringPoint.getCommunity());

        // 如果账单处于已缴费状态，则需要在重新计算完成后，调整为未缴费状态，将原来的账单金额加回余额，触发新的账单结算事件
        if (billStatus == Bill.BillStatus.PAID) {
            bill.setBillStatus(Bill.BillStatus.UNPAID);
            measuringPoint.setBalance(measuringPoint.getBalance().add(oldAmount));
        }

        // 重新计算账单后，账单状态已置为未缴费，需要根据新的账单金额触发账单结算事件，决定是否能够支付
        List<Long> billIds = List.of(Objects.requireNonNull(bill.getId()));
        BillAutoSettlementEvent event = new BillAutoSettlementEvent(this, measuringPoint.getAccountNo(), billIds, bill.getId());
        applicationContext.publishEvent(event);

        // 账单重新计算后，机械水表需要触发统计数据重新计算
        applicationContext.publishEvent(new BillReCalculateEvent(this, billId));
    }
}
