package com.acpiot.wsd.admin.mvc.archive.service;

import com.acpiot.wsd.admin.mvc.archive.dto.FastPackDto;
import com.acpiot.wsd.data.archive.entity.PackBox;
import com.acpiot.wsd.data.archive.entity.WaterMeterPackBox;
import org.springframework.data.domain.Page;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.util.List;
import java.util.Optional;

/**
 * Created by moxin on 2019-11-25-0025
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface PackBoxService {

    Page<PackBox> page(PagingQueryParams<PackBox> params);

    void addPackBox(PackBox packBox);

    Optional<PackBox> getPackBoxById(long id);

    void updatePackBox(PackBox packBox);

    void removePackBox(long id);

    List<WaterMeterPackBox> getBindWaterMeters(long packBoxId);

    void deleteWaterMeterPackBox(long waterMeterPackBoxId);

    void shipment(long packBoxId);

    void fastPack(FastPackDto fastPackDto);
}
