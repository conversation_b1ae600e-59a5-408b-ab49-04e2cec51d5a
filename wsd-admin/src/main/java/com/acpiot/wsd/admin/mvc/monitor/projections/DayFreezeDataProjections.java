package com.acpiot.wsd.admin.mvc.monitor.projections;

import com.acpiot.wsd.admin.mvc.common.projections.BaseProjection;
import com.acpiot.wsd.common.serializer.CompanyId;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by moxin on 2019-12-09-0009
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface DayFreezeDataProjections {

    interface MeasuringPoint extends BaseProjection {
        String getName();

        String getAccountNo();

        String getHouseHolder();

        String getMobile();

        @CompanyId
        String getCompanyId();
    }

    interface Paged extends BaseProjection {

        MeasuringPoint getMeasuringPoint();

        String getMeterCode();

        BigDecimal getValue();

        BigDecimal getConsumption();

        Date getFreezeDate();

        Date getLastModifiedDate();
    }

}
