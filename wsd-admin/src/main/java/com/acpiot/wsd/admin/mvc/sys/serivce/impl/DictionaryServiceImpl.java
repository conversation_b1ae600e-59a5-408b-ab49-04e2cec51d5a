package com.acpiot.wsd.admin.mvc.sys.serivce.impl;

import com.acpiot.wsd.admin.config.properties.WebsiteProperties;
import com.acpiot.wsd.admin.mvc.settings.dto.WebsiteParams;
import com.acpiot.wsd.admin.mvc.sys.serivce.DictionaryService;
import com.acpiot.wsd.data.sys.entity.Dictionary;
import com.acpiot.wsd.data.sys.entity.Dictionary_;
import com.acpiot.wsd.data.sys.repository.DictionaryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by <PERSON><PERSON>u
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class DictionaryServiceImpl implements DictionaryService {

    private final DictionaryRepository dictionaryRepository;

    /**
     * 初始化网站配置
     */
    @Override
    public WebsiteParams initWebsiteParams(WebsiteProperties websiteProperties) {
        WebsiteParams websiteParams = new WebsiteParams();
        websiteParams.setSysName(saveDictionary(WebsiteParams.WebsiteKey.SYS_NAME, websiteProperties.getSysName()));
        websiteParams.setCompanyName(saveDictionary(WebsiteParams.WebsiteKey.COMPANY_NAME, websiteProperties.getCompanyName()));
        websiteParams.setCopyright(saveDictionary(WebsiteParams.WebsiteKey.COPYRIGHT, websiteProperties.getCopyright()));
        websiteParams.setKeywords(saveDictionary(WebsiteParams.WebsiteKey.KEYWORDS, websiteProperties.getKeywords()));
        websiteParams.setDescription(saveDictionary(WebsiteParams.WebsiteKey.DESCRIPTION, websiteProperties.getDescription()));
        websiteParams.setAuthor(saveDictionary(WebsiteParams.WebsiteKey.AUTHOR, websiteProperties.getAuthor()));
        websiteParams.setScreenMonitorTitle(saveDictionary(WebsiteParams.WebsiteKey.SCREEN_MONITOR_TITLE, websiteProperties.getScreenMonitorTitle()));
        websiteParams.setScreenRevenueTitle(saveDictionary(WebsiteParams.WebsiteKey.SCREEN_REVENUE_TITLE, websiteProperties.getScreenRevenueTitle()));
        return websiteParams;
    }

    @Override
    public String saveDictionary(WebsiteParams.WebsiteKey key, String value) {
        return dictionaryRepository.findUniqueBy(Dictionary_.key, key.name())
                .map(Dictionary::getValue)
                .orElseGet(() -> {
                    dictionaryRepository.save(new Dictionary(key.name(), value));
                    return value;
                });
    }

    @Override
    @Transactional(readOnly = true)
    public String getValueByKey(String key) {
        return dictionaryRepository.getValueByKey(key);
    }
}
