package com.acpiot.wsd.admin.mvc.paycallback;

import com.acpiot.wsd.admin.mvc.revenue.service.SxyPaymentService;
import com.acpiot.wsd.common.aop.annotation.SystemLog;
import com.acpiot.wsd.common.provider.GlobalLockProvider;
import com.alibaba.fastjson.JSONObject;
import com.upay.sdk.FastJsonUtils;
import com.upay.sdk.core.CipherWrapper;
import com.upay.sdk.exception.HmacVerifyException;
import com.upay.sdk.exception.UnknownException;
import com.upay.sdk.executer.Executer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 首信易支付回调通知接口
 * Created by hsq on 2024-11-20-0001
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Slf4j
@RequiredArgsConstructor
@Controller
@RequestMapping("/revenue/sxypay")
public class SxyPayController extends Executer {

    private static final String ENCRYPT_KEY = "encryptKey";
    private static final String MERCHANT_ID = "merchantId";

    private final SxyPaymentService sxyPaymentService;
    private final GlobalLockProvider globalLockProvider;

    /**
     * 子商户入网回调通知
     *
     * @param companyId
     * @param req
     * @param resp
     */
    @SystemLog(description = "易支付-子商户入网回调通知")
    @RequestMapping("sxySubMerchantApplyCallback/{companyId}")
    public void sxySubMerchantApplyCallback(@PathVariable("companyId") String companyId, HttpServletRequest req, HttpServletResponse resp) throws IOException {
        // 获取回调参数
        JSONObject data = parseNotifyContentJson(req);
        if (data == null) {
            resp.getWriter().print("SUCCESS");
            return;
        }
        log.info("首信易支付：子商户入网回调，开始处理，companyId={}，data={}", companyId, data);
        sxyPaymentService.sxySubMerchantApplyCallback(companyId, data);
        log.info("首信易支付：子商户入网回调，处理结束，companyId={}", companyId);

        resp.getWriter().print("SUCCESS");
    }

    /**
     * 分成商户入网回调通知
     *
     * @param requestId
     * @param req
     * @param resp
     */
    @SystemLog(description = "易支付-分成商户入网回调通知")
    @RequestMapping("sxySplitMerchantApplyCallback/{requestId}")
    public void sxySplitMerchantApplyCallback(@PathVariable("requestId") String requestId, HttpServletRequest req, HttpServletResponse resp) throws IOException {
        // 获取回调参数
        JSONObject data = parseNotifyContentJson(req);
        if (data == null) {
            resp.getWriter().print("SUCCESS");
            return;
        }
        log.info("首信易支付：分成商户入网回调，开始处理，requestId={}，data={}", requestId, data);
        sxyPaymentService.sxySplitMerchantApplyCallback(requestId, data);
        log.info("首信易支付：分成商户入网回调，处理结束，requestId={}", requestId);

        resp.getWriter().print("SUCCESS");
    }

    /**
     * 支付成功回调通知
     *
     * @param companyId
     * @param req
     * @param resp
     */
    @SystemLog(description = "易支付-支付回调通知")
    @RequestMapping("sxyCallback/{companyId}")
    public void sxyCallback(@PathVariable("companyId") String companyId, HttpServletRequest req, HttpServletResponse resp) throws IOException {
        // 获取回调参数
        JSONObject data = parseNotifyContentJson(req);
        if (data == null) {
            resp.getWriter().print("SUCCESS");
            return;
        }
        log.info("首信易支付：支付成功回调，开始处理，companyId={}，data={}", companyId, data);
        sxyPaymentService.sxyCallback(companyId, data);
        log.info("首信易支付：支付成功回调，处理结束，companyId={}", companyId);

        resp.getWriter().print("SUCCESS");
    }

    /**
     * 订单分账回调通知
     *
     * @param companyId
     * @param req
     * @param resp
     */
    @SystemLog(description = "易支付-订单分账回调通知")
    @RequestMapping("sxySplitOrderCallback/{companyId}")
    public void sxySplitOrderCallback(@PathVariable("companyId") String companyId, HttpServletRequest req, HttpServletResponse resp) throws IOException {
        // 获取回调参数
        JSONObject data = parseNotifyContentJson(req);
        if (data == null) {
            resp.getWriter().print("SUCCESS");
            return;
        }
        log.info("首信易支付：订单分账回调，开始处理，companyId={}，data={}", companyId, data);
        // 分账接口回调会存在同一时间多次推送同一个订单下的子分账订单，所以加锁处理
        globalLockProvider.sxyPaySync(data.getString("requestId"), () -> sxyPaymentService.sxySplitOrderCallback(companyId, data));
        log.info("首信易支付：订单分账回调，处理结束，companyId={}", companyId);

        resp.getWriter().print("SUCCESS");
    }

    /**
     * 订单提现回调通知
     *
     * @param merchantId
     * @param req
     * @param resp
     */
    @SystemLog(description = "易支付-订单提现回调通知")
    @RequestMapping("sxyWithdrawCallback/{merchantId}")
    public void sxyWithdrawCallback(@PathVariable("merchantId") String merchantId, HttpServletRequest req, HttpServletResponse resp) throws IOException {
        // 获取回调参数
        JSONObject data = parseNotifyContentJson(req);
        if (data == null) {
            resp.getWriter().print("SUCCESS");
            return;
        }
        log.info("首信易支付：订单提现回调，开始处理，merchantId={}，data={}", merchantId, data);
        sxyPaymentService.sxyWithdrawCallback(merchantId, data);
        log.info("首信易支付：订单提现回调，处理结束，merchantId={}", merchantId);

        resp.getWriter().print("SUCCESS");
    }

    /**
     * 退款回调通知
     *
     * @param companyId
     * @param req
     * @param resp
     */
    @SystemLog(description = "易支付-退款回调通知")
    @RequestMapping("sxyRefundCallback/{companyId}")
    public void sxyRefundCallback(@PathVariable("companyId") String companyId, HttpServletRequest req, HttpServletResponse resp) throws IOException {
        // 获取回调参数
        JSONObject data = parseNotifyContentJson(req);
        if (data == null) {
            resp.getWriter().print("SUCCESS");
            return;
        }
        log.info("首信易支付：退款回调，开始处理，companyId={}，data={}", companyId, data);
        sxyPaymentService.sxyRefundCallback(companyId, data);
        log.info("首信易支付：退款回调，处理结束，companyId={}", companyId);

        resp.getWriter().print("SUCCESS");
    }

    /**
     * 解析回调通知并进行解密验签，返回解密并验签后的json内容
     *
     * @param req
     * @return
     */
    private JSONObject parseNotifyContentJson(HttpServletRequest req) {
        // 解析json数据
        JSONObject data;
        try {
            data = FastJsonUtils.convert(req.getInputStream());
        } catch (IOException e) {
            log.error("首信易支付：接收到回调通知，解析json数据失败", e);
            return null;
        }
        // 设置临时密钥和商户编号到json中
        data.put(ENCRYPT_KEY, req.getHeader(ENCRYPT_KEY));
        data.put(MERCHANT_ID, req.getHeader(MERCHANT_ID));
        // 解密并验签
        try {
            JSONObject content = CipherWrapper.bothDecryptWrap(data);
            this.bothVerifyHmacOrder(content);
            return content;
        } catch (HmacVerifyException e) {
            log.error("首信易支付：接收到回调通知，签名验证异常", e);
        } catch (UnknownException e) {
            log.error("首信易支付：接收到回调通知，未知异常", e);
        }
        return null;
    }
}
