package com.acpiot.wsd.admin.mvc.archive.dto;

import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.admin.mvc.archive.converter.IotPlatformConverter;
import com.acpiot.wsd.data.archive.entity.*;
import com.acpiot.wsd.data.archive.enums.IotPlatform;
import com.acpiot.wsd.data.archive.enums.NbDockingType;
import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.data.sys.entity.ThirdDeviceDocking;
import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.format.DateTimeFormat;
import cn.idev.excel.annotation.format.NumberFormat;
import cn.idev.excel.annotation.write.style.ColumnWidth;
import cn.idev.excel.annotation.write.style.ContentStyle;
import cn.idev.excel.annotation.write.style.HeadStyle;
import cn.idev.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * UDP-NB批量换表Excel格式
 * Created by YoungLu on 2024-11-12-0001
 *
 * <AUTHOR>
 */
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@Data
public class ExcelToNbBatchChangeWaterMeterDto {

    /**
     * 旧表号
     */
    @NotBlank(message = "旧表号不能为空")
    @Size(max = 14, message = "旧表号最多为14位")
    @ColumnWidth(18)
    @ExcelProperty(value = {"批量更换至NB水表", "旧表号"})
    private String oldMeterCode;

    /**
     * 旧表最后读数
     */
    @NotNull
    @NumberFormat("0.000")
    @ColumnWidth(12)
    @ExcelProperty({"批量更换至NB水表", "旧表最后读数"})
    private BigDecimal oldValue;

    /**
     * 新表号
     */
    @NotBlank(message = "新表号不能为空")
    @Size(max = 14, message = "新表号最多为14位")
    @ColumnWidth(18)
    @ExcelProperty(value = {"批量更换至NB水表", "新表号"})
    private String newMeterCode;



    /**
     * IoT平台
     */
    @ColumnWidth(18)
    @ExcelProperty(value = {"批量更换至NB水表", "IoT平台"}, converter = IotPlatformConverter.class)
    private IotPlatform iotPlatform;

    /**
     * IMEI
     */
    @Pattern(regexp = "\\d{15}", message = "IMEI为空或者长度为15位数字")
    @ColumnWidth(18)
    @ExcelProperty({"批量更换至NB水表", "IMEI"})
    private String imei;

    /**
     * IMSI
     */
    @Pattern(regexp = "\\d{15}", message = "IMSI为空或者长度为15位数字")
    @ColumnWidth(18)
    @ExcelProperty({"批量更换至NB水表", "IMSI"})
    private String imsi;

    /**
     * ICCID
     */
    @Size(max = 20, message = "ICCID最多可输入20个字符")
    @ColumnWidth(20)
    @ExcelProperty({"批量更换至NB水表", "ICCID"})
    private String iccid;

    /**
     * 是否有阀
     */
    @NotBlank(message = "是否有阀不能为空")
    @ColumnWidth(20)
    @ExcelProperty({"批量更换至NB水表", "是否有阀"})
    private String valve;

    /**
     * 启用读数
     */
    @NumberFormat("0.000")
    @ColumnWidth(12)
    @ExcelProperty({"批量更换至NB水表", "启用读数"})
    private BigDecimal startValue;

    /**
     * 口径
     */
    @ColumnWidth(12)
    @Min(value = 15, message = "水表口径不能小于15")
    @ExcelProperty({"批量更换至NB水表", "水表口径"})
    private Integer diameter;

    /**
     * 安装位置
     */
    @Size(max = 64, message = "安装位置最多可输入64个字符")
    @ColumnWidth(22)
    @ExcelProperty({"批量更换至NB水表", "安装位置"})
    private String installLocation;

    /**
     * 安装日期
     */
    @DateTimeFormat("yyyy-MM-dd")
    @ColumnWidth(22)
    @ExcelProperty({"批量更换至NB水表", "安装日期"})
    private Date installDate;

    public NbWaterMeter toNbWaterMeter(MeterModel model, boolean standard, ThirdDeviceDocking thirdDeviceDocking, NbDockingType nbDockingType) {
        NbWaterMeter nbWaterMeter = new NbWaterMeter();
        nbWaterMeter.setModel(model);
        nbWaterMeter.setCode(this.newMeterCode);
        NbInfo nbInfo = new NbInfo();
        nbInfo.setIotPlatform(this.iotPlatform);
        nbInfo.setImei(this.imei);
        nbInfo.setImsi(this.imsi);
        nbInfo.setIccid(this.iccid);
        nbWaterMeter.setNbInfo(nbInfo);
        nbWaterMeter.setStartValue(this.startValue);
        nbWaterMeter.setDiameter(this.diameter);
        if (StrUtil.isNotBlank(this.installLocation) && this.installDate != null) {
            InstallationDetails installationDetails = new InstallationDetails();
            installationDetails.setInstallDate(this.installDate);
            installationDetails.setInstallLocation(this.installLocation);
            nbWaterMeter.setInstallationDetails(installationDetails);
        }
        nbWaterMeter.setStandard(standard);
        if (!standard) {
            if (thirdDeviceDocking == null && nbDockingType == null) {
                throw new BusinessException("对接方式不能为空");
            }
            nbWaterMeter.setThirdDeviceDocking(thirdDeviceDocking);
            nbWaterMeter.setNbDockingType(nbDockingType);
        }
        nbWaterMeter.setValve(this.valve.equals("是"));
        return nbWaterMeter;
    }
}
