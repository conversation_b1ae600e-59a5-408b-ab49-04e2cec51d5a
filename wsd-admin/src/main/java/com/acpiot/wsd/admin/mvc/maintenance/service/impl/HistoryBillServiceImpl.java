package com.acpiot.wsd.admin.mvc.maintenance.service.impl;

import com.acpiot.wsd.admin.mvc.maintenance.query.HistoryBillQuery;
import com.acpiot.wsd.admin.mvc.maintenance.service.HistoryBillService;
import com.acpiot.wsd.data.maintenance.entity.HistoryBill;
import com.acpiot.wsd.data.maintenance.entity.HistoryBill_;
import com.acpiot.wsd.data.maintenance.repository.HistoryBillRepository;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.acpiot.wsd.common.security.util.SecurityContextUtils.getRegionCommunityFilter;

/**
 * Created by moxin on 2020-03-17-0017
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class HistoryBillServiceImpl implements HistoryBillService {

    private final HistoryBillRepository historyBillRepository;

    @Override
    @Transactional(readOnly = true)
    public Page<HistoryBill> pagedBill(HistoryBillQuery params) {
        boolean continueQuery = getRegionCommunityFilter(params, HistoryBill_.COMMUNITY_ID);
        if (!continueQuery) {
            return new PageImpl<>(Lists.newArrayList());
        }
        return historyBillRepository.findAll(params);
    }
}
