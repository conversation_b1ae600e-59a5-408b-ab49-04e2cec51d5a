package com.acpiot.wsd.admin.mvc.revenue.service;

import com.acpiot.wsd.admin.mvc.revenue.dto.BillBalanceDto;
import com.acpiot.wsd.admin.mvc.revenue.dto.ExcelBillDto;
import com.acpiot.wsd.admin.mvc.revenue.query.BillQuery;
import com.acpiot.wsd.data.archive.entity.Community;
import com.acpiot.wsd.data.archive.entity.WaterMeter;
import com.acpiot.wsd.data.revenue.entity.ApplyBillVerification;
import com.acpiot.wsd.data.revenue.entity.Bill;
import com.acpiot.wsd.data.revenue.entity.LateFees;
import com.acpiot.wsd.miniprogram.res.model.WaterPriceDetail;
import org.springframework.data.domain.Page;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by moxin on 2020-03-17-0017
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface BillService {

    Page<Bill> pagedBill(BillQuery params);

    List<Community> getCalcBillCommunities(Date billDate, String companyId);

    List<WaterMeter> getCalcBillWaterMeters(Community community);

    Bill calcBill(Date currentBillDate, WaterMeter waterMeter, Community community);

    void calcBillWithPay(Date currentBillDate, WaterMeter waterMeter, Community community, boolean pay);

    List<ExcelBillDto> getExportBillDtoList(BillQuery params);

    Bill findBillById(long billId);

    /**
     * 取消账单
     *
     * @param billId
     */
    void cancelBill(long billId);

    /**
     * 撤消账单
     *
     * @param billId
     * @param reduceTon
     */
    void reduceBill(long billId, BigDecimal reduceTon);

    Bill findBillByIdFetchMeasuringPointAndCommunityAndCustomer(Long billId);

    BigDecimal getTotalBillAmount(BillQuery queryParams);

    /**
     * 查询缴费后总可用余额
     *
     * @param queryParams
     * @return
     */
    BillBalanceDto getBillBalanceDto(BillQuery queryParams);

    /**
     * 刷新并获取最新可用余额
     *
     * @param measuringPointId
     * @return
     */
    BigDecimal refreshAndReturnRealBalance(Long measuringPointId);

    /**
     * 计算滞纳金
     */
    void calcLateFees();

    /**
     * 分页查询滞纳金
     *
     * @param params
     * @return
     */
    Page<LateFees> pageLateFees(PagingQueryParams<LateFees> params);

    /**
     * 申请账单核销
     *
     * @param billId
     * @param reason
     */
    void applyVerifyBill(long billId, String reason);

    /**
     * 分页查询账单核销申请表
     *
     * @param params
     * @return
     */
    Page<ApplyBillVerification> pageApplyBillVerification(PagingQueryParams<ApplyBillVerification> params);

    /**
     * 机械表指定延迟天数发送账单通知
     */
    void machineMeterDelaySendBillNotify();

    /**
     * 查询账单费用明细
     *
     * @param billId
     * @return
     */
    List<WaterPriceDetail.WaterPriceItem> getBillWaterPriceDetail(Long billId);

    /**
     * 主动发送账单缴费提醒
     *
     * @param billId
     */
    void billPaymentRemind(Long billId);

    /**
     * 使用新水价重新计算账单
     *
     * @param billId
     */
    void reCalculateBill(Long billId);
}
