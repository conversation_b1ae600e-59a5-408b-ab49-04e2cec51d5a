package com.acpiot.wsd.admin.mvc.revenue.dto;

import com.acpiot.wsd.common.serializer.CompanyId;
import com.acpiot.wsd.data.revenue.entity.PayCustomer;
import com.acpiot.wsd.data.revenue.entity.WxUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxUserDTO {
    private Long id;
    private String openid;
    private String nickname;
    private String headImgUrl;
    @CompanyId
    private String companyId;
    private String communityName;
    private Date createdDate;
}
