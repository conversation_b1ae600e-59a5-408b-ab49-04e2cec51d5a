package com.acpiot.wsd.admin.mvc.sys.serivce.impl;

import com.acpiot.wsd.admin.mvc.sys.serivce.ThirdDeviceDockingService;
import com.acpiot.wsd.data.archive.repository.WaterMeterRepository;
import com.acpiot.wsd.data.sys.entity.ThirdDeviceDocking_;
import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.data.sys.repository.ThirdDeviceDockingRepository;
import com.acpiot.wsd.data.sys.entity.ThirdDeviceDocking;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.PagingQueryParams;
import pers.mx.jupiter.jpa.exception.FieldException;
import pers.mx.jupiter.jpa.utils.AttributeReplication;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @created 2024/10/9 11:10
 */
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class ThirdDeviceDockingServiceImpl implements ThirdDeviceDockingService {

    private final ThirdDeviceDockingRepository thirdDeviceDockingRepository;
    private final WaterMeterRepository waterMeterRepository;

    @Override
    @Transactional(readOnly = true)
    public Page<ThirdDeviceDocking> pagedThirdDeviceDocking(PagingQueryParams<ThirdDeviceDocking> params) {
        return thirdDeviceDockingRepository.findAll(params);
    }

    @Override
    public void addThirdDeviceDocking(ThirdDeviceDocking thirdDeviceDocking) throws FieldException {
        checkThirdDeviceDockingName(thirdDeviceDocking);
        thirdDeviceDockingRepository.save(thirdDeviceDocking);
    }

    @Override
    public void removeThirdDeviceDocking(long id) {
        //查询该对接类型是否存在
        ThirdDeviceDocking thirdDeviceDocking = thirdDeviceDockingRepository.findById(id)
                .orElseThrow(() -> new BusinessException("该标准第三方对接类型不存在"));
        //查询该类型是否已启用
        if (thirdDeviceDocking.isEnable()){
            throw new BusinessException("该类型已启用，不能删除");
        }
        //查询是否有在使用中的水表已使用该对接类型
        if (waterMeterRepository.existsByThirdDeviceDockingId(thirdDeviceDocking.getId())) {
            throw new BusinessException("该类型已被使用，不能删除");
        }
        //执行删除
        thirdDeviceDockingRepository.deleteById(id);
    }

    @Override
    public void removeThirdDeviceDockings(long[] ids) {
        //查询要删除的类型是否存在
        List<Long> idList = Arrays.stream(ids).boxed().collect(Collectors.toList());
        List<ThirdDeviceDocking> results = thirdDeviceDockingRepository.findAllById(idList);
        if (results.size() != idList.size()) {
            throw new BusinessException("要删除的部分类型不存在");
        }
        //查询要删除的类型是否已被启用
        if (results.stream().anyMatch(ThirdDeviceDocking::isEnable)) {
            throw new BusinessException("部分类型已被启用，不能删除");
        }
        //查询要删除的类型是否已被水表使用
        if (waterMeterRepository.existsByThirdDeviceDockingIdIn(idList)) {
            throw new BusinessException("部分类型已被水表使用，不能删除");
        }
        //执行删除
        thirdDeviceDockingRepository.deleteAllById(idList);
    }

    @Override
    public void updateThirdDeviceDocking(ThirdDeviceDocking thirdDeviceDocking) throws FieldException {
        checkThirdDeviceDockingName(thirdDeviceDocking);
        ThirdDeviceDocking dbThirdDeviceDockingRepository = thirdDeviceDockingRepository.findById(thirdDeviceDocking.getId())
                .orElseThrow(() -> new BusinessException("该标准第三方对接类型不存在: {}", thirdDeviceDocking.getName()));
        AttributeReplication.copying(thirdDeviceDocking, dbThirdDeviceDockingRepository,
                //修改的属性
                ThirdDeviceDocking_.name,
                ThirdDeviceDocking_.enable,
                ThirdDeviceDocking_.enableMetering,
                ThirdDeviceDocking_.enableValve,
                ThirdDeviceDocking_.token,
                ThirdDeviceDocking_.baseUrl,
                ThirdDeviceDocking_.remark
        );
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ThirdDeviceDocking> getThirdDeviceDockingById(long id) {
        return thirdDeviceDockingRepository.findById(id);
    }

    private void checkThirdDeviceDockingName(ThirdDeviceDocking thirdDeviceDocking) throws FieldException {
        if (!thirdDeviceDockingRepository.isUnique(thirdDeviceDocking, ThirdDeviceDocking_.name)){
            throw new FieldException(ThirdDeviceDocking_.NAME, thirdDeviceDocking.getName(), "该对接类型名称已存在");
        }
    }
}
