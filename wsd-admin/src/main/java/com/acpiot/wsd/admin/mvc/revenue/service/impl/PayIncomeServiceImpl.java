package com.acpiot.wsd.admin.mvc.revenue.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.admin.constant.AdminConstant;
import com.acpiot.wsd.admin.mvc.archive.service.CommunityService;
import com.acpiot.wsd.admin.mvc.common.projections.BaseProjection;
import com.acpiot.wsd.admin.mvc.revenue.dto.ExcelPayIncomeDto;
import com.acpiot.wsd.admin.mvc.revenue.dto.PayIncomeExtDto;
import com.acpiot.wsd.admin.mvc.revenue.query.PayIncomeQuery;
import com.acpiot.wsd.admin.mvc.revenue.service.PayIncomeService;
import com.acpiot.wsd.admin.mvc.sys.projections.RegionProjections;
import com.acpiot.wsd.admin.mvc.sys.serivce.RegionService;
import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.data.archive.entity.Community_;
import com.acpiot.wsd.data.archive.entity.MeasuringPoint_;
import com.acpiot.wsd.data.archive.entity.QMeasuringPoint;
import com.acpiot.wsd.data.revenue.bean.ChargeDetail;
import com.acpiot.wsd.data.revenue.bean.WaterPrice;
import com.acpiot.wsd.data.revenue.entity.PayIncome;
import com.acpiot.wsd.data.revenue.entity.PayIncome_;
import com.acpiot.wsd.data.revenue.entity.QCustomer;
import com.acpiot.wsd.data.revenue.entity.QPayIncome;
import com.acpiot.wsd.data.revenue.repository.PayIncomeRepository;
import com.acpiot.wsd.data.sys.repository.UserRepository;
import com.google.common.collect.Lists;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.Filter;
import pers.mx.jupiter.jpa.Order;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.acpiot.wsd.admin.constant.AdminConstant.POLLUTE_WATER_PRICE_NAME;
import static com.acpiot.wsd.common.security.util.SecurityContextUtils.getGlobalFilter;
import static com.acpiot.wsd.common.security.util.SecurityContextUtils.getRegionCommunityFilter;

/**
 * Created by YoungLu on 2021-04-23
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class PayIncomeServiceImpl implements PayIncomeService {

    private final PayIncomeRepository payIncomeRepository;
    private final UserRepository userRepository;
    private final CommunityService communityService;
    private final RegionService regionService;
    private final JPAQueryFactory jpaQueryFactory;

    @Override
    @Transactional(readOnly = true)
    public PayIncome findByPayCode(String payCode) {
        return payIncomeRepository.findByPayCode(payCode);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PayIncome> pagedPayIncome(PayIncomeQuery params) {
        String communityPath = String.format("%s.%s.%s", PayIncome_.MEASURING_POINT, MeasuringPoint_.COMMUNITY, Community_.ID);
        boolean continueQuery = getRegionCommunityFilter(params, communityPath);
        if (!continueQuery) {
            return new PageImpl<>(Lists.newArrayList());
        }
        params.addExtFilters();
        getGlobalFilter(String.format("%s.%s", PayIncome_.MEASURING_POINT, MeasuringPoint_.COMPANY_ID), communityPath).ifPresent(params::and);
        params.fetch(PayIncome_.MEASURING_POINT).fetch(PayIncome_.BILL)
                .fetch(PayIncome_.CUSTOMER)
                .fetch(String.format("%s.%s", PayIncome_.MEASURING_POINT, MeasuringPoint_.COMMUNITY))
                .fetch(String.format("%s.%s", PayIncome_.MEASURING_POINT, MeasuringPoint_.LOWER_COMMUNITY));
        return payIncomeRepository.findAll(params);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ExcelPayIncomeDto> getExportPayIncomeDtoList(PayIncomeQuery params) {
        params.setLimit(AdminConstant.EXPORT_LIMIT);
        String communityPath = String.format("%s.%s.%s", PayIncome_.MEASURING_POINT, MeasuringPoint_.COMMUNITY, Community_.ID);
        boolean continueQuery = getRegionCommunityFilter(params, communityPath);
        if (!continueQuery) {
            return Lists.newArrayList();
        }
        getGlobalFilter(String.format("%s.%s", PayIncome_.MEASURING_POINT, MeasuringPoint_.COMPANY_ID), communityPath).ifPresent(params::and);
        params.fetch(PayIncome_.MEASURING_POINT)
                .fetch(String.format("%s.%s", PayIncome_.MEASURING_POINT, MeasuringPoint_.CUSTOMER))
                .fetch(String.format("%s.%s", PayIncome_.MEASURING_POINT, MeasuringPoint_.COMMUNITY))
                .fetch(String.format("%s.%s", PayIncome_.MEASURING_POINT, MeasuringPoint_.LOWER_COMMUNITY));
        Page<PayIncome> payIncomes = payIncomeRepository.findAll(params);
        communityService.buildBuildingUnitName(payIncomes.getContent());
        return payIncomes.stream().map(ExcelPayIncomeDto::new).collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public PayIncome findPayIncomeById(long id) {
        PayIncome payIncome = payIncomeRepository.findOne((root, query, cb) -> {
            root.fetch(PayIncome_.MEASURING_POINT).fetch(MeasuringPoint_.COMMUNITY);
            root.fetch(PayIncome_.CUSTOMER);
            return cb.equal(root.get(PayIncome_.id), id);
        }).orElseThrow(() -> new BusinessException("未找到缴费记录"));

        if (StrUtil.isNotBlank(payIncome.getCreatedBy())) {
            userRepository.findByName(payIncome.getCreatedBy()).ifPresent(user -> payIncome.setRealName(user.getRealName()));
        }
        return payIncome;
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getTotalIncomeByParams(PayIncomeQuery params) {
        QPayIncome payIncome = QPayIncome.payIncome;
        QMeasuringPoint measuringPoint = payIncome.measuringPoint;
        QCustomer customer = measuringPoint.customer;

        // 应用默认条件
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(payIncome.incomeStatus.eq(PayIncome.IncomeStatus.PAY_SUCCESS));
        getGlobalFilter(measuringPoint.companyId, measuringPoint.community.id).ifPresent(predicates::addAll);

        // 应用其它条件
        List<Filter> payIncomeFilters = params.getAndFilters().stream().filter(predicate -> predicate.getValue() != null).collect(Collectors.toList());
        payIncomeFilters.forEach(filter -> {
            if (filter.getValue() != null) {
                switch (filter.getProperty()) {
                    case "measuringPoint.customer.name": {
                        predicates.add(customer.name.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.customer.mobile": {
                        predicates.add(customer.mobile.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.customer.idCard": {
                        predicates.add(customer.idCard.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.roomNumber": {
                        predicates.add(measuringPoint.roomNumber.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.accountNo": {
                        predicates.add(measuringPoint.accountNo.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.linkCode": {
                        predicates.add(measuringPoint.linkCode.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.name": {
                        predicates.add(measuringPoint.name.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.meterCode": {
                        predicates.add(measuringPoint.meterCode.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "measuringPoint.community.id": {
                        predicates.add(measuringPoint.community.id.eq(Long.valueOf((String) filter.getValue())));
                        break;
                    }
                    case "measuringPoint.lowerCommunity.layerCode": {
                        predicates.add(measuringPoint.lowerCommunity.layerCode.like(filter.getValue() + "%"));
                        break;
                    }
                    case "payCode": {
                        predicates.add(payIncome.payCode.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "businessType": {
                        predicates.add(payIncome.businessType.eq(PayIncome.BusinessType.valueOf(filter.getValue().toString())));
                        break;
                    }
                    case "incomeType": {
                        predicates.add(payIncome.incomeType.eq(PayIncome.IncomeType.valueOf(filter.getValue().toString())));
                        break;
                    }
                    case "orderCode": {
                        predicates.add(payIncome.orderCode.like("%" + filter.getValue() + "%"));
                        break;
                    }
                    case "createdBy": {
                        predicates.add(payIncome.createdBy.eq((String) filter.getValue()));
                        break;
                    }
                    case "poundage": {
                        String poundage = filter.getValue().toString();
                        predicates.add(ObjectUtil.equal("1", poundage) ? payIncome.poundage.isNotNull() : payIncome.poundage.isNull());
                        break;
                    }
                    case "bankTransfer": {
                        String bankTransfer = filter.getValue().toString();
                        predicates.add(payIncome.bankTransfer.eq(ObjectUtil.equal("true", bankTransfer)));
                        break;
                    }
                }
            }
        });

        // 时间范围条件
        if (StrUtil.isNotBlank(params.getPayIncomePayTimeDatetimes())) {
            String payIncomePayTimeDatetimes = params.getPayIncomePayTimeDatetimes();
            String[] range = payIncomePayTimeDatetimes.split(" - ");
            DateTime endTime = DateUtil.parse(range[1], "yyyy-MM-dd HH:mm");
            endTime.setField(Calendar.SECOND, 59);
            endTime.setField(Calendar.MILLISECOND, 999);
            predicates.add(payIncome.payTime.between(DateUtil.parse(range[0], "yyyy-MM-dd HH:mm"), endTime));
        }
        // 区域条件
        Optional<Filter> regionIdOptional = payIncomeFilters.stream().filter(filter -> Objects.equals(filter.getProperty(), "regionId")).findFirst();
        if (regionIdOptional.isPresent()) {
            String regionId = regionIdOptional.get().getValue().toString();
            // 查询当前管辖区域下的顶级小区
            List<RegionProjections.Community> communities = regionService.getCommunitiesInRegion(Long.parseLong(regionId));
            // 如果所选管辖区下无顶级小区，则查询不到任何数据
            if (CollUtil.isEmpty(communities)) {
                predicates.add(payIncome.id.eq(0L));
            } else {
                List<Long> communityIds = communities.stream().map(BaseProjection::getId).collect(Collectors.toList());
                predicates.add(measuringPoint.community.id.in(communityIds));
            }
        }

        return jpaQueryFactory.select(payIncome.amount.sum()).from(payIncome).where(predicates.toArray(new Predicate[0])).fetchOne();
    }

    @Override
    @Transactional(readOnly = true)
    public PayIncomeExtDto calculatePayIncomeExtInfo(Long payIncomeId) {
        // 返回的计算结果
        PayIncomeExtDto payIncomeExtDto = new PayIncomeExtDto();
        // 查询当前缴费记录获取缴费金额
        PayIncome payIncome = payIncomeRepository.findById(payIncomeId).orElseThrow(() -> new BusinessException("不存在该缴费记录"));
        BigDecimal realIncome = payIncome.getAmount();
        // 获取充值时的水价以及明细数据
        WaterPrice waterPrice = payIncome.getWaterPrice();
        if (waterPrice == null) {
            return payIncomeExtDto;
        }
        List<ChargeDetail> chargeDetails = waterPrice.getChargeDetails();

        // 购水量/吨
        BigDecimal payWaterTon;
        // 基础水费单价
        String basePrice;
        // 基础水费合计
        BigDecimal baseWaterAmount;
        // 污水费单价
        String pollutePrice = "";
        // 污水费合计
        BigDecimal polluteWaterAmount = BigDecimal.ZERO;

        // 不管是阶梯水价还是单费率价格，均采用阶梯一的价格来计算：使用充值后实际增加的余额 / 单价 = 购水量吨
        BigDecimal ladderPrice = waterPrice.getLadderPrices()[0];
        payWaterTon = ladderPrice.compareTo(BigDecimal.ZERO) != 0 ? realIncome.divide(ladderPrice, 2, RoundingMode.DOWN) : BigDecimal.ZERO;
        // 计算污水费合计
        BigDecimal pollutePriceV = chargeDetails.stream().filter(c -> c.getName().contains(POLLUTE_WATER_PRICE_NAME))
                .findFirst().map(c -> c.getLadderCharges()[0]).orElse(null);
        if (pollutePriceV != null) {
            pollutePrice = pollutePriceV + "元/吨";
            // 污水费合计 = 购水量吨 * 单价
            polluteWaterAmount = payWaterTon.multiply(pollutePriceV).setScale(2, RoundingMode.UP);
        } else {
            pollutePriceV = BigDecimal.ZERO;
        }
        // 计算基础水费合计，排除污水价
        BigDecimal basePriceV = ladderPrice.subtract(pollutePriceV);
        basePrice = basePriceV + "元/吨";
        baseWaterAmount = realIncome.subtract(polluteWaterAmount);
        // 保存计算结果并返回
        payIncomeExtDto.fill(payWaterTon, basePrice, baseWaterAmount, pollutePrice, polluteWaterAmount);
        return payIncomeExtDto;
    }
}
