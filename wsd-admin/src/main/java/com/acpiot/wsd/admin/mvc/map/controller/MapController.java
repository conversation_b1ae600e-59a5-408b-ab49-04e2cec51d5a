package com.acpiot.wsd.admin.mvc.map.controller;

import com.acpiot.wsd.admin.mvc.archive.service.ArchiveService;
import com.acpiot.wsd.admin.mvc.map.service.MapService;
import com.acpiot.wsd.data.archive.enums.ConcentratorType;
import com.acpiot.wsd.common.enums.ValveStatus;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 地图监控
 */
@RequiredArgsConstructor
@Controller
@RequestMapping("/map")
public class MapController {

    private final MapService mapService;
    private final ArchiveService archiveService;

    @Value("${geode.key}")
    private String key;

    @Value("${geode.security-code}")
    private String securityCode;

    @GetMapping("")
    public String index(ModelMap map) {
        map.addAttribute("mapDeviceCount", mapService.getMapDeviceCount());
        map.addAttribute("communities", archiveService.getAllRootCommunitiesInfo());
        map.addAttribute("concentratorTypes", ConcentratorType.values());
        map.addAttribute("meterModels", archiveService.getMeterModels(null));
        map.addAttribute("valveStatues", ValveStatus.values());
        map.addAttribute("key", key);
        map.addAttribute("securityCode", securityCode);
        return "map/map-monitor";
    }
}
