package com.acpiot.wsd.admin.mvc.productiontest.service;

import com.acpiot.wsd.admin.mvc.productiontest.dto.ExportTestWaterMeterDto;
import com.acpiot.wsd.admin.mvc.productiontest.dto.ImportTestWaterMeterDto;
import com.acpiot.wsd.admin.mvc.productiontest.dto.TestWaterMeterCountDataDto;
import com.acpiot.wsd.admin.mvc.productiontest.query.TestPrepareCmdQuery;
import com.acpiot.wsd.admin.mvc.productiontest.query.TestWaterMeterQuery;
import com.acpiot.wsd.cat1wmservice.dto.command.C1CParam;
import com.acpiot.wsd.common.dto.ScanBindMeterCodes;
import com.acpiot.wsd.data.productiontest.entity.TestPrepareCmd;
import com.acpiot.wsd.data.productiontest.entity.TestWaterMeter;
import org.springframework.data.domain.Page;
import pers.mx.jupiter.jpa.exception.FieldException;

import java.math.BigDecimal;
import java.util.List;

/**
 * 产测水表服务接口
 *
 * <AUTHOR>
 * @since 2023/11/15
 */
public interface TestWaterMeterService {

    /**
     * 分页查询测试设备
     *
     * @param params
     * @return
     */
    Page<TestWaterMeter> pageTestWaterMeter(TestWaterMeterQuery params);

    /**
     * 删除测试设备
     *
     * @param ids
     */
    void removeTestWaterMeters(Long... ids);

    /**
     * 新增测试设备
     *
     * @param testWaterMeter
     * @throws FieldException
     */
    void addTestWaterMeter(TestWaterMeter testWaterMeter) throws FieldException;

    /**
     * 导入测试设备
     *
     * @param testBatchId
     * @param uploadMeterDtos
     */
    void batchImportTestWaterMeters(Long testBatchId, List<ImportTestWaterMeterDto> uploadMeterDtos);

    /**
     * 发送预置产测命令
     *
     * @param ids
     * @param params
     * @param c1CParam
     */
    void sendProductionTestPrepareCmd(String ids, String params, C1CParam c1CParam);

    /**
     * 发送预置阀控命令
     *
     * @param ids
     * @param params
     * @param type
     */
    void sendValveCtrlPrepareCmd(String ids, String params, int type);

    /**
     * 分页查询预置命令
     *
     * @param params
     * @return
     */
    Page<TestPrepareCmd> pagePrepareCmd(TestPrepareCmdQuery params);

    /**
     * 查询待导出测试水表
     *
     * @param queryParams
     * @return
     */
    List<ExportTestWaterMeterDto> getExportTestWaterMeters(TestWaterMeterQuery queryParams);

    /**
     * 下发预置IP命令
     *
     * @param ids
     * @param params
     */
    void sendSetIpPrepareCmd(String ids, String params, String masterIp, Integer masterPort, String slaveIp, Integer slavePort);

    /**
     * 标记成功
     *
     * @param testWaterMeterId
     */
    void markSuccess(Long testWaterMeterId);

    /**
     * 批量标记成功
     * @param ids
     */
    void markSuccessTestWaterMeters(String ids);

    /**
     * 扫码绑定表号
     *
     * @param scanBindMeterCodes
     */
    TestWaterMeter commitScanBindMeterCodes(ScanBindMeterCodes scanBindMeterCodes);

    /**
     * 下发表底数命令
     *
     * @param ids
     * @param params
     * @param val
     * @param multiply
     */
    void sendSetBaseValue(String ids, String params, int val, int multiply);

    /**
     * 下发写入表号命令
     *
     * @param ids
     * @param params
     */
    void sendSetMeterCode(String ids, String params);

    /**
     * 下发单个预置表底数命令
     *
     * @param id
     * @param val
     * @param multiply
     */
    void sendSingleSetBaseValue(Long id, int val, int multiply);

    /**
     * 下发单个预置表底数命令，如果存在未下发命令则取消
     *
     * @param id
     * @param val
     * @param multiply
     */
    void sendSingleSetBaseValueAndCancelUnSend(Long id, int val, int multiply);

    /**
     * 取消命令
     */
    void cancelCmd(Long id);

    /**
     * 成表产测-测试数据统计接口
     *
     * @param batchId
     * @param used
     * @return
     */
    TestWaterMeterCountDataDto countTestWaterMeterData(Long batchId, boolean used);

    /**
     * 修改当前公司的电池电压报警区间，仅公司用户可访问
     *
     * @param batteryAlarmStart
     * @param batteryAlarmEnd
     */
    void updateBatteryVoltageAlarmReportRange(BigDecimal batteryAlarmStart, BigDecimal batteryAlarmEnd);
}
