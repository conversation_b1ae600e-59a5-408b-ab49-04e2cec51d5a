package com.acpiot.wsd.admin.mvc.revenue.service.impl;

import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.admin.mvc.revenue.service.ApplyWxRefundService;
import com.acpiot.wsd.admin.mvc.revenue.service.PaymentService;
import com.acpiot.wsd.data.archive.entity.MeasuringPoint_;
import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.data.revenue.entity.ApplyWxRefund;
import com.acpiot.wsd.data.revenue.entity.ApplyWxRefund_;
import com.acpiot.wsd.data.revenue.entity.PayIncome;
import com.acpiot.wsd.data.revenue.entity.PayIncome_;
import com.acpiot.wsd.data.revenue.enums.ApplyStatus;
import com.acpiot.wsd.data.revenue.repository.ApplyWxRefundRepository;
import com.acpiot.wsd.data.revenue.repository.PayIncomeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.util.Optional;

/**
 * Created by moxin on 2020-03-11-0011
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class ApplyWxRefundServiceImpl implements ApplyWxRefundService {

    private final ApplyWxRefundRepository applyWxRefundRepository;
    private final PaymentService paymentService;
    private final PayIncomeRepository payIncomeRepository;

    @Override
    @Transactional(readOnly = true)
    public Page<ApplyWxRefund> page(PagingQueryParams<ApplyWxRefund> params) {
        params.fetch(String.format("%s.%s.%s", ApplyWxRefund_.PAY_INCOME, PayIncome_.MEASURING_POINT, MeasuringPoint_.COMMUNITY));
        params.fetch(String.format("%s.%s", ApplyWxRefund_.PAY_INCOME, PayIncome_.CUSTOMER));
        return applyWxRefundRepository.findAll(params);
    }

    @Override
    public void apply(Long applyId, ApplyStatus applyStatus, String failReason) {
        ApplyWxRefund applyWxRefund = applyWxRefundRepository.findById(applyId).orElseThrow(() -> new BusinessException("未找到对应的退费申请"));
        if (applyWxRefund.getApplyStatus() != ApplyWxRefund.ApplyStatus.WAIT_AUDIT) {
            throw new BusinessException("当前退费申请状态未处于待审核状态");
        }
        if (applyStatus == ApplyStatus.WAIT_AUDIT) {
            throw new BusinessException("提交的审核状态不能为待审核");
        }
        if (applyStatus == ApplyStatus.AUDIT_FAIL) {
            // 审核不通过
            if (StrUtil.isBlank(failReason)) {
                throw new BusinessException("审核不通过请提交失败原因");
            }
            // 更新退费申请状态为失败
            applyWxRefund.setApplyStatus(ApplyWxRefund.ApplyStatus.AUDIT_FAIL);
            applyWxRefund.setFailReason(failReason);
            return;
        }
        // 审核通过
        paymentService.refundPayIncome(applyWxRefund.getPayIncome().getId(), applyWxRefund.getAmount(), applyId);
    }

    @Override
    @Transactional(readOnly = true)
    public ApplyWxRefund findApplyWxRefundById(Long applyId) {
        return applyWxRefundRepository.findById(applyId).orElseThrow(() -> new BusinessException("不存在该申请记录"));
    }

    @Override
    @Transactional(readOnly = true)
    public ApplyWxRefund findTopApplyWxRefundByPayCode(String payCode) {
        PayIncome payIncome = payIncomeRepository.findByPayCode(payCode);
        if (payIncome == null) {
            return null;
        }
        // 查询当前缴费最新的一条退款申请记录
        Optional<ApplyWxRefund> optional = applyWxRefundRepository.findTopByPayIncome_IdOrderByCreatedDateDesc(payIncome.getId());
        return optional.orElse(null);
    }
}
