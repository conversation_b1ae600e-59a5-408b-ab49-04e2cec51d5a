package com.acpiot.wsd.admin.mvc.archive.service;

import com.acpiot.wsd.admin.mvc.archive.projections.*;
import com.acpiot.wsd.data.archive.entity.Community;
import com.acpiot.wsd.data.archive.entity.MeterModel;
import com.acpiot.wsd.data.sys.entity.ThirdDeviceDocking;
import com.acpiot.wsd.data.archive.entity.WaterMeter;
import com.acpiot.wsd.data.archive.enums.CommunicationChannel;
import com.acpiot.wsd.data.archive.enums.MeterType;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Optional;

/**
 * 提供对外的档案信息查询服务
 * Created by moxin on 2021-03-24-0024
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface ArchiveService {

    List<CommunityProjection> getAllRootCommunitiesInfo();

    List<Community> getAllRootCommunities();

    List<Community> getChildrenCommunityByParent(Long communityId);

    List<BaseMeterProjection> getBaseMeters();

    List<MeterModelProjection> getMeterModels(@Nullable MeterType meterType);

    List<MeterModel> getPreparedTestMeterModels(@Nullable CommunicationChannel communicationChannel);

    List<MeterModelProjection> getNbMeterModelsExceptTransparent();

    List<ConcentratorProjection> getConcentrators(long communityId, boolean standard);

    Optional<? extends WaterMeter> getWaterMeterById(long meterId);

    List<TollCollectorProjection> getTollCollectors();

    List<ThirdDeviceDocking> getAllThirdDeviceDockings();

    List<ConcentratorProjection> getCommunityConcentrators(long communityId);
}
