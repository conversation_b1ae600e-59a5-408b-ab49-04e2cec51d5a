package com.acpiot.wsd.admin.mvc.chart.service;

import com.acpiot.wsd.admin.mvc.chart.dto.MeasuringPointDataStatisticsDto;
import com.acpiot.wsd.admin.mvc.chart.dto.WaterMeterLatestData;
import com.acpiot.wsd.common.dto.CommonKeyValue;
import org.springframework.data.domain.Page;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.time.LocalDate;
import java.util.List;

/**
 * 客户用水量统计服务
 * Created by zc on 2021/7/22 13:53
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface CustomerConsumptionChartService {

    /**
     * 查询一年中所有月份的客户用水量统计
     *
     * @param year
     * @param communityId
     * @param regionId
     * @param waterClassificationId
     * @param pointName
     * @param mobile
     * @param meterCode
     * @param accountNo
     * @param customerName
     * @param idCard
     * @return
     */
    List<CommonKeyValue> getCustomerConsumptionDataByMonth(int year, Long communityId, Long regionId, Long waterClassificationId, String pointName,
                                                           String mobile, String meterCode, String accountNo, String customerName, String idCard);

    /**
     * 分页查询一年内的客户用水量详情
     *
     * @param params
     * @return
     */
    Page<MeasuringPointDataStatisticsDto> pageCustomerConsumptionByMonth(PagingQueryParams<MeasuringPointDataStatisticsDto> params);

    /**
     * 查询导出一年内的客户用水量详情
     *
     * @param params
     * @return
     */
    List<MeasuringPointDataStatisticsDto> getExportCustomerConsumptionByMonth(PagingQueryParams<MeasuringPointDataStatisticsDto> params);

    /**
     * 查询指定时间区间的客户用水量统计
     *
     * @param from
     * @param to
     * @param communityId
     * @param regionId
     * @param waterClassificationId
     * @param pointName
     * @param mobile
     * @param meterCode
     * @param accountNo
     * @param customerName
     * @param idCard
     * @return
     */
    List<CommonKeyValue> getCustomerConsumptionDataByDay(LocalDate from, LocalDate to, Long communityId, Long regionId, Long waterClassificationId, String pointName,
                                                         String mobile, String meterCode, String accountNo, String customerName, String idCard);

    /**
     * 分页查询指定时间区间内的客户用水量详情
     *
     * @param params
     * @return
     */
    Page<MeasuringPointDataStatisticsDto> pageCustomerConsumptionByDay(PagingQueryParams<MeasuringPointDataStatisticsDto> params);

    /**
     * 查询导出指定时间区间内的客户用水量详情
     *
     * @param params
     * @return
     */
    List<MeasuringPointDataStatisticsDto> getExportCustomerConsumptionByDay(PagingQueryParams<MeasuringPointDataStatisticsDto> params);

    /**
     * 查询水表最新数据
     *
     * @param measuringPointIds
     * @return
     */
    List<WaterMeterLatestData> getWaterMeterLatestData(List<Long> measuringPointIds);
}
