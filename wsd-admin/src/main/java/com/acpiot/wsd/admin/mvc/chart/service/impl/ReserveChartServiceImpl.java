package com.acpiot.wsd.admin.mvc.chart.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.acpiot.wsd.admin.mvc.chart.dto.MeasuringPointStatisticsDto;
import com.acpiot.wsd.admin.mvc.chart.service.AbstractChartService;
import com.acpiot.wsd.admin.mvc.chart.service.ReserveChartService;
import com.acpiot.wsd.admin.mvc.common.projections.BaseProjection;
import com.acpiot.wsd.common.utils.StatisticsDataFillUtil;
import com.acpiot.wsd.admin.mvc.sys.projections.RegionProjections;
import com.acpiot.wsd.data.archive.entity.Community_;
import com.acpiot.wsd.data.archive.repository.CommunityRepository;
import com.acpiot.wsd.common.dto.CommonKeyValue;
import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.data.revenue.entity.PayIncome;
import com.acpiot.wsd.data.revenue.entity.QPayIncome;
import com.google.common.collect.Lists;
import com.querydsl.core.QueryResults;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.StringTemplate;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.Filter;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.acpiot.wsd.common.utils.StatisticsDataFillUtil.fillZeroKeyValue;
import static com.acpiot.wsd.core.query.CommunityFilters.byRegionId;

/**
 * 现金预存相关统计接口
 * Created by hsq on 2021-07-09-0000
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class ReserveChartServiceImpl extends AbstractChartService implements ReserveChartService {

    private final JPAQueryFactory jpaQueryFactory;
    private final CommunityRepository communityRepository;

    @Override
    public List<CommonKeyValue> getReserveDataByMonth(Long communityId, Long regionId, String pointName, String mobile,
                                                      String meterCode, String accountNo, int year, PayIncome.IncomeType incomeType, PayIncome.BusinessType businessType) {
        QPayIncome payIncome = QPayIncome.payIncome;
        List<Predicate> predicates = getReservePredicateList(communityId, regionId, pointName, mobile, meterCode, accountNo, incomeType, businessType);
        applyYearPredicate(payIncome, predicates, String.valueOf(year));
        StringTemplate monthExpr = Expressions.stringTemplate("DATE_FORMAT({0},'%Y-%m')", payIncome.payTime);
        List<CommonKeyValue> commonKeyValues = jpaQueryFactory.select(monthExpr, payIncome.amount.sum())
                .from(payIncome)
                .where(predicates.toArray(new Predicate[0]))
                .groupBy(monthExpr)
                .fetch().stream()
                .map(tuple -> new CommonKeyValue(
                        tuple.get(monthExpr),
                        tuple.get(payIncome.amount.sum())
                )).collect(Collectors.toList());
        LocalDate start = LocalDate.of(year, 1, 1);
        LocalDate end = LocalDate.of(year, 12, 31);
        return fillZeroKeyValue(commonKeyValues, Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant()),
                Date.from(end.atStartOfDay(ZoneId.systemDefault()).toInstant()), StatisticsDataFillUtil.KeyType.MONTH);
    }

    @Override
    public List<CommonKeyValue> getReserveDataByDay(Long communityId, Long regionId, String pointName, String mobile, String meterCode,
                                                    String accountNo, LocalDate from, LocalDate to, PayIncome.IncomeType incomeType, PayIncome.BusinessType businessType) {
        datePeriodCheck(from, to);
        QPayIncome payIncome = QPayIncome.payIncome;
        Date dateFrom = Date.from(from.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date dateTo = DateUtil.endOfDay(Date.from(to.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        List<Predicate> predicates = getReservePredicateList(communityId, regionId, pointName, mobile, meterCode, accountNo, incomeType, businessType);
        addRangePredicate(predicates, dateFrom, dateTo);
        StringTemplate dayExpr = Expressions.stringTemplate("DATE_FORMAT({0},'%Y-%m-%d')", payIncome.payTime);
        List<CommonKeyValue> commonKeyValues = jpaQueryFactory.select(dayExpr, payIncome.amount.sum())
                .from(payIncome)
                .where(predicates.toArray(new Predicate[0]))
                .groupBy(dayExpr)
                .fetch().stream()
                .map(tuple -> new CommonKeyValue(
                        tuple.get(dayExpr),
                        tuple.get(payIncome.amount.sum())
                )).collect(Collectors.toList());
        return fillZeroKeyValue(commonKeyValues, dateFrom, dateTo, StatisticsDataFillUtil.KeyType.DAY);
    }

    @Override
    public Page<MeasuringPointStatisticsDto> pageMeasuringPointReserveByDay(PagingQueryParams<MeasuringPointStatisticsDto> params) {
        // params参数处理
        Long communityId = null;
        Long regionId = null;
        String pointName = null;
        String mobile = null;
        String meterCode = null;
        String accountNo = null;
        LocalDate from = null;
        LocalDate to = null;
        PayIncome.IncomeType incomeType = null;
        PayIncome.BusinessType businessType = null;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<Filter> andFilters = params.getAndFilters();
        for (Filter filter : andFilters) {
            if (filter.getValue() == null || StringUtils.isBlank(filter.getValue().toString())) {
                continue;
            }
            String value = filter.getValue().toString();
            if (filter.getProperty().equalsIgnoreCase("communityId")) {
                communityId = Long.parseLong(value);
            } else if (filter.getProperty().equalsIgnoreCase("regionId")) {
                regionId = Long.parseLong(value);
            } else if (filter.getProperty().equalsIgnoreCase("pointName")) {
                pointName = value;
            } else if (filter.getProperty().equals("from")) {
                from = LocalDate.parse(value, formatter);
            } else if (filter.getProperty().equals("to")) {
                to = LocalDate.parse(value, formatter);
            } else if (filter.getProperty().equalsIgnoreCase("mobile")) {
                mobile = value;
            } else if (filter.getProperty().equalsIgnoreCase("meterCode")) {
                meterCode = value;
            } else if (filter.getProperty().equalsIgnoreCase("accountNo")) {
                accountNo = value;
            } else if (filter.getProperty().equalsIgnoreCase("incomeType")) {
                incomeType = PayIncome.IncomeType.valueOf(value);
            } else if (filter.getProperty().equalsIgnoreCase("businessType")) {
                businessType = PayIncome.BusinessType.valueOf(value);
            }
        }
        datePeriodCheck(from, to);

        // 组织查询条件
        Date dateFrom = Date.from(from.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date dateTo = DateUtil.endOfDay(Date.from(to.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        QPayIncome payIncome = QPayIncome.payIncome;
        List<Predicate> predicates = getReservePredicateList(communityId, regionId, pointName, mobile, meterCode, accountNo, incomeType, businessType);
        addRangePredicate(predicates, dateFrom, dateTo);
        // 根据营收总额倒序排序，分页查询出计量点集合
        QueryResults<Tuple> tupleQueryResults = jpaQueryFactory.select(payIncome.measuringPoint.community.name, payIncome.measuringPoint.id, payIncome.measuringPoint.name,
                payIncome.customer.mobile, payIncome.measuringPoint.meterCode, payIncome.measuringPoint.accountNo,
                payIncome.amount.sum(), payIncome.measuringPoint.companyId)
                .from(payIncome)
                .where(predicates.toArray(new Predicate[0]))
                .groupBy(payIncome.measuringPoint.id)
                .orderBy(payIncome.amount.sum().desc())
                .offset(params.getOffset())
                .limit(params.getLimit())
                .fetchResults();
        // 获取总数
        long total = tupleQueryResults.getTotal();
        // 获取当页查询结果
        List<Tuple> results = tupleQueryResults.getResults();
        if (CollectionUtils.isEmpty(results)) {
            return new PageImpl<>(Lists.newArrayList());
        }

        // 拿到计量点id集合
        List<Long> measuringPointIds = results.stream().map(tuple -> tuple.get(payIncome.measuringPoint.id)).collect(Collectors.toList());
        List<Predicate> measuringPointPredicates = new ArrayList<>();
        measuringPointPredicates.add(payIncome.incomeStatus.eq(PayIncome.IncomeStatus.PAY_SUCCESS));
        measuringPointPredicates.add(payIncome.measuringPoint.id.in(measuringPointIds));
        if (incomeType != null) {
            measuringPointPredicates.add(payIncome.incomeType.eq(incomeType));
        } else {
            measuringPointPredicates.add(payIncome.incomeType.in(PayIncome.IncomeType.reserveType()));
        }
        if (businessType != null) {
            measuringPointPredicates.add(payIncome.businessType.eq(businessType));
        }
        addRangePredicate(measuringPointPredicates, dateFrom, dateTo);
        // 查询所有计量点每日的预存数据，在内存中处理
        StringTemplate dayExpr = Expressions.stringTemplate("DATE_FORMAT({0},'%Y-%m-%d')", payIncome.payTime);
        List<Tuple> tupleList = jpaQueryFactory.select(payIncome.measuringPoint.id, dayExpr, payIncome.amount.sum())
                .from(payIncome)
                .where(measuringPointPredicates.toArray(new Predicate[0]))
                .groupBy(dayExpr, payIncome.measuringPoint.id)
                .fetch();
        // 转为key为计量点id，值为计量点每日营收数据的map
        Map<Long, List<Tuple>> measuringPointMap = tupleList.stream().collect(Collectors.groupingBy(tuple -> tuple.get(payIncome.measuringPoint.id)));

        List<MeasuringPointStatisticsDto> measuringPointStatisticsDtoList = new ArrayList<>();
        for (Tuple tuple : results) {
            MeasuringPointStatisticsDto measuringPointStatisticsDto = getReserveMeasuringPointNameAndSumAmount(tuple, payIncome);
            // 处理时间段内每日的数据
            List<Tuple> dayData = measuringPointMap.get(tuple.get(payIncome.measuringPoint.id));
            Map<String, Object> data = getFillMapDay(payIncome, dayData, dayExpr, from, to, formatter);
            measuringPointStatisticsDto.setData(data);
            measuringPointStatisticsDtoList.add(measuringPointStatisticsDto);
        }
        return new PageImpl<>(measuringPointStatisticsDtoList, params.getPageRequest(), total);
    }

    @Override
    public Page<MeasuringPointStatisticsDto> pageMeasuringPointReserveByMonth(PagingQueryParams<MeasuringPointStatisticsDto> params) {
        Long communityId = null;
        Long regionId = null;
        String pointName = null;
        String mobile = null;
        String meterCode = null;
        String accountNo = null;
        String year = null;
        PayIncome.IncomeType incomeType = null;
        PayIncome.BusinessType businessType = null;
        List<Filter> andFilters = params.getAndFilters();
        for (Filter filter : andFilters) {
            if (filter.getValue() == null || StringUtils.isBlank(filter.getValue().toString())) {
                continue;
            }
            String value = filter.getValue().toString();
            if (filter.getProperty().equalsIgnoreCase("year")) {
                year = value;
            } else if (filter.getProperty().equalsIgnoreCase("communityId")) {
                communityId = Long.parseLong(value);
            } else if (filter.getProperty().equalsIgnoreCase("regionId")) {
                regionId = Long.parseLong(value);
            } else if (filter.getProperty().equalsIgnoreCase("pointName")) {
                pointName = value;
            } else if (filter.getProperty().equalsIgnoreCase("mobile")) {
                mobile = value;
            } else if (filter.getProperty().equalsIgnoreCase("meterCode")) {
                meterCode = value;
            } else if (filter.getProperty().equalsIgnoreCase("accountNo")) {
                accountNo = value;
            } else if (filter.getProperty().equalsIgnoreCase("incomeType")) {
                incomeType = PayIncome.IncomeType.valueOf(value);
            } else if (filter.getProperty().equalsIgnoreCase("businessType")) {
                businessType = PayIncome.BusinessType.valueOf(value);
            }
        }
        // 必须提供年份选择
        if (StringUtils.isBlank(year)) {
            throw new BusinessException("请提供年份选择");
        }

        QPayIncome payIncome = QPayIncome.payIncome;
        List<Predicate> predicates = getReservePredicateList(communityId, regionId, pointName, mobile, meterCode, accountNo, incomeType, businessType);
        applyYearPredicate(payIncome, predicates, year);
        // 根据营收总额倒序排序，分页查询出计量点集合
        QueryResults<Tuple> tupleQueryResults = jpaQueryFactory.select(payIncome.measuringPoint.community.name, payIncome.measuringPoint.id, payIncome.measuringPoint.name,
                payIncome.customer.mobile, payIncome.measuringPoint.meterCode, payIncome.measuringPoint.accountNo,
                payIncome.amount.sum(), payIncome.measuringPoint.companyId)
                .from(payIncome)
                .where(predicates.toArray(new Predicate[0]))
                .groupBy(payIncome.measuringPoint.id)
                .orderBy(payIncome.amount.sum().desc())
                .offset(params.getOffset())
                .limit(params.getLimit())
                .fetchResults();
        // 获取总数
        long total = tupleQueryResults.getTotal();
        // 获取当页查询结果
        List<Tuple> results = tupleQueryResults.getResults();
        if (CollectionUtils.isEmpty(results)) {
            return new PageImpl<>(Lists.newArrayList());
        }

        List<MeasuringPointStatisticsDto> measuringPointStatisticsDtoList = new ArrayList<>();
        // 拿到计量点id集合
        List<Long> measuringPointIds = results.stream().map(tuple -> tuple.get(payIncome.measuringPoint.id)).collect(Collectors.toList());
        List<Predicate> measuringPointPredicates = new ArrayList<>();
        measuringPointPredicates.add(payIncome.incomeStatus.eq(PayIncome.IncomeStatus.PAY_SUCCESS));
        measuringPointPredicates.add(payIncome.measuringPoint.id.in(measuringPointIds));
        if (incomeType != null) {
            measuringPointPredicates.add(payIncome.incomeType.eq(incomeType));
        } else {
            measuringPointPredicates.add(payIncome.incomeType.in(PayIncome.IncomeType.reserveType()));
        }

        if (businessType != null) {
            measuringPointPredicates.add(payIncome.businessType.eq(businessType));
        }

        applyYearPredicate(payIncome, measuringPointPredicates, year);
        // 查询所有计量点每月的预存数据，在内存中处理
        StringTemplate monthExpr = Expressions.stringTemplate("DATE_FORMAT({0},'%Y-%m')", payIncome.payTime);
        List<Tuple> tupleList = jpaQueryFactory.select(payIncome.measuringPoint.id, monthExpr, payIncome.amount.sum())
                .from(payIncome)
                .where(measuringPointPredicates.toArray(new Predicate[0]))
                .groupBy(monthExpr, payIncome.measuringPoint.id)
                .fetch();
        // 转为key为计量点id，值为计量点每月营收数据的map
        Map<Long, List<Tuple>> measuringPointMap = tupleList.stream().collect(Collectors.groupingBy(tuple -> tuple.get(payIncome.measuringPoint.id)));
        String finalYear = year;
        // 循环分页结果，以免数据顺序被打乱
        results.forEach(tuple -> {
            MeasuringPointStatisticsDto measuringPointStatisticsDto = getReserveMeasuringPointNameAndSumAmount(tuple, payIncome);
            // 处理每个月的营收数据
            List<Tuple> monthData = measuringPointMap.get(tuple.get(payIncome.measuringPoint.id));
            Map<String, Object> data = getFillMapMonth(payIncome, monthData, monthExpr, finalYear);
            measuringPointStatisticsDto.setData(data);
            measuringPointStatisticsDtoList.add(measuringPointStatisticsDto);
        });
        return new PageImpl<>(measuringPointStatisticsDtoList, params.getPageRequest(), total);
    }

    private List<Predicate> getReservePredicateList(Long communityId, Long regionId, String pointName, String mobile, String meterCode,
                                                    String accountNo, PayIncome.IncomeType incomeType, PayIncome.BusinessType businessType) {
        QPayIncome payIncome = QPayIncome.payIncome;
        List<Predicate> predicates = new ArrayList<>();
        appendFilter(predicates, payIncome.measuringPoint);
        // 查询的是预存类型的营收情况
        predicates.add(payIncome.incomeType.in(PayIncome.IncomeType.reserveType()));
        predicates.add(payIncome.incomeStatus.eq(PayIncome.IncomeStatus.PAY_SUCCESS));
        if (communityId != null) {
            predicates.add(payIncome.measuringPoint.community.id.eq(communityId));
        }
        if (regionId != null) {
            List<RegionProjections.Community> communityList = communityRepository.findAll(RegionProjections.Community.class,
                    new Filter[]{byRegionId(regionId), Filter.isNull(Community_.PARENT)});
            if (CollUtil.isEmpty(communityList)) {
                predicates.add(payIncome.measuringPoint.community.id.eq(0L));
            } else {
                List<Long> communityIds = communityList.stream().map(BaseProjection::getId).collect(Collectors.toList());
                predicates.add(payIncome.measuringPoint.community.id.in(communityIds));
            }
        }
        if (StringUtils.isNotBlank(pointName)) {
            predicates.add(payIncome.measuringPoint.name.like(pointName));
        }
        if (StringUtils.isNotBlank(mobile)) {
            predicates.add(payIncome.customer.mobile.like(mobile));
        }
        if (StringUtils.isNotBlank(meterCode)) {
            predicates.add(payIncome.measuringPoint.meterCode.like(meterCode));
        }
        if (StringUtils.isNotBlank(accountNo)) {
            predicates.add(payIncome.measuringPoint.accountNo.like(accountNo));
        }
        if (incomeType != null) {
            predicates.add(payIncome.incomeType.eq(incomeType));
        }
        if (businessType != null) {
            predicates.add(payIncome.businessType.eq(businessType));
        }
        return predicates;
    }

    private void addRangePredicate(List<Predicate> predicates, Date from, Date to) {
        QPayIncome payIncome = QPayIncome.payIncome;
        predicates.add(payIncome.payTime.goe(from));
        predicates.add(payIncome.payTime.loe(to));
    }

    /**
     * 得到指定计量点Id的计量点名称和该计量点总金额
     *
     * @param tuple
     * @param payIncome
     * @return
     */
    private MeasuringPointStatisticsDto getReserveMeasuringPointNameAndSumAmount(Tuple tuple, QPayIncome payIncome) {
        MeasuringPointStatisticsDto measuringPointStatisticsDto = new MeasuringPointStatisticsDto();
        measuringPointStatisticsDto.setCommunityName(tuple.get(payIncome.measuringPoint.community.name));
        measuringPointStatisticsDto.setMeasuringPointName(tuple.get(payIncome.measuringPoint.name));
        measuringPointStatisticsDto.setMobile(tuple.get(payIncome.customer.mobile));
        measuringPointStatisticsDto.setMeterCode(tuple.get(payIncome.measuringPoint.meterCode));
        measuringPointStatisticsDto.setAccountNo(tuple.get(payIncome.measuringPoint.accountNo));
        measuringPointStatisticsDto.setCompanyId(tuple.get(payIncome.measuringPoint.companyId));
        measuringPointStatisticsDto.setValue(tuple.get(payIncome.amount.sum()));
        return measuringPointStatisticsDto;
    }

    /**
     * 应用年份条件
     *
     * @param payIncome
     * @param predicates
     * @param year
     */
    private void applyYearPredicate(QPayIncome payIncome, List<Predicate> predicates, String year) {
        Date firstDayStart = DateUtil.parse(year + "-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        Date lastDayEnd = DateUtil.parse(year + "-12-31 23:59:59", "yyyy-MM-dd HH:mm:ss");
        predicates.add(payIncome.payTime.between(firstDayStart, lastDayEnd));
    }
}
