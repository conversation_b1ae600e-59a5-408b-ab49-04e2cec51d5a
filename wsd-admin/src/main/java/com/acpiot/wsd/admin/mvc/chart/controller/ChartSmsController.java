package com.acpiot.wsd.admin.mvc.chart.controller;

import com.acpiot.wsd.admin.mvc.chart.dto.TenantDataStatisticsDto;
import com.acpiot.wsd.admin.mvc.chart.service.SmsChartService;
import com.acpiot.wsd.common.dto.CommonKeyValue;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by hsq on 2021-07-06-0000
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Controller
@RequestMapping("/chart/sms")
public class ChartSmsController {

    private final SmsChartService smsChartService;

    @GetMapping("show")
    public String show() {
        return "chart/sms/show";
    }

    /**
     * 查询指定时间区间的短信发送统计
     *
     * @param from
     * @param to
     * @return
     */
    @GetMapping("getSmsSendDataByDay")
    @ResponseBody
    public List<CommonKeyValue> getSmsSendDataByDay(@DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate from,
                                                    @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate to) {
        return smsChartService.getSmsSendDataByDay(from, to);
    }

    /**
     * 查询一年中所有月份的短信发送统计
     *
     * @param year
     * @return
     */
    @GetMapping("getSmsSendDataByMonth")
    @ResponseBody
    public List<CommonKeyValue> getSmsSendDataByMonth(@RequestParam("year") int year) {
        return smsChartService.getSmsSendDataByMonth(year);
    }

    /**
     * 分页查询时间区间内，各公司每天的短信发送统计
     *
     * @param params
     * @return
     */
    @PostMapping("pageTenantSmsSendByDay")
    @ResponseBody
    public Page<TenantDataStatisticsDto> pageTenantSmsSendByDay(@RequestBody PagingQueryParams<TenantDataStatisticsDto> params) {
        return smsChartService.pageTenantSmsSendByDay(params);
    }

    /**
     * 分页查询一年内，各公司每月的短信发送统计
     *
     * @param params
     * @return
     */
    @PostMapping("pageTenantSmsSendByMonth")
    @ResponseBody
    public Page<TenantDataStatisticsDto> pageTenantSmsSendByMonth(@RequestBody PagingQueryParams<TenantDataStatisticsDto> params) {
        return smsChartService.pageTenantSmsSendByMonth(params);
    }
}
