package com.acpiot.wsd.admin.mvc.chart.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.acpiot.wsd.admin.mvc.chart.dto.BillDetailDto;
import com.acpiot.wsd.admin.mvc.chart.dto.BillDetailSummaryDto;
import com.acpiot.wsd.admin.mvc.chart.service.BillDetailChartService;
import com.acpiot.wsd.admin.mvc.common.projections.BaseProjection;
import com.acpiot.wsd.admin.mvc.sys.projections.RegionProjections;
import com.acpiot.wsd.common.security.util.SecurityContextUtils;
import com.acpiot.wsd.data.archive.entity.Community_;
import com.acpiot.wsd.data.archive.entity.QCommunity;
import com.acpiot.wsd.data.archive.entity.QMeasuringPoint;
import com.acpiot.wsd.data.archive.entity.QWaterMeter;
import com.acpiot.wsd.data.archive.repository.CommunityRepository;
import com.acpiot.wsd.data.revenue.entity.Bill;
import com.acpiot.wsd.data.revenue.entity.QBill;
import com.acpiot.wsd.core.utils.LocalDateTimeUtils;
import com.acpiot.wsd.data.revenue.entity.QCustomer;
import com.google.common.collect.Lists;
import com.querydsl.core.QueryResults;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.Filter;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.acpiot.wsd.core.query.CommunityFilters.byRegionId;

/**
 * 账单明细相关统计接口
 * Created by hsq on 2021-07-09-0000
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class BillDetailChartServiceImpl implements BillDetailChartService {

    private final JPAQueryFactory jpaQueryFactory;
    private final CommunityRepository communityRepository;

    @Override
    public Page<BillDetailDto> pageBillDetail(PagingQueryParams<BillDetailDto> params) {
        // Long communityId, Long regionId, String pointName, String mobile, String meterCode, String accountNo, LocalDate from, LocalDate to
        QWaterMeter qWaterMeter = QWaterMeter.waterMeter;
        QMeasuringPoint qMeasuringPoint = qWaterMeter.measuringPoint;
        QCommunity qCommunity = qWaterMeter.community;
        QCustomer qCustomer = qMeasuringPoint.customer;
        LocalDate from = null;
        LocalDate to = null;
        for(Filter filter: params.getAndFilters()) {
            if (filter.getProperty().equalsIgnoreCase("from")) {
                from = LocalDate.parse(filter.getValue().toString());
            } else if (filter.getProperty().equalsIgnoreCase("to")) {
                to = LocalDate.parse(filter.getValue().toString());
            }
        }

        // 分页查询计量点，按照账单生成时间倒序排序，查询出本页的数据
        QueryResults<Tuple> tupleQueryResults = jpaQueryFactory.select(qMeasuringPoint.id, qCommunity.name, qMeasuringPoint.name,
                        qCustomer.mobile, qMeasuringPoint.meterCode, qMeasuringPoint.accountNo, qMeasuringPoint.companyId)
                .from(qWaterMeter)
                .where(getCommonPredicateList(params, qWaterMeter, qMeasuringPoint, qCommunity, qCustomer).toArray(new Predicate[0]))
                .offset(params.getOffset())
                .limit(params.getLimit())
                .fetchResults();
        // 获取总数
        long total = tupleQueryResults.getTotal();
        // 获取当页查询结果
        List<Tuple> results = tupleQueryResults.getResults();
        if (CollectionUtils.isEmpty(results)) {
            return new PageImpl<>(Lists.newArrayList());
        }
        List<BillDetailDto> billDetailDtoList = results.stream().map(tuple
                        -> new BillDetailDto(tuple.get(qMeasuringPoint.id), tuple.get(qCommunity.name), tuple.get(qMeasuringPoint.name),
                        tuple.get(qCustomer.mobile), tuple.get(qMeasuringPoint.meterCode), tuple.get(qMeasuringPoint.accountNo), tuple.get(qMeasuringPoint.companyId)))
                .collect(Collectors.toList());
        // 取出计量点id的集合
        List<Long> measuringPointIds = billDetailDtoList.stream().map(BillDetailDto::getMeasuringPointId).distinct().collect(Collectors.toList());
        // 构建账单查询条件
        QBill bill = QBill.bill;
        List<Predicate> billPredicates = new ArrayList<>();
        billPredicates.add(bill.measuringPoint.id.in(measuringPointIds));
        billPredicates.add(bill.billStatus.in(Bill.BillStatus.PAID, Bill.BillStatus.UNPAID));
        if (from != null && to != null) {
            billPredicates.add(bill.endDate.goe(LocalDateTimeUtils.toDate(from)));
            billPredicates.add(bill.endDate.loe(LocalDateTimeUtils.toDate(to)));
        }
        // 查询账单
        List<Bill> billList = jpaQueryFactory.select(bill)
                .from(bill)
                .where(billPredicates.toArray(new Predicate[0]))
                .fetch();
        if (CollUtil.isEmpty(billList)) {
            return new PageImpl<>(billDetailDtoList, params.getPageRequest(), total);
        }
        // 给计量点的统计结果赋值
        billDetailDtoList.forEach(billDetailDto -> {
            // 找到当前计量点的账单数据
            List<Bill> measuringPointBillList = billList.stream().filter(b
                    -> Objects.equals(b.getMeasuringPoint().getId(), billDetailDto.getMeasuringPointId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(measuringPointBillList)) {
                // 应收水量：所有账单的总用水量之和
                BigDecimal receivableConsumption = measuringPointBillList.stream().map(Bill::getTotalUsedTon).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                billDetailDto.setReceivableConsumption(receivableConsumption);
                // 已收水量：所有已支付账单的总用水量之和
                BigDecimal receivedConsumption = measuringPointBillList.stream().filter(b -> Objects.equals(b.getBillStatus(), Bill.BillStatus.PAID))
                        .map(Bill::getTotalUsedTon).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                billDetailDto.setReceivedConsumption(receivedConsumption);
                // 应收水费：所有账单的金额之和
                BigDecimal receivableAmount = measuringPointBillList.stream().map(Bill::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                billDetailDto.setReceivableAmount(receivableAmount);
                // 已收水费：所有已支付账单的金额之和
                BigDecimal receivedAmount = measuringPointBillList.stream().filter(b -> Objects.equals(b.getBillStatus(), Bill.BillStatus.PAID))
                        .map(Bill::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                billDetailDto.setReceivedAmount(receivedAmount);
                // 未收水费：应收水费 - 已收水费
                billDetailDto.setUnreceivedAmount(receivableAmount.subtract(receivedAmount));
                // 应收污水费：所有账单的污水费之和
                BigDecimal receivableWasteWaterFee = measuringPointBillList.stream()
                        .map(Bill::getWasteWaterFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                billDetailDto.setReceivableWasteWaterFee(receivableWasteWaterFee);
                // 已收污水费：所有已支付账单的污水费之和
                BigDecimal receivedWasteWaterFee = measuringPointBillList.stream().filter(b -> Objects.equals(b.getBillStatus(), Bill.BillStatus.PAID))
                        .map(Bill::getWasteWaterFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                billDetailDto.setReceivedWasteWaterFee(receivedWasteWaterFee);
                // 未收污水费：应收污水费 - 已收污水费
                billDetailDto.setUnreceivedWasteWaterFee(receivableWasteWaterFee.subtract(receivedWasteWaterFee));
            }
        });
        return new PageImpl<>(billDetailDtoList, params.getPageRequest(), total);
    }

    @Override
    public BillDetailSummaryDto getBillDetailSummary(PagingQueryParams<BillDetailDto> params) {
        // 平台用户不返回
        if (SecurityContextUtils.isPlatformUser()) {
            return null;
        }
        QWaterMeter qWaterMeter = QWaterMeter.waterMeter;
        QMeasuringPoint qMeasuringPoint = qWaterMeter.measuringPoint;
        QCommunity qCommunity = qWaterMeter.community;
        QCustomer qCustomer = qMeasuringPoint.customer;
        List<Long> measuringPointIds = jpaQueryFactory.select(qMeasuringPoint.id)
                .from(qWaterMeter)
                .where(getCommonPredicateList(params, qWaterMeter, qMeasuringPoint, qCommunity, qCustomer).toArray(new Predicate[0]))
                .fetch();
        if (CollUtil.isEmpty(measuringPointIds)) {
            return null;
        }
        LocalDate from = null;
        LocalDate to = null;
        for(Filter filter: params.getAndFilters()) {
            if (filter.getProperty().equalsIgnoreCase("from")) {
                from = LocalDate.parse(filter.getValue().toString());
            } else if (filter.getProperty().equalsIgnoreCase("to")) {
                to = LocalDate.parse(filter.getValue().toString());
            }
        }
        QBill bill = QBill.bill;
        List<Predicate> billPredicates = new ArrayList<>();
        billPredicates.add(bill.measuringPoint.id.in(measuringPointIds));
        billPredicates.add(bill.billStatus.in(Bill.BillStatus.PAID, Bill.BillStatus.UNPAID));
        if (from != null && to != null) {
            billPredicates.add(bill.endDate.goe(LocalDateTimeUtils.toDate(from)));
            billPredicates.add(bill.endDate.loe(LocalDateTimeUtils.toDate(to)));
        }
        BillDetailSummaryDto billDetailSummaryDto = new BillDetailSummaryDto();
        Tuple receivableTuple = jpaQueryFactory.select(bill.value.sum().subtract(bill.lastValue.sum())
                        .add(bill.preUsedTon.sum()).subtract(bill.reduce.sum()).subtract(bill.childUsedTon.sum()),
                        bill.amount.sum(), bill.wasteWaterFee.sum())
                .from(bill)
                .where(billPredicates.toArray(new Predicate[0]))
                .fetchOne();
        if (receivableTuple != null) {
            // 应收水量：所有账单的总用水量之和
            billDetailSummaryDto.setReceivableConsumption(ObjectUtil.defaultIfNull(receivableTuple.get(0, BigDecimal.class), BigDecimal.ZERO));
            // 应收水费：所有账单的金额之和
            billDetailSummaryDto.setReceivableAmount(ObjectUtil.defaultIfNull(receivableTuple.get(1, BigDecimal.class), BigDecimal.ZERO));
            // 应收污水费：所有账单的污水费之和
            billDetailSummaryDto.setReceivableWasteWaterFee(ObjectUtil.defaultIfNull(receivableTuple.get(2, BigDecimal.class), BigDecimal.ZERO));
        }

        billPredicates.remove(bill.billStatus.in(Bill.BillStatus.PAID, Bill.BillStatus.UNPAID));
        billPredicates.add(bill.billStatus.eq(Bill.BillStatus.PAID));
        Tuple receivedTuple = jpaQueryFactory.select(bill.value.sum().subtract(bill.lastValue.sum())
                        .add(bill.preUsedTon.sum()).subtract(bill.reduce.sum()).subtract(bill.childUsedTon.sum()),
                        bill.amount.sum(), bill.wasteWaterFee.sum())
                .from(bill)
                .where(billPredicates.toArray(new Predicate[0]))
                .fetchOne();
        if (receivedTuple != null) {
            // 已收水量：所有已支付账单的总用水量之和
            billDetailSummaryDto.setReceivedConsumption(ObjectUtil.defaultIfNull(receivedTuple.get(0, BigDecimal.class), BigDecimal.ZERO));
            // 已收水费：所有已支付账单的金额之和
            billDetailSummaryDto.setReceivedAmount(ObjectUtil.defaultIfNull(receivedTuple.get(1, BigDecimal.class), BigDecimal.ZERO));
            // 已收污水费：所有已支付账单的污水费之和
            billDetailSummaryDto.setReceivedWasteWaterFee(ObjectUtil.defaultIfNull(receivedTuple.get(2, BigDecimal.class), BigDecimal.ZERO));
        }
        // 未收水费：应收水费 - 已收水费
        billDetailSummaryDto.setUnreceivedAmount(ObjectUtil.defaultIfNull(billDetailSummaryDto.getReceivableAmount(), BigDecimal.ZERO)
                .subtract(ObjectUtil.defaultIfNull(billDetailSummaryDto.getReceivedAmount(), BigDecimal.ZERO)));
        // 未收污水费：应收污水费 - 已收污水费
        billDetailSummaryDto.setUnreceivedWasteWaterFee(ObjectUtil.defaultIfNull(billDetailSummaryDto.getReceivableWasteWaterFee(), BigDecimal.ZERO)
                .subtract(ObjectUtil.defaultIfNull(billDetailSummaryDto.getReceivedWasteWaterFee(), BigDecimal.ZERO)));
        return billDetailSummaryDto;
    }

    private List<Predicate> getCommonPredicateList(PagingQueryParams<BillDetailDto> params, QWaterMeter waterMeter,
                                                   QMeasuringPoint measuringPoint, QCommunity community, QCustomer customer) {
        List<Filter> andFilters = params.getAndFilters();
        List<Predicate> predicateList = new ArrayList<>();
        for (Filter filter : andFilters) {
            if (filter.getValue() == null || StringUtils.isBlank(filter.getValue().toString())) {
                continue;
            }
            if (filter.getProperty().equalsIgnoreCase("communityId")) {
                long communityId = Long.parseLong(filter.getValue().toString());
                predicateList.add(community.id.eq(communityId));
            } else if (filter.getProperty().equalsIgnoreCase("regionId")) {
                long regionId = Long.parseLong(filter.getValue().toString());
                List<RegionProjections.Community> communityList = communityRepository.findAll(RegionProjections.Community.class,
                        new Filter[]{byRegionId(regionId), Filter.isNull(Community_.PARENT)});
                if (CollUtil.isEmpty(communityList)) {
                    predicateList.add(community.id.eq(0L));
                } else {
                    List<Long> communityIds = communityList.stream().map(BaseProjection::getId).collect(Collectors.toList());
                    predicateList.add(community.id.in(communityIds));
                }
            } else if (filter.getProperty().equalsIgnoreCase("pointName")) {
                predicateList.add(measuringPoint.name.like("%" + filter.getValue().toString() + "%"));
            } else if (filter.getProperty().equalsIgnoreCase("mobile")) {
                predicateList.add(customer.mobile.eq(filter.getValue().toString()));
            } else if (filter.getProperty().equalsIgnoreCase("meterCode")) {
                predicateList.add(waterMeter.code.eq(filter.getValue().toString()));
            } else if (filter.getProperty().equalsIgnoreCase("accountNo")) {
                predicateList.add(measuringPoint.accountNo.eq(filter.getValue().toString()));
            }
        }
        return predicateList;
    }
}
