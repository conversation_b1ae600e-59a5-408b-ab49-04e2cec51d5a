package com.acpiot.wsd.miniprogram.service.impl;

import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.common.service.BasePaymentService;
import com.acpiot.wsd.data.archive.entity.MeasuringPoint;
import com.acpiot.wsd.data.archive.entity.MeasuringPoint_;
import com.acpiot.wsd.data.archive.repository.MeasuringPointRepository;
import com.acpiot.wsd.miniprogram.service.ReserveMiniService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.PagingQueryParams;

@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class ReserveMiniServiceImpl implements ReserveMiniService {

    private final MeasuringPointRepository measuringPointRepository;
    @Delegate
    private final BasePaymentService basePaymentService;

    @Override
    @Transactional(readOnly = true)
    public Page<MeasuringPoint> pagedMeasuringPoint(PagingQueryParams<MeasuringPoint> params) {
        params.fetch(MeasuringPoint_.CUSTOMER);
        params.fetch(MeasuringPoint_.COMMUNITY);
        params.fetch(MeasuringPoint_.LOWER_COMMUNITY);
        return measuringPointRepository.findAll(params);
    }

    @Override
    @Transactional(readOnly = true)
    public MeasuringPoint getMeasuringPointById(Long measuringPointId) {
        return measuringPointRepository.findById(measuringPointId).orElseThrow(
                () -> new BusinessException("未找到该计量点"));
    }
}
