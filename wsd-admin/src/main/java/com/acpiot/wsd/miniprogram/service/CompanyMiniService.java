package com.acpiot.wsd.miniprogram.service;

import com.acpiot.wsd.data.sys.entity.Company;

import java.util.List;
import java.util.Optional;

/**
 * 公司服务
 *
 * <AUTHOR>
 * @since 2023/12/18
 */
public interface CompanyMiniService {

    Optional<Company> findByCompanyId(String companyId);

    List<Company> getCompaniesByIds(List<Long> ids);

    /**
     * 查询所有公司
     *
     * @return
     */
    List<Company> getAllCompanies();

    /**
     * 查询指定公司编码下的所有直接和间接子级公司
     *
     * @param layerCode
     * @return
     */
    List<Company> findAllChildrenCompanies(String layerCode);

    /**
     * 根据CompanyId查询对应公司列表
     *
     * @param companyIds
     * @return
     */
    List<Company> getCompaniesByCompanyIds(List<String> companyIds);
}
