package com.acpiot.wsd.miniprogram.service.impl;

import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.common.security.util.SecurityContextUtils;
import com.acpiot.wsd.data.archive.entity.Community_;
import com.acpiot.wsd.data.archive.entity.MeasuringPoint_;
import com.acpiot.wsd.data.monitor.entity.DayFreezeData;
import com.acpiot.wsd.data.monitor.entity.DayFreezeData_;
import com.acpiot.wsd.data.monitor.repository.DayFreezeDataRepository;
import com.acpiot.wsd.miniprogram.service.DayFreezeDataMiniService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.PagingQueryParams;

/**
 * 冻结数据Service实现类
 * Created by zc on 2022/2/11 11:03
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DayFreezeDataMiniServiceImpl implements DayFreezeDataMiniService {

    private final DayFreezeDataRepository dayFreezeDataRepository;

    @Override
    public Page<DayFreezeData> pageDayFreezeData(PagingQueryParams<DayFreezeData> params) {
        String companyId = StrUtil.format("{}.{}", DayFreezeData_.MEASURING_POINT, MeasuringPoint_.COMPANY_ID);
        String communityId = StrUtil.format("{}.{}.{}", DayFreezeData_.MEASURING_POINT, MeasuringPoint_.COMMUNITY, Community_.ID);
        SecurityContextUtils.getGlobalFilter(companyId, communityId).ifPresent(params::and);
        return dayFreezeDataRepository.findAll(params);
    }
}