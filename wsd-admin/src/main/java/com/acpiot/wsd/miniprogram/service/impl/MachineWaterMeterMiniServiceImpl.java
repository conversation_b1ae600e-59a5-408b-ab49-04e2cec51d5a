package com.acpiot.wsd.miniprogram.service.impl;

import com.acpiot.wsd.common.service.BaseMachineWaterMeterService;
import com.acpiot.wsd.data.archive.entity.MeterBook;
import com.acpiot.wsd.data.archive.repository.MeterBookRepository;
import com.acpiot.wsd.miniprogram.service.MachineWaterMeterMiniService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.PagingQueryParams;

@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class MachineWaterMeterMiniServiceImpl implements MachineWaterMeterMiniService {

    @Delegate
    private final BaseMachineWaterMeterService baseMachineWaterMeterService;
    private final MeterBookRepository meterBookRepository;

    @Override
    @Transactional(readOnly = true)
    public Page<MeterBook> pageMeterBook(PagingQueryParams<MeterBook> params) {
        return meterBookRepository.findAll(params);
    }
}
