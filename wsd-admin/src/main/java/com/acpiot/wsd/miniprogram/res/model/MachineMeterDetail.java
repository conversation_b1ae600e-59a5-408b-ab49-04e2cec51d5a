package com.acpiot.wsd.miniprogram.res.model;

import com.acpiot.wsd.data.archive.entity.MeasuringPoint;
import com.acpiot.wsd.data.archive.entity.WaterMeter;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 机械表用户详情
 */
@Data
public class MachineMeterDetail {

    /**
     * 计量点名称
     */
    private String measuringPointName;

    /**
     * 小区名称
     */
    private String communityName;

    /**
     * 表号
     */
    private String meterCode;

    /**
     * 户号
     */
    private String accountNo;

    /**
     * 联系电话
     */
    private String mobile;

    /**
     * 户主名
     */
    private String houseHolder;

    /**
     * 水表口径
     */
    private Integer diameter;

    /**
     * 型号名称
     */
    private String meterModelName;

    /**
     * 是否有阀
     */
    private boolean valve;

    /**
     * 上月读数
     */
    private BigDecimal lastMonthValue;

    /**
     * 上月读数时间
     */
    private LocalDate lastMonthMeterDate;

    /**
     * 旧表读数
     */
    private BigDecimal oldMeterValue;

    /**
     * 新表读数
     */
    private BigDecimal newMeterValue;

    /**
     * 本月读数
     */
    private BigDecimal curMonthValue;

    /**
     * 本月读数时间
     */
    private LocalDate curMonthMeterDate;

    /**
     * 抄表异常
     */
    private String abnormalMeter;

    /**
     * 本月用水量
     */
    private BigDecimal curMonthConsumption;

    /**
     * 本月水费
     */
    private BigDecimal curMonthAmount;

    /**
     * 预存水费余额
     */
    private BigDecimal balance;

    /**
     * 欠费金额
     */
    private BigDecimal oweAmount;

    /**
     * 是否第一期
     */
    private boolean first;

    public static MachineMeterDetail of(WaterMeter waterMeter) {
        MachineMeterDetail machineMeterDetail = new MachineMeterDetail();
        machineMeterDetail.setMeasuringPointName(waterMeter.getMeasuringPoint().getName());
        machineMeterDetail.setCommunityName(waterMeter.getCommunity().getName());
        MeasuringPoint measuringPoint = waterMeter.getMeasuringPoint();
        machineMeterDetail.setMeterCode(waterMeter.getCode());
        machineMeterDetail.setAccountNo(measuringPoint.getAccountNo());
        machineMeterDetail.setMobile(measuringPoint.getMobile());
        machineMeterDetail.setHouseHolder(measuringPoint.getCustomer() != null ? measuringPoint.getCustomer().getName() : "");
        machineMeterDetail.setDiameter(waterMeter.getDiameter());
        machineMeterDetail.setMeterModelName(waterMeter.getModel().getName());
        machineMeterDetail.setValve(waterMeter.isValve());
        machineMeterDetail.setNewMeterValue(waterMeter.getStartValue());
        machineMeterDetail.setBalance(measuringPoint.getBalance());
        return machineMeterDetail;
    }
}
