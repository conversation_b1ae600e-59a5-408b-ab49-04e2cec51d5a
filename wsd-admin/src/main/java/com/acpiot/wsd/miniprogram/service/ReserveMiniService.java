package com.acpiot.wsd.miniprogram.service;

import com.acpiot.wsd.data.archive.entity.MeasuringPoint;
import com.acpiot.wsd.data.revenue.entity.PayIncome;
import org.springframework.data.domain.Page;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.math.BigDecimal;

public interface ReserveMiniService {
    /**
     * 查询计量点列表
     *
     * @param params
     * @return
     */
    Page<MeasuringPoint> pagedMeasuringPoint(PagingQueryParams<MeasuringPoint> params);

    /**
     * 现金预存
     *
     * @param measuringPointId
     * @param amount
     * @param payCustomerName
     * @param remark
     */
    PayIncome cashReserve(long measuringPointId, BigDecimal amount, String payCustomerName, String remark, PayIncome.BusinessType businessType, boolean bankTransfer);

    /**
     * 获取计量点信息
     *
     * @param measuringPointId
     * @return
     */
    MeasuringPoint getMeasuringPointById(Long measuringPointId);
}
