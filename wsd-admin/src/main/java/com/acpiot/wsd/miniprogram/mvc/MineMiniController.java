package com.acpiot.wsd.miniprogram.mvc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.common.aop.annotation.NoCompanyFilter;
import com.acpiot.wsd.common.aop.annotation.NoRepeatSubmit;
import com.acpiot.wsd.common.aop.annotation.SystemLog;
import com.acpiot.wsd.common.security.jwt.JwtTokenProvider;
import com.acpiot.wsd.common.security.util.SecurityContextUtils;
import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.common.validate.ValidCodeManager;
import com.acpiot.wsd.core.sms.adapter.SmsServiceAdapter;
import com.acpiot.wsd.core.sms.bean.SmsResult;
import com.acpiot.wsd.data.sys.entity.Company;
import com.acpiot.wsd.data.sys.entity.User;
import com.acpiot.wsd.miniprogram.res.model.UserModel;
import com.acpiot.wsd.miniprogram.service.CompanyMiniService;
import com.acpiot.wsd.miniprogram.service.LoginMiniService;
import com.acpiot.wsd.miniprogram.service.MineMiniService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import pers.mx.jupiter.web.util.ResponseUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.acpiot.wsd.common.validate.ValidCodeManager.generateCode;
import static com.acpiot.wsd.miniprogram.config.crypto.RSACoder.decrypt;

/**
 * "我的"控制层
 */
@RestController
@RequestMapping("/miniprogram/auth/mine")
@RequiredArgsConstructor
public class MineMiniController extends BaseMiniController {

    private final MineMiniService mineMiniService;
    private final ValidCodeManager validCodeManager;
    private final SmsServiceAdapter smsServiceAdapter;
    private final CompanyMiniService companyMiniService;
    private final LoginMiniService loginMiniService;
    private final JwtTokenProvider jwtTokenProvider;

    /**
     * 修改当前登陆用户的个人信息
     *
     * @param user
     * @return
     */
    @NoRepeatSubmit
    @SystemLog(description = "小程序-修改个人信息")
    @PutMapping("updateMineInfo")
    public ResponseEntity<?> info(@RequestBody User user) {
        try {
            mineMiniService.updateInfo(user);
            return ResponseUtils.ok("更新成功");
        } catch (Exception e) {
            return ResponseUtils.error(e.getMessage());
        }
    }

    /**
     * 发送短信验证码
     */
    @GetMapping("/smsCode")
    public void createSmsCode() {
        User user = mineMiniService.getInfo();
        String mobile = user.getMobile();
        if (StrUtil.isBlank(mobile)) {
            throw new BusinessException("当前用户未设置手机号");
        }

        final String code = generateCode();

        // 将验证码存入内存缓存中
        ValidCodeManager.ValidCodeKey key = ValidCodeManager.ValidCodeKey.of(mobile, ValidCodeManager.CodeType.SMS, ValidCodeManager.CodeUse.UPDATE_PASSWORD);
        validCodeManager.put(key, code);

        // 发送修改密码验证码
        SmsResult result = smsServiceAdapter.updatePasswordCode(user, code);
        // 判断是否发送成功
        if (!result.isResult()) {
            throw new BusinessException(String.format("短信验证码发送失败，error=%s", result.getMsg()));
        }
    }

    @NoRepeatSubmit
    @SystemLog(description = "小程序-通过旧密码修改个人密码")
    @PutMapping("updatePasswordByOldPassword")
    public ResponseEntity<?> updatePasswordByOldPassword(@RequestParam("oldPassword") String oldPassword,
                                                         @RequestParam("newPassword") String newPassword) {
        if (mineMiniService.changePassword(decrypt(oldPassword), decrypt(newPassword))) {
            return ResponseUtils.ok("修改密码成功");
        } else {
            return ResponseUtils.error("旧密码错误");
        }
    }

    @NoRepeatSubmit
    @SystemLog(description = "小程序-通过短信验证码修改个人密码")
    @PutMapping("updatePasswordBySmsCode")
    public ResponseEntity<?> updatePasswordBySmsCode(@RequestParam("newPassword") String newPassword, @RequestParam("smsCode") String smsCode) {
        mineMiniService.updatePasswordBySmsCode(decrypt(newPassword), smsCode);
        return ResponseUtils.ok("修改密码成功");
    }

    /**
     * 1. 平台超级管理员查询指定公司;
     * 2. 公司超级管理员查询指定的子公司的数据;
     * 3. 平台超级管理员切换回平台身份；
     *
     * @param companyIdArr
     * @return
     */
    @NoCompanyFilter
    @PostMapping("loginCompany")
    @ResponseBody
    public UserModel loginCompany(@RequestParam("companyIds") String companyIdArr) {
        // 只有超级管理员权限的账户支持此操作
        if (SecurityContextUtils.getLoginUserType() == User.UserType.COMPANY && !SecurityContextUtils.hasRoleAdmin()) {
            throw new BusinessException("当前账号不支持此操作");
        }
        String username = SecurityContextUtils.getAdminUser().getUsername();
        User user = loginMiniService.getUserByName(username).orElseThrow(() -> new BusinessException("不存在当前用户"));
        // 判断是否为平台账户切换回平台身份
        if (SecurityContextUtils.isPlatformUser() && "0".equals(companyIdArr)) {
            String token = jwtTokenProvider.generateMiniToken(user);
            return handleLoginUser(token, user);
        }
        // 查询目标公司
        List<Long> ids = Arrays.stream(companyIdArr.split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<Company> companies = companyMiniService.getCompaniesByIds(ids);
        if (CollUtil.isEmpty(companies)) {
            throw new BusinessException("不存在对应的公司");
        }
        // 判断当前账号是否有查看权限
        if (SecurityContextUtils.isCompanyUser()) {
            if (!companies.stream().allMatch(c -> c.getLayerCode().startsWith(Objects.requireNonNull(SecurityContextUtils.getCompanyLayerCode())))) {
                throw new BusinessException("选择了无权限操作的公司");
            }
            companyMiniService.findByCompanyId(SecurityContextUtils.getCompanyId()).ifPresent(company -> {
                user.setCompany(company);
                user.setLayerCode(company.getLayerCode());
            });
        }
        // 设置当前账号查询指定公司的数据
        String[] companyIds = companies.stream().map(Company::getCompanyId).toArray(String[]::new);
        user.setCompanyIds(companyIds);
        String token = jwtTokenProvider.generateMiniToken(user);
        return handleLoginUser(token, user);
    }
}
