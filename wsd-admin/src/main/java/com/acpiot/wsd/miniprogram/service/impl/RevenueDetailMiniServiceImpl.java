package com.acpiot.wsd.miniprogram.service.impl;

import cn.hutool.core.date.DateUtil;
import com.acpiot.wsd.common.dto.CommonKeyValue;
import com.acpiot.wsd.common.dto.CommunityDataStatisticsDto;
import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.data.revenue.entity.PayIncome;
import com.acpiot.wsd.data.revenue.entity.QPayIncome;
import com.acpiot.wsd.miniprogram.service.RevenueDetailMiniService;
import com.acpiot.wsd.common.utils.StatisticsDataFillUtil;
import com.google.common.collect.Lists;
import com.querydsl.core.QueryResults;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.StringTemplate;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.Filter;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.acpiot.wsd.common.security.util.SecurityContextUtils.getGlobalFilter;
import static com.acpiot.wsd.common.utils.StatisticsDataFillUtil.datePeriodCheck;
import static com.acpiot.wsd.common.utils.StatisticsDataFillUtil.fillZeroKeyValue;

/**
 * 营收明细统计
 * Created by zc on 2022/3/17 9:15
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class RevenueDetailMiniServiceImpl implements RevenueDetailMiniService {

    private final JPAQueryFactory jpaQueryFactory;

    @Override
    public List<CommonKeyValue> getRevenueDataByDay(Long communityId, LocalDate from, LocalDate to, PayIncome.IncomeType incomeType, PayIncome.BusinessType businessType) {
        datePeriodCheck(from, to);
        Date dateFrom = Date.from(from.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date dateTo = DateUtil.endOfDay(Date.from(to.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        QPayIncome payIncome = QPayIncome.payIncome;
        StringTemplate dayExpr = Expressions.stringTemplate("DATE_FORMAT({0},'%Y-%m-%d')", payIncome.payTime);
        List<Predicate> predicates = getPayIncomeSuccessPredicates(communityId, dateFrom, dateTo, incomeType, businessType, payIncome);
        List<CommonKeyValue> commonKeyValues = getCommonKeyValues(predicates, payIncome, dayExpr);
        return fillZeroKeyValue(commonKeyValues, dateFrom, dateTo, StatisticsDataFillUtil.KeyType.DAY);
    }

    @Override
    public List<CommonKeyValue> getRevenueDataByMonth(Long communityId, int year, PayIncome.IncomeType incomeType, PayIncome.BusinessType businessType) {
        QPayIncome payIncome = QPayIncome.payIncome;
        List<Predicate> predicates = new ArrayList<>();
        getGlobalFilter(payIncome.measuringPoint.companyId, payIncome.measuringPoint.community.id).ifPresent(predicates::addAll);
        applyYearPredicate(payIncome, predicates, String.valueOf(year));
        predicates.add(payIncome.incomeStatus.eq(PayIncome.IncomeStatus.PAY_SUCCESS));
        if (communityId != null) {
            predicates.add(payIncome.measuringPoint.community.id.eq(communityId));
        }

        StringTemplate monthExpr = Expressions.stringTemplate("DATE_FORMAT({0},'%Y-%m')", payIncome.payTime);
        List<CommonKeyValue> commonKeyValues = getCommonKeyValues(predicates, payIncome, monthExpr);
        LocalDate start = LocalDate.of(year, 1, 1);
        LocalDate end = LocalDate.of(year, 12, 31);
        return fillZeroKeyValue(commonKeyValues, Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant()),
                Date.from(end.atStartOfDay(ZoneId.systemDefault()).toInstant()), StatisticsDataFillUtil.KeyType.MONTH);
    }

    @Override
    public Page<CommunityDataStatisticsDto> pageCommunityRevenueByDay(PagingQueryParams<CommunityDataStatisticsDto> params) {
        // 从请求参数中取出所需查询条件
        LocalDate from = null;
        LocalDate to = null;
        Long communityId = null;
        PayIncome.BusinessType businessType = null;
        PayIncome.IncomeType incomeType = null;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (Filter filter : params.getAndFilters()) {
            if (filter.getValue() == null || StringUtils.isBlank(filter.getValue().toString())) {
                continue;
            }
            if (filter.getProperty().equalsIgnoreCase("from")) {
                from = LocalDate.parse(filter.getValue().toString(), formatter);
            } else if (filter.getProperty().equalsIgnoreCase("to")) {
                to = LocalDate.parse(filter.getValue().toString(), formatter);
            } else if (filter.getProperty().equalsIgnoreCase("communityId")) {
                communityId = Long.parseLong(filter.getValue().toString());
            } else if (filter.getProperty().equalsIgnoreCase("incomeType")) {
                incomeType = PayIncome.IncomeType.valueOf(filter.getValue().toString());
            } else if (filter.getProperty().equalsIgnoreCase("businessType")) {
                businessType = PayIncome.BusinessType.valueOf(filter.getValue().toString());
            }
        }

        datePeriodCheck(from, to);

        // 组织查询条件
        QPayIncome payIncome = QPayIncome.payIncome;
        Date fromQuery = Date.from(from.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date toQuery = DateUtil.endOfDay(Date.from(to.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        List<Predicate> predicates = getPayIncomeSuccessPredicates(communityId, fromQuery, toQuery, incomeType, businessType, payIncome);

        // 根据营收总额倒序排序，分页查询出小区id集合
        QueryResults<Tuple> tupleQueryResults = getTupleQueryResults(payIncome, predicates, params);
        if (CollectionUtils.isEmpty(tupleQueryResults.getResults())) {
            return new PageImpl<>(Lists.newArrayList());
        }
        List<CommunityDataStatisticsDto> communityDataStatisticsDtoList = tupleQueryResults.getResults().stream().map(tuple ->
                new CommunityDataStatisticsDto(tuple.get(payIncome.measuringPoint.community.id), tuple.get(payIncome.measuringPoint.community.name), tuple.get(payIncome.amount.sum()))
        ).collect(Collectors.toList());
        return new PageImpl<>(communityDataStatisticsDtoList, params.getPageRequest(), tupleQueryResults.getTotal());
    }

    @Override
    public Page<CommunityDataStatisticsDto> pageCommunityRevenueByMonth(PagingQueryParams<CommunityDataStatisticsDto> params) {
        QPayIncome payIncome = QPayIncome.payIncome;
        List<Predicate> predicates = new ArrayList<>();
        getGlobalFilter(payIncome.measuringPoint.companyId, payIncome.measuringPoint.community.id).ifPresent(predicates::addAll);
        List<Filter> andFilters = params.getAndFilters();
        String year = null;

        // 从分页对象中取出时间区间条件
        for (Filter filter : andFilters) {
            if (filter.getValue() == null || StringUtils.isBlank(filter.getValue().toString())) {
                continue;
            }
            if (filter.getProperty().equalsIgnoreCase("year")) {
                year = String.valueOf(filter.getValue());
                applyYearPredicate(payIncome, predicates, year);
            } else if (filter.getProperty().equalsIgnoreCase("communityId")) {
                predicates.add(payIncome.measuringPoint.community.id.eq(Long.parseLong(filter.getValue().toString())));
            }
        }
        // 必须提供查询年份
        if (StringUtils.isBlank(year)) {
            throw new BusinessException("请提供查询年份");
        }

        predicates.add(payIncome.incomeStatus.eq(PayIncome.IncomeStatus.PAY_SUCCESS));
        // 根据营收总额倒序排序，分页查询出小区id集合
        QueryResults<Tuple> tupleQueryResults = getTupleQueryResults(payIncome, predicates, params);
        if (CollectionUtils.isEmpty(tupleQueryResults.getResults())) {
            return new PageImpl<>(Lists.newArrayList());
        }
        List<CommunityDataStatisticsDto> communityDataStatisticsDtoList = tupleQueryResults.getResults().stream().map(tuple ->
                new CommunityDataStatisticsDto(tuple.get(payIncome.measuringPoint.community.id), tuple.get(payIncome.measuringPoint.community.name), tuple.get(payIncome.amount.sum()))
        ).collect(Collectors.toList());
        return new PageImpl<>(communityDataStatisticsDtoList, params.getPageRequest(), tupleQueryResults.getTotal());
    }

    @Override
    public List<CommonKeyValue> getCommunityRevenueMonthByCommunityId(Long communityId, int year, PayIncome.IncomeType incomeType, PayIncome.BusinessType businessType) {
        QPayIncome payIncome = QPayIncome.payIncome;
        List<Predicate> predicates = new ArrayList<>();
        getGlobalFilter(payIncome.measuringPoint.companyId, payIncome.measuringPoint.community.id).ifPresent(predicates::addAll);
        predicates.add(payIncome.incomeStatus.eq(PayIncome.IncomeStatus.PAY_SUCCESS));
        predicates.add(payIncome.measuringPoint.community.id.eq(communityId));
        applyYearPredicate(payIncome, predicates, String.valueOf(year));
        StringTemplate monthExpr = Expressions.stringTemplate("DATE_FORMAT({0},'%Y-%m')", payIncome.payTime);
        List<CommonKeyValue> result = jpaQueryFactory.select(monthExpr, payIncome.amount.sum())
                .from(payIncome)
                .where(predicates.toArray(new Predicate[0]))
                .groupBy(monthExpr)
                .fetch().stream().map(tuple ->
                        new CommonKeyValue(tuple.get(monthExpr), tuple.get(payIncome.amount.sum()))).collect(Collectors.toList());
        LocalDate to = LocalDate.of(year, 12, 31);
        LocalDate fromTemp = LocalDate.of(year, 1, 1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        do {
            String key = fromTemp.format(formatter);
            boolean exist = result.stream().anyMatch(commonKeyValue -> commonKeyValue.getKey().equals(key));
            if (!exist) {
                result.add(new CommonKeyValue(key, 0));
            }
            fromTemp = fromTemp.plusMonths(1);
        } while (!fromTemp.isAfter(to));
        // 将日期排序
        result.sort(Comparator.comparing(CommonKeyValue::getKey));
        return result;
    }

    @Override
    public List<CommonKeyValue> getCommunityRevenueDayByCommunityId(Long communityId, LocalDate from, LocalDate to, PayIncome.IncomeType incomeType, PayIncome.BusinessType businessType) {
        QPayIncome payIncome = QPayIncome.payIncome;
        List<Predicate> predicates = new ArrayList<>();
        getGlobalFilter(payIncome.measuringPoint.companyId, payIncome.measuringPoint.community.id).ifPresent(predicates::addAll);
        predicates.add(payIncome.incomeStatus.eq(PayIncome.IncomeStatus.PAY_SUCCESS));
        predicates.add(payIncome.measuringPoint.community.id.eq(communityId));
        Date dateFrom = Date.from(from.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date dateTo = DateUtil.endOfDay(Date.from(to.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        predicates.add(payIncome.payTime.between(dateFrom, dateTo));
        StringTemplate dayExpr = Expressions.stringTemplate("DATE_FORMAT({0},'%Y-%m-%d')", payIncome.payTime);
        List<CommonKeyValue> result = jpaQueryFactory.select(dayExpr, payIncome.amount.sum())
                .from(payIncome)
                .where(predicates.toArray(new Predicate[0]))
                .groupBy(dayExpr)
                .fetch().stream().map(tuple ->
                        new CommonKeyValue(tuple.get(dayExpr), tuple.get(payIncome.amount.sum()))).collect(Collectors.toList());
        LocalDate fromTemp = from;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        do {
            String key = fromTemp.format(formatter);
            boolean exist = result.stream().anyMatch(commonKeyValue -> commonKeyValue.getKey().equals(key));
            if (!exist) {
                result.add(new CommonKeyValue(key, 0));
            }
            fromTemp = fromTemp.plusDays(1);
        } while (!fromTemp.isAfter(to));
        // 将日期排序
        result.sort(Comparator.comparing(CommonKeyValue::getKey));
        return result;
    }

    private List<Predicate> getPayIncomeSuccessPredicates(Long communityId, Date from, Date to, PayIncome.IncomeType incomeType, PayIncome.BusinessType businessType, QPayIncome payIncome) {
        List<Predicate> predicates = new ArrayList<>();
        getGlobalFilter(payIncome.measuringPoint.companyId, payIncome.measuringPoint.community.id).ifPresent(predicates::addAll);
        predicates.add(payIncome.incomeStatus.eq(PayIncome.IncomeStatus.PAY_SUCCESS));
        predicates.add(payIncome.payTime.goe(from));
        predicates.add(payIncome.payTime.loe(to));
        if (businessType != null) {
            predicates.add(payIncome.businessType.eq(businessType));
        }
        if (incomeType != null) {
            predicates.add(payIncome.incomeType.eq(incomeType));
        }
        if (communityId != null) {
            predicates.add(payIncome.measuringPoint.community.id.eq(communityId));
        }
        return predicates;
    }

    private List<CommonKeyValue> getCommonKeyValues(List<Predicate> predicates, QPayIncome payIncome, StringTemplate path) {
        return jpaQueryFactory.select(path, payIncome.amount.sum())
                .from(payIncome)
                .where(predicates.toArray(new Predicate[0]))
                .groupBy(path)
                .fetch().stream()
                .map(tuple -> new CommonKeyValue(
                        tuple.get(path),
                        tuple.get(payIncome.amount.sum())
                )).collect(Collectors.toList());
    }

    private QueryResults<Tuple> getTupleQueryResults(QPayIncome payIncome, List<Predicate> predicates, PagingQueryParams<CommunityDataStatisticsDto> params) {
        return jpaQueryFactory.select(payIncome.measuringPoint.community.id, payIncome.measuringPoint.community.name, payIncome.amount.sum())
                .from(payIncome)
                .where(predicates.toArray(new Predicate[0]))
                .groupBy(payIncome.measuringPoint.community.id)
                .orderBy(payIncome.amount.sum().desc())
                .offset(params.getOffset())
                .limit(params.getLimit())
                .fetchResults();
    }

    /**
     * 应用年份条件
     *
     * @param payIncome
     * @param predicates
     * @param year
     */
    private void applyYearPredicate(QPayIncome payIncome, List<Predicate> predicates, String year) {
        Date firstDayStart = DateUtil.parse(year + "-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        Date lastDayEnd = DateUtil.parse(year + "-12-31 23:59:59", "yyyy-MM-dd HH:mm:ss");
        predicates.add(payIncome.payTime.between(firstDayStart, lastDayEnd));
    }
}