package com.acpiot.wsd.miniprogram.mvc;

import com.acpiot.wsd.data.archive.entity.WaterMeter;
import com.acpiot.wsd.data.monitor.entity.MeterCmdLog;
import com.acpiot.wsd.data.monitor.entity.MeterData;
import com.acpiot.wsd.data.monitor.entity.MeterEvent;
import com.acpiot.wsd.data.monitor.entity.ServiceValveCtrlRecord;
import com.acpiot.wsd.miniprogram.res.model.ServiceValveCtrlRecordModel;
import com.acpiot.wsd.miniprogram.res.projections.MeterCmdLogProjections;
import com.acpiot.wsd.miniprogram.res.projections.MeterDataProjections;
import com.acpiot.wsd.miniprogram.res.projections.MeterEventProjections;
import com.acpiot.wsd.miniprogram.service.CommunityMiniService;
import com.acpiot.wsd.miniprogram.service.MonitorMeterMiniService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.util.List;

/**
 * 水表监控详情控制层
 */
@RestController
@RequestMapping("/miniprogram/auth/monitorDetail")
@RequiredArgsConstructor
public class MonitorMeterDetailMiniController {

    private final MonitorMeterMiniService monitorMeterMiniService;
    private final CommunityMiniService communityMiniService;

    @GetMapping("findMeasuringPointById")
    public WaterMeter findMeasuringPointById(@RequestParam("measuringPointId") Long measuringPointId) {
        WaterMeter waterMeter = monitorMeterMiniService.findMeasuringPointById(measuringPointId);
        communityMiniService.buildBuildingUnitName(List.of(waterMeter));
        return waterMeter;
    }

    @GetMapping("findWaterMeterByMeasuringPointId")
    public WaterMeter findWaterMeterByMeasuringPointId(@RequestParam("measuringPointId") Long measuringPointId) {
        return monitorMeterMiniService.findWaterMeterByMeasuringPointId(measuringPointId);
    }

    @PostMapping("pagedMeterData")
    public Page<MeterDataProjections> pagedMeterData(@RequestBody PagingQueryParams<MeterData> params, @RequestParam("meterId") long meterId) {
        return monitorMeterMiniService.pagedMeterData(params, meterId);
    }

    @PostMapping("pagedMeterEvents")
    public Page<MeterEventProjections> pagedMeterEvents(@RequestBody PagingQueryParams<MeterEvent> params, @RequestParam("meterId") long meterId) {
        return monitorMeterMiniService.pagedMeterEvents(params, meterId);
    }

    @PostMapping("pagedMeterCmdLogs")
    public Page<MeterCmdLogProjections> pagedMeterCmdLogs(@RequestBody PagingQueryParams<MeterCmdLog> params, @RequestParam("meterId") long meterId) {
        return monitorMeterMiniService.pagedMeterCmdLogs(params, meterId);
    }

    @PostMapping("pagedServiceValveCtrlRecord")
    public Page<ServiceValveCtrlRecordModel> pagedServiceValveCtrlRecord(@RequestBody PagingQueryParams<ServiceValveCtrlRecord> params, @RequestParam("meterId") long meterId) {
        return monitorMeterMiniService.pagedServiceValveCtrlRecord(params, meterId);
    }
}
