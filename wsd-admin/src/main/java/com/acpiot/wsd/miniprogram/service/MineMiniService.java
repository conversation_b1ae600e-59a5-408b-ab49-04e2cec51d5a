package com.acpiot.wsd.miniprogram.service;

import com.acpiot.wsd.data.sys.entity.User;

/**
 * "我的"接口
 */
public interface MineMiniService {
    /**
     * 获取当前登陆用户信息
     *
     * @return
     */
    User getInfo();

    /**
     * 修改当前登陆用户信息
     *
     * @param user
     */
    void updateInfo(User user);

    /**
     * 通过旧密码修改个人密码
     *
     * @param oldPassword
     * @param newPassword
     * @return
     */
    boolean changePassword(String oldPassword, String newPassword);

    /**
     * 通过短信验证码修改个人密码
     *
     * @param newPassword
     * @param smsCode
     */
    void updatePasswordBySmsCode(String newPassword, String smsCode);
}
