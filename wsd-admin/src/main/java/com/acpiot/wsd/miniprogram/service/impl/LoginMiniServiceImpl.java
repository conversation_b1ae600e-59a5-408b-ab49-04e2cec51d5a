package com.acpiot.wsd.miniprogram.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.acpiot.wsd.data.sys.entity.Region;
import com.acpiot.wsd.data.sys.entity.Role;
import com.acpiot.wsd.data.sys.entity.User;
import com.acpiot.wsd.data.sys.repository.*;
import com.acpiot.wsd.miniprogram.service.LoginMiniService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class LoginMiniServiceImpl implements LoginMiniService {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final RegionRepository regionRepository;

    @Override
    public Optional<User> getUserByName(String name) {
        Optional<User> user = userRepository.findByName(name);
        user.ifPresent(this::loadUserData);
        return user;
    }

    private void loadUserData(User user) {
        if (CollUtil.isNotEmpty(user.getRoles())) {
            List<Role> roles = roleRepository.getRolesByIdIn(user.getRoleIds());
            user.setRoles(new HashSet<>(roles));
        }
        if (CollUtil.isNotEmpty(user.getRegions())) {
            List<Region> regions = regionRepository.getRegionsByIdIn(user.getRegionIds());
            user.setRegions(new HashSet<>(regions));
        }
    }
}
