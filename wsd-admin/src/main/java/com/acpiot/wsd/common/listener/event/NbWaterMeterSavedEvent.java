package com.acpiot.wsd.common.listener.event;

import com.acpiot.wsd.data.archive.entity.NbWaterMeter;
import com.acpiot.wsd.data.archive.entity.NbWmProgram;
import com.acpiot.wsd.data.archive.enums.IotPlatform;
import lombok.Value;

import java.util.List;
import java.util.Map;

/**
 * NB水务新增事件
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Value
public class NbWaterMeterSavedEvent {

    NbWmProgram nbWmProgram;
    Map<IotPlatform, List<NbWaterMeter>> map;

}
