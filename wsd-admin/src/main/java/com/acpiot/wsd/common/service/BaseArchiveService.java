package com.acpiot.wsd.common.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.microservice.meterservice.dto.Rest;
import com.acpiot.microservice.meterservice.dto.devicemanager.DeviceType;
import com.acpiot.wsd.admin.mvc.archive.dto.ImportExcelCat1MeterDto;
import com.acpiot.wsd.admin.mvc.archive.dto.ImportExcelNbMeterDto;
import com.acpiot.wsd.admin.mvc.archive.dto.NewWaterMeterModuleVo;
import com.acpiot.wsd.cat1wmservice.Cat1WmApiClient;
import com.acpiot.wsd.cat1wmservice.dto.Cat1WmApiResult;
import com.acpiot.wsd.cat1wmservice.dto.device.AddDeviceReqDto;
import com.acpiot.wsd.cat1wmservice.dto.device.AddDeviceRespDto;
import com.acpiot.wsd.core.event.DeviceArchiveSyncEvent;
import com.acpiot.wsd.data.archive.entity.*;
import com.acpiot.wsd.data.archive.enums.ChannelType;
import com.acpiot.wsd.data.archive.enums.NbState;
import com.acpiot.wsd.data.archive.enums.UdpModuleType;
import com.acpiot.wsd.data.archive.repository.*;
import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.data.sys.repository.CompanyRepository;
import com.acpiot.wsd.meterservice.archive.NbDeviceService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 档案匹配通用规则
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/11 14:43
 */
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class BaseArchiveService {

    private final NbDeviceService nbDeviceService;
    private final Cat1WmApiClient cat1WmApiClient;

    private final NbInfoRepository nbInfoRepository;
    private final CompanyRepository companyRepository;
    private final WaterMeterRepository waterMeterRepository;
    private final NbWaterMeterRepository nbWaterMeterRepository;
    private final DeviceArchiveRepository deviceArchiveRepository;
    private final Cat1WaterMeterRepository cat1WaterMeterRepository;

    /**
     * 设备档案信息同步事件
     *
     * @param event
     */
    public void handleDeviceArchiveSyncEvent(DeviceArchiveSyncEvent event) {
        List<String> imeis = event.getImeis();
        Boolean used = event.getUsed();
        Boolean sync = event.getSync();
        if (used == null && sync == null) {
            return;
        }
        if (sync != null && used != null) {
            deviceArchiveRepository.updateSyncAndUsedByImei(imeis, used, sync);
            return;
        }
        if (sync != null) {
            deviceArchiveRepository.updateSyncByImei(imeis, sync);
            return;
        }
        deviceArchiveRepository.updateUsedByImei(imeis, used);
    }

    /**
     * 新增UDP水表尝试从预置产测中提取档案
     *
     * @param frontMeter
     * @param dbMeterModel
     * @param companyId
     * @return
     */
    public Cat1WaterMeter loadAndUpdateCat1WaterMeterForPreArchive(Cat1WaterMeter frontMeter, MeterModel dbMeterModel, String companyId) {
        // 设备供货商
        List<String> supplierIds = companyRepository.getSupplierIds(companyId);

        // 尝试从预置产测中匹配档案
        Optional<DeviceArchive> deviceArchiveOptional;
        DeviceArchive deviceArchive = null;
        if (StrUtil.isNotBlank(frontMeter.getImei())) {
            deviceArchiveOptional = deviceArchiveRepository.findByImei(frontMeter.getImei());
        } else {
            if (CollUtil.isEmpty(supplierIds)) {
                throw new BusinessException("未输入IMEI信息也未配置供货商，操作失败，表号：{}", frontMeter.getCode());
            }
            deviceArchiveOptional = deviceArchiveRepository.findByCodeAndCompanyIdIn(frontMeter.getCode(), supplierIds);
        }
        if (deviceArchiveOptional.isPresent()) {
            deviceArchive = deviceArchiveOptional.get();
            if (deviceArchive.getCommunicationChannel().getChannelType() != frontMeter.getChannelType()) {
                throw new BusinessException("匹配到的产测档案与页面传入的通道类型不一致，操作失败，表号：{}", frontMeter.getCode());
            }
            checkDeviceArchiveUseCondition(deviceArchive, dbMeterModel, supplierIds, frontMeter.isStandard(), frontMeter.getCode(), null);
            frontMeter.setDeviceArchive(deviceArchive);
            deviceArchive.setUsed(true);
            if (deviceArchive.getModuleType() != null) {
                frontMeter.setModuleType(deviceArchive.getModuleType());
            }
        }
        // 最终的IMEI不能为空
        if (StrUtil.isBlank(frontMeter.getImei())) {
            throw new BusinessException("未通过表号匹配到预置产测档案，请输入IMEI");
        }
        // 检测IMEI是否重复使用
        if (!cat1WaterMeterRepository.isUnique(frontMeter, Cat1WaterMeter_.imei)) {
            throw new BusinessException("该IMEI已存在：{}", frontMeter.getImei());
        }
        checkCodeExistInDeviceArchive(deviceArchiveOptional, companyId, frontMeter.getCode());
        // 标准设备未同步的情况下需要注册档案
        registerCat1WaterMeter(frontMeter, deviceArchive);

        return frontMeter;
    }

    /**
     * 批量导入UDP水表尝试从预置产测中加载档案
     *
     * @param meterDto
     * @param dbMeterModel
     * @param community
     * @param standard
     * @param channelType
     * @param supplierIds
     * @param deviceArchiveList
     * @return
     */
    public Cat1WaterMeter loadAndUpdateCat1WaterMeterForPreArchive(ImportExcelCat1MeterDto meterDto, MeterModel dbMeterModel, Community community,
                                                                   boolean standard, ChannelType channelType, UdpModuleType moduleType, List<String> supplierIds, List<DeviceArchive> deviceArchiveList) {
        // 设备供货商
        String companyId = community.getCompanyId();

        // 设置水表信息
        Cat1WaterMeter waterMeter = new Cat1WaterMeter();
        waterMeter.setCompanyId(companyId);
        waterMeter.setCommunity(community);
        waterMeter.setCode(meterDto.getMeterCode());
        waterMeter.setStandard(standard);
        waterMeter.setChannelType(channelType);
        waterMeter.setStartValue(ObjectUtil.defaultIfNull(meterDto.getStartValue(), BigDecimal.ZERO));
        waterMeter.setDiameter(meterDto.getDiameter());
        waterMeter.setInstallationDetails(meterDto.getInstallationDetails());
        waterMeter.setModel(dbMeterModel);
        waterMeter.setModuleType(moduleType);
        // 创建计量点
        MeasuringPoint measuringPoint = new MeasuringPoint();
        measuringPoint.setCompanyId(community.getCompanyId());
        measuringPoint.setCommunity(community);
        measuringPoint.setName(meterDto.getMeasuringPointName());
        measuringPoint.setLinkCode(meterDto.getLinkCode());
        measuringPoint.setHouseHolder(meterDto.getHouseHolder());
        measuringPoint.setMobile(meterDto.getMobile());
        measuringPoint.setMeterCode(meterDto.getMeterCode());
        measuringPoint.setRoomNumber(meterDto.getRoomNumber());
        waterMeter.setMeasuringPoint(measuringPoint);

        // 尝试从预置产测中获取档案
        Optional<DeviceArchive> deviceArchiveOptional;
        DeviceArchive deviceArchive;
        if (StrUtil.isNotBlank(meterDto.getImei())) {
            deviceArchiveOptional = deviceArchiveList.stream().filter(m -> Objects.equals(m.getImei(), meterDto.getImei())).findFirst();
        } else {
            deviceArchiveOptional = deviceArchiveList.stream().filter(m -> Objects.equals(m.getCode(), meterDto.getMeterCode())).findFirst();
        }
        if (deviceArchiveOptional.isPresent()) {
            deviceArchive = deviceArchiveOptional.get();
            if (deviceArchive.getCommunicationChannel().getChannelType() != channelType) {
                throw new BusinessException("匹配到的产测档案与页面传入的通道类型不一致，操作失败，表号：{}", meterDto.getMeterCode());
            }
            checkDeviceArchiveUseCondition(deviceArchive, dbMeterModel, supplierIds, standard, meterDto.getMeterCode(), null);
            waterMeter.setDeviceArchive(deviceArchive);
            deviceArchive.setUsed(true);
            if (deviceArchive.getModuleType() != null) {
                waterMeter.setModuleType(deviceArchive.getModuleType());
            } else {
                deviceArchive.setModuleType(moduleType);
            }
        } else {
            // 从excel中获取imei信息
            waterMeter.setImei(meterDto.getImei());
            waterMeter.setValve(ObjectUtil.equal(meterDto.getValve(), "是"));
        }
        // 校验IMEI参数
        if (StrUtil.isBlank(waterMeter.getImei())) {
            throw new BusinessException("未匹配到产测水表，请输入IMEI，表号：{}", meterDto.getMeterCode());
        }

        return waterMeter;
    }

    /**
     * 新增NB水表尝试从预置产测中或NB预导入中提取档案
     *
     * @param frontMeter
     * @param dbMeterModel
     * @param companyId
     * @return
     */
    public NbWaterMeter loadAndUpdateNbWaterMeterForPreArchive(NbWaterMeter frontMeter, MeterModel dbMeterModel, String companyId) {
        // 获取页面上输入的IMEI信息
        NbInfo nbInfo = frontMeter.getNbInfo();
        // 检查NB信息输入完整性
        nbInfo.checkInputValid();
        // 获取供货商
        List<String> supplierIds = companyRepository.getSupplierIds(companyId);

        // 优先从NB预导入中匹配档案
        Optional<NbWaterMeter> nbWaterMeterOptional;
        if (nbInfo.isInputThirdCode()) {
            nbWaterMeterOptional = nbWaterMeterRepository.findByImei(nbInfo.getImei());
        } else {
            nbWaterMeterOptional = nbWaterMeterRepository.findByCodeAndCompanyId(frontMeter.getCode(), companyId);
        }
        if (nbWaterMeterOptional.isPresent()) {
            NbWaterMeter nbWaterMeter = nbWaterMeterOptional.get();
            // 检查NB预导入档案的使用条件
            checkNbWaterMeterPreImportUseCondition(nbWaterMeter, nbInfo, frontMeter.isStandard(), supplierIds, companyId);
            // 成功匹配到预导入档案，直接修改为正式档案后返回
            nbWaterMeter.setStandard(true);
            nbWaterMeter.setPreImport(false);
            MeasuringPoint measuringPoint = nbWaterMeter.getMeasuringPoint();
            measuringPoint.setCommunity(frontMeter.getCommunity());
            measuringPoint.setName(frontMeter.getMeasuringPoint().getName());
            measuringPoint.setLinkCode(frontMeter.getMeasuringPoint().getLinkCode());
            measuringPoint.setHouseHolder(frontMeter.getMeasuringPoint().getHouseHolder());
            measuringPoint.setMobile(frontMeter.getMeasuringPoint().getMobile());
            measuringPoint.setRoomNumber(frontMeter.getMeasuringPoint().getRoomNumber());
            measuringPoint.setLowerCommunity(frontMeter.getMeasuringPoint().getLowerCommunity());
            measuringPoint.setCompanyId(companyId);
            measuringPoint.setCreatedDate(new Date());
            nbWaterMeter.setCommunity(frontMeter.getCommunity());
            nbWaterMeter.setStartValue(ObjectUtil.defaultIfNull(frontMeter.getStartValue(), BigDecimal.ZERO));
            nbWaterMeter.setDiameter(frontMeter.getDiameter());
            nbWaterMeter.setInstallationDetails(frontMeter.getInstallationDetails());
            nbWaterMeter.setLowerCommunity(frontMeter.getLowerCommunity());
            nbWaterMeter.setCompanyId(companyId);
            nbWaterMeter.setCreatedDate(new Date());
            nbWaterMeter.setNewImport(false);
            return nbWaterMeter;
        }

        // 从NB预导入中未匹配到档案，尝试从预置产测中匹配
        Optional<DeviceArchive> archiveOptional;
        DeviceArchive deviceArchive = null;
        if (nbInfo.isInputThirdCode()) {
            archiveOptional = deviceArchiveRepository.findByImei(nbInfo.getImei());
        } else {
            if (CollUtil.isEmpty(supplierIds)) {
                throw new BusinessException("未输入IMEI信息也未配置供货商，操作失败，表号：{}", frontMeter.getCode());
            }
            archiveOptional = deviceArchiveRepository.findByCodeAndCompanyIdIn(frontMeter.getCode(), supplierIds);
        }
        if (archiveOptional.isPresent()) {
            deviceArchive = archiveOptional.get();
            checkDeviceArchiveUseCondition(deviceArchive, dbMeterModel, supplierIds, frontMeter.isStandard(), frontMeter.getCode(), nbInfo);
            nbInfo = NbInfo.of(deviceArchive);
            frontMeter.setValve(deviceArchive.isValve());
            frontMeter.setNbInfo(nbInfo);
            frontMeter.setStandard(true);
            deviceArchive.setUsed(true);
        }
        // 如果最终的三码信息为空，则报错提示
        if (!nbInfo.isInputThirdCode()) {
            throw new BusinessException("未通过表号匹配到预置产测档案需补充IMEI信息，操作失败，表号：{}", frontMeter.getCode());
        }

        // 检查当前NbInfo信息是否被使用
        checkNbInfoUseCondition(nbInfo, frontMeter.getCode());
        checkCodeExistInDeviceArchive(archiveOptional, companyId, frontMeter.getCode());
        // 标准设备未同步的情况下需要注册档案
        registerNbWaterMeter(nbInfo, deviceArchive, frontMeter.getCode(), frontMeter.isStandard(), dbMeterModel.getNbWmProgram());

        frontMeter.setNewImport(true);
        return frontMeter;
    }

    /**
     * 批量导入NB水表尝试从预置产测中或NB预导入中提取档案
     *
     * @param meterDto
     * @param dbMeterModel
     * @param community
     * @param standard
     * @return
     */
    public NbWaterMeter loadAndUpdateNbWaterMeterForPreArchive(ImportExcelNbMeterDto meterDto, MeterModel dbMeterModel, Community community, boolean standard,
                                                               List<String> supplierIds, List<NbWaterMeter> dbWaterMeters, List<DeviceArchive> dbDeviceArchives) {
        // 获取表格上输入的信息
        String companyId = community.getCompanyId();
        NbInfo nbInfo = meterDto.getNbInfo();
        String imei = nbInfo.getImei();
        String meterCode = meterDto.getMeterCode();

        // 创建NB水表档案，注意：相关参数（初始余额、对接方式、楼栋单元）在该方法外部设置或处理
        NbWaterMeter nbWaterMeter = new NbWaterMeter();
        // 临时字段，用于后续识别是否触发水表新增事件，如果从NB预导入进来的就不需要触发新增事件
        nbWaterMeter.setNewImport(true);

        // 优先从NB预导入中匹配档案
        Optional<NbWaterMeter> nbWaterMeterOptional;
        if (nbInfo.isInputThirdCode()) {
            nbWaterMeterOptional = dbWaterMeters.stream().filter(m -> Objects.equals(m.getNbInfo().getImei(), imei)).findFirst();
        } else {
            nbWaterMeterOptional = dbWaterMeters.stream().filter(m -> Objects.equals(m.getCode(), meterCode) && Objects.equals(m.getCompanyId(), companyId)).findFirst();
        }
        if (nbWaterMeterOptional.isPresent()) {
            nbWaterMeter = nbWaterMeterOptional.get();
            // 检查NB预导入档案的使用条件
            checkNbWaterMeterPreImportUseCondition(nbWaterMeter, nbInfo, standard, supplierIds, companyId);
            // 标记非新增设备
            nbWaterMeter.setNewImport(false);
            // 成功匹配到预导入档案，直接修改为正式档案
            nbWaterMeter.setPreImport(false);
            // 以预导入档案中的NbInfo为准
            nbInfo = nbWaterMeter.getNbInfo();
        }

        // 从NB预导入中未匹配到档案，尝试从预置产测中匹配
        DeviceArchive deviceArchive = null;
        if (nbWaterMeterOptional.isEmpty()) {
            Optional<DeviceArchive> archiveOptional;
            if (nbInfo.isInputThirdCode()) {
                archiveOptional = dbDeviceArchives.stream().filter(d -> Objects.equals(d.getImei(), imei)).findFirst();
            } else {
                archiveOptional = dbDeviceArchives.stream().filter(m -> Objects.equals(m.getCode(), meterCode) && supplierIds.contains(m.getCompanyId())).findFirst();
            }
            if (archiveOptional.isPresent()) {
                deviceArchive = archiveOptional.get();
                checkDeviceArchiveUseCondition(deviceArchive, dbMeterModel, supplierIds, standard, meterCode, nbInfo);
                nbInfo = NbInfo.of(deviceArchive);
                deviceArchive.setUsed(true);
            }
        }

        // 未匹配到NB预导入和预置产测档案时，必须输入IMEI
        if (!nbInfo.isInputThirdCode()) {
            throw new BusinessException("未通过表号匹配到NB预导入和产测档案需补充IMEI相关信息，操作失败，表号：{}", meterCode);
        }

        // 如果成功从NB预导入中提到了档案则直接修改档案，否则创建新的计量点
        MeasuringPoint measuringPoint = nbWaterMeter.getMeasuringPoint() != null ? nbWaterMeter.getMeasuringPoint() : new MeasuringPoint();
        measuringPoint.setCompanyId(community.getCompanyId());
        measuringPoint.setCommunity(community);
        measuringPoint.setName(meterDto.getMeasuringPointName());
        measuringPoint.setLinkCode(meterDto.getLinkCode());
        measuringPoint.setHouseHolder(meterDto.getHouseHolder());
        measuringPoint.setMobile(StrUtil.isNotBlank(meterDto.getMobile()) ? meterDto.getMobile() : null);
        measuringPoint.setMeterCode(meterCode);
        measuringPoint.setRoomNumber(meterDto.getRoomNumber());

        // 如果成功从NB预导入中提取到了档案则直接修改档案，否则创建新的NB水表
        nbWaterMeter.setCompanyId(community.getCompanyId());
        nbWaterMeter.setModel(dbMeterModel);
        nbWaterMeter.setNbInfo(nbInfo);
        nbWaterMeter.setValve(deviceArchive != null ? deviceArchive.isValve() : ObjectUtil.equal(meterDto.getValve(), "是"));
        nbWaterMeter.setMeasuringPoint(measuringPoint);
        nbWaterMeter.setCommunity(community);
        nbWaterMeter.setCode(meterCode);
        nbWaterMeter.setStandard(standard);
        nbWaterMeter.setStartValue(ObjectUtil.defaultIfNull(meterDto.getStartValue(), BigDecimal.ZERO));
        nbWaterMeter.setDiameter(meterDto.getDiameter());
        nbWaterMeter.setInstallationDetails(meterDto.getInstallationDetails());

        return nbWaterMeter;
    }

    /**
     * 修改NB设备信息（用于批量修改NB水表）
     *
     * @param dbNbWaterMeter
     * @param nbInfo
     */
    public void loadAndUpdateNbWaterMeterForPreArchive(NbWaterMeter dbNbWaterMeter, NbInfo nbInfo) {
        // 获取被修改的水表相关信息
        String code = dbNbWaterMeter.getCode();
        MeterModel model = dbNbWaterMeter.getModel();
        boolean standard = dbNbWaterMeter.isStandard();
        String companyId = dbNbWaterMeter.getCompanyId();
        NbInfo dbNbInfo = dbNbWaterMeter.getNbInfo();

        // 判断是否允许修改IMEI信息
        if (dbNbInfo.isRegistered()) {
            // 已经注册的情况下，只能修改ICCID
            if (!Objects.equals(dbNbInfo.getIccid(), nbInfo.getIccid())) {
                dbNbInfo.setIccid(nbInfo.getIccid());
                if (StrUtil.isNotBlank(dbNbInfo.getIccid()) && !nbInfoRepository.isUnique(dbNbInfo, NbInfo_.iccid)) {
                    throw new BusinessException("此ICCID：{} 已被使用，表号：{}", dbNbInfo.getIccid(), code);
                }
            }
            return;
        }

        // 当前NB设备未注册，允许修改IMEI信息，检查NB信息输入完整性
        if (!nbInfo.isInputThirdCode()) {
            throw new BusinessException("请输入IMEI相关信息，表号：{}", code);
        }

        // 新匹配到的预置产测档案
        DeviceArchive deviceArchive = null;

        // 如果修改了IMEI，修改旧IMEI关联的设备档案的使用状态为未使用
        if (!Objects.equals(dbNbInfo.getImei(), nbInfo.getImei())) {
            deviceArchiveRepository.findByImei(dbNbInfo.getImei()).ifPresent(d -> {
                d.setUsed(false);
                d.setSync(false);
            });
            // 修改IMEI相关信息
            dbNbInfo.setIotPlatform(nbInfo.getIotPlatform());
            dbNbInfo.setImei(nbInfo.getImei());
            dbNbInfo.setImsi(nbInfo.getImsi());
            dbNbInfo.setIccid(nbInfo.getIccid());
            // 尝试从预置产测中匹配档案
            Optional<DeviceArchive> archiveOptional = deviceArchiveRepository.findByImei(dbNbInfo.getImei());
            // 如果匹配到预置产测档案，则检查使用条件
            if (archiveOptional.isPresent()) {
                deviceArchive = archiveOptional.get();
                List<String> supplierIds = companyRepository.getSupplierIds(companyId);
                checkDeviceArchiveUseCondition(deviceArchive, model, supplierIds, standard, code, dbNbInfo);
                deviceArchive.setUsed(true);
                dbNbInfo.setNbState(deviceArchive.isSync() ? NbState.INACTIVE : NbState.UNREGISTERED);
            }
        }

        // 检查当前NbInfo信息是否被使用
        checkNbInfoUseCondition(dbNbInfo, code);
        // 标准设备未同步的情况下需要注册档案
        registerNbWaterMeter(dbNbInfo, deviceArchive, code, standard, model.getNbWmProgram());
    }

    /**
     * 换为NB水表尝试从预置产测中提取档案
     *
     * @param nbWaterMeter
     * @param oldCode
     * @param dbMeterModel
     * @param companyId
     * @return
     */
    public NbWaterMeter changeToNbWaterMeterForPreArchive(NbWaterMeter nbWaterMeter, String oldCode, MeterModel dbMeterModel, String companyId) {
        // 获取供货商
        List<String> supplierIds = companyRepository.getSupplierIds(companyId);
        // 新表和旧表的编号允许相同，否则要校验新表的表号是否唯一
        if (!Objects.equals(nbWaterMeter.getCode(), oldCode)) {
            Optional<WaterMeter> optionalWaterMeter = waterMeterRepository.findByCodeAndCompanyId(nbWaterMeter.getCode(), companyId);
            if (optionalWaterMeter.isPresent()) {
                WaterMeter waterMeter = optionalWaterMeter.get();
                if (waterMeter instanceof NbWaterMeter && ((NbWaterMeter) waterMeter).isPreImport()) {
                    throw new BusinessException("通过新表号可匹配到NB预导入档案，换表失败，表号：{}", nbWaterMeter.getCode());
                } else {
                    throw new BusinessException("新输入的水表编号已存在");
                }
            }
            // 如果表号不同，则需要查询是否存在旧设备档案，如果存在则将是否被正式档案使用置为false
            if (CollUtil.isNotEmpty(supplierIds)) {
                deviceArchiveRepository.findByCodeAndCompanyIdIn(oldCode, supplierIds).ifPresent(deviceArchive -> deviceArchive.setUsed(false));
            }
        }

        // 设置公司ID和水表型号
        nbWaterMeter.setCompanyId(companyId);
        nbWaterMeter.setModel(dbMeterModel);
        // 获取NB信息
        NbInfo nbInfo = nbWaterMeter.getNbInfo();
        // 检查NB信息输入完整性
        nbInfo.checkInputValid();

        // 尝试从预置产测中提取档案
        Optional<DeviceArchive> deviceArchiveOptional;
        DeviceArchive deviceArchive = null;
        if (nbInfo.isInputThirdCode()) {
            deviceArchiveOptional = deviceArchiveRepository.findByImei(nbInfo.getImei());
        } else {
            if (CollUtil.isEmpty(supplierIds)) {
                throw new BusinessException("未输入IMEI信息也未配置供货商，操作失败，表号：{}", nbWaterMeter.getCode());
            }
            deviceArchiveOptional = deviceArchiveRepository.findByCodeAndCompanyIdIn(nbWaterMeter.getCode(), supplierIds);
        }
        if (deviceArchiveOptional.isPresent()) {
            deviceArchive = deviceArchiveOptional.get();
            checkDeviceArchiveUseCondition(deviceArchive, dbMeterModel, supplierIds, nbWaterMeter.isStandard(), nbWaterMeter.getCode(), nbInfo);
            nbInfo = NbInfo.of(deviceArchive);
            nbWaterMeter.setValve(deviceArchive.isValve());
            nbWaterMeter.setNbInfo(nbInfo);
            deviceArchive.setUsed(true);
        }
        // 最终的三码信息不能为空
        if (!nbInfo.isInputThirdCode()) {
            throw new BusinessException("未通过表号匹配到预置产测档案需补充IMEI相关信息，操作失败，表号：{}", nbWaterMeter.getCode());
        }

        // 检查当前NbInfo信息是否被使用
        checkNbInfoUseCondition(nbInfo, nbWaterMeter.getCode());
        checkCodeExistInDeviceArchive(deviceArchiveOptional, companyId, nbWaterMeter.getCode());
        // 标准设备未同步的情况下需要注册档案
        registerNbWaterMeter(nbInfo, deviceArchive, nbWaterMeter.getCode(), nbWaterMeter.isStandard(), dbMeterModel.getNbWmProgram());
        return nbWaterMeter;
    }

    /**
     * 换为UDP水表尝试从预置产测中提取档案
     *
     * @param cat1WaterMeter
     * @param oldCode
     * @param dbMeterModel
     * @param companyId
     * @return
     */
    public Cat1WaterMeter changeToCat1WaterMeterForPreArchive(Cat1WaterMeter cat1WaterMeter, String oldCode, MeterModel dbMeterModel, String companyId) {
        // 设置公司ID和水表型号
        cat1WaterMeter.setCompanyId(companyId);
        cat1WaterMeter.setModel(dbMeterModel);
        // 获取供货商
        List<String> supplierIds = companyRepository.getSupplierIds(companyId);
        // 新表和旧表的编号允许相同，否则要校验新表的表号是否唯一
        if (!Objects.equals(cat1WaterMeter.getCode(), oldCode)) {
            if (!waterMeterRepository.isUnique(cat1WaterMeter, WaterMeter_.companyId, WaterMeter_.code)) {
                throw new BusinessException("新输入的水表编号已存在");
            }
            // 如果表号不同，则需要查询是否存在旧设备档案，如果存在则将是否被正式档案使用置为false
            if (CollUtil.isNotEmpty(supplierIds)) {
                deviceArchiveRepository.findByCodeAndCompanyIdIn(oldCode, supplierIds).ifPresent(deviceArchive -> deviceArchive.setUsed(false));
            }
        }
        // 尝试从预置产测中提取档案
        Optional<DeviceArchive> deviceArchiveOptional;
        DeviceArchive deviceArchive = null;
        if (StrUtil.isNotBlank(cat1WaterMeter.getImei())) {
            deviceArchiveOptional = deviceArchiveRepository.findByImei(cat1WaterMeter.getImei());
        } else {
            if (CollUtil.isEmpty(supplierIds)) {
                throw new BusinessException("未输入IMEI信息也未配置供货商，操作失败，表号：{}", cat1WaterMeter.getCode());
            }
            deviceArchiveOptional = deviceArchiveRepository.findByCodeAndCompanyIdIn(cat1WaterMeter.getCode(), supplierIds);
        }
        if (deviceArchiveOptional.isPresent()) {
            deviceArchive = deviceArchiveOptional.get();
            if (deviceArchive.getCommunicationChannel().getChannelType() != cat1WaterMeter.getChannelType()) {
                throw new BusinessException("匹配到的产测档案与页面传入的通道类型不一致，操作失败，表号：{}", cat1WaterMeter.getCode());
            }
            checkDeviceArchiveUseCondition(deviceArchive, dbMeterModel, supplierIds, cat1WaterMeter.isStandard(), cat1WaterMeter.getCode(), null);
            cat1WaterMeter.setValve(deviceArchive.isValve());
            cat1WaterMeter.setImei(deviceArchive.getImei());
            deviceArchive.setUsed(true);
            if (deviceArchive.getModuleType() != null) {
                cat1WaterMeter.setModuleType(deviceArchive.getModuleType());
            }
        }
        // 最终的IMEI不能为空
        if (StrUtil.isBlank(cat1WaterMeter.getImei())) {
            throw new BusinessException("未通过表号匹配到预置产测档案需补充IMEI相关信息，操作失败，表号：{}", cat1WaterMeter.getCode());
        }
        // 检测IMEI是否重复使用
        if (!cat1WaterMeterRepository.isUnique(cat1WaterMeter, Cat1WaterMeter_.imei)) {
            throw new BusinessException("该IMEI已存在：{}", cat1WaterMeter.getImei());
        }
        checkCodeExistInDeviceArchive(deviceArchiveOptional, companyId, cat1WaterMeter.getCode());
        // 标准设备未同步的情况下需要注册档案
        registerCat1WaterMeter(cat1WaterMeter, deviceArchive);
        return cat1WaterMeter;
    }

    /**
     * 更换UDP水表模块为NB水表模块
     *
     * @param newWaterMeterModuleVo
     * @param oldWaterMeter
     * @param dbMeterModel
     */
    public void changeCat1ModuleToNbModule(NewWaterMeterModuleVo newWaterMeterModuleVo, Cat1WaterMeter oldWaterMeter, MeterModel dbMeterModel) {
        // 获取NB信息
        NbInfo nbInfo = newWaterMeterModuleVo.getNbInfo();
        // 检查NB信息输入完整性
        nbInfo.checkInputValid();
        // 获取当前水表的模块供应商
        List<String> supplierIds = companyRepository.getSupplierIds(oldWaterMeter.getCompanyId());

        // 尝试从预置产测中提取档案
        Optional<DeviceArchive> deviceArchiveOptional;
        DeviceArchive deviceArchive = null;
        if (nbInfo.isInputThirdCode()) {
            deviceArchiveOptional = deviceArchiveRepository.findByImei(nbInfo.getImei());
        } else {
            if (CollUtil.isEmpty(supplierIds)) {
                throw new BusinessException("未输入IMEI信息也未配置供货商，操作失败，表号：{}", oldWaterMeter.getCode());
            }
            deviceArchiveOptional = deviceArchiveRepository.findByCodeAndCompanyIdIn(oldWaterMeter.getCode(), supplierIds);
        }
        if (deviceArchiveOptional.isPresent()) {
            deviceArchive = deviceArchiveOptional.get();
            checkDeviceArchiveUseCondition(deviceArchive, dbMeterModel, supplierIds, oldWaterMeter.isStandard(), oldWaterMeter.getCode(), nbInfo);
            nbInfo = NbInfo.of(deviceArchive);
            deviceArchive.setUsed(true);
        }

        // 未匹配到预置产测档案时，必须输入IMEI信息
        if (!nbInfo.isInputThirdCode()) {
            throw new BusinessException("通过表号未匹配到产测档案需补充IMEI相关信息，操作失败，表号：{}", oldWaterMeter.getCode());
        }

        // 检查当前NbInfo信息是否被使用
        checkNbInfoUseCondition(nbInfo, oldWaterMeter.getCode());

        // 删除CAT1子表
        cat1WaterMeterRepository.deleteCat1WaterMeterById(Objects.requireNonNull(oldWaterMeter.getId()));
        // 新增NB信息
        NbInfo dbNbInfo = nbInfoRepository.save(nbInfo);
        // 设置NB信息外键到父表
        waterMeterRepository.updateNbInfoIdById(oldWaterMeter.getId(), dbNbInfo.getId());
        // 新增NB水表子表
        nbWaterMeterRepository.insertNbWaterMeter(Objects.requireNonNull(oldWaterMeter.getId()));

        // 标准设备未同步的情况下需要注册档案
        registerNbWaterMeter(nbInfo, deviceArchive, oldWaterMeter.getCode(), oldWaterMeter.isStandard(), dbMeterModel.getNbWmProgram());
    }

    /**
     * 更换UDP水表模块为UDP水表模块
     *
     * @param newWaterMeterModuleVo
     * @param oldWaterMeter
     * @param dbMeterModel
     */
    public void changeCat1ModuleToCat1Module(NewWaterMeterModuleVo newWaterMeterModuleVo, Cat1WaterMeter oldWaterMeter, MeterModel dbMeterModel) {
        if (newWaterMeterModuleVo.getChannelType() == null) {
            throw new BusinessException("通道类型不能为空");
        }
        if (newWaterMeterModuleVo.getModuleType() == null) {
            throw new BusinessException("模组类型不能为空");
        }
        // 更新旧模块的模组类型
        oldWaterMeter.setModuleType(newWaterMeterModuleVo.getModuleType());

        // 创建Cat1水表档案
        Cat1WaterMeter cat1WaterMeter = new Cat1WaterMeter();
        // 获取Cat1模块IMEI
        cat1WaterMeter.setImei(newWaterMeterModuleVo.getUdpImei());
        cat1WaterMeter.setStandard(oldWaterMeter.isStandard());
        cat1WaterMeter.setChannelType(newWaterMeterModuleVo.getChannelType());
        // 获取当前水表的模块供应商
        List<String> supplierIds = companyRepository.getSupplierIds(oldWaterMeter.getCompanyId());

        // 尝试从预置产测中提取档案
        DeviceArchive deviceArchive = null;
        Optional<DeviceArchive> deviceArchiveOptional;
        if (StrUtil.isNotBlank(cat1WaterMeter.getImei())) {
            deviceArchiveOptional = deviceArchiveRepository.findByImei(cat1WaterMeter.getImei());
        } else {
            if (CollUtil.isEmpty(supplierIds)) {
                throw new BusinessException("未输入IMEI信息也未配置供货商，操作失败，表号：{}", oldWaterMeter.getCode());
            }
            deviceArchiveOptional = deviceArchiveRepository.findByCodeAndCompanyIdIn(oldWaterMeter.getCode(), supplierIds);
        }
        if (deviceArchiveOptional.isPresent()) {
            deviceArchive = deviceArchiveOptional.get();
            if (oldWaterMeter.getImei().equals(deviceArchive.getImei())) {
                throw new BusinessException("匹配到产测档案，新模块IMEI与旧模块IMEI相同，操作失败，表号：{}", oldWaterMeter.getCode());
            }
            if (deviceArchive.getCommunicationChannel().getChannelType() != cat1WaterMeter.getChannelType()) {
                throw new BusinessException("匹配到的产测档案与选择的通道类型不一致，操作失败，表号：{}", oldWaterMeter.getCode());
            }
            checkDeviceArchiveUseCondition(deviceArchive, dbMeterModel, supplierIds, oldWaterMeter.isStandard(), oldWaterMeter.getCode(), null);
            cat1WaterMeter.setSync(deviceArchive.isSync());
            cat1WaterMeter.setImei(deviceArchive.getImei());
            deviceArchive.setUsed(true);
            if (deviceArchive.getModuleType() != null) {
                oldWaterMeter.setModuleType(deviceArchive.getModuleType());
            } else {
                deviceArchive.setModuleType(oldWaterMeter.getModuleType());
            }
        }

        // 未匹配到预置产测档案时，必须输入IMEI信息
        if (StrUtil.isBlank(cat1WaterMeter.getImei())) {
            throw new BusinessException("通过表号未匹配到产测档案需补充IMEI相关信息，操作失败，表号：{}", oldWaterMeter.getCode());
        }
        // 检测IMEI是否重复使用
        if (!cat1WaterMeterRepository.isUnique(cat1WaterMeter, Cat1WaterMeter_.imei)) {
            throw new BusinessException("该IMEI已存在：{}", cat1WaterMeter.getImei());
        }

        // 标准设备未同步的情况下需要注册档案
        registerCat1WaterMeter(cat1WaterMeter, deviceArchive);
        // 更新水表的imei和同步状态
        oldWaterMeter.setImei(cat1WaterMeter.getImei());
        oldWaterMeter.setSync(cat1WaterMeter.isSync());
        oldWaterMeter.setChannelType(cat1WaterMeter.getChannelType());
    }

    /**
     * 更换NB水表模块为NB水表模块
     *
     * @param newWaterMeterModuleVo
     * @param oldWaterMeter
     * @param dbMeterModel
     */
    public void changeNbModuleToNbModule(NewWaterMeterModuleVo newWaterMeterModuleVo, NbWaterMeter oldWaterMeter, MeterModel dbMeterModel) {
        // 旧NB信息
        NbInfo oldNbInfo = oldWaterMeter.getNbInfo();
        // 获取NB信息
        NbInfo nbInfo = newWaterMeterModuleVo.getNbInfo();
        // 检查NB信息输入完整性
        nbInfo.checkInputValid();
        // 获取当前水表的模块供应商
        List<String> supplierIds = companyRepository.getSupplierIds(oldWaterMeter.getCompanyId());

        // 尝试从预置产测中提取档案
        Optional<DeviceArchive> deviceArchiveOptional;
        DeviceArchive deviceArchive = null;
        if (nbInfo.isInputThirdCode()) {
            deviceArchiveOptional = deviceArchiveRepository.findByImei(nbInfo.getImei());
        } else {
            if (CollUtil.isEmpty(supplierIds)) {
                throw new BusinessException("未输入IMEI信息也未配置供货商，操作失败，表号：{}", oldWaterMeter.getCode());
            }
            deviceArchiveOptional = deviceArchiveRepository.findByCodeAndCompanyIdIn(oldWaterMeter.getCode(), supplierIds);
        }
        if (deviceArchiveOptional.isPresent()) {
            deviceArchive = deviceArchiveOptional.get();
            checkDeviceArchiveUseCondition(deviceArchive, dbMeterModel, supplierIds, oldWaterMeter.isStandard(), oldWaterMeter.getCode(), nbInfo);
            nbInfo = NbInfo.of(deviceArchive);
            deviceArchive.setUsed(true);
        }

        // 未匹配到预置产测档案时，必须输入IMEI信息
        if (!nbInfo.isInputThirdCode()) {
            throw new BusinessException("通过表号未匹配到产测档案需补充IMEI相关信息，操作失败，表号：{}", oldWaterMeter.getCode());
        }

        // 检查当前NbInfo信息是否被使用
        checkNbInfoUseCondition(nbInfo, oldWaterMeter.getCode());
        // 设置新NB信息，删除旧NB信息
        oldWaterMeter.setNbInfo(nbInfo);
        nbInfoRepository.deleteById(Objects.requireNonNull(oldNbInfo.getId()));

        // 标准设备未同步的情况下需要注册档案
        registerNbWaterMeter(nbInfo, deviceArchive, oldWaterMeter.getCode(), oldWaterMeter.isStandard(), dbMeterModel.getNbWmProgram());
    }

    /**
     * 更换NB水表模块为UDP水表模块
     *
     * @param newWaterMeterModuleVo
     * @param oldWaterMeter
     * @param dbMeterModel
     */
    public void changeNbModuleToCat1Module(NewWaterMeterModuleVo newWaterMeterModuleVo, NbWaterMeter oldWaterMeter, MeterModel dbMeterModel) {
        if (newWaterMeterModuleVo.getChannelType() == null) {
            throw new BusinessException("通道类型不能为空");
        }
        if (newWaterMeterModuleVo.getModuleType() == null) {
            throw new BusinessException("模组类型不能为空");
        }

        // 创建Cat1水表档案
        Cat1WaterMeter cat1WaterMeter = new Cat1WaterMeter();
        // 获取Cat1模块IMEI
        cat1WaterMeter.setImei(newWaterMeterModuleVo.getUdpImei());
        cat1WaterMeter.setStandard(oldWaterMeter.isStandard());
        cat1WaterMeter.setChannelType(newWaterMeterModuleVo.getChannelType());
        cat1WaterMeter.setModuleType(newWaterMeterModuleVo.getModuleType());
        // 获取当前水表的模块供应商
        List<String> supplierIds = companyRepository.getSupplierIds(oldWaterMeter.getCompanyId());

        // 尝试从预置产测中提取档案
        DeviceArchive deviceArchive = null;
        Optional<DeviceArchive> deviceArchiveOptional;
        if (StrUtil.isNotBlank(cat1WaterMeter.getImei())) {
            deviceArchiveOptional = deviceArchiveRepository.findByImei(cat1WaterMeter.getImei());
        } else {
            if (CollUtil.isEmpty(supplierIds)) {
                throw new BusinessException("未输入IMEI信息也未配置供货商，操作失败，表号：{}", oldWaterMeter.getCode());
            }
            deviceArchiveOptional = deviceArchiveRepository.findByCodeAndCompanyIdIn(oldWaterMeter.getCode(), supplierIds);
        }
        if (deviceArchiveOptional.isPresent()) {
            deviceArchive = deviceArchiveOptional.get();
            if (deviceArchive.getCommunicationChannel().getChannelType() != cat1WaterMeter.getChannelType()) {
                throw new BusinessException("匹配到的产测档案与选择的通道类型不一致，操作失败，表号：{}", oldWaterMeter.getCode());
            }
            checkDeviceArchiveUseCondition(deviceArchive, dbMeterModel, supplierIds, oldWaterMeter.isStandard(), oldWaterMeter.getCode(), null);
            cat1WaterMeter.setSync(deviceArchive.isSync());
            cat1WaterMeter.setImei(deviceArchive.getImei());
            deviceArchive.setUsed(true);
            if (deviceArchive.getModuleType() != null) {
                cat1WaterMeter.setModuleType(deviceArchive.getModuleType());
            } else {
                deviceArchive.setModuleType(cat1WaterMeter.getModuleType());
            }
        }

        // 未匹配到预置产测档案时，必须输入IMEI信息
        if (StrUtil.isBlank(cat1WaterMeter.getImei())) {
            throw new BusinessException("通过表号未匹配到产测档案需补充IMEI相关信息，操作失败，表号：{}", oldWaterMeter.getCode());
        }
        // 检测IMEI是否重复使用
        if (!cat1WaterMeterRepository.isUnique(cat1WaterMeter, Cat1WaterMeter_.imei)) {
            throw new BusinessException("该IMEI已存在：{}", cat1WaterMeter.getImei());
        }
        // 父表中的nb_info_id置空
        waterMeterRepository.updateNbInfoIdById(oldWaterMeter.getId(), null);
        // 删除子表
        nbWaterMeterRepository.deleteNbWaterMeterById(Objects.requireNonNull(oldWaterMeter.getId()));
        NbInfo oldNbInfo = oldWaterMeter.getNbInfo();
        nbInfoRepository.deleteNbInfoById(Objects.requireNonNull(oldNbInfo.getId()));
        // 创建子表
        cat1WaterMeterRepository.insertCat1WaterMeter(Objects.requireNonNull(oldWaterMeter.getId()), cat1WaterMeter.getImei(), cat1WaterMeter.isSync(), cat1WaterMeter.getChannelType().name(), cat1WaterMeter.getModuleType().name());

        // 标准设备未同步的情况下需要注册档案
        registerCat1WaterMeter(cat1WaterMeter, deviceArchive);
    }

    /**
     * 检查NB预导入档案的使用条件
     *
     * @param nbWaterMeter
     * @param nbInfo
     * @param standard
     * @param supplierIds
     * @param companyId
     */
    private void checkNbWaterMeterPreImportUseCondition(NbWaterMeter nbWaterMeter, NbInfo nbInfo, boolean standard, List<String> supplierIds, String companyId) {
        String meterCode = nbWaterMeter.getCode();
        if (!nbWaterMeter.isPreImport()) {
            throw new BusinessException("通过{}匹配到预导入档案，但已被正式使用，操作失败，表号：{}", nbInfo.isInputThirdCode() ? "IMEI" : "表号", meterCode);
        }
        if (CollUtil.isNotEmpty(supplierIds) && !supplierIds.contains(nbWaterMeter.getCompanyId()) && !Objects.equals(nbWaterMeter.getCompanyId(), companyId)) {
            throw new BusinessException("通过{}匹配到预导入档案，但不属于所配置的供货商，操作失败，表号：{}", nbInfo.isInputThirdCode() ? "IMEI" : "表号", meterCode);
        }
        if (CollUtil.isEmpty(supplierIds) && !Objects.equals(nbWaterMeter.getCompanyId(), companyId)) {
            throw new BusinessException("通过{}匹配到预导入档案，但不属于当前公司，操作失败，表号：{}", nbInfo.isInputThirdCode() ? "IMEI" : "表号", meterCode);
        }
        if (!standard) {
            throw new BusinessException("通过{}匹配到预导入档案，但提交的设备信息不是标准设备，操作失败，表号：{}", nbInfo.isInputThirdCode() ? "IMEI" : "表号", meterCode);
        }
        if (nbInfo.isInputThirdCode() && !nbWaterMeter.getNbInfo().isMatchConfig(nbInfo)) {
            throw new BusinessException("通过{}匹配到预导入档案，但提交的IMEI信息与预导入档案不匹配，操作失败，表号：{}", nbInfo.isInputThirdCode() ? "IMEI" : "表号", meterCode);
        }
    }

    /**
     * 检查使用预置产测档案的条件
     *
     * @param deviceArchive
     * @param dbMeterModel
     * @param supplierIds
     * @param standard
     * @param meterCode
     * @param nbInfo
     */
    public void checkDeviceArchiveUseCondition(DeviceArchive deviceArchive, MeterModel dbMeterModel, List<String> supplierIds, boolean standard, String meterCode, NbInfo nbInfo) {
        if (!standard) {
            throw new BusinessException("匹配到产测档案但提交的是非标准设备，操作失败，表号：{}", meterCode);
        }
        if (CollUtil.isEmpty(supplierIds) || !supplierIds.contains(deviceArchive.getCompanyId())) {
            throw new BusinessException("当前公司未配置供货商或对应的产测档案不属于配置的供货商，操作失败，表号：{}", meterCode);
        }
        if (StrUtil.isBlank(deviceArchive.getCode())) {
            throw new BusinessException("对应的产测档案未写入表号，操作失败，表号：{}", meterCode);
        }
        if (!deviceArchive.getCode().equals(meterCode)) {
            throw new BusinessException("对应的产测档案表号与新水表表号不同，操作失败，表号：{}", meterCode);
        }
        if (!deviceArchive.isSyncCode()) {
            throw new BusinessException("对应的产测档案表号暂未写入模块，操作失败，表号：{}", meterCode);
        }
        if (!Objects.equals(deviceArchive.getModel().getId(), dbMeterModel.getId())) {
            throw new BusinessException("对应的产测档案中的水表型号与页面上选择的不匹配，操作失败，表号：{}", meterCode);
        }
        if (deviceArchive.isUsed()) {
            throw new BusinessException("对应的产测档案已被正式设备使用，操作失败，表号：{}", meterCode);
        }
        if (nbInfo != null && nbInfo.isInputThirdCode() && deviceArchive.isMatchErrorConfig(nbInfo)) {
            throw new BusinessException("对应的产测档案中的IMEI信息与页面上输入的不匹配，操作失败，表号：{}", meterCode);
        }
    }

    /**
     * 检查NbInfo的使用条件
     *
     * @param nbInfo
     * @param meterCode
     */
    private void checkNbInfoUseCondition(NbInfo nbInfo, String meterCode) {
        if (!nbInfoRepository.isUnique(nbInfo, NbInfo_.imei)) {
            throw new BusinessException("此IMEI：{} 已被使用，表号：{}", nbInfo.getImei(), meterCode);
        }
        if (!nbInfoRepository.isUnique(nbInfo, NbInfo_.imsi)) {
            throw new BusinessException("此IMSI：{} 已被使用，表号：{}", nbInfo.getImsi(), meterCode);
        }
        if (StrUtil.isNotBlank(nbInfo.getIccid())) {
            if (!nbInfoRepository.isUnique(nbInfo, NbInfo_.iccid)) {
                throw new BusinessException("此ICCID：{} 已被使用，表号：{}", nbInfo.getIccid(), meterCode);
            }
        }
    }

    /**
     * Cat1水表注册IMEI信息
     *
     * @param cat1WaterMeter
     * @param deviceArchive
     */
    private void registerCat1WaterMeter(Cat1WaterMeter cat1WaterMeter, DeviceArchive deviceArchive) {
        if (cat1WaterMeter.isStandard() && !cat1WaterMeter.isSync()) {
            // 在前置机注册Cat1设备
            String imei = cat1WaterMeter.getImei();
            AddDeviceReqDto addDeviceReqDto = new AddDeviceReqDto();
            addDeviceReqDto.setImei(imei);
            addDeviceReqDto.setChannelType(cat1WaterMeter.getChannelType());
            Cat1WmApiResult<AddDeviceRespDto> rest = cat1WmApiClient.addDevice(addDeviceReqDto);
            if (rest.isSuccess() && rest.getData() != null && rest.getData().getImei().equals(imei)) {
                cat1WaterMeter.setSync(true);
            }
            if (deviceArchive != null) {
                deviceArchive.setSync(cat1WaterMeter.isSync());
            }
        }
    }

    /**
     * 注册NB水表IMEI信息
     *
     * @param nbInfo
     * @param deviceArchive
     * @param meterCode
     * @param standard
     * @param nbWmProgram
     */
    private void registerNbWaterMeter(NbInfo nbInfo, DeviceArchive deviceArchive, String meterCode, boolean standard, NbWmProgram nbWmProgram) {
        if (standard && nbInfo.getNbState() == NbState.UNREGISTERED) {
            Rest<Void> rest = nbDeviceService.createDevice(DeviceType.WATER_METER, meterCode, nbWmProgram, nbInfo);
            if (rest.isSuccess()) {
                nbInfo.setNbState(NbState.INACTIVE);
                nbInfo.setOnline(false);
                if (deviceArchive != null) {
                    deviceArchive.setSync(nbInfo.isRegistered());
                }
            }
        }
    }

    /**
     * 检查表号是否在设备档案中存在
     *
     * @param deviceArchiveOptional
     * @param companyId
     * @param meterCode
     */
    private void checkCodeExistInDeviceArchive(Optional<DeviceArchive> deviceArchiveOptional, String companyId, String meterCode) {
        if (deviceArchiveOptional.isPresent()) {
            return;
        }
        deviceArchiveRepository.findByCompanyIdAndCode(companyId, meterCode).ifPresent(dbDeviceArchive -> {
            throw new BusinessException("操作失败，该表号: {}已在产测档案中存在，产测档案IMEI: {}", meterCode, dbDeviceArchive.getImei());
        });
    }
}
