package com.acpiot.wsd.common.security.jwt;

import cn.hutool.core.collection.CollUtil;
import com.acpiot.wsd.data.sys.entity.Role;
import com.acpiot.wsd.data.sys.entity.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by moxin on 2021-06-30-0030
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClaimsData {

    public static ClaimsData ofApi(User user) {
        return new ClaimsData(user.getId(), user.getRealName(), user.getUserType().name(), user.getCompanyId(), null,
                user.getCompany().getLayerCode(), user.getRegionIds(), user.getCommunityIds(), null, null);
    }

    public static ClaimsData ofMini(User user) {
        String[] roleCodes = CollUtil.isNotEmpty(user.getRoles()) ? user.getRoles().stream().map(Role::getCode).toArray(String[]::new) : null;
        return new ClaimsData(user.getId(), user.getRealName(), user.getUserType().name(), user.getCompanyId(), null, user.getCompany() != null ?
                user.getCompany().getLayerCode() : null, user.getRegionIds(), user.getCommunityIds(), roleCodes, user.getCompanyIds());
    }

    public static ClaimsData ofTabletapp(User user) {
        String[] roleCodes = CollUtil.isNotEmpty(user.getRoles()) ? user.getRoles().stream().map(Role::getCode).toArray(String[]::new) : null;
        return new ClaimsData(user.getId(), user.getRealName(), user.getUserType().name(), user.getCompanyId(), null, user.getCompany() != null ?
                user.getCompany().getLayerCode() : null, user.getRegionIds(), user.getCommunityIds(), roleCodes, null);
    }

    /**
     * 用户id
     */
    private Long userId;

    private String realName;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 租户ID（为兼容公司层级升级前，旧api token的tenantId问题）
     */
    private String tenantId;

    /**
     * 公司层级编码
     */
    private String layerCode;

    /**
     * 管辖区域ID
     */
    private Long[] regionIds;

    /**
     * 所有管辖区域小区ID
     */
    private Long[] communityIds;

    /**
     * 用户拥有的角色
     */
    private String[] roleCodes;

    /**
     * 当前要查询的公司数据范围
     */
    private String[] companyIds;
}
