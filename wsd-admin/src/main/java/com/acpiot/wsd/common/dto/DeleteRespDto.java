package com.acpiot.wsd.common.dto;

import com.acpiot.wsd.data.archive.entity.*;
import lombok.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class DeleteRespDto {

    private List<DelDto> delDtoList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DelDto {
        /**
         * 被删除的id
         */
        private Long id;

        /**
         * 被删除的数据
         */
        private Map<String, Object> infos;

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder("DelDto{");
            sb.append("id=").append(id);
            sb.append(", infos={");
            if (infos != null) {
                for (Map.Entry<String, Object> entry : infos.entrySet()) {
                    sb.append(entry.getKey()).append("=").append(entry.getValue()).append(", ");
                }
                // 删除最后一个逗号和空格
                if (!infos.isEmpty()) {
                    sb.setLength(sb.length() - 2);
                }
            }
            sb.append('}');
            return sb.toString();
        }
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("DeleteRespDto{");
        sb.append("delDtoList=[");
        if (delDtoList != null) {
            for (DelDto delDto : delDtoList) {
                sb.append(delDto.toString()).append(", ");
            }
            // 删除最后一个逗号和空格
            if (!delDtoList.isEmpty()) {
                sb.setLength(sb.length() - 2);
            }
        }
        sb.append(']');
        sb.append('}');
        return sb.toString();
    }

    public static DelDto mapToWaterMeterDelDto(WaterMeter waterMeter) {
        Community community = waterMeter.getCommunity();
        MeasuringPoint measuringPoint = waterMeter.getMeasuringPoint();
        DelDto delDto = new DeleteRespDto.DelDto();
        delDto.setId(waterMeter.getId());

        Map<String, Object> infos = new HashMap<>();
        infos.put("code", waterMeter.getCode());
        infos.put("measuringPointName", measuringPoint.getName());
        infos.put("accountNo", measuringPoint.getAccountNo());
        infos.put("linkCode", measuringPoint.getLinkCode());
        infos.put("communityName", community.getName());
        infos.put("startValue", waterMeter.getStartValue());
        infos.put("valve", waterMeter.isValve());
        Optional.ofNullable(waterMeter.getLatestData())
                .ifPresent(latestData -> {
                    infos.put("endValue", latestData.getValue());
                    infos.put("meteringDate", latestData.getMeteringTime());
                    infos.put("valveStatus", latestData.getValveStatus());
                    infos.put("batteryVoltage", latestData.getBatteryVoltage());
                    infos.put("magneticAttack", latestData.getMagneticAttack());
                    infos.put("meterVersion", latestData.getMeterVersion());
                });
        infos.put("chargeValue", measuringPoint.getChargeValue());
        infos.put("standard", waterMeter.isStandard());
        if (!waterMeter.isStandard() && waterMeter.getThirdDeviceDocking() != null) {
            infos.put("thirdDeviceDocking", waterMeter.getThirdDeviceDocking().getName());
        }

        if (waterMeter instanceof Cat1WaterMeter) {
            Cat1WaterMeter cat1WaterMeter = (Cat1WaterMeter) waterMeter;
            infos.put("imei", cat1WaterMeter.getImei());
            infos.put("moduleType", cat1WaterMeter.getModuleType());
        } else if (waterMeter instanceof NbWaterMeter) {
            NbWaterMeter nbWaterMeter = (NbWaterMeter) waterMeter;
            NbInfo nbInfo = nbWaterMeter.getNbInfo();
            infos.put("iotPlatform", nbInfo.getIotPlatform());
            infos.put("imei", nbInfo.getImei());
            infos.put("imsi", nbInfo.getImsi());
            infos.put("iccid", nbInfo.getIccid());
            if (!waterMeter.isStandard()) {
                infos.put("nbDockingType", nbWaterMeter.getNbDockingType());
            }
        } else if (waterMeter instanceof ConcentratorWaterMeter) {
            ConcentratorWaterMeter concentratorWaterMeter = (ConcentratorWaterMeter) waterMeter;
            Concentrator concentrator = concentratorWaterMeter.getConcentrator();
            infos.put("concentratorCode", concentrator.getCode());
            infos.put("concentratorType", concentrator.getConcentratorType());
            infos.put("concentratorVersion", concentrator.getConcentratorVersion());
            if (!waterMeter.isStandard()) {
                infos.put("concentratorDockingType", concentrator.getConcentratorDockingType());
            }
        }
        delDto.setInfos(infos);
        return delDto;
    }
}
