package com.acpiot.wsd.common.service;

import com.acpiot.wsd.data.archive.entity.Cat1WaterMeter;
import com.acpiot.wsd.data.archive.entity.NbWaterMeter;
import com.acpiot.wsd.data.archive.entity.WaterMeter;
import com.acpiot.wsd.data.monitor.entity.DayFreezeData;
import com.acpiot.wsd.data.monitor.entity.MeterCmdLog;
import com.acpiot.wsd.data.sys.entity.User;
import com.acpiot.wsd.docking.hysw.req.body.HyCommandStateRequestRequestBody;
import org.springframework.data.domain.Page;
import pers.mx.jupiter.jpa.PagingQueryParams;
import pers.mx.jupiter.jpa.QueryParams;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 公共查询相关接口
 */
public interface CommonService {

    /**
     * 根据用户名查询用户信息
     *
     * @param userName 用户名
     * @return
     */
    Optional<User> findByUserName(String userName);

    /**
     * 根据公司ID查询水表
     *
     * @param companyId 公司ID
     * @return
     */
    List<WaterMeter> findByCompanyId(String companyId);

    /**
     * 根据表号查询水表信息
     *
     * @param meterCode 表号
     * @param companyId 公司ID
     * @return
     */
    Optional<WaterMeter> findByCodeAndCompanyId(String meterCode, String companyId);

    /**
     * 根据公司ID查询指定水表
     *
     * @param meterCodeList
     * @param companyId
     * @return
     */
    List<WaterMeter> findByCodeInAndCompanyId(List<String> meterCodeList, String companyId);

    /**
     * 根据水表命令commandId获取水表命令日志
     *
     * @param commandId 命令id
     * @return
     */
    Optional<MeterCmdLog> findMeterCmdLogByCommandId(String commandId);

    /**
     * 根据表号查询水表信息
     *
     * @param meterCode 表号
     * @return
     */
    Page<WaterMeter> pageByCodeAndCompanyId(String meterCode, String companyId, int pageIndex, int pageSize);

    /**
     * 根据表号和时间范围查询水表冻结数据
     *
     * @param meterCode
     * @param companyId
     * @param pageIndex
     * @param pageSize
     * @param startDate
     * @param endDate
     * @return
     */
    Page<DayFreezeData> pageByCodeAndDatePeriodAndCompanyId(String meterCode, String companyId, int pageIndex, int pageSize, Date startDate, Date endDate);

    /**
     * 根据条件分页查询所有命令
     *
     * @param commandStateRequest
     * @param companyId
     * @param pageIndex
     * @param pageSize
     * @return
     */
    Page<MeterCmdLog> pageMeterCmdLog(HyCommandStateRequestRequestBody.CommandStateRequest commandStateRequest, String companyId, int pageIndex, int pageSize);

    /**
     * 根据表号、公司ID和时间范围查询水表信息
     *
     * @param meterCode
     * @param companyId
     * @param pageIndex
     * @param pageSize
     * @param startDate
     * @param endDate
     * @return
     */
    Page<WaterMeter> pageByCodeAndCompanyIdAndPeriod(String meterCode, String companyId, int pageIndex, int pageSize, Date startDate, Date endDate);


    /**
     * 拆除指定表号的水表
     *
     * @param meterCode
     * @param companyId
     * @param lastReading
     * @param dismantleDate
     * @return
     */
    void dismantleWaterMeter(String meterCode, String companyId, BigDecimal lastReading, String dismantleDate) throws ParseException;

    /**
     * 根据ID查询NB水表
     *
     * @param id
     * @return
     */
    Optional<NbWaterMeter> findNbWaterMeterById(Long id);

    /**
     * 根据命令ID设置关联编号
     *
     * @param linkCode
     * @param logId
     */
    void updateMeterCmdLogLinkCodeById(String linkCode, Long logId);

    /**
     * 根据水表ID和关联编号查询命令记录
     *
     * @param meterId
     * @param linkCode
     * @return
     */
    Optional<MeterCmdLog> findMeterCmdLogByWaterMeterIdAndLinkCode(Long meterId, String linkCode);

    /**
     * 根据公司ID查询NB水表
     *
     * @param companyId
     * @return
     */
    List<NbWaterMeter> findNbWaterMeterByCompanyId(String companyId);

    /**
     * 分页查询水表
     *
     * @param params
     * @return
     */
    Page<WaterMeter> pageWaterMeters(PagingQueryParams<WaterMeter> params);

    List<WaterMeter> findWaterMeterByIds(List<Long> waterMeterIds, String... fetch);

    List<Cat1WaterMeter> findAllCat1WaterMeterByCompanyIdAndLinkCodeNotNull(String companyId);

    List<Cat1WaterMeter> findAllCat1WaterMeterByCompanyIdAndMeterCodesAndLinkCodeNotNull(String companyId, List<String> meterCodes);

    /**
     * 根据水表code修改水表使用状态
     *
     * @param meterCodeList
     */
    void updateWaterMeterUseStateByCodesAndCompanyId(List<String> meterCodeList, String companyId);

    List<MeterCmdLog> getWaterMeterCmdLogs(QueryParams<MeterCmdLog> params);

    void deleteByCommandId(String commandId);
}
