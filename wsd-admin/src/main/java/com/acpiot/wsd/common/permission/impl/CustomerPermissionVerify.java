package com.acpiot.wsd.common.permission.impl;

import com.acpiot.wsd.common.permission.AbstractPermissionVerify;
import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.data.revenue.entity.QCustomer;
import com.querydsl.core.types.Predicate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户数据权限验证执行器
 */
@Component
public class CustomerPermissionVerify extends AbstractPermissionVerify {

    @Override
    public void verify(List<String> operateIds) {
        // 判断客户的操作权限，直接通过客户Id执行查询匹配数量，查询计量点时应用公司ID、公司ID、小区ID等条件，如果应用上这些条件后的查询匹配数量与提交的ID数量一致，则表示有权限
        QCustomer customer = QCustomer.customer;
        List<Predicate> predicates = super.buildCommonPredicates(customer.companyId, customer.community.id);
        predicates.add(customer.id.in(operateIds.stream().map(Long::parseLong).collect(Collectors.toList())));
        Long customerCount = jpaQueryFactory.select(customer.countDistinct()).from(customer).where(predicates.toArray(new Predicate[0])).fetchOne();
        if (customerCount == null || customerCount < operateIds.size()) {
            throw new BusinessException("您提交操作请求的数据中存在无权限处理的客户");
        }
    }
}
