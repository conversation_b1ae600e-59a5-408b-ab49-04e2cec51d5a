package com.acpiot.wsd.common.listener;

import com.acpiot.wsd.api.model.message.DataReportPayload;
import com.acpiot.wsd.api.model.message.PushMessageModel;
import com.acpiot.wsd.api.mqtt.MqttPublisher;
import com.acpiot.wsd.data.archive.entity.WaterMeter;
import com.acpiot.wsd.data.monitor.entity.CurveData;
import com.acpiot.wsd.core.event.CurveDataSavedEvent;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Component
public class MonitorDataEventListener {

    private final MqttPublisher mqttPublisher;

    @TransactionalEventListener
    public void onCurveDataSavedEvent(CurveDataSavedEvent event) {
        WaterMeter waterMeter = event.getWaterMeter();
        CurveData curveData = event.getCurveData();

        // 推送曲线数据
        mqttPublisher.publish(waterMeter.getCompanyId(),
                () -> PushMessageModel.newDataReport(waterMeter.getCode(),
                        DataReportPayload.buildCurvePayload(curveData)));
    }

}
