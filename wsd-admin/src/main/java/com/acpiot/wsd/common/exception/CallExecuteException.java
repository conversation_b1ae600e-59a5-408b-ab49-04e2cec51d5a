package com.acpiot.wsd.common.exception;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * Created by hsq on 2021-09-13-0001
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Getter
public class CallExecuteException extends RuntimeException {

    private final int errorCode;
    private final String errorMsg;

    public CallExecuteException(String message, int errorCode, String errorMsg) {
        super(StrUtil.format("{}, errorCode: {}, errorMsg: {}", message, errorCode, errorMsg));
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public CallExecuteException(String message, int errorCode, String errorMsg, Throwable cause) {
        super(StrUtil.format("{}, errorCode: {}, errorMsg: {}", message, errorCode, errorMsg), cause);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

}
