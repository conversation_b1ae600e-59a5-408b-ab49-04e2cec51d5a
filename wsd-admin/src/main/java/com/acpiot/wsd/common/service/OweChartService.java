package com.acpiot.wsd.common.service;

import com.acpiot.wsd.common.dto.OweAmountDto;
import org.springframework.data.domain.Page;
import pers.mx.jupiter.jpa.PagingQueryParams;

/**
 * 欠费汇总相关统计接口
 * Created by hsq on 2021-07-09-0000
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface OweChartService {

    /**
     * 统计当前总欠费情况
     *
     * @param communityId
     * @param regionId
     * @return
     */
    OweAmountDto getTotalOweAmountDto(Long communityId, Long regionId);

    /**
     * 分页查询小区欠费汇总统计数据
     *
     * @param params
     * @return
     */
    Page<OweAmountDto> pageCommunityOweAmounts(PagingQueryParams<OweAmountDto> params);
}
