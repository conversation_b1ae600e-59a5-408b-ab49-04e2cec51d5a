package com.acpiot.wsd.common.service;

import com.acpiot.wsd.data.archive.entity.WaterMeter;
import com.acpiot.wsd.data.revenue.entity.Bill;

/**
 * 机械表事件处理
 * Created by hsq on 2023-01-05-0001
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface MachineWaterMeterEventService {

    /**
     * 处理机械表统计数据更新事件
     *
     * @param waterMeter
     * @param bill
     */
    void handleMachineWaterMeterReportChangeEvent(WaterMeter waterMeter, Bill bill);

    /**
     * 机械表账单重新计算后，需要重新生成统计数据
     *
     * @param billId
     */
    void handleMachineBillReCalculateEvent(Long billId);
}
