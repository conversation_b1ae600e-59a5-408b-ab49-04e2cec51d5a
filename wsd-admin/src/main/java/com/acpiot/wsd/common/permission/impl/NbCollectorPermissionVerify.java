package com.acpiot.wsd.common.permission.impl;

import com.acpiot.wsd.common.permission.AbstractPermissionVerify;
import com.acpiot.wsd.data.archive.entity.QNbCollector;
import com.acpiot.wsd.common.exception.BusinessException;
import com.querydsl.core.types.Predicate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * nb采集器权限验证执行器
 */
@Component
public class NbCollectorPermissionVerify extends AbstractPermissionVerify {

    @Override
    public void verify(List<String> operateIds) {
        // 判断nb采集器的操作权限
        QNbCollector nbCollector = QNbCollector.nbCollector;
        List<Predicate> predicates = super.buildCommonPredicates(nbCollector.companyId, nbCollector.community.id);
        predicates.add(nbCollector.id.in(operateIds.stream().map(Long::parseLong).collect(Collectors.toList())));
        Long nbCollectorCount = jpaQueryFactory.select(nbCollector.countDistinct()).from(nbCollector).where(predicates.toArray(new Predicate[0])).fetchOne();
        if (nbCollectorCount == null || nbCollectorCount < operateIds.size()) {
            throw new BusinessException("您提交操作请求的数据中存在无权限处理的nb采集器");
        }
    }
}
