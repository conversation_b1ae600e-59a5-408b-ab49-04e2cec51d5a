package com.acpiot.wsd.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;

/**
 * Created by moxin on 2021-05-14-0014
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Configuration
public class ReloadMessageConfig {

    @Bean
    public ReloadableResourceBundleMessageSource messageSource() {
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        // 指定类路径的时候，不需要添加文件后缀[.properties]
        messageSource.setBasenames("classpath:org/springframework/security/messages_zh_CN", "classpath:messages");
        return messageSource;
    }

}
