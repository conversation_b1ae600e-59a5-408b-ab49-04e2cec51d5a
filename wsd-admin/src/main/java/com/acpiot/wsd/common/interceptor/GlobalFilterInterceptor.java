package com.acpiot.wsd.common.interceptor;

import com.acpiot.wsd.common.security.user.AdminUser;
import com.acpiot.wsd.core.multi_tenancy.util.GlobalFilterContext;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.ui.ModelMap;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.context.request.WebRequestInterceptor;

/**
 * Created by moxin on 2021-04-27-0027
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Component
public class GlobalFilterInterceptor implements WebRequestInterceptor {

    @Override
    public void preHandle(WebRequest request) {
        AbstractAuthenticationToken token = (AbstractAuthenticationToken) request.getUserPrincipal();
        if (token != null) {
            Object principal = token.getPrincipal();
            if (principal instanceof AdminUser) {
                AdminUser adminUser = (AdminUser) principal;
                GlobalFilterContext.setFilterBean(adminUser.getFilterBean());
            }
        }
    }

    @Override
    public void postHandle(@NonNull WebRequest request, ModelMap model) {
        GlobalFilterContext.clear();
    }

    @Override
    public void afterCompletion(@NonNull WebRequest request, Exception ex) {
        // NOOP
        GlobalFilterContext.clear();
    }

}
