package com.acpiot.wsd.api.config;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.config.annotation.ObjectPostProcessor;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.access.intercept.FilterInvocationSecurityMetadataSource;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import static com.acpiot.wsd.common.security.constant.SecurityConstant.ROLE_COMPANY_ADMIN;

/**
 * Created by moxin on 2020-12-30-0030
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Configuration
@Order(0)
public class ApiSecurityConfig extends WebSecurityConfigurerAdapter {

    private final AccessDeniedHandler accessDeniedHandler;
    private final AccessDecisionManager accessDecisionManager;
    private final AuthenticationEntryPoint authenticationEntryPoint;
    private final ApiJwtAuthorizationTokenFilter apiJwtAuthorizationTokenFilter;

    @Qualifier("filterInvocationSecurityMetadataSourceImpl")
    @Autowired
    private FilterInvocationSecurityMetadataSource filterInvocationSecurityMetadataSourceImpl;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        // 禁用cors、csrf
        http.cors().and().csrf().disable();
        // 禁用缓存
        http.headers().cacheControl();
        // 设置异常处理
        http.exceptionHandling().authenticationEntryPoint(authenticationEntryPoint).accessDeniedHandler(accessDeniedHandler);
        // 基于token，所以不需要session
        http.sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);
        // 设置匹配路径和权限处理器
        http.antMatcher("/api/**").authorizeRequests().antMatchers(HttpMethod.OPTIONS).permitAll()
                .anyRequest().hasRole(ROLE_COMPANY_ADMIN.replaceFirst("ROLE_", ""))
                .withObjectPostProcessor(new ObjectPostProcessor<FilterSecurityInterceptor>() {
                    @Override
                    public <O extends FilterSecurityInterceptor> O postProcess(O fsi) {
                        // 该行代码很关键，如果不设置为false，则API权限将无法应用，因为后台管理系统有一个相同类型的过滤器该标志位为true，
                        // 意味着这种类型的过滤器只会执行一次，此处的这个过滤器设置为false后就不会有这个限制，权限拦截才能生效
                        fsi.setObserveOncePerRequest(false);
                        fsi.setAccessDecisionManager(accessDecisionManager);
                        // 对外客户对接API的权限拦截逻辑与管理后台的一致，所以引用同一个组件实例
                        fsi.setSecurityMetadataSource(filterInvocationSecurityMetadataSourceImpl);
                        return fsi;
                    }
                });
        // 设置token登录过滤器
        http.addFilterBefore(apiJwtAuthorizationTokenFilter, UsernamePasswordAuthenticationFilter.class);
    }
}
