package com.acpiot.wsd.api.service.impl;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.api.exception.ApiException;
import com.acpiot.wsd.api.exception.ErrorCode;
import com.acpiot.wsd.api.model.PageResultModel;
import com.acpiot.wsd.api.model.WaterMeterModel;
import com.acpiot.wsd.api.model.dto.CreateConcentratorWaterMeterReqDto;
import com.acpiot.wsd.api.model.dto.QueryWaterMeterListReqDto;
import com.acpiot.wsd.api.service.WaterMeterApiService;
import com.acpiot.wsd.common.service.BaseCat1WaterMeterService;
import com.acpiot.wsd.common.service.BaseConcentratorWaterMeterService;
import com.acpiot.wsd.common.service.BaseMachineWaterMeterService;
import com.acpiot.wsd.common.service.BaseNbWaterMeterService;
import com.acpiot.wsd.data.archive.entity.*;
import com.acpiot.wsd.data.archive.enums.MeterType;
import com.acpiot.wsd.data.archive.repository.ConcentratorRepository;
import com.acpiot.wsd.data.archive.repository.ConcentratorWaterMeterRepository;
import com.acpiot.wsd.data.archive.repository.MeterModelRepository;
import com.acpiot.wsd.data.archive.repository.WaterMeterRepository;
import com.acpiot.wsd.core.multi_tenancy.util.GlobalFilterContext;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.Filter;
import pers.mx.jupiter.jpa.QueryParams;

import java.math.BigDecimal;

import static com.acpiot.wsd.data.archive.entity.ConcentratorWaterMeter.MAX_SERIAL_NO;

/**
 * Created by moxin on 2021-06-28-0028
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class WaterMeterApiServiceImpl implements WaterMeterApiService {

    private final ConcentratorRepository concentratorRepository;
    private final MeterModelRepository meterModelRepository;
    private final WaterMeterRepository waterMeterRepository;
    private final ConcentratorWaterMeterRepository concentratorWaterMeterRepository;

    private final BaseNbWaterMeterService baseNbWaterMeterService;
    private final BaseCat1WaterMeterService baseCat1WaterMeterService;
    private final BaseMachineWaterMeterService baseMachineWaterMeterService;
    private final BaseConcentratorWaterMeterService baseConcentratorWaterMeterService;

    @Override
    @Transactional(readOnly = true)
    public WaterMeterModel queryWaterMeter(String meterCode) {
        WaterMeter waterMeter = waterMeterRepository.findOne(new Filter[] {
                Filter.eq(WaterMeter_.CODE, meterCode)
        }, WaterMeter_.COMMUNITY, WaterMeter_.MEASURING_POINT)
                .orElseThrow(() -> new ApiException(ErrorCode.CODE_NOT_FOUNT));
        return WaterMeterModel.of(waterMeter);
    }

    @Override
    @Transactional(readOnly = true)
    public PageResultModel<WaterMeterModel> queryWaterMeters(QueryWaterMeterListReqDto reqDto) {
        QueryParams<WaterMeter> params = reqDto.getQueryParams();
        params.fetch(WaterMeter_.MEASURING_POINT).fetch(WaterMeter_.COMMUNITY);
        Page<WaterMeterModel> page = waterMeterRepository.findAll(params, reqDto.getPageRequest()).map(WaterMeterModel::of);
        return PageResultModel.of(page);
    }

    @Override
    public void createConcentratorWaterMeter(String concentratorCode, CreateConcentratorWaterMeterReqDto reqDto) {
        Concentrator concentrator = concentratorRepository.findByCode(concentratorCode)
                .orElseThrow(() -> new ApiException(ErrorCode.CONCENTRATOR_NOT_EXISTS));
        long meterNum = concentratorWaterMeterRepository.countByConcentrator(concentrator);
        if (meterNum >= MAX_SERIAL_NO) {
            throw new ApiException(ErrorCode.CONCENTRATOR_MAX_METER_NUM);
        }
        MeterModel meterModel = meterModelRepository.findById(reqDto.getModelId())
                .orElseThrow(() -> new ApiException(ErrorCode.METER_MODEL_NOT_EXISTS));
        if (meterModel.getMeterType() != MeterType.CONCENTRATOR) {
            throw new ApiException(ErrorCode.METER_MODEL_ERR);
        }

        ConcentratorWaterMeter waterMeter = new ConcentratorWaterMeter();
        switch (concentrator.getConcentratorType()) {
            case WIRED_MULTI_FUNCTION:
                if (reqDto.getProtocol() == null
                        || reqDto.getPort() == null) {
                    throw new ApiException(ErrorCode.METER_PROTOCOL_PORT_ERR);
                }
                waterMeter.setProtocolPort(reqDto.getProtocolPort());
                break;

            case WIRELESS:
                String collectorUid = reqDto.getCollectorUid();
                if (StrUtil.isNotBlank(collectorUid)) {
                    String[] split = collectorUid.split(",");
                    for (String uid : split) {
                        boolean match = ReUtil.isMatch("\\d{12}", uid);
                        if (!match) {
                            throw new ApiException(ErrorCode.METER_COLLECTOR_ERR);
                        }
                    }
                    waterMeter.setCollectorUid(collectorUid);
                }
                break;

            default:
                // ignore
                break;
        }

        Community community = concentrator.getCommunity();
        waterMeter.setCommunity(community);
        waterMeter.setConcentrator(concentrator);
        waterMeter.setModel(meterModel);
        String meterCode = reqDto.getMeterCode();
        waterMeter.setCode(meterCode);
        BigDecimal startValue = reqDto.getStartValue();
        if (startValue != null) {
            waterMeter.setStartValue(startValue);
        }

        String companyId = GlobalFilterContext.getCompanyId();
        if (StringUtils.isNotBlank(companyId)) {
            waterMeter.setCompanyId(companyId);
        }
        if (!waterMeterRepository.isUnique(waterMeter, WaterMeter_.companyId, WaterMeter_.code)) {
            throw new ApiException(ErrorCode.METER_CODE_EXISTS);
        }

        MeasuringPoint measuringPoint = new MeasuringPoint();
        measuringPoint.setCommunity(community);
        measuringPoint.setName(meterCode);
        measuringPoint.setMeterCode(meterCode);
        waterMeter.setMeasuringPoint(measuringPoint);
        concentratorWaterMeterRepository.save(waterMeter);

        concentrator.setArchiveSynced(false);
    }

    @Override
    public void deleteWaterMeter(String meterCode) {
        waterMeterRepository.findOne(new Filter[] {Filter.eq(WaterMeter_.CODE, meterCode)}, WaterMeter_.MODEL)
                .ifPresent(waterMeter -> {
                    MeterModel meterModel = waterMeter.getModel();
                    switch (meterModel.getMeterType()) {
                        case NB_IOT:
                            baseNbWaterMeterService.removeWaterMeters(waterMeter.getId());
                            break;
                        case CONCENTRATOR:
                            baseConcentratorWaterMeterService.removeWaterMeters(waterMeter.getId());
                            break;
                        case MACHINE:
                            baseMachineWaterMeterService.removeWaterMeters(waterMeter.getId());
                            break;
                        case CAT1:
                            baseCat1WaterMeterService.removeWaterMeters(waterMeter.getId());
                            break;
                    }
                });
    }
}
