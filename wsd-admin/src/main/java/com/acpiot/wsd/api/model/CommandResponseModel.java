package com.acpiot.wsd.api.model;

import com.acpiot.microservice.meterservice.dto.CommandRspDto;
import com.acpiot.wsd.cat1wmservice.dto.command.CommandRespDto;
import com.acpiot.wsd.core.event.WaterMeterCmdEvent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 命令响应模型
 * Created by moxin on 2021-07-01-0001
 *
 * <AUTHOR> Email: <EMAIL>
 */
@ApiModel(description = "命令响应模型")
@Data
public class CommandResponseModel {

    public static CommandResponseModel of(WaterMeterCmdEvent evt) {
        CommandResponseModel model = new CommandResponseModel();
        model.setCommandId(evt.getCommandId());
        model.setCommandState(evt.getCommandState());
        model.setCommandDesc(evt.getCommandDesc());
        model.setData(evt.getData());
        return model;
    }

    public static CommandResponseModel of(CommandRspDto commandRspDto) {
        CommandResponseModel model = new CommandResponseModel();
        model.setCommandId(commandRspDto.getCommandId());
        model.setCommandState(commandRspDto.getCommandStatus().name());
        model.setCommandDesc(commandRspDto.getCommandDesc());
        model.setData(commandRspDto.getData());
        return model;
    }

    public static CommandResponseModel of(CommandRespDto commandRespDto) {
        CommandResponseModel model = new CommandResponseModel();
        model.setCommandId(commandRespDto.getCommandId());
        model.setCommandState(commandRespDto.getCmdState().name());
        model.setCommandDesc(commandRespDto.getCmdDesc());
        model.setData(commandRespDto.getData());
        return model;
    }

    @ApiModelProperty(value = "命令ID", required = true)
    private String commandId;

    @ApiModelProperty(value = "命令状态", required = true)
    private String commandState;

    @ApiModelProperty(value = "命令状态描述", required = true)
    private String commandDesc;

    @ApiModelProperty(value = "响应数据，仅当 commandState=COMPLETED 时有值", required = true)
    private Object data;
}
