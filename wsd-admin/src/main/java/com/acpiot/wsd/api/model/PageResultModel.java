package com.acpiot.wsd.api.model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * Created by moxin on 2020-12-23-0023
 *
 * <AUTHOR> Email: <EMAIL>
 */
@ApiModel(description = "分页模型")
@Data
public class PageResultModel<T> {

    public static <T> PageResultModel<T> of(Page<T> page) {
        PageResultModel<T> model = new PageResultModel<>();
        model.setTotalPages(page.getTotalPages());
        model.setCurrentPage(page.getNumber());
        model.setSize(page.getSize());
        model.setContent(page.getContent());
        return model;
    }

    @ApiModelProperty("总页数")
    private int totalPages;

    @ApiModelProperty("当前页号，从0开始")
    private int currentPage;

    @ApiModelProperty("当前页数据数量")
    private int size;

    @ApiModelProperty("当前页数据")
    private List<T> content;

}
