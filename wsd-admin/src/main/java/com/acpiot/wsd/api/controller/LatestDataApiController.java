package com.acpiot.wsd.api.controller;

import com.acpiot.wsd.api.model.ApiResult;
import com.acpiot.wsd.api.model.PageResultModel;
import com.acpiot.wsd.api.model.dto.QueryLatestDataReqDto;
import com.acpiot.wsd.api.model.dto.QueryLatestDataRspDto;
import com.acpiot.wsd.api.service.LatestDataApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * Created by moxin on 2021-06-24-0024
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Api(tags = "最新水表数据")
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/api/data")
public class LatestDataApiController {

    private final LatestDataApiService latestDataApiService;

    @ApiOperation("获取最新水表数据")
    @PostMapping("latest")
    public ApiResult<PageResultModel<QueryLatestDataRspDto>> queryLatestData(@Valid @RequestBody QueryLatestDataReqDto reqDto) {
        PageResultModel<QueryLatestDataRspDto> model = latestDataApiService.queryLatestData(reqDto);
        return ApiResult.success(model);
    }

}
