package com.acpiot.wsd.api.facade.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.api.exception.ApiException;
import com.acpiot.wsd.api.exception.ErrorCode;
import com.acpiot.wsd.api.facade.DockingApiFacade;
import com.acpiot.wsd.api.model.dto.MeterDataDto;
import com.acpiot.wsd.api.model.dto.MeteringCmdRspDto;
import com.acpiot.wsd.api.model.dto.UploadMeterDataDto;
import com.acpiot.wsd.api.model.dto.ValveCtrlCmdRspDto;
import com.acpiot.wsd.data.archive.entity.WaterMeter;
import com.acpiot.wsd.data.archive.entity.WaterMeter_;
import com.acpiot.wsd.data.archive.service.MeterArchiveService;
import com.acpiot.wsd.common.enums.ValveStatus;
import com.acpiot.wsd.data.monitor.entity.MeterCmdLog;
import com.acpiot.wsd.data.monitor.entity.MeterCmdLog_;
import com.acpiot.wsd.data.monitor.entity.MeterData;
import com.acpiot.wsd.data.monitor.entity.MeterEvent;
import com.acpiot.wsd.data.monitor.enums.CommandState;
import com.acpiot.wsd.data.monitor.enums.MeterCmdType;
import com.acpiot.wsd.data.monitor.service.MonitorDataService;
import com.acpiot.wsd.data.sys.entity.ThirdDeviceDocking;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DockingApiFacadeImpl implements DockingApiFacade {

    private final MeterArchiveService meterArchiveService;
    private final MonitorDataService monitorDataService;
    private final Validator defaultValidator;

    @Override
    public void uploadMeterData(UploadMeterDataDto uploadMeterDataDto) {
        String meterCode = uploadMeterDataDto.getMeterCode();
        WaterMeter waterMeter = meterArchiveService.getWaterMeterByCode(meterCode, WaterMeter_.THIRD_DEVICE_DOCKING)
                .orElseThrow(() -> new ApiException(ErrorCode.CODE_NOT_FOUNT));
        ThirdDeviceDocking thirdDeviceDocking = waterMeter.getThirdDeviceDocking();
        if (thirdDeviceDocking == null) {
            throw new ApiException(500, "当前水表不属于第三方标准对接水表");
        }
        if (!thirdDeviceDocking.isEnable()) {
            throw new ApiException(500, "当前第三方标准对接方式未启用");
        }
        log.info("第三方标准对接：接收水表数据上传，开始处理，表号：{}，对接名称：{}", meterCode, thirdDeviceDocking.getName());
        // 将水表数据保存到数据库
        MeterData meterData = MeterDataDto.toMeterData(uploadMeterDataDto);
        meterData.setMeterDataType(MeterData.MeterDataType.DEVICE_REPORT);
        monitorDataService.saveMeterData(waterMeter.getId(), meterData);
        log.info("第三方标准对接：接收水表数据上传，处理成功，表号：{}，对接名称：{}", meterCode, thirdDeviceDocking.getName());
    }

    @Override
    public void meteringCmdRes(MeteringCmdRspDto meteringCmdRspDto) {
        CommandState commandState = toCommandStateEnum(meteringCmdRspDto.getCommandState());
        if (commandState == null) {
            throw new ApiException(500, "不支持的命令状态");
        }

        String commandId = meteringCmdRspDto.getCommandId();

        // 验证命令是否存在
        MeterCmdLog meterCmdLog = monitorDataService.getMeterCmdLogByCommandId(commandId,
                        StrUtil.format("{}.{}", MeterCmdLog_.WATER_METER, WaterMeter_.THIRD_DEVICE_DOCKING))
                .orElseThrow(() -> new ApiException(500, "不存在对应的命令"));
        if (meterCmdLog.getCmdType() != MeterCmdType.METERING_DATA) {
            throw new ApiException(500, "命令类型非抄表");
        }
        WaterMeter waterMeter = meterCmdLog.getWaterMeter();
        ThirdDeviceDocking thirdDeviceDocking = waterMeter.getThirdDeviceDocking();
        if (thirdDeviceDocking == null) {
            throw new ApiException(500, "命令所属水表不属于第三方标准对接水表");
        }
        if (!thirdDeviceDocking.isEnable()) {
            throw new ApiException(500, "当前第三方标准对接方式未启用");
        }

        log.info("第三方标准对接：抄表命令响应上传，开始处理，表号：{}，命令状态：{}", waterMeter.getCode(), commandState);

        // 抄表成功
        if (commandState == CommandState.COMPLETED) {
            MeterDataDto meterDataDto = meteringCmdRspDto.getMeterDataDto();
            if (meterDataDto == null) {
                throw new ApiException(500, "命令状态为已完成时，抄表数据不能为空");
            }
            Set<ConstraintViolation<MeterDataDto>> validate = defaultValidator.validate(meterDataDto);
            if (!validate.isEmpty()) {
                ConstraintViolation<MeterDataDto> constraintViolation = validate.stream().findFirst().get();
                throw new ApiException(500, "命令状态为已完成时，" + constraintViolation.getMessage());
            }

            // 将水表数据保存到数据库
            MeterData meterData = MeterDataDto.toMeterData(meterDataDto);
            meterData.setMeterDataType(MeterData.MeterDataType.MAIN_STATION);
            monitorDataService.saveMeterData(waterMeter.getId(), meterData);
        }

        // 更新命令详情
        String details = String.format("状态：%s，时间：%s", commandState.getDesc(), DateUtil.now());
        monitorDataService.updateMeterCmdStateAndDetails(meterCmdLog.getId(), commandState, details);

        log.info("第三方标准对接：抄表命令响应上传，处理成功，表号：{}，命令状态：{}", waterMeter.getCode(), commandState);
    }

    @Override
    public void valveCtrlCmdRes(ValveCtrlCmdRspDto valveCtrlCmdRspDto) {
        CommandState commandState = toCommandStateEnum(valveCtrlCmdRspDto.getCommandState());
        if (commandState == null) {
            throw new ApiException(500, "不支持的命令状态");
        }
        String commandId = valveCtrlCmdRspDto.getCommandId();

        // 验证命令是否存在
        MeterCmdLog meterCmdLog = monitorDataService.getMeterCmdLogByCommandId(commandId,
                        StrUtil.format("{}.{}", MeterCmdLog_.WATER_METER, WaterMeter_.THIRD_DEVICE_DOCKING))
                .orElseThrow(() -> new ApiException(500, "不存在对应的命令"));
        if (!meterCmdLog.getCmdType().isValveCtrl()) {
            throw new ApiException(500, "命令类型非阀控");
        }
        WaterMeter waterMeter = meterCmdLog.getWaterMeter();
        ThirdDeviceDocking thirdDeviceDocking = waterMeter.getThirdDeviceDocking();
        if (thirdDeviceDocking == null) {
            throw new ApiException(500, "命令所属水表不属于第三方标准对接水表");
        }
        if (!thirdDeviceDocking.isEnable()) {
            throw new ApiException(500, "当前第三方标准对接方式未启用");
        }

        log.info("第三方标准对接：阀控命令响应上传，开始处理，表号：{}，命令状态：{}", waterMeter.getCode(), commandState);

        // 阀控成功
        if (commandState == CommandState.COMPLETED) {
            String valveStatus = valveCtrlCmdRspDto.getValveStatus();
            if (StrUtil.isBlank(valveStatus)) {
                throw new ApiException(500, "命令状态为已完成时，阀门状态不能为空");
            }
            ValveStatus newValveStatus;
            try {
                newValveStatus = ValveStatus.valueOf(valveStatus);
            } catch (IllegalArgumentException e) {
                throw new ApiException(500, "不支持的阀门状态");
            }
            ValveStatus oldValveStatus = waterMeter.getLatestData() != null ? waterMeter.getLatestData().getValveStatus() : null;

            // 更新水表阀门状态
            monitorDataService.updateMeterValveState(waterMeter.getId(), newValveStatus);

            // 保存阀门状态变更事件
            MeterEvent meterEvent = new MeterEvent();
            meterEvent.setWaterMeter(waterMeter);
            meterEvent.setEventType(MeterEvent.EventType.VALVE_STATE_CHANGED);
            meterEvent.setDetails(StrUtil.format("水表 {} 阀门状态变化: {} -> {}", waterMeter.getCode(),
                    oldValveStatus == null ? "未知" : oldValveStatus.getDesc(), newValveStatus.getDesc()));
            monitorDataService.saveMeterEvent(meterEvent);
        }

        // 更新命令详情
        String details = String.format("状态：%s，时间：%s", commandState.getDesc(), DateUtil.now());
        monitorDataService.updateMeterCmdStateAndDetails(meterCmdLog.getId(), commandState, details);

        log.info("第三方标准对接：阀控命令响应上传，处理成功，表号：{}，命令状态：{}", waterMeter.getCode(), commandState);
    }

    private static CommandState toCommandStateEnum(int commandState) {
        switch (commandState) {
            case 1:
                return CommandState.SAVED;
            case 2:
                return CommandState.CREATED;
            case 3:
                return CommandState.DELIVERED;
            case 4:
                return CommandState.COMPLETED;
            case 5:
                return CommandState.TIMEOUT;
            case 6:
                return CommandState.FAILED;
            default:
                return null;
        }
    }

}
