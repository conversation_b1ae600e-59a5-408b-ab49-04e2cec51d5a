package com.acpiot.wsd.api.model.dto;

import cn.hutool.core.date.DateUtil;
import com.acpiot.wsd.data.monitor.entity.MeterCmdLog;
import com.acpiot.wsd.data.monitor.enums.CommandState;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 命令状态
 *
 * <AUTHOR>
 * @since 2024/06/07
 */
@Data
public class CmdStateRspDto {
    /**
     * 命令ID
     */
    @ApiModelProperty(value = "命令ID", required = true)
    private String commandId;

    /**
     * 命令类型
     */
    @ApiModelProperty(value = "命令类型：METERING_DATA(抄表)|" +
            "OPEN_VALVE(开阀)|" +
            "CLOSE_VALVE(关阀)", required = true)
    private String cmdType;

    /**
     * 命令状态
     */
    @ApiModelProperty(value = "命令状态：SAVED(已保存)|" +
            "CREATE_FAIL(创建失败)|CREATED(已创建)|" +
            "SENT(已发送)|DELIVERED(已送达)|" +
            "COMPLETED(已完成)|FAILED(失败)|" +
            "TIMEOUT(超时)|EXPIRED(过期)|" +
            "CANCELED(已撤消)", required = true)
    private CommandState commandState;

    /**
     * 命令详细
     */
    @ApiModelProperty(value = "命令详细", required = true)
    private String details;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = true)
    private String createdDate;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", required = true)
    private String lastModifiedDate;

    public CmdStateRspDto(MeterCmdLog meterCmdLog) {
        this.commandId = meterCmdLog.getCommandId();
        this.cmdType = meterCmdLog.getCmdType().name();
        this.commandState = meterCmdLog.getCommandState();
        this.details = meterCmdLog.getDetails();
        this.createdDate = DateUtil.format(meterCmdLog.getCreatedDate(), "yyyy-MM-dd HH:mm:ss");
        this.lastModifiedDate = DateUtil.format(meterCmdLog.getLastModifiedDate(), "yyyy-MM-dd HH:mm:ss");
    }
}
