package com.acpiot.wsd.tabletapp.res;

import com.acpiot.wsd.common.enums.Gender;
import com.acpiot.wsd.common.serializer.CompanyId;
import com.acpiot.wsd.data.sys.entity.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Delegate;

import javax.persistence.Enumerated;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserModel {

    public static UserModel of(User user) {
        UserModel userModel = new UserModel();
        userModel.setUserId(user.getId());
        userModel.setUserType(user.getUserType());
        userModel.setName(user.getName());
        userModel.setRealName(user.getRealName());
        userModel.setRoleNames(user.getRoleNames());
        userModel.setRoleCodes(user.getRoleCodes());
        userModel.setMobile(user.getMobile());
        userModel.setGender(user.getGender());
        userModel.setEmail(user.getEmail());
        userModel.setRemark(user.getRemark());
        userModel.setCompanyId(user.getCompanyId());
        userModel.setCompanyIdVal(user.getCompanyId());
        return userModel;
    }

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户类型
     */
    @Delegate
    private User.UserType userType;

    /**
     * 用户名
     */
    private String name;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 角色名
     */
    private String roleNames;

    /**
     * 角色编码
     */
    private String roleCodes;

    /**
     * 性别
     */
    @Enumerated
    private Gender gender = Gender.UNKNOWN;

    /**
     * 管理员邮箱
     */
    private String email;

    /**
     * 管理员手机号
     */
    private String mobile;

    /**
     * 备注
     */
    private String remark;

    /**
     * 登录成功后的token
     */
    private String token;

    /**
     * 公司ID
     */
    @CompanyId
    private String companyId;

    /**
     * 公司ID（原始值）
     */
    private String companyIdVal;
}
