package com.acpiot.wsd.tabletapp.service.impl;

import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.data.sys.entity.User;
import com.acpiot.wsd.data.sys.entity.User_;
import com.acpiot.wsd.data.sys.repository.UserRepository;
import com.acpiot.wsd.tabletapp.service.MineTpService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.utils.AttributeReplication;

import static com.acpiot.wsd.common.security.util.SecurityContextUtils.getLoginUsername;

@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class MineTpServiceImpl implements MineTpService {

    private final UserRepository userRepository;

    @Override
    public void updateInfo(User user) {
        String username = getLoginUsername();
        User dbUser = userRepository.findUniqueBy(User_.name, username)
                .orElseThrow(() -> new BusinessException("用户(" + username + ")不存在"));

        String mobile = user.getMobile();
        if (StrUtil.isNotBlank(mobile)) {
            user.setId(dbUser.getId());
            if (!userRepository.isUnique(user, User_.mobile)) {
                throw new BusinessException("手机号(" + mobile + ")已存在");
            }
        }

        AttributeReplication.copying(user, dbUser,
                User_.realName,
                User_.gender,
                User_.email,
                User_.mobile,
                User_.remark
        );
    }

    @Override
    public boolean changePassword(String oldPassword, String newPassword) {
        String username = getLoginUsername();
        User dbUser = userRepository.findUniqueBy(User_.name, username)
                .orElseThrow(() -> new BusinessException("用户(" + username + ")不存在"));
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        if (encoder.matches(oldPassword, dbUser.getPassword())) {
            dbUser.setPassword(encoder.encode(newPassword));
            return true;
        }
        return false;
    }
}
