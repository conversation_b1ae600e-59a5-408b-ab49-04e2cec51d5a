package com.acpiot.wsd.docking.ysxt.base.call;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import java.security.*;
import java.security.cert.X509Certificate;
import java.util.concurrent.TimeUnit;

/**
 * 回调第三方API接口定义
 * Created by hsq on 2021-09-13-0001
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Slf4j
public class YsxtCallUtil {

    public static YsxtCallService ysxtCallService(ObjectMapper objectMapper, String baseUrl) throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        HttpLoggingInterceptor interceptor = new HttpLoggingInterceptor(log::info);
        interceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .addInterceptor(interceptor)
                // 添加证书
                .sslSocketFactory(getSSLContext().getSocketFactory(), getDefaultTrustManager())
                .addInterceptor(chain -> {
                    Request.Builder builder = chain.request().newBuilder();
                    builder.addHeader("X-API-VERSION", "v1.0");
                    return chain.proceed(builder.build());
                })
                .followRedirects(false)
                .build();
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(baseUrl)
                .client(client)
                .addConverterFactory(JacksonConverterFactory.create(objectMapper))
                .build();
        return retrofit.create(YsxtCallService.class);
    }

    private static SSLContext getSSLContext() throws NoSuchAlgorithmException, KeyManagementException {
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, getTrustManager(), new SecureRandom());
        return sslContext;
    }

    private static TrustManager[] getTrustManager() {
        return new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                }
        };
    }

    private static X509TrustManager getDefaultTrustManager() throws NoSuchAlgorithmException, KeyStoreException {
        TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        trustManagerFactory.init((KeyStore) null);
        TrustManager[] trustManagers = trustManagerFactory.getTrustManagers();
        if (trustManagers.length == 0 || !(trustManagers[0] instanceof X509TrustManager)) {
            throw new IllegalStateException("No X509TrustManager found");
        }
        return (X509TrustManager) trustManagers[0];
    }
}
