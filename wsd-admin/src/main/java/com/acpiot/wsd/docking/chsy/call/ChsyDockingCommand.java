package com.acpiot.wsd.docking.chsy.call;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.microservice.meterservice.dto.CommandStatus;
import com.acpiot.wsd.docking.chsy.properties.ChsyProperties;
import com.acpiot.wsd.docking.chsy.req.ChsyValveCtrlReq;
import com.acpiot.wsd.docking.chsy.res.ChsyCommonRes;
import com.acpiot.wsd.docking.chsy.res.ChsyValveCtrlRes;
import com.acpiot.wsd.docking.common.command.DockingCommand;
import com.acpiot.wsd.docking.common.command.DockingResult;
import com.acpiot.wsd.common.service.CommonService;
import com.acpiot.wsd.data.archive.entity.WaterMeter;
import com.acpiot.wsd.data.archive.entity.NbWaterMeter;
import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.data.monitor.bean.ValveCtrlParams;
import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 调用册亨思源-山科命令接口
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ChsyDockingCommand implements DockingCommand {

    private final ChsyProperties chsyProperties;
    private final ChsyCallProvider chsyCallProvider;
    private final CommonService commonService;
    @Value("${third-system-docking.chsy.enable}")
    private boolean enable;

    /**
     * 开阀
     *
     * @param waterMeter
     * @param valveCtrlParams
     * @return
     */
    @Override
    public DockingResult openValve(WaterMeter waterMeter, ValveCtrlParams valveCtrlParams) {
        if (!enable) {
            throw new BusinessException("当前对接(chsy)权限未启用，请联系管理员");
        }
        return valveCtrl(waterMeter, 1);
    }

    /**
     * 关阀
     *
     * @param waterMeter
     * @param valveCtrlParams
     * @return
     */
    @Override
    public DockingResult closeValve(WaterMeter waterMeter, ValveCtrlParams valveCtrlParams) {
        if (!enable) {
            throw new BusinessException("当前对接(chsy)权限未启用，请联系管理员");
        }
        return valveCtrl(waterMeter, 0);
    }

    private DockingResult valveCtrl(WaterMeter waterMeter, int state) {
        NbWaterMeter nbWaterMeter = commonService.findNbWaterMeterById(waterMeter.getId()).orElseThrow(() -> new BusinessException("不存在当前水表"));
        // 构建册亨思源开阀参数
        ChsyValveCtrlReq chsyValveCtrlReq = new ChsyValveCtrlReq();
        chsyValveCtrlReq.setPermissionId(chsyProperties.getPermissionId());
        chsyValveCtrlReq.setState(state);
        chsyValveCtrlReq.setDevices(List.of(new ChsyValveCtrlReq.ChsyDevice(StrUtil.padPre(nbWaterMeter.getCode(), 20, "0"), nbWaterMeter.getNbInfo().getImei())));
        log.info("册亨思源阀控请求参数：{}", new Gson().toJson(chsyValveCtrlReq));
        ChsyCommonRes<List<ChsyValveCtrlRes>> listChsyCommonRes = chsyCallProvider.valveCtrl(chsyValveCtrlReq);
        // 执行阀控结果回调并获取接口响应
        log.info("册亨思源阀控请求结果：{}", new Gson().toJson(listChsyCommonRes));
        // 随机生成commandId
        String commandId = IdUtil.fastSimpleUUID().substring(0, 30);
        // 判断是否成功发送
        List<ChsyValveCtrlRes> errorList = listChsyCommonRes.getData();
        if (errorList.isEmpty()) {
            return DockingResult.builder()
                    .commandId(commandId)
                    .commandStatus(CommandStatus.COMPLETED)
                    .commandDesc("已完成")
                    .build();
        }
        return DockingResult.builder()
                .commandId(commandId)
                .commandStatus(CommandStatus.FAILED)
                .commandDesc(errorList.get(0).getError())
                .build();
    }
}
