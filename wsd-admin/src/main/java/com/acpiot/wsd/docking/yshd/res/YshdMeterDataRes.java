package com.acpiot.wsd.docking.yshd.res;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.acpiot.wsd.data.monitor.entity.MeterData;
import com.acpiot.wsd.data.monitor.entity.MeterStatus;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 抄表请求结果
 * Created by YoungLu on 2024/03/28
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@ToString
public class YshdMeterDataRes {

    /**
     * 返回码 , 为0表示正常返回，否则为错误码
     */
    @JsonProperty("Code")
    private int code;

    /**
     * 返回数据结构
     */
    @JsonProperty("Response")
    private List<YshdMeterDataDto> response;

    /**
     * 异常消息文本
     */
    @JsonProperty("Message")
    private String message;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class YshdMeterDataDto {

        /**
         * 用户编号
         */
        private String custid;

        /**
         * 水表编号
         */
        private String no;

        /**
         * 抄表日期 yyyy-MM-dd HH:mm:ss
         */
        private String mrdate;

        /**
         * 抄见值
         */
        private int mrread;

        public MeterData toMeterData() {
            MeterData meterData = new MeterData();
            meterData.setValue(BigDecimal.valueOf(mrread));
            meterData.setMeterDataType(MeterData.MeterDataType.DEVICE_REPORT);
            DateTime meteringTime = DateUtil.parse(mrdate, "yyyy-MM-dd HH:mm:ss");
            meterData.setMeteringTime(meteringTime);
            meterData.setMeterTime(meteringTime);
            meterData.setMeterStatus(new MeterStatus());
            return meterData;
        }
    }
}