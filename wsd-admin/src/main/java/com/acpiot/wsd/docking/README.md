[toc]

# 包结构说明

|  包名  |  说明  |
|  ----  | ----  |
|com.acpiot.wsd|该包仅开发【道成&武强水务局】平台对接的定制化业务|
| com.acpiot.wsd.docking.common  | 该包存放所有第三方对接业务公共的部分 |
| com.acpiot.wsd.docking.dcwqswj  | 该包仅开发【道成&武强水务局】平台对接的定制化业务 |

# 对接说明

## 道成&武强水务局

### 登录认证

##### 简要描述

- 请求河南卓正获取登录认证

##### 请求URL

- `http://ip:port/api/auth/user/login`

##### 请求方式

- POST

##### 参数

| 参数名   | 必选 | 类型   | 说明     |
| :------- | :--- | :----- | -------- |
| username | 是   | string | 登录账号 |
| password | 是   | string | 接口密码 |

##### 返回参数说明

| 参数名 | 类型   | 说明                                 |
| :----- | :----- | ------------------------------------ |
| code   | int    | 状态码(200：获取成功；400：获取失败) |
| info   | string | 提示信息                             |
| data   | DataDo | 返回数据（请求失败时返回null）       |

##### DataDo

| 参数名   | 类型       | 说明                                 |
| :------- | :--------- | ------------------------------------ |
| baseinfo | BaseInfoDo | 登录用户基本信息（可以忽略，不使用） |
| token    | string     | 令牌                                 |

##### 请求示例

```
  {
  	"password": "a5c85f32ed50a49f",
  	"username": "System"
  }
```

##### 返回示例

```
{
 	"code": 200,
 	"info": "响应成功",
 	"data": {
 		"baseinfo": {},
 		"token": "2bc691e3-9a89-42a8-8634-27eeee83aabc"
 	}
}
```

  

### 数据上报

##### 简要描述

- 填写表地址、表读数和冻结时间

##### 请求URL

- `http://ip:port/api/ThirdDataReport/DataReport`

##### 请求方式

- POST

##### 参数

| 参数名         | 必选 | 类型    | 说明                                      |
| :------------- | :--- | :------ | ----------------------------------------- |
| meterAddr      | 是   | string  | 表地址                                    |
| readNumber     | 是   | decimal | 表读数                                    |
| freezeDateTime | 是   | string  | 冻结时间（yyyy-MM-dd HH:mm:ss）           |
| voltage        | 否   | decimal | 电池电压                                  |
| csq            | 否   | int     | 信号值                                    |
| valveState     | 否   | int     | 阀门状态（1：开阀，2：关阀，3：阀门异常） |
| token          | 是   | string  | 令牌（通过登录认证获取）                  |

##### 返回参数说明

| 参数名 | 类型   | 说明                               |
| :----- | :----- | ---------------------------------- |
| code   | int    | 状态码（200上传成功，400上传失败） |
| info   | string | 提示信息                           |
| data   | DataDo | 返回信息（上传失败返回null）       |

##### DataDo

| 参数名 | 类型   | 说明        |
| :----- | :----- | ----------- |
| Result | int    | 1：上传成功 |
| Msg    | string | 提示信息    |

##### 请求示例

```
{  "meterAddr": "88888888",  "readNumber": 1.23,  "freezeDateTime": "2020-11-30 16:29:00",  "token": "64978680-583a-4c5e-bd0a-3f27180a83a8"}
```

##### 返回示例（上传成功）

```
{
	"code": 200,
	"info": "响应成功",
	"data": {
		"Result": "1",
		"Msg": "数据上传成功"
  } 
}
```

##### 返回示例（上传失败）

```
{
	"code": 400,
	"info": "表档案不存在",
	"data": {}
}
```

### 阀门控制

#### 阀控-指令下发

##### 简要描述

- 根据开关阀参数进行开阀、关阀操作，并且返回开关阀对应的命令ID
- 此接口由表商进行实现

##### 请求URL

- `http://IP:Port/Site/SwitchMeter`

##### 请求方式

- POST

##### 请求参数

| 参数名      | 必选 | 类型                       | 说明                             |
| :---------- | :--- | :------------------------- | -------------------------------- |
| ValveWrites | 是   | IEnumerable<ValveWriteDto> | 开关阀水表数据(支持多水表开关阀) |
| Account     | 是   | AccountDto                 | 鉴权账户信息                     |

##### ValveWriteDto

| 参数名    | 必选 | 类型   | 说明                        |
| :-------- | :--- | :----- | --------------------------- |
| MeterAddr | 是   | string | 水表表地址                  |
| Command   | 是   | int    | 开关阀指令（0:关阀、1:开阀) |
| Reason    | 否   | string | 开关阀原因（允许空）        |
| Remark    | 否   | string | 备注（允许空）              |

##### AccountDto

| 参数名   | 必选 | 类型   | 说明 |
| :------- | :--- | :----- | ---- |
| UserName | 是   | string | 账号 |
| Password | 是   | string | 密码 |

------

##### 返回参数

| 参数名   | 类型      | 说明                              |
| :------- | :-------- | --------------------------------- |
| Success  | bool      | 是否成功（True:成功、False:失败） |
| ErrorMsg | string    | 错误消息                          |
| Result   | ResultDto | 返回数据对象                      |

##### ResultDto

| 参数名 | 类型                     | 说明                                         |
| :----- | :----------------------- | -------------------------------------------- |
| Total  | int                      | 记录条数                                     |
| Data   | IEnumerable<MeterCmdDto> | 水表读数列表（查无此水表，则不包含返回值内） |

##### MeterCmdDto

| 参数名     | 类型   | 说明                                                         |
| :--------- | :----- | ------------------------------------------------------------ |
| MeterAddr  | string | 表地址                                                       |
| SendStatus | bool   | true 下发成功 false 下发失败                                 |
| CommandId  | string | 当前水表下发的开关阀命令ID（注意：如果为空则默认是无回调的） |

##### 参数示例

```
{
	"ValveWrites":[{
		"MeterAddr": "********",
		"Command": 0,
		"Reason":"欠费关阀门",
		"Remark":""
		}],
		"Account":{
		"UserName":"superadmin",
		"Password":"123456"
		}
}
```

##### 返回示例

```
{
	"Success": true,
  "ErrorMsg":"", 
  "Result":{
  	"Total":2,
  	"Data": [{
  		"MeterAddr": "********",
  		"SendStatus": 1,
  		"CommandId": "0dd6a42b-5dc7-4c6e-bbee-d7bb4bb7bce1"
  	},
  	{
  	"MeterAddr": "********",
  	"SendStatus": 1,
  	"CommandId": "0dd6a42b-5dc7-4c6e-bbee-d7bb4bb7bce1"
  	}]
  }
}
```

#### 阀控-执行回调

##### 简要描述

- 回写开关阀命令执行结果，对应水表的真实状态
- 此接口由收费方实现，由抄表方调用

##### 请求URL

- `http://IP:Port/api/data/CallBackMeterStatus`

##### 请求方式

- POST

##### 参数

| 参数名       | 必选 | 类型   | 说明                                   |
| :----------- | :--- | :----- | -------------------------------------- |
| MeterAddr    | 是   | string | 表号                                   |
| CommandId    | 是   | string | 回调的命令ID（要保证其唯一性）         |
| ValveStatus  | 是   | int    | 水表当前阀门状态阀门状态（0:关、1:开） |
| ExecuteState | 是   | string | 详见下方注释                           |
| Account      | 是   | string | 账号                                   |
| ApiKey       | 是   | string | 接口验证Key                            |

##### ExecuteState

| 参数名     | 说明                   |
| :--------- | ---------------------- |
| PENDING    | 表示缓存未下发         |
| EXPIRED    | 表示命令已经过期       |
| SUCCESSFUL | 表示命令已经成功执行   |
| FAILED     | 表示命令执行失败       |
| TIMEOUT    | 表示命令下发执行超时   |
| CANCELED   | 表示命令已经被撤销执行 |
| DELIVERED  | 表示命令已送达设备     |
| SENT       | 表示命令正在下发       |

##### 返回参数

| 参数名   | 类型   | 说明                              |
| :------- | :----- | --------------------------------- |
| Success  | bool   | 是否成功（True:成功、False:失败） |
| ErrorMsg | string | 错误消息                          |
| Message  | string | 回调信息回传                      |

##### 请求示例

```
{
	"MeterAddr": "********",
	"CommandId": "1",
	"ValveStatus":1,
	"ExecuteState":"SUCCESSFUL",
	"Account":"superadmin",
	"ApiKey":"6e2e6383444945af8d7e22470e0df710"
}
```

##### 返回示例

```
   {
   "Success": true,
   "ErrorMsg":"",
   "Message":"回写成功"
   }
```

### 数据查询

##### 简要描述

- 查询当前读数

##### 请求URL

- `http://ip:port/api/data/GetReadNowData`

##### 请求方式

- GET

##### 参数

| 参数名     | 必选 | 类型     | 说明                                                       |
| :--------- | :--- | :------- | ---------------------------------------------------------- |
| meterAddrs | 否   | string[] | 表地址集合（为null或者不填表地址，则查询全部表的当前读数） |

##### 返回参数说明

| 参数名   | 类型      | 说明                               |
| :------- | :-------- | ---------------------------------- |
| Success  | bool      | 是否成功（true：成功，false:失败） |
| ErrorMsg | string    | 错误提示                           |
| Result   | ResultDto | 返回数据对象                       |

##### ResultDto

| 参数名 | 类型            | 说明               |
| :----- | :-------------- | ------------------ |
| Total  | int             | 返回日用量数据条数 |
| Data   | FreezeDayDosage | 返回数据对象       |

##### FreezeDayDosage

| 参数名      | 类型   | 说明                       |
| :---------- | :----- | -------------------------- |
| Address     | string | 安装地址                   |
| OnlineState | int    | 在线状态，在线：1，离线：0 |
| MeterAddr   | string | 表地址                     |
| ReadNumber  | string | 当前读数                   |
| ReadData    | string | 抄表时间                   |

##### 返回示例

```
{
	"Success": true,
	"ErrorMsg": null,
	"Result": {
		"Total": 3,
		"Data": [
		{
		"Address": "",
		"OnlineState": 1,
		"MeterAddr": "102006170001",
		"ReadNumber": "41.7000",
		"ReadData": "2021-04-20 06:00:11"
		},
		{
		"Address": "",
		"OnlineState": 1,
		"MeterAddr": "102006170002",
		"ReadNumber": "14.0000",
		"ReadData": "2021-04-20 06:00:04"
		},
		{
		"Address": "",
		"OnlineState": 1,
		"MeterAddr": "102006170003",
		"ReadNumber": "37.0000",
		"ReadData": "2021-04-20 06:00:11"
		}]    
	}
}
```



