package com.acpiot.wsd.docking.hysw.req.body;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 拆表命令请求体
 * Created by YoungLu on 2022-11-04-0001
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class HyDismantleRequestBody {

    @Valid
    @NotNull(message = "拆表命令请求体不能为空")
    @JsonProperty("dismantleMeterRequest")
    private DismantleMeterRequest dismantleMeterRequest;

    @Data
    public static class DismantleMeterRequest {
        /**
         * 水表编号
         */
        @JsonProperty("meterNo")
        @NotBlank(message = "水表编号不能为空")
        @Size(max = 20, message = "水表编号长度不能超过20位")
        private String meterNo;

        /**
         * 最后读数 13位数字（3位小数）
         */
        @JsonProperty("lastReading")
        @NotNull(message = "最后读数不能为空")
        private BigDecimal lastReading;

        /**
         * 拆表日期 YYYYMMDD
         */
        @JsonProperty("dismantleDate")
        @NotBlank(message = "拆表日期不能为空")
        @Pattern(regexp = "^[1-9]\\d{3}(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])$", message = "拆表日期格式错误")
        private String dismantleDate;
    }
}
