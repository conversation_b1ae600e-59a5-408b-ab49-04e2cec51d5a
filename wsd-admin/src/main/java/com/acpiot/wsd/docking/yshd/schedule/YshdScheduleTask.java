package com.acpiot.wsd.docking.yshd.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.acpiot.wsd.docking.common.util.ApiCallUtil;
import com.acpiot.wsd.docking.yshd.call.YshdCallService;
import com.acpiot.wsd.docking.yshd.properties.YshdProperties;
import com.acpiot.wsd.docking.yshd.req.YshdMeterDataReq;
import com.acpiot.wsd.docking.yshd.res.YshdMeterDataRes;
import com.acpiot.wsd.docking.yshd.service.YshdMeterDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = {"third-system-docking.yshd.enable"}, havingValue = "true")
public class YshdScheduleTask {

    private final YshdCallService yshdCallService;
    private final YshdProperties yshdProperties;
    private final YshdMeterDataService yshdMeterDataService;

    @Async
    @Scheduled(cron = "0 0 9 * * ?")
    public void fetchMeterData() {
        YshdMeterDataReq yshdMeterDataReq = new YshdMeterDataReq();
        yshdMeterDataReq.setDate(DateUtil.format(new Date(), "yyyy-MM-dd"));
        yshdMeterDataReq.setToken(yshdProperties.getAccessToken());

        // 调用接口获取所有抄表数据
        YshdMeterDataRes yshdMeterDataRes = ApiCallUtil.execute("云水和达-抄表", yshdCallService.getMeterData(yshdMeterDataReq));
        if (yshdMeterDataRes.getCode() != 0) {
            log.error("云水和达抄表失败，调用和达接口获取抄表数据失败，错误原因：{}", yshdMeterDataRes.getMessage());
            return;
        }
        List<YshdMeterDataRes.YshdMeterDataDto> yshdMeterDataDtos = yshdMeterDataRes.getResponse();
        if (CollUtil.isEmpty(yshdMeterDataDtos)) {
            log.error("云水和达抄表失败，调用和达接口获取抄表数据失败，抄表数据为空");
            return;
        }
        int batchSize = 100;
        // 计算需要保存的批次数量
        int batchCount = (int) Math.ceil((double) yshdMeterDataDtos.size() / batchSize);
        log.info("云水和达抄表数据处理开始，处理数据总数：{}。数据拆分保存中（100条/批，共{}批）", yshdMeterDataDtos.size(), batchCount);
        // 遍历数据并分批次保存
        for (int i = 0; i < batchCount; i++) {
            int start = i * batchSize;
            int end = Math.min(start + batchSize, yshdMeterDataDtos.size());
            // 获取当前批次的数据
            List<YshdMeterDataRes.YshdMeterDataDto> currentBatch = yshdMeterDataDtos.subList(start, end);
            // 保存当前批次的数据
            log.info("云水和达抄表数据处理开始，当前批次数：{}，批次处理数据总数：{}", i + 1, currentBatch.size());
            yshdMeterDataService.saveMeterData(currentBatch);
        }
    }
}
