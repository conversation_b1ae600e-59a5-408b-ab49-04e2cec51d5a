package com.acpiot.wsd.docking.chsy.call;

import com.acpiot.wsd.docking.chsy.req.ChsyLatestDataReq;
import com.acpiot.wsd.docking.chsy.req.ChsyValveCtrlReq;
import com.acpiot.wsd.docking.chsy.res.ChsyCommonRes;
import com.acpiot.wsd.docking.chsy.res.ChsyLatestDataRes;
import com.acpiot.wsd.docking.chsy.res.ChsyValveCtrlRes;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.List;

/**
 * 回调第三方接口声明
 */
public interface ChsyCallService {

    /**
     * 查询最新数据
     *
     * @param chsyLatestDataReq
     * @return
     */
    @POST("/Api/LatestData/GetListLatestData")
    Call<ChsyCommonRes<List<ChsyLatestDataRes>>> getListLatestData(@Body ChsyLatestDataReq chsyLatestDataReq);

    /**
     * 开关阀指令
     *
     * @param chsyValveCtrlReq
     * @return
     */
    @POST("/api/protocolcommand/valvecommandbatchexternal")
    Call<ChsyCommonRes<List<ChsyValveCtrlRes>>> valveCtrl(@Body ChsyValveCtrlReq chsyValveCtrlReq);
}
