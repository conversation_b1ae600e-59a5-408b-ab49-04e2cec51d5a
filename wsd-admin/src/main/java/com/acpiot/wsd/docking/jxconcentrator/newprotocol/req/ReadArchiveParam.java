package com.acpiot.wsd.docking.jxconcentrator.newprotocol.req;

import lombok.*;
import org.hibernate.validator.constraints.Range;

import static com.acpiot.wsd.docking.jxconcentrator.newprotocol.util.NewProtocolUtil.serialNoToTn;

/**
 * 读取档案参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ReadArchiveParam {

    public static ReadArchiveParam of(int serialNo) {
        int[] tnNos = serialNoToTn(serialNo);
        return new ReadArchiveParam(tnNos[0], tnNos[1]);
    }

    /**
     * 采集器 TN 号
     */
    @Range(min = 0, max = 255)
    private int collectorTn;

    /**
     * 表 TN 号
     */
    @Range(min = 1, max = 255)
    private int meterTn;
}
