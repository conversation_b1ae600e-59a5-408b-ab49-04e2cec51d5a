package com.acpiot.wsd.docking.jxconcentrator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.acpiot.wsd.docking.jxconcentrator.common.service.JxConcentratorMonitorService;
import com.acpiot.wsd.admin.mvc.monitor.service.MonitorConcentratorService;
import com.acpiot.wsd.common.aop.annotation.SystemLog;
import com.acpiot.wsd.data.archive.entity.ConcentratorWaterMeter;
import com.acpiot.wsd.common.exception.BusinessException;
import com.acpiot.wsd.data.monitor.enums.ConcentratorCmdType;
import com.acpiot.wsd.docking.jxconcentrator.common.JxConcentratorCmdExecutor;
import com.acpiot.wsd.docking.jxconcentrator.common.req.*;
import com.acpiot.wsd.docking.jxconcentrator.common.res.ExecuteResult;
import com.acpiot.wsd.docking.jxconcentrator.newprotocol.call.JxNewCallService;
import com.acpiot.wsd.docking.jxconcentrator.newprotocol.res.JxNewMeterArchive;
import com.acpiot.wsd.docking.jxconcentrator.oldprotocol.call.JxOldCallService;
import com.acpiot.wsd.docking.jxconcentrator.oldprotocol.res.JxOldMeterArchive;
import lombok.RequiredArgsConstructor;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Created by YoungLu on 2022-10-24-0001
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/monitor/jxMonitor")
public class JxMonitorController {

    private final JxConcentratorMonitorService jxConcentratorMonitorService;
    private final MonitorConcentratorService monitorConcentratorService;
    private final JxConcentratorCmdExecutor jxConcentratorCmdExecutor;
    private final JxOldCallService jxOldCallService;
    private final JxNewCallService jxNewCallService;

    /**
     * 缓存同步档案中的集中器
     */
    private final Set<String> syncingConcentrators = new HashSet<>();

    @SystemLog(description = "捷先集中器-初始化")
    @PostMapping("resetData")
    public ExecuteResult<Boolean> resetData(@RequestParam String concentratorCode) {
        return jxConcentratorMonitorService.resetData(concentratorCode);
    }

    @SystemLog(description = "捷先集中器-读取版本")
    @GetMapping("readVersion")
    public ExecuteResult<ConcentratorVersion> readVersion(@RequestParam String concentratorCode) {
        return jxConcentratorMonitorService.readVersion(concentratorCode);
    }

    @SystemLog(description = "捷先集中器-同步系统时钟")
    @PostMapping("syncClock")
    public ExecuteResult<Boolean> syncClock(@RequestParam String concentratorCode) {
        return jxConcentratorMonitorService.syncClock(concentratorCode, LocalDateTimeUtil.now());
    }

    @SystemLog(description = "捷先集中器-查询集中器时钟")
    @GetMapping("readClock")
    public ExecuteResult<ConcentratorClock> readClock(@RequestParam String concentratorCode) {
        return jxConcentratorMonitorService.readClock(concentratorCode);
    }

    @SystemLog(description = "捷先集中器-设置是否允许上报")
    @PostMapping("setReport")
    public ExecuteResult<Boolean> setReport(@RequestParam String concentratorCode, @RequestParam boolean allowReport) {
        return jxConcentratorMonitorService.setReport(concentratorCode, allowReport);
    }

    @SystemLog(description = "捷先集中器-读取是否允许上报")
    @GetMapping("readReport")
    public ExecuteResult<ReportParam> readReport(@RequestParam String concentratorCode) {
        return jxConcentratorMonitorService.readReport(concentratorCode);
    }

    @SystemLog(description = "捷先集中器-设置上报时间")
    @PostMapping("setReportTime")
    public ExecuteResult<Boolean> setReportTime(@RequestParam String concentratorCode, @RequestParam ReportTime.ReportPeriod reportPeriod, @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime sendBaseTime) {
        ReportTime reportTime = new ReportTime();
        reportTime.setReportPeriod(reportPeriod);
        reportTime.setSendBaseTime(sendBaseTime);
        return jxConcentratorMonitorService.setReportTime(concentratorCode, reportTime);
    }

    @SystemLog(description = "捷先集中器-读取上报时间")
    @GetMapping("readReportTime")
    public ExecuteResult<ReportTime> readReportTime(@RequestParam String concentratorCode) {
        return jxConcentratorMonitorService.readReportTime(concentratorCode);
    }

    @SystemLog(description = "捷先集中器-设置GPRS参数")
    @PostMapping("setGprsParam")
    public ExecuteResult<Boolean> setGprsParam(@RequestParam String concentratorCode,
                                               @RequestParam String masterIp,
                                               @RequestParam
                                               @DecimalMin(value = "1", message = "主用端口必须在1-65535之间")
                                               @DecimalMax(value = "65535", message = "主用端口必须在1-65535之间") int masterPort,
                                               @RequestParam String apn) {
        GprsParam gprsParam = new GprsParam();
        gprsParam.setMasterIp(masterIp);
        gprsParam.setMasterPort(masterPort);
        gprsParam.setApn(apn);
        return jxConcentratorMonitorService.setGprsParam(concentratorCode, gprsParam);
    }

    @SystemLog(description = "捷先集中器-读取GPRS参数")
    @GetMapping("readGprsParam")
    public ExecuteResult<GprsParam> readGprsParam(@RequestParam String concentratorCode) {
        return jxConcentratorMonitorService.readGprsParam(concentratorCode);
    }

    @SystemLog(description = "捷先集中器-重启集中器")
    @PostMapping("reboot")
    public ExecuteResult<Boolean> reboot(@RequestParam String concentratorCode) {
        return jxConcentratorMonitorService.reboot(concentratorCode);
    }

    @SystemLog(description = "捷先集中器-设置考核大表采集器周期")
    @PostMapping("setCollectPeriod")
    public ExecuteResult<Boolean> setCollectPeriod(@RequestParam String concentratorCode,
                                                   @RequestParam
                                                   @DecimalMin(value = "1", message = "周期必须在1-65535之间")
                                                   @DecimalMax(value = "65535", message = "周期必须在1-65535之间") int minutes) {
        CollectPeriod collectPeriod = new CollectPeriod(minutes);
        return jxConcentratorMonitorService.setCollectPeriod(concentratorCode, collectPeriod);
    }

    @Synchronized
    @SystemLog(description = "旧版捷先集中器-同步表档案")
    @PostMapping("setArchives")
    public boolean setArchives(@RequestParam String concentratorCode) {
        if (syncingConcentrators.contains(concentratorCode)) {
            throw new BusinessException("集中器当前正在同步档案");
        }

        // 获取要下载到集中器的档案数据
        List<ConcentratorWaterMeter> waterMeters = jxConcentratorMonitorService.getConcentratorArchives(concentratorCode);
        if (CollUtil.isEmpty(waterMeters)) {
            throw new BusinessException("无档案信息需要执行同步");
        }

        CompletableFuture.runAsync(() -> {
            try {
                List<List<ConcentratorWaterMeter>> split = CollUtil.split(waterMeters, 10);
                for (List<ConcentratorWaterMeter> splitMeters : split) {
                    ExecuteResult<?> result = null;
                    for (int i = 0; i < 2; i++) {
                        result = jxConcentratorCmdExecutor.execCmd("下载集中器档案",
                                () -> {
                                    List<JxOldMeterArchive> splitArchives = splitMeters.stream()
                                            .map(JxOldMeterArchive::of)
                                            .collect(Collectors.toList());
                                    return jxOldCallService.setArchives(new CommandReqDto<>(concentratorCode, splitArchives));
                                });
                        if (result.getExecuteState() == ExecuteResult.ExecuteState.COMPLETED) {
                            break;
                        }
                    }

                    // 保存命令日志
                    monitorConcentratorService.addConcentratorCmdLog(concentratorCode, ConcentratorCmdType.SET_ARCHIVES, result.getCommandId(), result.getMessage());

                    if (result.getExecuteState() != ExecuteResult.ExecuteState.COMPLETED
                            || !((boolean) result.getFnResult().getData())) {
                        // 指令执行失败或者应答失败
                        log.info("捷先集中器 {} 同步档案失败", concentratorCode);
                        break;
                    }

                    // 更新数据库
                    jxConcentratorMonitorService.handleSetArchivesSuccess(concentratorCode, splitMeters);

                    // 旧协议集中器连续的档案同步成功率很低，这里加入一定的延时
                    ThreadUtil.sleep(200);
                }
            } finally {
                syncingConcentrators.remove(concentratorCode);
            }
        });
        syncingConcentrators.add(concentratorCode);
        return true;
    }

    @Synchronized
    @SystemLog(description = "新版捷先集中器-同步表档案")
    @PostMapping("newSetArchives")
    public boolean newSetArchives(@RequestParam String concentratorCode) {
        if (syncingConcentrators.contains(concentratorCode)) {
            throw new BusinessException("集中器当前正在同步档案");
        }

        // 获取要下载到集中器的档案数据
        List<ConcentratorWaterMeter> waterMeters = jxConcentratorMonitorService.getConcentratorArchives(concentratorCode);
        if (CollUtil.isEmpty(waterMeters)) {
            throw new BusinessException("无档案信息需要执行同步");
        }

        CompletableFuture.runAsync(() -> {
            try {
                List<List<ConcentratorWaterMeter>> split = CollUtil.split(waterMeters, 10);
                for (List<ConcentratorWaterMeter> splitMeters : split) {
                    ExecuteResult<?> result = null;
                    for (int i = 0; i < 2; i++) {
                        result = jxConcentratorCmdExecutor.execCmd("下载集中器档案",
                                () -> {
                                    List<JxNewMeterArchive> splitArchives = splitMeters.stream()
                                            .map(JxNewMeterArchive::of)
                                            .collect(Collectors.toList());
                                    return jxNewCallService.setArchives(new CommandReqDto<>(concentratorCode, splitArchives));
                                });
                        if (result.getExecuteState() == ExecuteResult.ExecuteState.COMPLETED) {
                            break;
                        }
                    }

                    // 保存命令日志
                    monitorConcentratorService.addConcentratorCmdLog(concentratorCode, ConcentratorCmdType.SET_ARCHIVES, result.getCommandId(), result.getMessage());

                    if (result.getExecuteState() != ExecuteResult.ExecuteState.COMPLETED
                            || !((boolean) result.getFnResult().getData())) {
                        // 指令执行失败或者应答失败
                        log.info("捷先集中器 {} 同步档案失败", concentratorCode);
                        break;
                    }

                    // 更新数据库
                    jxConcentratorMonitorService.handleSetArchivesSuccess(concentratorCode, splitMeters);
                }
            } finally {
                syncingConcentrators.remove(concentratorCode);
            }
        });
        syncingConcentrators.add(concentratorCode);
        return true;
    }

    @SystemLog(description = "旧版捷先集中器-读表档案")
    @GetMapping("readArchives")
    public ExecuteResult<List<JxOldMeterArchive>> readArchives(@RequestParam String concentratorCode,
                                                               @RequestParam("startNum") int start,
                                                               @RequestParam("endNum") int end) {
        return jxConcentratorMonitorService.readArchives(concentratorCode, IntStream.rangeClosed(start, end).boxed().collect(Collectors.toList()));
    }

    @SystemLog(description = "新版捷先集中器-读表档案")
    @GetMapping("newReadArchives")
    public ExecuteResult<List<JxNewMeterArchive>> newReadArchives(@RequestParam String concentratorCode,
                                                                  @RequestParam("startNum") int start,
                                                                  @RequestParam("endNum") int end) {
        return jxConcentratorMonitorService.newReadArchives(concentratorCode, IntStream.rangeClosed(start, end).boxed().collect(Collectors.toList()));
    }

    @GetMapping("getExecuteResult")
    public String getExecuteResult(@RequestParam long commandId) {
        return monitorConcentratorService.getExecuteResult(commandId);
    }
}
