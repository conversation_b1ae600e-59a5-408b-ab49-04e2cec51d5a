package com.acpiot.wsd.docking.hysw.res.body;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 登录请求体
 * Created by YoungLu on 2022-10-31-0001
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
@ToString
public class HyGetMeterDataResponseBody {

    /**
     * 响应
     */
    @JsonProperty("meterDataList")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<HyMeterData> meterDataList;

    @Data
    public static class HyMeterData {

        /**
         * 表号
         */
        @JsonProperty("meterNo")
        private String meterNo;

        /**
         * 读数时间 格式YYYYMMDDhhmmss
         */
        @JsonProperty("readingTime")
        private String readingTime;

        /**
         * 读数 13位数字（3位小数）
         */
        @JsonProperty("reading")
        private BigDecimal reading;

        /**
         * 电压时间
         */
        @JsonProperty("voltageTime")
        private String voltageTime;

        /**
         * 电压 12位数字 2位小数
         */
        @JsonProperty("voltage")
        private BigDecimal voltage;

        /**
         * 阀门状态 Y=关 N=开
         */
        @JsonProperty("valveState")
        private String valveState;

        /**
         * 磁干扰状态 Y=有磁干扰 N=无磁干扰
         */
        @JsonProperty("emiState")
        private String emiState;

        /**
         * 状态更新时间 阀门状态与磁干扰更新时间 格式：YYYYMMDDhhmmss
         */
        @JsonProperty("stateTime")
        private String stateTime;

        /**
         * 磁干扰次数
         */
        @JsonProperty("emiCount")
        private int emiCount;

        /**
         * 上次磁干扰次数
         */
        @JsonProperty("preEmiCount")
        private int preEmiCount;

        /**
         * 磁干扰次数最新读取时间 格式：YYYYMMDDhhmmss
         */
        @JsonProperty("emiCountTime")
        private String emiCountTime;
    }
}
