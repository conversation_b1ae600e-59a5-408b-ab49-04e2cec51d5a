package com.acpiot.wsd.docking.hysw.res.body;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 获取仪表档案请求体
 * Created by YoungLu on 2022-11-02-0001
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class HyGetMeterInfoResponseBody {

    @JsonProperty("meterInfoList")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<MeterInfo> meterInfoList;

    @Data
    public static class MeterInfo {

        /**
         * 表号
         */
        @JsonProperty("meterNo")
        private String meterNo;

        /**
         * 仪表类型 1=水表 2=电表 3=燃气表
         */
        @JsonProperty("meterType")
        private int meterType;

        /**
         * 规格名称
         */
        @JsonProperty("specification")
        private String specification;

        /**
         * 模数。表的最大示数加上一个单位的最小可示数，即计量表走满一圈到相同数字时的用量
         */
        @JsonProperty("modul")
        private int modul;

        /**
         * 倍率 13位数字，3位小数
         */
        @JsonProperty("multiple")
        private BigDecimal multiple;

        /**
         * 无线表标识 Y=无线表 N=有线表
         */
        @JsonProperty("wireless")
        private String wireless;

        /**
         * 无线仪表低电压告警值。默认：0
         */
        @JsonProperty("lowVoltage")
        private BigDecimal lowVoltage = BigDecimal.ZERO;

        /**
         * 生产日期 格式：YYYYMMDD
         */
        @JsonProperty("makeDate")
        private String makeDate;

        /**
         * 安装日期
         */
        @JsonProperty("installDate")
        private String installDate;

        /**
         * 安装地址
         */
        @JsonProperty("installSite")
        private String installSite;

        /**
         * 初始读数 13位数字，3位小数
         */
        @JsonProperty("initialReading")
        private BigDecimal initialReading;

        /**
         * 仪表状态
         */
        @JsonProperty("meterState")
        private int meterState;
    }
}
