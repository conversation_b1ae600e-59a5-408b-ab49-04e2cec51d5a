package com.acpiot.wsd.docking.dcwqswj.api;

import com.acpiot.wsd.docking.dcwqswj.req.DcSwitchMeterReq;
import com.acpiot.wsd.docking.dcwqswj.res.DcSwitchMeterRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * Created by hsq on 2021-09-13-0001
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/Site")
@ConditionalOnProperty(name = {"third-system-docking.dc-wuqiang-zhuozheng.enable"}, havingValue = "true")
public class DcApiController {

    private final DcRequestService dcRequestService;

    /**
     * 阀控-指令下发
     *
     * @param dcSwitchMeterReq
     * @return
     */
    @PostMapping("/SwitchMeter")
    public DcSwitchMeterRes switchMeter(@Valid @RequestBody DcSwitchMeterReq dcSwitchMeterReq, BindingResult bindingResult) {
        log.info("系统对接[道成&武强水务局]，受理阀控指令下发请求，request：{}", dcSwitchMeterReq);
        // 校验参数合法性
        if (bindingResult.hasErrors()) {
            String error = bindingResult.getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.joining("，"));
            log.error("系统对接[道成&武强水务局]，受理阀控指令下发请求失败，error：{}", error);
            return DcSwitchMeterRes.ofError("请求参数校验失败：" + error);
        }
        // 处理阀控请求业务并组织响应数据
        DcSwitchMeterRes dcSwitchMeterRes = dcRequestService.switchMeter(dcSwitchMeterReq);
        log.info("系统对接[道成&武强水务局]，受理阀控指令下发请求执行完成，response：{}", dcSwitchMeterRes);
        return dcSwitchMeterRes;
    }
}