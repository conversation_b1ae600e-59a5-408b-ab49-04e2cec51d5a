package com.acpiot.wsd.docking.lkhx.service.impl;

import cn.hutool.core.date.DateUtil;
import com.acpiot.wsd.data.archive.entity.LatestData;
import com.acpiot.wsd.data.archive.entity.WaterMeter;
import com.acpiot.wsd.data.monitor.entity.MeterCmdLog;
import com.acpiot.wsd.data.monitor.enums.CommandState;
import com.acpiot.wsd.data.monitor.enums.MeterCmdType;
import com.acpiot.wsd.data.monitor.repository.MeterCmdLogRepository;
import com.acpiot.wsd.docking.lkhx.call.LkhxCallProvider;
import com.acpiot.wsd.docking.lkhx.properties.LkhxProperties;
import com.acpiot.wsd.docking.lkhx.req.LkhxDataReportReq;
import com.acpiot.wsd.docking.lkhx.service.LkhxService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

import static com.acpiot.wsd.data.monitor.enums.CommandState.COMPLETED;

@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
@ConditionalOnProperty(name = {"third-system-docking.lkhx.enable"}, havingValue = "true")
public class LkhxServiceImpl implements LkhxService {

    private final MeterCmdLogRepository meterCmdLogRepository;
    private final LkhxProperties lkhxProperties;
    private final LkhxCallProvider lkhxCallProvider;

    @Override
    public void handleValveCtrlResponse(String commandId, boolean valveCtrlResult) {
        // 查询对应的命令日志
        Optional<MeterCmdLog> meterCmdLogOptional = meterCmdLogRepository.findFirstByCommandIdOrderByCreatedDateDesc(commandId);
        if (meterCmdLogOptional.isEmpty()) {
            log.warn("系统对接[蓝科宏瑄]：阀控响应上传，不存在对应的命令，commandId：{}", commandId);
            return;
        }
        MeterCmdLog meterCmdLog = meterCmdLogOptional.get();
        WaterMeter waterMeter = meterCmdLog.getWaterMeter();

        // 判断阀控命令状态
        CommandState commandState = meterCmdLog.getCommandState();
        if (commandState != COMPLETED) {
            log.info("系统对接[蓝科宏瑄]：阀控响应上传，表号：{}，命令状态：{}，命令状态为未完成，无需调用抄表数据上传接口", waterMeter.getCode(), commandState);
            return;
        }
        // 上报的抄表数据从水表最新状态中获取，如果最新状态为空或者读数为空，则表示不具备上报抄表数据的前提，记录日志
        LatestData latestData = waterMeter.getLatestData();
        if (latestData == null || latestData.getValue() == null) {
            log.info("系统对接[蓝科宏瑄]：阀控响应上传，表号：{}，命令状态：{}，抄表数据为空，无法调用抄表数据上传接口", waterMeter.getCode(), commandState);
            return;
        }

        LkhxDataReportReq.DataReportReqDataRow row = new LkhxDataReportReq.DataReportReqDataRow();
        row.setStealNo(waterMeter.getCode());
        row.setReadVal(latestData.getValue().stripTrailingZeros().toPlainString());
        row.setReadDate(DateUtil.format(latestData.getMeteringTime(), "yyyy-MM-dd HH:mm:ss"));
        // 如果阀控成功，则按照命令类型设置；若阀控失败，返回状态为异常
        String valveState;
        if (valveCtrlResult) {
            valveState = meterCmdLog.getCmdType() == MeterCmdType.CLOSE_VALVE ? "关阀" : "开阀";
        } else {
            valveState = "异常";
        }
        row.setValveState(valveState);

        if (latestData.getBatteryVoltage() != null) {
            row.setVoltage(latestData.getBatteryVoltage().stripTrailingZeros().toPlainString());
        }
        LkhxDataReportReq lkhxDataReportReq = new LkhxDataReportReq();
        lkhxDataReportReq.setSign(lkhxProperties.getSign());
        lkhxDataReportReq.setRows(List.of(row));

        try {
            log.info("系统对接[蓝科宏瑄]，阀门状态:{}，开始上报水表数据，水表编号：{}", valveState, waterMeter.getCode());
            lkhxCallProvider.dataReport(lkhxDataReportReq);
            log.info("系统对接[蓝科宏瑄]，阀门状态:{}，水表数据上报完成，水表编号：{}", valveState, waterMeter.getCode());
        } catch (Exception e) {
            log.warn("系统对接[蓝科宏瑄]，阀门状态:{}，处理水表数据上报异常, 操作异常信息：", valveState, e);
        }

        log.info("系统对接[蓝科宏瑄]：阀控响应上传，处理完成，表号：{}，命令状态：{}", waterMeter.getCode(), commandState);
    }
}