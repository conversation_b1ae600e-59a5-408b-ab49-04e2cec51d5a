package com.acpiot.wsd.docking.dcwqswj.call;

import com.acpiot.wsd.docking.dcwqswj.req.DcCallBackMeterStatusReq;
import com.acpiot.wsd.docking.dcwqswj.req.DcDataReportReq;
import com.acpiot.wsd.docking.dcwqswj.req.DcLoginReq;
import com.acpiot.wsd.docking.dcwqswj.res.DcCallBackMeterStatusRes;
import com.acpiot.wsd.docking.dcwqswj.res.DcDataReportRes;
import com.acpiot.wsd.docking.dcwqswj.res.DcLoginRes;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * 回调第三方接口声明
 */
public interface DcCallService {

    /**
     * 登录获取token
     *
     * @param dcLoginReq
     * @return
     */
    @POST("/api/auth/user/login")
    Call<DcLoginRes> login(@Body DcLoginReq dcLoginReq);

    /**
     * 数据上报
     *
     * @param dcDataReportReq
     * @return
     */
    @POST("/api/ThirdDataReport/DataReport")
    Call<DcDataReportRes> dataReport(@Body DcDataReportReq dcDataReportReq);

    /**
     * 阀控-执行回调
     *
     * @param dcCallBackMeterStatusReq
     * @return
     */
    @POST("/api/data/CallBackMeterStatus")
    Call<DcCallBackMeterStatusRes> callBackMeterStatus(@Body DcCallBackMeterStatusReq dcCallBackMeterStatusReq);
}
