package com.acpiot.wsd.docking.ysxt.lpxhy.api;

import com.acpiot.wsd.docking.ysxt.base.api.YsxtApiController;
import com.acpiot.wsd.docking.ysxt.base.api.YsxtRequestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by YoungLu on 2022-10-31-0001
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/ysxt/api/meters")
@ConditionalOnProperty(name = {"third-system-docking.ysxt.lpxhy.enable"}, havingValue = "true")
public class YsxtLpxhyApiController extends YsxtApiController {

    public YsxtLpxhyApiController(@Qualifier("ysxtLpxhyRequestService") YsxtRequestService ysxtLpxhyRequestService) {
        super(ysxtLpxhyRequestService);
    }

}
