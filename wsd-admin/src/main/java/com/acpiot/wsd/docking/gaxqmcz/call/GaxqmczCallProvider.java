package com.acpiot.wsd.docking.gaxqmcz.call;

import com.acpiot.wsd.docking.common.util.ApiCallUtil;
import com.acpiot.wsd.docking.gaxqmcz.req.GaxqmczDataReportReq;
import com.acpiot.wsd.docking.gaxqmcz.res.GaxqmczLoginRes;
import com.acpiot.wsd.docking.gaxqmcz.res.GaxqmczReportDataRes;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 回调第三方接口服务
 */
@Component
@RequiredArgsConstructor
public class GaxqmczCallProvider {

    private final GaxqmczCallService csxmCallService;

    /**
     * 登录
     *
     * @param username
     * @param password
     */
    public GaxqmczLoginRes login(String username, String password) {
        // 执行登录请求并获取接口响应
        return ApiCallUtil.execute("登录", csxmCallService.login(username, password));
    }

    /**
     * 数据上报
     *
     * @param gaxqmczDataReportReqList
     * @param token
     * @return
     */
    public GaxqmczReportDataRes dataReport(List<GaxqmczDataReportReq> gaxqmczDataReportReqList, String token) {
        // 执行数据上报请求并获取接口响应
        return ApiCallUtil.execute("数据上报", csxmCallService.dataReport(token, gaxqmczDataReportReqList));
    }
}
