package com.acpiot.wsd.docking.dxzls.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 获取单个或所有水表的实时数据响应结构
 * Created by YoungLu on 2024-11-28-0001
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
@ToString
public class DxzlsGetRealTimeDataResponse extends DxzlsResponse {

    @JsonProperty("data")
    private List<DxzlsGetRealTimeDataDto> data;

    public DxzlsGetRealTimeDataResponse(String success, String message, String total, List<DxzlsGetRealTimeDataDto> data) {
        super(success, message, total);
        this.data = data;
    }

    @Data
    public static class DxzlsGetRealTimeDataDto {

        /**
         * 水表编号
         */
        @JsonProperty(value = "meterCode")
        private String meterCode;

        /**
         * 水表当前读数
         */
        @JsonProperty(value = "meterNumber")
        private String meterNumber = "";

        /**
         * 水表压力
         */
        @JsonProperty(value = "meterPress")
        private String meterPress = "";

        /**
         * 阀门状态（阀开、阀关、未启用）
         */
        @JsonProperty(value = "metervalveStatus")
        private String metervalveStatus = "未知";

        /**
         * 数据采集时间
         */
        @JsonProperty(value = "collectTime")
        private String collectTime = "";

        /**
         * 表具状态（正常/其他异常状态）
         */
        @JsonProperty(value = "meterStatus")
        private String meterStatus = "正常";
    }
}
