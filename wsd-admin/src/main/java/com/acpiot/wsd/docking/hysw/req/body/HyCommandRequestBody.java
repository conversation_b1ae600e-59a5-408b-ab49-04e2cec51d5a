package com.acpiot.wsd.docking.hysw.req.body;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 公共命令请求体（适用于开阀、关阀）
 * Created by YoungLu on 2022-10-31-0001
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class HyCommandRequestBody {

    @Valid
    @NotNull(message = "阀控命令请求体不能为空")
    @JsonProperty("commandRequest")
    private CommandRequest commandRequest;

    @Data
    public static class CommandRequest {
        /**
         * 水表编号
         */
        @JsonProperty("meterNo")
        @NotBlank(message = "水表编号不能为空")
        @Size(max = 20, message = "水表编号长度不能超过20位")
        private String meterNo;
    }
}
