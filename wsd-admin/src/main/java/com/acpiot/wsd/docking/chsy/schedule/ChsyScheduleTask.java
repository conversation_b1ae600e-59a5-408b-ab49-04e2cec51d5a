package com.acpiot.wsd.docking.chsy.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.wsd.docking.chsy.call.ChsyCallProvider;
import com.acpiot.wsd.docking.chsy.properties.ChsyProperties;
import com.acpiot.wsd.docking.chsy.req.ChsyLatestDataReq;
import com.acpiot.wsd.docking.chsy.res.ChsyCommonRes;
import com.acpiot.wsd.docking.chsy.res.ChsyLatestDataRes;
import com.acpiot.wsd.docking.chsy.res.LatestDataInfo;
import com.acpiot.wsd.common.service.CommonService;
import com.acpiot.wsd.data.archive.entity.NbWaterMeter;
import com.acpiot.wsd.common.enums.ValveStatus;
import com.acpiot.wsd.data.monitor.entity.MeterData;
import com.acpiot.wsd.data.monitor.entity.MeterStatus;
import com.acpiot.wsd.data.monitor.entity.NbSignal;
import com.acpiot.wsd.data.monitor.service.MonitorDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 山科对接定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = {"third-system-docking.chsy.enable"}, havingValue = "true")
public class ChsyScheduleTask {

    private final ChsyCallProvider chsyCallProvider;
    private final CommonService commonService;
    private final MonitorDataService monitorDataService;
    private final ChsyProperties chsyProperties;

    /**
     * 获取最新抄表数据（每日早上9点半执行）
     */
    @Async
    @Scheduled(cron = "0 30 9 * * ?")
    public void getListLatestData() {
        String companyId = chsyProperties.getCompanyId();
        List<NbWaterMeter> waterMeterList = commonService.findNbWaterMeterByCompanyId(companyId);
        if (CollUtil.isEmpty(waterMeterList)) {
            log.warn("系统对接册亨思源山科平台：册亨思源下暂无NB水表");
            return;
        }
        CollUtil.split(waterMeterList, 500).forEach(subList -> {
            ChsyLatestDataReq chsyLatestDataReq = new ChsyLatestDataReq();
            List<String> queryRecords = subList.stream().map(waterMeter -> StrUtil.padPre(waterMeter.getCode(), 20, '0')).collect(Collectors.toList());
            chsyLatestDataReq.setQueryRecords(queryRecords);
            List<String> dataTypeCodes = List.of("MeterVoltage", "CSQ", "AccNetFlow", "ValveState");
            chsyLatestDataReq.setDataTypeCodes(dataTypeCodes);
            ChsyCommonRes<List<ChsyLatestDataRes>> listChsyCommonRes = chsyCallProvider.getListLatestData(chsyLatestDataReq);
            if (listChsyCommonRes.getState() != 0) {
                log.warn("系统对接册亨思源山科平台：定时获取水表最新数据失败，失败原因：{}", listChsyCommonRes.getMsg());
                return;
            }
            List<ChsyLatestDataRes> data = listChsyCommonRes.getData();
            if (CollUtil.isEmpty(data)) {
                log.warn("系统对接册亨思源山科平台：定时获取水表最新数据失败，返回的数据为空");
                return;
            }
            // 将返回结果转为MeterData
            List<MeterData> meterDataList = data.stream().flatMap(chsyLatestDataRes -> chsyLatestDataRes.getMeterDataList().stream()).map(meterLatestDataInfo -> {
                String meterNo = meterLatestDataInfo.getMeterCommaddr();
                Optional<NbWaterMeter> optional = waterMeterList.stream().filter(waterMeter -> ObjectUtil.equals(meterNo, StrUtil.padPre(waterMeter.getCode(), 16, '0'))).findFirst();
                if (optional.isEmpty()) {
                    return null;
                }
                List<LatestDataInfo> latestDataList = meterLatestDataInfo.getLatestDataList();
                if (CollUtil.isEmpty(latestDataList)) {
                    return null;
                }
                // 必须有表读数
                Optional<LatestDataInfo> accNetFlowOptional = latestDataList.stream().filter(latestDataInfo -> ObjectUtil.equals("AccNetFlow", latestDataInfo.getDataTypeCode())).findFirst();
                if (accNetFlowOptional.isEmpty()) {
                    return null;
                }
                NbWaterMeter nbWaterMeter = optional.get();
                MeterData meterData = new MeterData();
                meterData.setWaterMeter(nbWaterMeter);
                meterData.setMeterDataType(MeterData.MeterDataType.DEVICE_REPORT);
                MeterStatus meterStatus = new MeterStatus();
                latestDataList.forEach(latestDataInfo -> {
                    // 表读数
                    if (ObjectUtil.equals("AccNetFlow", latestDataInfo.getDataTypeCode())) {
                        meterData.setValue(latestDataInfo.getData());
                        meterData.setMeteringTime(DateUtil.parse(latestDataInfo.getDataTime()));
                        meterData.setMeterTime(DateUtil.parse(latestDataInfo.getDataTime()));
                    }
                    // 电池电压
                    if (ObjectUtil.equals("MeterVoltage", latestDataInfo.getDataTypeCode())) {
                        meterData.setBatteryVoltage(latestDataInfo.getData());
                        // 如果电池电压小于3.3，则认为是电池欠压
                        if (latestDataInfo.getData().compareTo(new BigDecimal("3.3")) < 0) {
                            meterStatus.setBatteryUnderVoltage(true);
                        }
                    }
                    // 信号质量
                    if (ObjectUtil.equals("CSQ", latestDataInfo.getDataTypeCode())) {
                        NbSignal nbSignal = new NbSignal();
                        nbSignal.setCsq(latestDataInfo.getData().intValue());
                        meterData.setNbSignal(nbSignal);
                    }
                    // 阀门状态
                    if (ObjectUtil.equals("ValveState", latestDataInfo.getDataTypeCode())) {
                        int valve = latestDataInfo.getData().intValue();
                        ValveStatus valveStatus;
                        if (valve == 0) {
                            valveStatus = ValveStatus.CLOSED;
                        } else if (valve == 1 || valve == 2) {
                            valveStatus = ValveStatus.OPENED;
                        } else {
                            valveStatus = ValveStatus.ALARM;
                        }
                        meterStatus.setValveStatus(valveStatus);
                    }
                });
                meterData.setMeterStatus(meterStatus);
                return meterData;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollUtil.isEmpty(meterDataList)) {
                log.warn("系统对接册亨思源山科平台：定时获取水表最新数据失败，转化为meterData后数据为空");
                return;
            }
            // 批量保存抄表数据，每10条数据一起保存
            CollUtil.split(meterDataList, 10).forEach(monitorDataService::saveAllMeterData);
        });
        log.info("系统对接册亨思源山科平台：定时获取水表最新数据成功");
    }

}
