package com.acpiot.wsd.docking.jxconcentrator.common.adapter;

import com.acpiot.wsd.docking.jxconcentrator.common.req.*;
import com.acpiot.wsd.docking.jxconcentrator.common.res.ExecuteResult;
import com.acpiot.wsd.docking.jxconcentrator.common.res.HttpResult;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;

import java.util.List;

/**
 * The interface Jx call service.
 */
public interface JxCallService {

    /**
     * 数据初始化
     *
     * @return online concentrators
     */
    @GET("/api/concentrator/online")
    Call<HttpResult<List<String>>> getOnlineConcentrators();

    /**
     * 数据初始化
     *
     * @param reqDto the req dto
     * @return call
     */
    @POST("/api/concentrator/resetData")
    Call<HttpResult<ExecuteResult<Boolean>>> reset(@Body CommandReqDto<Void> reqDto);

    /**
     * 同步时钟
     *
     * @param reqDto the req dto
     * @return call
     */
    @POST("/api/concentrator/syncClock")
    Call<HttpResult<ExecuteResult<Boolean>>> syncClock(@Body CommandReqDto<ConcentratorClock> reqDto);

    /**
     * 读集中器时钟
     *
     * @param reqDto the req dto
     * @return call
     */
    @POST("/api/concentrator/readClock")
    Call<HttpResult<ExecuteResult<ConcentratorClock>>> readClock(@Body CommandReqDto<Void> reqDto);

    /**
     * 读集中器版本
     *
     * @param reqDto the req dto
     * @return call
     */
    @POST("/api/concentrator/readVersion")
    Call<HttpResult<ExecuteResult<ConcentratorVersion>>> readVersion(@Body CommandReqDto<Void> reqDto);

    /**
     * 设置集中器上报允许/停止.
     *
     * @param reqDto the req dto
     * @return report report
     */
    @POST("/api/concentrator/setReport")
    Call<HttpResult<ExecuteResult<Boolean>>> setReport(@Body CommandReqDto<ReportParam> reqDto);

    /**
     * 读取上报是否允许.
     *
     * @param reqDto the req dto
     * @return the execute result
     */
    @POST("/api/concentrator/readReport")
    Call<HttpResult<ExecuteResult<ReportParam>>> readReport(@Body CommandReqDto<Void> reqDto);

    /**
     * 设置上报时间.
     *
     * @param reqDto the req dto
     * @return report time
     */
    @POST("/api/concentrator/setReportTime")
    Call<HttpResult<ExecuteResult<Boolean>>> setReportTime(@Body CommandReqDto<ReportTime> reqDto);

    /**
     * 读取上报时间.
     *
     * @param reqDto the req dto
     * @return the execute result
     */
    @POST("/api/concentrator/readReportTime")
    Call<HttpResult<ExecuteResult<ReportTime>>> readReportTime(@Body CommandReqDto<Void> reqDto);

    /**
     * 设置 GPRS 参数.
     *
     * @param reqDto the req dto
     * @return gprs param
     */
    @POST("/api/concentrator/setGprsParam")
    Call<HttpResult<ExecuteResult<Boolean>>> setGprsParam(@Body CommandReqDto<GprsParam> reqDto);

    /**
     * 读 GPRS 参数.
     *
     * @param reqDto the req dto
     * @return execute result
     */
    @POST("/api/concentrator/readGprsParam")
    Call<HttpResult<ExecuteResult<GprsParam>>> readGprsParam(@Body CommandReqDto<Void> reqDto);

    /**
     * 重启集中器.
     *
     * @param reqDto the req dto
     * @return execute result
     */
    @POST("/api/concentrator/reboot")
    Call<HttpResult<ExecuteResult<Boolean>>> reboot(@Body CommandReqDto<Void> reqDto);

    /**
     * 设置考核（大）表采集周期
     *
     * @param reqDto the req dto
     * @return the collect period
     */
    @POST("/api/concentrator/setCollectPeriod")
    Call<HttpResult<ExecuteResult<Boolean>>> setCollectPeriod(@Body CommandReqDto<CollectPeriod> reqDto);
}
