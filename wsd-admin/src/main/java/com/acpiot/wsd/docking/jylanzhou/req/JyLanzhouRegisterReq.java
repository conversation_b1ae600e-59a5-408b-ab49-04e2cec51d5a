package com.acpiot.wsd.docking.jylanzhou.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 注册请求参数
 *  [
 *    {
 *      "meterAddr":"201144556666",
 *      "installDate":"2020-04-21 14:11:10",
 *      "updateTime":"2020-04-22 14:11:10",
 *      "userName":"张三",
 *      "userType":1,
 *      "address":"XXXXXXX 8幢2单元",
 *      "phone":"15123462154"
 *    }
 *  ]
 * <AUTHOR>
 * @created 2025/2/27 10:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class JyLanzhouRegisterReq {

    /**
     * 表号
     */
    @NotBlank(message = "表号不能为空")
    @JsonProperty("meterAddr")
    private String meterAddr;

    /**
     * 出厂日期 yyyy-MM-dd HH:mm:ss
     */
    @JsonProperty("updateTime")
    private String updateTime;

    /**
     * 安装时间 yyyy-MM-dd HH:mm:ss
     */
    @JsonProperty("installDate")
    private String installDate;

    /**
     * 用户名称
     */
    @JsonProperty("userName")
    private String userName;

    /**
     * 用户类型，1-民用 2-商用
     */
    @NotNull(message = "用户类型不能为空")
    @JsonProperty("userType")
    private Integer userType;

    /**
     * 地址描述
     */
    @JsonProperty("address")
    private String address;

    /**
     * 电话号码
     */
    @JsonProperty("phone")
    private String phone;

    /**
     * 门牌号
     */
    @JsonProperty("doorPlate")
    private String doorPlate;
}
