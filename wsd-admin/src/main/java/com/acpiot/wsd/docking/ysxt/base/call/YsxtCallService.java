package com.acpiot.wsd.docking.ysxt.base.call;

import com.acpiot.wsd.docking.ysxt.base.req.YsxtDataReportReq;
import com.acpiot.wsd.docking.ysxt.base.req.YsxtLoginReq;
import com.acpiot.wsd.docking.ysxt.base.res.YsxtCommonRes;
import com.acpiot.wsd.docking.ysxt.base.res.YsxtLoginRes;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * 回调第三方接口声明
 */
public interface YsxtCallService {

    /**
     * 登录接口
     *
     * @param ysxtLoginReq
     * @return
     */
    @POST("/frame/api/SystemMoule/Account/login")
    Call<YsxtLoginRes> login(@Body YsxtLoginReq ysxtLoginReq);

    /**
     * 数据上报
     *
     * @param ysxtDataReportReq
     * @return
     */
    @POST("/ys/api/meters/UploadData")
    Call<YsxtCommonRes> dataReport(@Body YsxtDataReportReq ysxtDataReportReq);
}
