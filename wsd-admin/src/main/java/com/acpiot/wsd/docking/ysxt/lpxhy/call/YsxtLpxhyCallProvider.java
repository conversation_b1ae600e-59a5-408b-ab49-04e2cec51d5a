package com.acpiot.wsd.docking.ysxt.lpxhy.call;

import com.acpiot.wsd.docking.common.util.ApiCallUtil;
import com.acpiot.wsd.docking.ysxt.base.call.YsxtCallProvider;
import com.acpiot.wsd.docking.ysxt.base.call.YsxtCallService;
import com.acpiot.wsd.docking.ysxt.base.req.YsxtDataReportReq;
import com.acpiot.wsd.docking.ysxt.base.req.YsxtLoginReq;
import com.acpiot.wsd.docking.ysxt.base.res.YsxtCommonRes;
import com.acpiot.wsd.docking.ysxt.base.res.YsxtLoginRes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * 回调第三方接口服务
 */
@Component
public class YsxtLpxhyCallProvider implements YsxtCallProvider {

    @Autowired
    @Qualifier("ysxtLpxhyCallService")
    private YsxtCallService ysxtLpxhyCallService;

    /**
     * 登录
     *
     * @param ysxtLoginReq
     * @return
     */
    public YsxtLoginRes login(YsxtLoginReq ysxtLoginReq) {
        // 执行登录请求并获取接口响应
        return ApiCallUtil.execute("登录", ysxtLpxhyCallService.login(ysxtLoginReq));
    }

    /**
     * 数据上报
     *
     * @param ysxtDataReportReq
     * @return
     */
    public YsxtCommonRes dataReport(YsxtDataReportReq ysxtDataReportReq) {
        // 执行数据上报请求并获取接口响应
        return ApiCallUtil.execute("数据上报", ysxtLpxhyCallService.dataReport(ysxtDataReportReq));
    }
}
