package com.acpiot.wsd.docking.jylanzhou.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 基础属性
 *
 * <AUTHOR>
 * @created 2025/2/26 16:49
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "third-system-docking.jy-lanzhou")
public class JyLanzhouProperties {

    /**
     * 客户端id（第三方对接系统提供）
     */
    private String client_id;

    /**
     * 客户端密钥（第三方对接系统提供）
     */
    private String client_secret;

    /**
     * 当前公司ID（从智慧水务系统中获取）
     */
    private String companyId;

    /**
     * 回调基本URL（从第三方对接系统中获取）
     */
    private String callBaseUrl;

    /**
     * 数据加密密钥（第三方对接系统提供，固定值）
     */
    private String secretKey;

    /**
     * 允许访问的IP地址（第三方对接系统提供，固定值）
     */
    private String allowIp;
}
