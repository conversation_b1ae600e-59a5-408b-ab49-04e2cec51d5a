package com.acpiot.wsd.docking.jxconcentrator.oldprotocol.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Created by moxin on 2020-04-13-0013
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "third-system-docking.jx-concentrator-old")
public class JxOldProperties {

    /**
     * 基本URL
     */
    private String baseUrl;
}
