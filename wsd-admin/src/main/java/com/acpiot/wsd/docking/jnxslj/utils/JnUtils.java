package com.acpiot.wsd.docking.jnxslj.utils;

import com.acpiot.wsd.common.enums.ValveStatus;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 汉威对接工具类
 * Created by zc on 2021/12/29 15:59
 *
 * <AUTHOR> Email: <EMAIL>
 */
public class JnUtils {

    /**
     * 升转立方米，1升=0.001立方米
     *
     * @param liter 升
     * @return
     */
    public static BigDecimal liter2CubicMeter(long liter) {
        if (liter == 0) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(liter).divide(new BigDecimal(1000), 3, RoundingMode.HALF_UP);
    }

    /**
     * 转化阀门状态
     *
     * @param valveStatus 阀门状态：0 无阀控 1开-合 2 关-断 3 异常
     * @return
     */
    public static ValveStatus transferValveStatus(int valveStatus) {
        switch (valveStatus) {
            case 1:
                return ValveStatus.OPENED;
            case 2:
                return ValveStatus.CLOSED;
            case 3:
                return ValveStatus.ALARM;
            default:
                return null;
        }
    }
}