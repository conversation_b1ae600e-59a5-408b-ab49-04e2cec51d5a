package com.acpiot.wsd.cat1wmservice.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.acpiot.wsd.cat1wmservice.dto.command.CommandRespDto;
import com.acpiot.wsd.cat1wmservice.service.Cat1MeterService;
import com.acpiot.wsd.common.utils.CommandStateUtils;
import com.acpiot.wsd.data.archive.entity.Cat1WaterMeter;
import com.acpiot.wsd.data.archive.entity.WaterMeter;
import com.acpiot.wsd.data.archive.repository.Cat1WaterMeterRepository;
import com.acpiot.wsd.data.monitor.entity.MeterCmdLog;
import com.acpiot.wsd.data.monitor.repository.MeterCmdLogRepository;
import com.acpiot.wsd.data.productiontest.entity.TestWaterMeter;
import com.acpiot.wsd.data.productiontest.repository.TestWaterMeterRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * UDP直连水表服务实现类
 *
 * <AUTHOR>
 * @since 2023/8/31
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class Cat1MeterServiceImpl implements Cat1MeterService {

    private final Cat1WaterMeterRepository cat1WaterMeterRepository;
    private final TestWaterMeterRepository testWaterMeterRepository;
    private final MeterCmdLogRepository meterCmdLogRepository;

    @Override
    @Transactional(readOnly = true)
    public Optional<TestWaterMeter> getTestWaterMeterByImei(String imei) {
        return testWaterMeterRepository.findByDeviceArchive_Imei(imei);
    }

    @Override
    public Optional<MeterCmdLog> getAndUpdateCmdLog(WaterMeter waterMeter, CommandRespDto commandRespDto) {
        return meterCmdLogRepository.findFirstByWaterMeterAndCommandIdOrderByCreatedDateDesc(waterMeter, commandRespDto.getCommandId())
                .stream()
                .peek(meterCmdLog -> {
                    String details = String.format("\n状态：%s，时间：%s", commandRespDto.getCmdDesc(), DateUtil.now());
                    meterCmdLog.setDetails(meterCmdLog.getDetails() + details);
                    meterCmdLog.setCommandState(CommandStateUtils.ofCommandState(commandRespDto.getCmdState()));
                })
                .findFirst();
    }

    @Override
    public void updateCat1MeterOnline() {
        // 查询是否存在在线的UDP直连水表
        boolean exists = cat1WaterMeterRepository.existsByOnlineIsTrue();
        if (!exists) {
            return;
        }
        // 没有最新抄表数据的水表、最新抄表时间为空的水表、将最新抄表时间在3天以前的水表设置为离线，设置为离线
        DateTime lastOnlineTime = DateUtil.date().offset(DateField.DAY_OF_MONTH, -3);
        cat1WaterMeterRepository.updateOnlineIsFalse(lastOnlineTime);
    }

    @Override
    public Optional<Cat1WaterMeter> getWaterMeterByImeiAndUpdateOnline(String imei) {
        return cat1WaterMeterRepository.findByImei(imei)
                .stream()
                .peek(waterMeter -> waterMeter.setOnline(true))
                .findFirst();
    }

}
