package com.acpiot.wsd.meterservice.service;

import com.acpiot.microservice.meterservice.mqtt.dto.payload.CommandRspMsg;
import com.acpiot.microservice.meterservice.mqtt.dto.payload.DeviceOnlineOfflineMsg;
import com.acpiot.microservice.meterservice.mqtt.dto.payload.EventReportMsg;
import com.acpiot.wsd.data.archive.entity.NbWaterMeter;
import com.acpiot.wsd.data.archive.entity.WaterMeter;
import com.acpiot.wsd.data.archive.enums.ForceValveType;
import com.acpiot.wsd.data.monitor.entity.CurveData;
import com.acpiot.wsd.data.monitor.entity.MeterCmdLog;
import com.acpiot.wsd.data.monitor.entity.MeterEvent;
import com.acpiot.wsd.data.monitor.entity.MeterStatus;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Created by moxin on 2020-12-28-0028
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface MeterService {

    /**
     * 处理NB水表上下线事件
     *
     * @param imei
     * @param msg
     * @return
     */
    Optional<NbWaterMeter> handleOnlineOfflineMsg(String imei, DeviceOnlineOfflineMsg msg);

    void saveMeterEvent(WaterMeter waterMeter, MeterEvent.EventType eventType, String details);

    /**
     * 处理NB水表事件上报
     *
     * @param imei
     * @param msg
     */
    void handleEventReportMsg(String imei, EventReportMsg msg);

    Optional<NbWaterMeter> getWaterMeterByImei(String imei, String... fetches);

    Optional<MeterCmdLog> updateMeterCmdLog(String imei, CommandRspMsg msg);

    void updateMeterCmdLog(Long logId, String details);

    void updateMeterStatus(WaterMeter waterMeter, MeterStatus meterStatus);

    void updateForceValve(Long meterId, ForceValveType forceValveType, Date durationTime);

    void updateDailyFlow(long logId, int dailyFlowThreshold);

    void updateReportInterval(long logId, int reportIntervalUnit, int reportInterval);

    /**
     * 根据水表和日期查询曲线数据
     *
     * @param waterMeter
     * @param curveDate
     * @return
     */
    Optional<CurveData> getCurveDataBy(WaterMeter waterMeter, LocalDate curveDate);

    /**
     * 查询需要补抄曲线数据的水表
     *
     * @return
     */
    List<NbWaterMeter> getNotReportCurveDataMeters();

    /**
     * 查询曲线数据未抄收完整的水表
     *
     * @return
     */
    List<WaterMeter> getDataFullIsFalseMeters();

    /**
     * 获取需要请求补抄的NB水表ID，该方法配合定时任务用于实现分布式补抄
     *
     * @return
     */
    List<Long> getMissingNbMeterHasCellIdMeterIds();

    /**
     * 查询没有上报过cellId的设备，配合定时任务在晚上执行补抄
     *
     * @return
     */
    List<Long> getMissingNbMeterNoneCellIdMeterIds();

    /**
     * 更新基表读数
     *
     * @param logId
     * @param factor
     * @param baseValue
     */
    void updateBaseValue(long logId, int factor, String baseValue);

    /**
     * 判断当前水表是否需要排除今日补抄表
     *
     * @param waterMeterId
     * @return
     */
    boolean isExcludeMeteringDataToday(Long waterMeterId);

    /**
     * 当日未上报的断电版本NB水表
     *
     * @return
     */
    List<NbWaterMeter> getMissingDataNbMeters();
}
