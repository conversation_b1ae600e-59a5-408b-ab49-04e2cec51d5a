package com.acpiot.wsd.productiontest.event;

import com.acpiot.wsd.cat1wmservice.event.UdpCommandRespDto;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 测试设备命令响应事件
 * Created by moxin on 2021-08-09-0009
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Getter
public class TestWaterMeterCmdRspEvent extends ApplicationEvent {

    private final String imei;
    private final Object commandRespDto;
    private final long eventTimestamp;

    public TestWaterMeterCmdRspEvent(Object source, String imei, UdpCommandRespDto commandRespDto, long eventTimestamp) {
        super(source);
        this.imei = imei;
        this.commandRespDto = commandRespDto;
        this.eventTimestamp = eventTimestamp;
    }
}
