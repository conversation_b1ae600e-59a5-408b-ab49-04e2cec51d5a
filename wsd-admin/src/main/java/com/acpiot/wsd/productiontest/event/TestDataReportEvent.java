package com.acpiot.wsd.productiontest.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 测试设备数据上报事件
 * Created by moxin on 2021-08-09-0009
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Getter
public class TestDataReportEvent extends ApplicationEvent {

    private final String imei;
    /**
     * 命令类型  0x07：常规数据上报，0x08：曲线数据上报，0x0B：7日冻结数据上报
     */
    private final int cmd;
    private final Object payload;
    private final long eventTimestamp;

    public TestDataReportEvent(Object source, String imei, int cmd, Object payload, long eventTimestamp) {
        super(source);
        this.imei = imei;
        this.cmd = cmd;
        this.payload = payload;
        this.eventTimestamp = eventTimestamp;
    }
}
