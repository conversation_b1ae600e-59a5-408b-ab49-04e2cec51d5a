package com.acpiot.wsd.mvc.controller;

import cn.hutool.captcha.LineCaptcha;
import cn.hutool.captcha.generator.RandomGenerator;
import com.acpiot.wsd.common.validate.ValidCodeManager;
import com.acpiot.wsd.common.validate.ValidCodeManager.CodeType;
import com.acpiot.wsd.common.validate.ValidCodeManager.CodeUse;
import com.acpiot.wsd.common.validate.ValidCodeManager.ValidCodeKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;

/**
 * Created by moxin on 2019-09-19-0019
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Slf4j
@RequiredArgsConstructor
@Controller
@RequestMapping("/wechat/green")
public class CaptchaController {

    private final ValidCodeManager validCodeManager;

    /**
     * 获取验证码
     *
     * @param randomKey
     * @param response
     * @throws Exception
     */
    @RequestMapping("/captcha")
    public void captcha(@RequestParam("randomKey") String randomKey, HttpServletResponse response) throws Exception {
        // 设置响应头信息
        response.setContentType("image/jpg");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);

        LineCaptcha captcha = new LineCaptcha(120, 38, new RandomGenerator("**********", 4), 10);
        captcha.write(response.getOutputStream());

        ValidCodeKey key = ValidCodeKey.of(randomKey, CodeType.IMAGE, CodeUse.BIND_ACCOUNT_NO);
        validCodeManager.put(key, captcha.getCode());
    }
}
