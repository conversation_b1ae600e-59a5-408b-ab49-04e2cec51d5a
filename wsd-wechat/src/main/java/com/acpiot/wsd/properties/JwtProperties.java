package com.acpiot.wsd.properties;

import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Created by moxin on 2019-06-21-0021
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Data
@Getter
@Setter
@NoArgsConstructor
@Configuration
@ConfigurationProperties(prefix = "application.jwt")
public class JwtProperties {

    /**
     * 密钥，必填
     */
    private String secret;

    /**
     * 是JWT的唯一标识，根据业务需要，这个可以设置为一个不重复的值，主要用来作为一次性token,从而回避重放攻击。可选
     */
    private String id;

    /**
     * jwt的有效时长，毫秒，可选
     */
    private long tokenValidityInMilliseconds;

    /**
     * 该JWT的签发者，可选
     */
    private String issuer;

    /**
     * 该JWT所面向的用户，可选
     */
    private String subject;
}
