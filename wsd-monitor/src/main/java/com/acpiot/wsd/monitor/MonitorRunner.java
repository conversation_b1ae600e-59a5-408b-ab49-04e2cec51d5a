package com.acpiot.wsd.monitor;

import com.acpiot.wsd.monitor.service.MonitorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> E-mail: <EMAIL>
 * @version 1.0 2018/11/8 5:00 PM
 */
@Slf4j
@RequiredArgsConstructor
@Component
@ConditionalOnProperty(name = {"application.enabled"}, havingValue = "true")
public class MonitorRunner implements CommandLineRunner {

    private final MonitorServer monitorServer;
    private final MonitorService monitorService;

    @Override
    public void run(String... args) throws Exception {
        monitorService.updateAllConcentratorOffline();
        monitorServer.start();
    }
}
