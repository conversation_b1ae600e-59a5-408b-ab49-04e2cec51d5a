package com.acpiot.wsd.monitor.protocol.transparent.zzmbus.structure;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.nio.ByteBuffer;

import static cn.hutool.core.util.HexUtil.decodeHex;

/**
 * Created by moxin on 2022-04-09-0009
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
public class SetValueStructure extends BaseZZMBusPacketStructure {

    private final byte type;
    private final String meterCode;
    private final BigDecimal value;

    @Override
    protected byte[] getFixedBytes() {
        return new byte[]{0x53, 0x1A, 0x0B, 0x74, 0x00};
    }

    @Override
    protected byte[] getPayload() {
        ByteBuffer buf = ByteBuffer.allocate(23);
        buf.put(type);
        buf.put(decodeHex(meterCode));

        // 设置表底数
        String valueFormat = NumberUtil.decimalFormat("000000.00", value);
        valueFormat = valueFormat.replace(".", "");
        byte[] valueBytes = decodeHex(valueFormat);
        Assert.isTrue(ArrayUtil.length(valueBytes) == 4);
        buf.put(valueBytes);

        buf.put((byte) 0x2C);
        return buf.array();
    }
}
