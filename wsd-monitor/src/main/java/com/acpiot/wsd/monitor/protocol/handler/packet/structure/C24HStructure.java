package com.acpiot.wsd.monitor.protocol.handler.packet.structure;

import static com.acpiot.wsd.monitor.protocol.handler.packet.CmdConstants.C_24H;

/**
 * 系统读取充电时间
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version 1.0 2021/11/2 02:48 PM
 */
public class C24HStructure extends BasePacketStructure {

    @Override
    protected byte getCmd() {
        return C_24H;
    }

    @Override
    public String getDescription() {
        return "系统读取充电时间";
    }

}
