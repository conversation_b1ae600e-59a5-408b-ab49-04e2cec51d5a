package com.acpiot.wsd.monitor.protocol.handler.packet.structure;

import lombok.RequiredArgsConstructor;

import java.nio.ByteBuffer;

import static com.acpiot.wsd.monitor.protocol.handler.packet.CmdConstants.C_1AH;

/**
 * Created by moxin on 2022-04-09-0009
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
public class C1AHStructure extends BasePacketStructure {

    /**
     * 透传数据
     */
    private final byte[] data;

    @Override
    protected byte getCmd() {
        return C_1AH;
    }

    @Override
    protected byte[] getPacketData() {
        ByteBuffer buffer = ByteBuffer.allocate(data.length + 1);
        buffer.put((byte) data.length);
        buffer.put(data);
        return buffer.array();
    }

    @Override
    public String getDescription() {
        return "系统透传数据(系统平台主动发出)";
    }
}
