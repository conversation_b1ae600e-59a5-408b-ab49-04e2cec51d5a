package com.acpiot.wsd.monitor.protocol.handler.packet.structure;

import com.acpiot.wsd.monitor.protocol.handler.dto.ValveControlParams;
import lombok.RequiredArgsConstructor;

import static com.acpiot.wsd.monitor.protocol.handler.packet.CmdConstants.C_0EH;

/**
 * 系统设置集中器开启指定的水表阀门(系统平台主动发出)
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version 1.0 2018/11/9 5:41 PM
 */
@RequiredArgsConstructor
public class C0EHStructure extends BasePacketStructure {

    private final ValveControlParams params;

    @Override
    protected byte getCmd() {
        return C_0EH;
    }

    @Override
    protected byte[] getPacketData() {
        byte[] data = new byte[10];
        System.arraycopy(params.toBytes(), 0, data, 0, 9);
        data[9] = (byte) 0x55;
        return data;
    }

    @Override
    public String getDescription() {
        return "系统设置集中器开启指定的水表阀门";
    }

    // private final MonitorService monitorService;
    // @Override
    // public void done() throws Exception {
    //     if (isSuccess()) {
    //         monitorService.updateValveStats(getCode(), params.getSerialNo(), ValveStatus.OPENED, getCommandId());
    //     }
    //
    //     monitorService.getConcentratorWaterMeter(getCode(), params.getSerialNo())
    //             .ifPresent(waterMeter -> {
    //                 // 发布水表命令响应事件
    //                 ValveCmdDataModel data = new ValveCmdDataModel(isSuccess() ? 0 : 1, waterMeter.getLatestValveStatus());
    //
    //                 String commandState = toCommandState(getExecuteState());
    //                 String commandDesc = getCommandDesc(commandState);
    //                 SpringUtils.publishEvent(new WaterMeterCmdEvent(this, waterMeter, getCommandId(),
    //                         commandState, commandDesc, data));
    //
    //                 // 更新命令日志
    //                 monitorService.updateMeterCmdLog(waterMeter, getCommandId(), commandDesc);
    //             });
    // }
}
