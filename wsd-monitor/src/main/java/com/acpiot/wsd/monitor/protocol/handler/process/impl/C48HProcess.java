package com.acpiot.wsd.monitor.protocol.handler.process.impl;

import com.acpiot.wsd.monitor.protocol.handler.dto.ReportTimeInfo;
import com.acpiot.wsd.monitor.protocol.handler.packet.Packet;
import com.acpiot.wsd.monitor.protocol.handler.process.MonitorProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 系统查询上报间隔时间(系统平台主动发出)响应处理
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version 1.0 2022/03/29 16:19 PM
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class C48HProcess implements MonitorProcess<ReportTimeInfo> {

    @Override
    public ReportTimeInfo process(Channel channel, Packet packet) throws Exception {
        ByteBuf buf = Unpooled.copiedBuffer(packet.getD());
        return ReportTimeInfo.of(buf);
    }
}
