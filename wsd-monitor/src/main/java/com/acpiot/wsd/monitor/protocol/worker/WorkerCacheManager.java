package com.acpiot.wsd.monitor.protocol.worker;

import cn.hutool.cache.GlobalPruneTimer;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.thread.ThreadUtil;
import com.acpiot.wsd.monitor.protocol.dto.ExecuteResult;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;

/**
 * Created by moxin on 2022-01-24-0024
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Component
public class WorkerCacheManager extends TimedCache<String, WorkerExecutor<?>> {

    public WorkerCacheManager() {
        super(60_000L);
        setListener((key, worker) -> {
            if (worker.getExecuteState() == ExecuteResult.ExecuteState.EXECUTING) {
                // 命令等待响应超时，注意：这里放入线程中执行，防止触发时在同一个锁中执行任务，导致死锁。
                ThreadUtil.execute(() -> worker.done(ExecuteResult.ExecuteState.TIME_OUT, null, null));
            }
        });
        schedulePrune(100);
    }

    public void putWorker(String uid, WorkerExecutor<?> worker) {
        put(uid, worker, worker.getTotalTimeout());
    }

    public void removeWorker(String uid) {
        remove(uid);
    }

    public WorkerExecutor<?> getWorker(String uid) {
        return get(uid, false);
    }

    @PreDestroy
    void onDestroy() {
        cancelPruneSchedule();
        clear();
        GlobalPruneTimer.INSTANCE.shutdown();
    }

}
