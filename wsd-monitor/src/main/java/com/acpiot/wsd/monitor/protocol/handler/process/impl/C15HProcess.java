package com.acpiot.wsd.monitor.protocol.handler.process.impl;

import cn.hutool.core.map.MapUtil;
import com.acpiot.wsd.data.monitor.entity.MeterData;
import com.acpiot.wsd.monitor.facade.MonitorFacade;
import com.acpiot.wsd.monitor.protocol.handler.packet.Packet;
import com.acpiot.wsd.monitor.protocol.handler.process.MonitorProcess;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.acpiot.wsd.monitor.protocol.utils.ProtocolUtils.bytes2WaterMeterData;

/**
 * 系统抄读集中器中单块水表的实时数据响应处理
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version 1.0 2018/11/9 4:39 PM
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class C15HProcess implements MonitorProcess<Map<Integer, MeterData>> {

    private final MonitorFacade monitorFacade;

    @Override
    public Map<Integer, MeterData> process(Channel channel, Packet packet) throws Exception {
        ByteBuf buf = Unpooled.copiedBuffer(packet.getD());
        boolean hasRssi = buf.readableBytes() >= 16;

        final int serialNo = buf.readShortLE();
        MeterData meterData = bytes2WaterMeterData(buf, hasRssi);
        Map<Integer, MeterData> map = MapUtil.builder(serialNo, meterData).build();
        if (meterData != null) {
            monitorFacade.addMeterData(packet.getAddress(), map, false);
            meterData.setWaterMeter(null);
        } else {
            log.info("抄读集中器下{}测量点{}水表数据无效", packet.getAddress(), serialNo);
        }
        return map;
    }
}
