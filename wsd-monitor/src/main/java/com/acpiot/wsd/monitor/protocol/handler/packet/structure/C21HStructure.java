package com.acpiot.wsd.monitor.protocol.handler.packet.structure;

import static com.acpiot.wsd.monitor.protocol.handler.packet.CmdConstants.C_21H;

/**
 * 系统启动抄表
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version 1.0 2021/11/2 02:48 PM
 */
public class C21HStructure extends BasePacketStructure {

    @Override
    protected byte getCmd() {
        return C_21H;
    }

    @Override
    public String getDescription() {
        return "系统启动抄表";
    }

}
