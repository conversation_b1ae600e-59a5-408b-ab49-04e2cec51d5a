package com.acpiot.wsd.monitor.tasks;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.acpiot.wsd.monitor.protocol.handler.packet.structure.C15HStructure;
import lombok.extern.slf4j.Slf4j;

/**
 * 单个集中器补抄实时数据
 * Created by moxin on 2019-12-24-0024
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Slf4j
public class ReadRealtimeDataTask extends ConcentratorTask {

    private final int[] totalSerialNos;

    private TimeInterval timer;

    public ReadRealtimeDataTask(String code, int[] totalSerialNos) {
        super(code);
        this.totalSerialNos = totalSerialNos;
    }

    private void readData(int i) {
        if (i >= totalSerialNos.length) {
            log.info("集中器 {} 补抄任务结束，总耗时：{}", code, timer.intervalPretty());
            onFinish(true, "补抄任务结束");
            return;
        }

        int serialNo = totalSerialNos[i];
        monitorExecutor.executeCmdNow(code, new C15HStructure(serialNo),
                workerExecutor -> workerExecutor.setRetryNum(3),
                executeResult -> {
                    if (executeResult.isSuccess()) {
                        readData(i + 1);
                    } else {
                        log.info("集中器 {} 下序号 {} 补抄失败", code, serialNo);
                        onFinish(false, executeResult.getDetails());
                    }
                });
    }

    @Override
    public void start() {
        log.info("集中器 {} 开始补抄表读数，表数量：{}", code, totalSerialNos.length);
        timer = DateUtil.timer();
        readData(0);
    }
}
