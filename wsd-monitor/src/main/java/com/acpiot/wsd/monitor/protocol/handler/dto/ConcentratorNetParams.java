package com.acpiot.wsd.monitor.protocol.handler.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 集中器网络参数
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version 1.0 2018/11/12 5:40 PM
 */
@Data
public class ConcentratorNetParams implements Serializable {

    /**
     * 主用IP
     */
    private String masterIp;

    /**
     * 主用端口
     */
    private int masterPort;

    /**
     * 备用IP
     */
    private String slaveIp;

    /**
     * 备用端口
     */
    private int slavePort;

    /**
     * APN
     */
    private String apn;
}
