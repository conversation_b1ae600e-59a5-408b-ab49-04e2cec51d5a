package com.acpiot.wsd.monitor.protocol.handler.process.impl;

import com.acpiot.wsd.monitor.protocol.handler.packet.Packet;
import com.acpiot.wsd.monitor.protocol.handler.process.MonitorProcess;
import io.netty.channel.Channel;
import org.springframework.stereotype.Component;

/**
 * 系统读取RSSI命令响应处理
 * Created by moxin on 2021-11-01-0025
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Component
public class C22HProcess implements MonitorProcess<Byte> {
    @Override
    public Byte process(Channel channel, Packet packet) throws Exception {
        return packet.getD()[0];
    }
}
