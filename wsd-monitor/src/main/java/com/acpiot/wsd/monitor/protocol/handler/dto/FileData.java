package com.acpiot.wsd.monitor.protocol.handler.dto;

import cn.hutool.core.util.ArrayUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.Builder;
import lombok.Value;

/**
 * 集中器升级文件信息
 * Created by moxin on 2020-08-25-0025
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Value
@Builder
public class FileData {

    /**
     * ——文件标识
     * 00H：清除下装文件，恢复到升级前状态。
     * 01H：终端升级文件。
     * 02H：远程（上行）通讯模块升级文件。
     * 03H：本地通信模块升级文件。
     * 04H：中继升级文件。
     * 05H：采集器升级的采集器程序文件。
     */
    int fileFlag;

    /**
     * 文件属性
     * 起始、中间帧：00H；结束帧：01H。
     */
    boolean finish;

    /**
     * 文件指令
     * 00H：报文方式下装；
     */
    int fileInstructions = 0x00;

    /**
     * 总段数n
     */
    int n;

    /**
     * 第i段标识或偏移（i=0～n-1）
     */
    int i;

    /**
     * 文件数据
     */
    byte[] data;

    public byte[] toBytes() {
        int dataLen = ArrayUtil.length(data);
        ByteBuf buf = Unpooled.buffer(dataLen + 11);
        buf.writeByte(fileFlag);
        buf.writeByte(finish ? 0x01 : 0x00);
        buf.writeByte(fileInstructions);
        buf.writeShortLE(n);
        buf.writeIntLE(i);
        buf.writeShortLE(dataLen);
        if (dataLen > 0) {
            buf.writeBytes(data);
        }
        return buf.array();
    }

}
