package com.acpiot.cps.docking.outbound.common.mqtt;

import com.acpiot.cps.common.utils.JsonUtils;
import com.acpiot.cps.event.MqttMessageDockingEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.MessagingException;
import org.springframework.util.StopWatch;

import java.util.Objects;

/**
 * 定制对接消息事件服务：定制对接事件消息处理器
 */
@Slf4j
@RequiredArgsConstructor
public class DockingMessageHandler implements MessageHandler, ApplicationEventPublisherAware {

    private ApplicationEventPublisher applicationEventPublisher;
    private final DockingInboundProperties dockingInboundProperties;

    @Override
    public void handleMessage(Message<?> message) throws MessagingException {
        log.info("定制对接消息事件服务：收到MQTT消息，message={}", message.getPayload());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            String topic = getTopic(message);
            if (dockingInboundProperties.getDockingTopic().equals(topic)) {
                MqttMessageDockingEvent event = JsonUtils.readValue((String) message.getPayload(), MqttMessageDockingEvent.class);
                Object realEvent = JsonUtils.readValue((String) message.getPayload(), Class.forName(event.getSource()));
                log.info("定制对接消息事件服务：收到MQTT定制对接事件消息 << {}", realEvent);
                if (realEvent != null && this.applicationEventPublisher != null) {
                    this.applicationEventPublisher.publishEvent(realEvent);
                }
            }
        } catch (Exception e) {
            log.error("定制对接消息事件服务：转换为应用事件发布异常", e);
        } finally {
            stopWatch.stop();
            log.info("定制对接消息事件服务：MQTT消息处理耗时：{} ms", stopWatch.getTotalTimeMillis());
        }
    }

    /**
     * 获取当前消息的主题
     *
     * @param message
     * @return
     */
    private String getTopic(Message<?> message) {
        MessageHeaders headers = message.getHeaders();
        return Objects.requireNonNull(headers.get("mqtt_receivedTopic")).toString();
    }

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }
}
