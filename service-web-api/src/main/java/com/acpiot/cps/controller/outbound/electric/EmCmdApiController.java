package com.acpiot.cps.controller.outbound.electric;

import com.acpiot.cps.adapter.electric.ElectricMeterCmdAdapter;
import com.acpiot.cps.common.enums.ErrorCode;
import com.acpiot.cps.common.logging.SystemLog;
import com.acpiot.cps.common.model.Rest;
import com.acpiot.cps.data.archive.entity.EmElectricMeter;
import com.acpiot.cps.data.monitor.entity.mongo.EmMeterCmdLog;
import com.acpiot.cps.common.enums.CommandState;
import com.acpiot.cps.data.monitor.enums.EmMeterCmdType;
import com.acpiot.cps.exception.ApiException;
import com.acpiot.cps.model.ApiResult;
import com.acpiot.cps.model.rsp.CmdRspApiModel;
import com.acpiot.cps.service.MeterCmdApiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;
import java.util.function.Function;

import static com.acpiot.cps.security.util.SecurityContextUtils.getLoginUsername;

/**
 * Created by moxin on 2020-12-22-0022
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Tag(name = "电表API(出站)")
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/api/command/em")
public class EmCmdApiController {

    private final MeterCmdApiService meterCmdApiService;
    private final ElectricMeterCmdAdapter electricMeterCmdAdapter;

    @SystemLog(description = "API-抄表")
    @Operation(summary = "抄表", parameters = {
            @Parameter(name = "meterCode", description = "电表表号，长度为10-12位数字", required = true, in = ParameterIn.QUERY)
    })
    @GetMapping("meterData")
    public ApiResult<CmdRspApiModel> metering(@NotBlank(message = "表号不能为空")
                                              @Pattern(regexp = "\\d{10,12}", message = "表号必须为10-12位数字")
                                              @RequestParam("meterCode") String meterCode) {
        return createAncCacheCmd(meterCode, electricMeter -> {
            // 判断当前电表是否有正在执行的同类型命令，如果存在则不允许下发命令
            Optional<EmMeterCmdLog> cmdLogOptional = meterCmdApiService.findFirstByElectricMeterAndCmdType(electricMeter.getId(), EmMeterCmdType.READ_CUR_FORWARD_POWER);
            if (cmdLogOptional.isPresent()) {
                CommandState commandState = cmdLogOptional.get().getCommandState();
                // 如果命令状态不为空，并且处于执行中，则不允许下发命令
                if (commandState == CommandState.SAVED) {
                    return ApiResult.error(403, "存在正在执行的命令");
                }
            }
            return transferApiResult(electricMeterCmdAdapter.readCurForwardPowerCommand(electricMeter.getId(), getLoginUsername()));
        });
    }

    @SystemLog(description = "API-电闸控制")
    @Operation(summary = "电闸控制", parameters = {
            @Parameter(name = "meterCode", description = "电表表号，长度为10-12位数字", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "action", description = "电闸动作，true 表示合闸，false 表示拉闸", required = true, in = ParameterIn.QUERY)
    })
    @PutMapping("switch")
    public ApiResult<CmdRspApiModel> ctrlSwitch(@NotBlank(message = "表号不能为空")
                                                @Pattern(regexp = "\\d{10,12}", message = "表号必须为10-12位数字")
                                                @RequestParam("meterCode") String meterCode,
                                                @RequestParam("action") boolean action) {
        return createAncCacheCmd(meterCode, electricMeter -> {
            if (action) {
                return transferApiResult(electricMeterCmdAdapter.onBrakeCommand(electricMeter.getId(), getLoginUsername()));
            } else {
                return transferApiResult(electricMeterCmdAdapter.offBrakeCommand(electricMeter.getId(), getLoginUsername()));
            }
        });
    }

    private ApiResult<CmdRspApiModel> createAncCacheCmd(String meterCode, Function<EmElectricMeter, ApiResult<CmdRspApiModel>> mapper) {
        return meterCmdApiService.getElectricMeterByCode(meterCode).map(mapper).orElseThrow(() -> new ApiException(ErrorCode.CODE_NOT_FOUNT));
    }

    private ApiResult<CmdRspApiModel> transferApiResult(Rest<com.acpiot.cps.common.model.CmdRspModel> cmdRspModelRest) {
        if (cmdRspModelRest.isSuccess()) {
            CmdRspApiModel cmdRspApiModel = new CmdRspApiModel(cmdRspModelRest.getData().getCmdRspDto());
            String meterCmdLogId = cmdRspModelRest.getData().getMeterCmdLogId();
            meterCmdApiService.updateEmMeterCmdLogDockingTrue(meterCmdLogId);
            return ApiResult.success(cmdRspApiModel);
        }
        return ApiResult.error(cmdRspModelRest.getErrCode(), cmdRspModelRest.getMessage());
    }
}
