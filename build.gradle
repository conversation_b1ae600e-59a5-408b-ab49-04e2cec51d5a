buildscript {
    repositories {
        maven {
            url "https://plugins.gradle.org/m2/"
        }
    }
    dependencies {
        classpath "org.springframework.boot:spring-boot-gradle-plugin:3.1.12"
        classpath "io.spring.gradle:dependency-management-plugin:1.1.5"
    }
}

allprojects {
    apply plugin: 'idea'
    apply plugin: 'java-library'
    apply plugin: 'maven-publish'
    group 'pers.mx.fileserver'
    version '2.0.2'
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17

    repositories {
        mavenLocal()
        maven {
            url 'https://maven.aliyun.com/repository/public'
        }
    }
}

subprojects {
    apply plugin: 'java'
    apply plugin: "io.spring.dependency-management"

    // java编译的时候缺省状态下会因为中文字符而失败
    [compileJava, compileTestJava, javadoc]*.options*.encoding = 'UTF-8'

    configurations {
        compileOnly {
            extendsFrom annotationProcessor
        }
    }

    // 依赖管理
    dependencyManagement {

        dependencies {
            imports {
                mavenBom org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES
            }

            // Hutool组
            dependencySet(group: 'cn.hutool', version: '5.8.38') {
                entry 'hutool-core'
            }
        }

    }

}
