plugins {
    id 'java'
    id 'org.springframework.boot' version '3.5.0'
    id 'io.spring.dependency-management' version '1.1.7'
    id 'org.hibernate.orm' version '6.6.15.Final'
    id 'org.hidetake.ssh' version '2.11.2'
}

group = 'com.acpiot'
version = '1.0.0'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

configurations.configureEach {
    exclude group: 'commons-logging', module: 'commons-logging'
}

repositories {
    mavenLocal()
    maven { url = 'https://maven.aliyun.com/repository/public' }
    maven {
        credentials {
            username = '609c9edd6d822d6b01ab9755'
            password = ')m4zU_tnSnfd'
        }
        url = 'https://packages.aliyun.com/maven/repository/2102844-release-O5kcx3/'
    }
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.hibernate.orm:hibernate-community-dialects'
    annotationProcessor 'org.hibernate.orm:hibernate-jpamodelgen'
    implementation 'com.github.gavlyukovskiy:p6spy-spring-boot-starter:1.11.0'
    implementation 'pers.mx.jupiter:jupiter-jpa:3.1.11u2'
    implementation 'pers.mx.jupiter:jupiter-web:3.1.11u2'
    runtimeOnly 'com.h2database:h2'

//    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    implementation 'com.dtflys.forest:forest-spring-boot3-starter:1.6.4'
    implementation 'cn.hutool:hutool-core:5.8.38'

    implementation 'org.apache.poi:poi-ooxml:5.4.1'
    implementation "pers.mx.fileserver:fileserver-spring-boot-starter:2.1.0"

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'

    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'
}

tasks.named('test') {
    useJUnitPlatform()
}

hibernate {
    enhancement
}

jar {
    enabled = false
}

remotes {
    webServer {
        host = '************'
        user = 'moxin'
        password = 'YRum%&gt;0IXC[@W&amp;j}'
        knownHosts = allowAnyHosts
    }
}
tasks.register('deploy') {
    doLast {
        ssh.run {
            session(remotes.webServer) {
                def remoteDir = '/home/<USER>/app/large-screen/'
                def localFile = bootJar.archiveFile.get().asFile

                remove remoteDir + localFile.name
                put from: localFile, into: remoteDir
                execute 'sh ' + remoteDir + 'start.sh'
            }
        }
    }
}
