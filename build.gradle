buildscript {
    repositories {
        gradlePluginPortal()
        maven { url = "https://plugins.gradle.org/m2/" }
    }
    dependencies {
        classpath "org.springframework.boot:spring-boot-gradle-plugin:3.5.0"
        classpath "io.spring.gradle:dependency-management-plugin:1.1.7"
        classpath "org.hibernate.orm:org.hibernate.orm.gradle.plugin:6.6.13.Final"
        classpath "org.hidetake:core:2.11.2"
    }
}

allprojects {
    apply plugin: 'idea'
    apply plugin: 'eclipse'
    apply plugin: 'java-library'
    group = 'com.acpiot.production'
    version = '1.0.0'

    java {
        sourceCompatibility = JavaVersion.VERSION_21
    }

    repositories {
        mavenLocal()
//        mavenCentral()
        maven { url = 'https://maven.aliyun.com/repository/public' }
        maven {
            url = 'https://packages.aliyun.com/maven/repository/2102844-release-O5kcx3/'
            credentials {
                username = '609c9edd6d822d6b01ab9755'
                password = ')m4zU_tnSnfd'
            }
        }
    }
}

subprojects {
    apply plugin: 'java'
    apply plugin: "io.spring.dependency-management"

    // java编译的时候缺省状态下会因为中文字符而失败
    [compileJava, compileTestJava, javadoc]*.options*.encoding = 'UTF-8'

    configurations {
        compileOnly {
            extendsFrom annotationProcessor
        }
    }

    configurations.configureEach {
        exclude group: 'commons-logging', module: 'commons-logging'
    }

    // 依赖管理
    dependencyManagement {

        dependencies {
            imports {
                mavenBom org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES
            }
            dependency("io.hypersistence:hypersistence-utils-hibernate-63:3.9.10")
            dependency("com.github.gavlyukovskiy:p6spy-spring-boot-starter:1.11.0")
            dependency("com.github.liaochong:myexcel:4.5.6")

            dependency("net.dreamlu:mica-ip2region:3.4.5")

            // Hutool组
            dependencySet(group: 'cn.hutool', version: '5.8.38') {
                entry 'hutool-core'
                entry 'hutool-crypto'
                entry 'hutool-captcha'
                entry 'hutool-poi'
                entry 'hutool-cache'
            }
            // Retrofit组
            dependencySet(group: 'com.squareup.retrofit2', version: '2.11.0') {
                entry 'retrofit'
                entry 'converter-jackson'
            }
            // OkHttp组
            dependencySet(group: 'com.squareup.okhttp3', version: '3.14.9') {
                entry 'okhttp'
                entry 'logging-interceptor'
            }

            // poi
            dependencySet(group: 'org.apache.poi', version: '5.4.1') {
                entry 'poi-ooxml'
                entry 'poi-ooxml-full'
            }

            dependencySet(group: 'pers.mx.jupiter', version: '3.1.11u2') {
                entry 'jupiter-common'
                entry 'jupiter-jpa'
                entry 'jupiter-web'
            }
            dependency("com.acpiot:meterservice-spring-boot-starter:3.0.0")
            dependency("pers.mx.scheduler:scheduler-spring-boot-starter:2.0.0")

            // jjwt组
            dependencySet(group: 'io.jsonwebtoken', version: '0.12.6') {
                entry 'jjwt-api'
                entry 'jjwt-impl'
                entry 'jjwt-jackson'
            }

            // Forest
            dependency('com.dtflys.forest:forest-spring-boot3-starter:1.6.4')

            dependency('org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.5')

            // 前端依赖
            dependency("pers.mx.webjars:light-year-admin-v4:1.0.2")
            dependency('org.webjars.bower:jsencrypt:3.1.0')
            dependency('org.webjars.bowergithub.wenzhixin:bootstrap-table:1.18.1')
            dependency('org.webjars.bower:jquery-validation:1.19.5')
            dependency('org.webjars.bowergithub.borismoore:jsrender:1.0.12')
            dependency('org.webjars.bower:multiselect:0.9.12')
            dependency('org.webjars.bowergithub.vuejs:vue:2.6.14')
        }

    }

    dependencies {
        compileOnly 'org.projectlombok:lombok'
        annotationProcessor 'org.projectlombok:lombok'
    }
}
