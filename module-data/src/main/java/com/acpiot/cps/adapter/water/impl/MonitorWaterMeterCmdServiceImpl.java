package com.acpiot.cps.adapter.water.impl;

import cn.hutool.core.date.DateUtil;
import com.acpiot.cps.adapter.water.WaterMeterCmdService;
import com.acpiot.cps.common.enums.ErrorCode;
import com.acpiot.cps.common.model.CmdRspModel;
import com.acpiot.cps.common.model.ExecuteResult;
import com.acpiot.cps.common.model.Rest;
import com.acpiot.cps.common.utils.JsonUtils;
import com.acpiot.cps.data.archive.entity.WmConcentratorWaterMeter;
import com.acpiot.cps.data.archive.entity.WmWaterMeter;
import com.acpiot.cps.data.monitor.dto.ForceCtrlParams;
import com.acpiot.cps.data.monitor.dto.ValveCtrlParams;
import com.acpiot.cps.data.monitor.entity.mongo.WmMeterCmdLog;
import com.acpiot.cps.common.enums.CommandState;
import com.acpiot.cps.data.monitor.enums.ForceValveType;
import com.acpiot.cps.data.monitor.enums.ValveCtrlType;
import com.acpiot.cps.data.monitor.enums.WmMeterCmdType;
import com.acpiot.cps.data.monitor.repository.mongo.WmMeterCmdLogRepository;
import com.acpiot.cps.monitor.MonitorWaterMeterCmd;
import com.acpiot.cps.monitor.dto.MonitorCmdReqDto;
import com.acpiot.cps.monitor.dto.QueryMeterDataDto;
import com.acpiot.cps.monitor.dto.ValveCtrlDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.function.Function;

import static com.acpiot.cps.common.model.Rest.toExecuteResult;

/**
 * 集中器水表命令操作实现
 * Created by moxin on 2021-03-30-0030
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Service("monitorWaterMeterCmdService")
public class MonitorWaterMeterCmdServiceImpl implements WaterMeterCmdService {

    public static final byte VALVE_CTRL_OPEN = 0x55;
    public static final byte VALVE_CTRL_CLOSE = (byte) 0x99;

    private final MonitorWaterMeterCmd monitorWaterMeterCmd;
    private final WmMeterCmdLogRepository wmMeterCmdLogRepository;

    @Override
    public Rest<CmdRspModel> meteringData(WmWaterMeter waterMeter, String operator) {
        return ctrlMeter(waterMeter, wmConcentratorWaterMeter -> {
            boolean isMultiFunc = wmConcentratorWaterMeter.getConcentrator().isMultiFunc();
            QueryMeterDataDto dto = new QueryMeterDataDto(isMultiFunc);
            if (isMultiFunc) {
                dto.setMeterCode(wmConcentratorWaterMeter.getCode());
                dto.setProtocolPort(wmConcentratorWaterMeter.getProtocolPort());
            } else {
                dto.setSerialNo(wmConcentratorWaterMeter.getSerialNo());
            }
            return monitorWaterMeterCmd.queryMeterData(MonitorCmdReqDto.of(wmConcentratorWaterMeter.getConcentrator().getCode(), dto));
        }, (commandRspDtoRest) -> {
            WmMeterCmdLog meterCmdLog = WmMeterCmdLog.builder().build();
            return addMeterCmdLog(waterMeter, WmMeterCmdType.METERING_DATA, meterCmdLog, commandRspDtoRest);
        });
    }

    @Override
    public Rest<CmdRspModel> openValve(WmWaterMeter waterMeter, ValveCtrlParams valveCtrlParams) {
        return ctrlMeter(waterMeter, wmConcentratorWaterMeter -> {
            boolean isMultiFunc = wmConcentratorWaterMeter.getConcentrator().isMultiFunc();
            if (isMultiFunc) {
                ValveCtrlDto dto = new ValveCtrlDto(wmConcentratorWaterMeter.getCode(), wmConcentratorWaterMeter.getProtocolPort(), VALVE_CTRL_OPEN);
                return monitorWaterMeterCmd.valveCtrl(MonitorCmdReqDto.of(wmConcentratorWaterMeter.getConcentrator().getCode(), dto));
            } else {
                return monitorWaterMeterCmd.openValve(MonitorCmdReqDto.of(wmConcentratorWaterMeter.getConcentrator().getCode(), wmConcentratorWaterMeter.getSerialNo()));
            }
        }, (commandRspDtoRest) -> {
            // 设置强制开关阀参数
            String params = null;
            ValveCtrlType valveCtrlType = valveCtrlParams.getValveCtrlType();
            if (valveCtrlType == ValveCtrlType.FORCE) {
                ForceValveType forceValveType = ForceValveType.FORCE_OPEN_VALVE;
                Long forceValveTime = (long) valveCtrlParams.getDuration();
                params = JsonUtils.writeValueAsString(new ForceCtrlParams(forceValveTime, forceValveType));
            }
            WmMeterCmdLog meterCmdLog = WmMeterCmdLog.builder().params(params).build();
            return addMeterCmdLog(waterMeter, WmMeterCmdType.OPEN_VALVE, meterCmdLog, commandRspDtoRest);
        });
    }

    @Override
    public Rest<CmdRspModel> closeValve(WmWaterMeter waterMeter, ValveCtrlParams valveCtrlParams) {
        return ctrlMeter(waterMeter, wmConcentratorWaterMeter -> {
            boolean isMultiFunc = wmConcentratorWaterMeter.getConcentrator().isMultiFunc();
            if (isMultiFunc) {
                ValveCtrlDto dto = new ValveCtrlDto(wmConcentratorWaterMeter.getCode(), wmConcentratorWaterMeter.getProtocolPort(), VALVE_CTRL_CLOSE);
                return monitorWaterMeterCmd.valveCtrl(MonitorCmdReqDto.of(wmConcentratorWaterMeter.getConcentrator().getCode(), dto));
            } else {
                return monitorWaterMeterCmd.closeValve(MonitorCmdReqDto.of(wmConcentratorWaterMeter.getConcentrator().getCode(), wmConcentratorWaterMeter.getSerialNo()));
            }
        }, (commandRspDtoRest) -> {
            // 设置强制开关阀参数
            String params = null;
            ValveCtrlType valveCtrlType = valveCtrlParams.getValveCtrlType();
            if (valveCtrlType == ValveCtrlType.FORCE) {
                ForceValveType forceValveType = ForceValveType.FORCE_CLOSE_VALVE;
                Long forceValveTime = (long) valveCtrlParams.getDuration();
                params = JsonUtils.writeValueAsString(new ForceCtrlParams(forceValveTime, forceValveType));
            }
            WmMeterCmdLog meterCmdLog = WmMeterCmdLog.builder().params(params).build();
            return addMeterCmdLog(waterMeter, WmMeterCmdType.CLOSE_VALVE, meterCmdLog, commandRspDtoRest);
        });
    }

    /**
     * 请求执行命令
     *
     * @param waterMeter
     * @param reqHandler
     * @param rspHandler
     * @return
     */
    private Rest<CmdRspModel> ctrlMeter(WmWaterMeter waterMeter, Function<WmConcentratorWaterMeter, Rest<ExecuteResult>> reqHandler,
                                        Function<Rest<ExecuteResult>, WmMeterCmdLog> rspHandler) {
        // 集中器水表需要同步后才能请求命令
        WmConcentratorWaterMeter wcm = (WmConcentratorWaterMeter) waterMeter;
        if (!wcm.isSync()) {
            return Rest.error(ErrorCode.METER_NOT_SYNC, wcm.getId(), wcm.getCode());
        }
        if (!wcm.getConcentrator().isOnline()) {
            return Rest.error(ErrorCode.METER_OFFLINE, wcm.getId(), wcm.getCode());
        }
        // 根据不同的设备类型执行命令请求，获取响应对象
        Rest<ExecuteResult> rest = reqHandler.apply(wcm);
        // 根据命令请求结果，创建请求命令日志
        WmMeterCmdLog meterCmdLog = rspHandler.apply(rest);
        // 返回操作结果
        return toExecuteResult(waterMeter.getId(), waterMeter.getCode(), meterCmdLog.getId(), rest);
    }

    /**
     * 保存集中器水表命令日志
     *
     * @param wmWaterMeter
     * @param cmdType
     * @param meterCmdLog
     * @param rest
     * @return
     */
    private WmMeterCmdLog addMeterCmdLog(WmWaterMeter wmWaterMeter, WmMeterCmdType cmdType, WmMeterCmdLog meterCmdLog, Rest<ExecuteResult> rest) {
        meterCmdLog.fillWaterMeterData(wmWaterMeter);
        meterCmdLog.setCmdType(cmdType);
        if (rest.isSuccess()) {
            ExecuteResult executeResult = rest.getData();
            meterCmdLog.setCommandId(executeResult.getCommandId());
            meterCmdLog.setDetails(String.format("集中器水表指令\n状态：%s，时间：%s", executeResult.getMessage(), DateUtil.now()));
            meterCmdLog.setCommandState(toCommandState(executeResult.getExecuteState()));
            return wmMeterCmdLogRepository.save(meterCmdLog);
        } else {
            meterCmdLog.setDetails(String.format("集中器水表指令创建失败：%s", rest.getMessage()));
            meterCmdLog.setCommandState(CommandState.CREATE_FAIL);
            return wmMeterCmdLogRepository.save(meterCmdLog);
        }
    }

    /**
     * 转换命令请求状态
     *
     * @param executeState
     * @return
     */
    private CommandState toCommandState(ExecuteResult.ExecuteState executeState) {
        return switch (executeState) {
            case DISCONNECTED -> CommandState.CREATE_FAIL;
            case SAVED -> CommandState.SAVED;
            case SEND -> CommandState.SENT;
            case COMPLETED -> CommandState.COMPLETED;
            case TIME_OUT -> CommandState.TIMEOUT;
            case FAILURE -> CommandState.FAILED;
            case ERROR -> CommandState.FAILED;
            case BUSY -> CommandState.FAILED;
        };
    }
}
