package com.acpiot.cps.data.revenue.repository.mongo;

import com.acpiot.cps.common.mongodb.repository.MongoCompanyRepository;
import com.acpiot.cps.data.revenue.entity.mongo.EmDayFreezeAmount;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.Update;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface EmDayFreezeAmountRepository extends MongoCompanyRepository<EmDayFreezeAmount> {

    default Class<EmDayFreezeAmount> getClazz() {
        return EmDayFreezeAmount.class;
    }

    Optional<EmDayFreezeAmount> findByMeterIdAndFreezeDate(Long id, LocalDate toLocalDate);

    List<EmDayFreezeAmount> findByMeterIdAndFreezeDateBetweenOrderByFreezeDate(Long electricMeterId, LocalDate lastBillDate, LocalDate now);

    List<EmDayFreezeAmount> findByMeterId(Long id);

    void deleteByMeterIdIn(Long[] ids);

    void deleteByMeterIdAndFreezeDateAfter(Long id, LocalDate localDate);

    void deleteByMeterIdAndFreezeDateBetween(Long id, LocalDate startDate, LocalDate endDate);

    @Query("{meterId: {$in: ?0}}")
    @Update("{$set: {'communityId': ?1, 'communityName': ?2, 'companyId': ?3}}")
    void updateCommunityAndCompanyIdByMeterIdIn(List<Long> meterIds, Long communityId, String communityName, String companyId);
}
