package com.acpiot.cps.data.sys.entity;

import com.acpiot.cps.common.jpa.entity.BaseAuditingCompanyEntity;
import com.acpiot.cps.data.archive.entity.Community;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.HashSet;
import java.util.Set;

/**
 * 管辖区域(Region)实体类
 *
 * <AUTHOR>
 * @since 2022-08-30 14:14:12
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
public class Region extends BaseAuditingCompanyEntity {

    /**
     * 区域名称
     */
    @Column(nullable = false, length = 24)
    @NotBlank
    @Size(max = 24, message = "区域名称最多可输入24个字符")
    private String name;

    /**
     * 备注信息
     */
    @Column
    @Size(max = 255, message = "备注最多可输入255个字符")
    private String remark;

    /**
     * 配置管辖区域和用户的映射关系
     */
    @ManyToMany(mappedBy = "regions", fetch = FetchType.LAZY)
    private Set<User> users = new HashSet<>(0);

    /**
     * 该管辖区域下的小区
     */
    @OneToMany(mappedBy = "region", fetch = FetchType.LAZY)
    private Set<Community> communities = new HashSet<>(0);

    @Transient
    public boolean isSysDefaultRegion() {
        return this.getCompanyId() == null && "平台内置管辖区域(预导入用)".equals(this.name);
    }
}