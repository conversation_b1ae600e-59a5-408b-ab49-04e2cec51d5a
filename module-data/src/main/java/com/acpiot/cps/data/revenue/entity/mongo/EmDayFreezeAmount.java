package com.acpiot.cps.data.revenue.entity.mongo;

import com.acpiot.cps.common.mongodb.entity.MongoDeviceAuditingEntity;
import com.acpiot.cps.data.archive.enums.EmFreezeDataCalcType;
import com.acpiot.cps.data.monitor.enums.ReadingMeterType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.springframework.data.mongodb.core.mapping.FieldType.DECIMAL128;

/**
 * 电表日冻结金额实体
 */
@Getter
@Setter
@Document("em_day_freeze_amount")
@NoArgsConstructor
@CompoundIndexes({
        @CompoundIndex(name = "meterId_freezeDate_index", def = "{'meterId': 1, 'freezeDate': -1}")
})
public class EmDayFreezeAmount extends MongoDeviceAuditingEntity {

    /**
     * 冻结日期
     */
    @Indexed(name = "freezeDate")
    private LocalDate freezeDate;

    /**
     * 冻结月份
     */
    @Indexed(name = "freezeMonth")
    private String freezeMonth;

    /**
     * 房间余额
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal roomBalance;

    /**
     * 表读数
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal value;

    /**
     * 表读数（尖）
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal value1;

    /**
     * 表读数（峰）
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal value2;

    /**
     * 表读数（平）
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal value3;

    /**
     * 表读数（谷）
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal value4;

    /**
     * 用电量
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal consumption;

    /**
     * 用电量（尖）
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal consumption1;

    /**
     * 用电量（峰）
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal consumption2;

    /**
     * 用电量（平）
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal consumption3;

    /**
     * 用电量（谷）
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal consumption4;

    /**
     * 金额
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal amount;

    /**
     * 金额（尖）
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal amount1;

    /**
     * 金额（峰）
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal amount2;

    /**
     * 金额（平）
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal amount3;

    /**
     * 金额（谷）
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal amount4;

    /**
     * 用水状态
     */
    private UseElectricState useElectricState;

    /**
     * 抄收类型
     */
    private ReadingMeterType readingMeterType;

    /**
     * 上次上报时间
     */
    private LocalDateTime lastFreezeDate;

    /**
     * 上次上报读数
     */
    @Field(targetType = DECIMAL128)
    private BigDecimal lastFreezeValue;

    /**
     * 本次日冻结的结束时间（仅用于日冻结正向有功类型的电表）
     */
    private LocalDateTime endDateTime;

    /**
     * 日冻结计算方式
     */
    private EmFreezeDataCalcType freezeDataCalcType;

    public enum UseElectricState {
        /**
         * 正常
         */
        NORMAL,
        /**
         * 零电量
         */
        ZERO_ELECTRIC_QUANTITY,

        /**
         * 小电量
         */
        MIN_ELECTRIC_QUANTITY,

        /**
         * 大电量
         */
        MAX_ELECTRIC_QUANTITY;
    }

    public LocalDateTime getLastModifiedDate() {
        // 如果endDateTime不为空，则返回endDateTime，否则返回lastModifiedDate
        return endDateTime != null ? endDateTime : super.getLastModifiedDate();
    }
}
