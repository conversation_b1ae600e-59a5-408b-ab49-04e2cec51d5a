package com.acpiot.cps.data.monitor.entity;

import com.acpiot.cps.common.jpa.entity.BaseAuditingCompanyEntity;
import com.acpiot.cps.data.archive.entity.Room;
import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.math.BigDecimal;
import java.util.List;

/**
 * 房间抄表参数
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
public class RoomMeterConfig extends BaseAuditingCompanyEntity {

    /**
     * 所属房间
     */
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    private Room room;

    /**
     * 小流量阈值
     */
    @Column
    private BigDecimal minFlow;

    /**
     * 大流量阈值
     */
    @Column
    private BigDecimal maxFlow;

    /**
     * 小电量阈值
     */
    @Column
    private BigDecimal minElectric;

    /**
     * 大电量阈值
     */
    @Column
    private BigDecimal maxElectric;

    /**
     * 房间ID
     */
    @Transient
    private List<Long> roomIds;
}