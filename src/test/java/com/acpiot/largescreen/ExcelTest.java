package com.acpiot.largescreen;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Slf4j
public class ExcelTest {

    public static void appendColumn(String filePath, String newColumnName) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = WorkbookFactory.create(fis)) {

            for (Sheet sheet : workbook) {
                // 获取最后一行的行号
                int lastRowNum = sheet.getLastRowNum();

                // 获取最大列数
                int maxColumnIndex = 7;

                // for (int i = 0; i <= lastRowNum; i++) {
                //     Row row = sheet.getRow(i);
                //     if (row != null) {
                //         maxColumnIndex = Math.max(maxColumnIndex, row.getLastCellNum());
                //     }
                // }

                // 倍数
                Integer multiple = null;

                // 实际用量
                Integer actualUsage = null;

                // 在每行末尾添加新列
                for (int i = 0; i <= lastRowNum; i++) {
                    Row row = sheet.getRow(i);
                    if (row == null) {
                        row = sheet.createRow(i);
                    }

                    if (i == 3) {
                        // 这里是数量单元格
                        Cell cell = row.getCell(maxColumnIndex);
                        if (cell.getCellType() == CellType.NUMERIC) {
                            multiple = (int) cell.getNumericCellValue();
                        } else if (cell.getCellType() == CellType.STRING) {
                            String cellVal = cell.getStringCellValue();
                            if (StrUtil.isNotBlank(cellVal)) {
                                multiple = Integer.parseInt(cellVal);
                            }
                        }

                        Assert.notNull(multiple, "第 {} 行 {} 列数量数据错误，请检查。", i + 1, maxColumnIndex);

                        log.info("multiple: {}", multiple);
                    } else if (i >= 5) {
                        // 这行开始处理数据
                        Cell cell = row.getCell(maxColumnIndex - 1);

                        // 用量
                        Integer usage = null;
                        if (cell != null) {
                            if (cell.getCellType() == CellType.NUMERIC) {
                                usage = (int) cell.getNumericCellValue();
                            } else if (cell.getCellType() == CellType.STRING) {
                                String cellVal = cell.getStringCellValue();
                                if (StrUtil.isNotBlank(cellVal)) {
                                    usage = Integer.parseInt(cellVal);
                                }
                            }
                        }

                        Assert.notNull(usage, "第 {} 行 {} 列用量数据错误，请检查。", i + 1, maxColumnIndex);

                        // 实际用量
                        actualUsage = usage * multiple;
                    }

                    Cell newCell = row.createCell(maxColumnIndex + 1); // 插入新列
                    if (i == 0) {
                        newCell.setCellValue(newColumnName); // 设置标题
                    } else if (i < 5) {
                        newCell.setCellValue(""); // 默认空值
                    } else {
                        newCell.setCellValue(actualUsage);
                    }

                    // 保留原行样式（可选）
                    if (i > 0 && row.getCell(0) != null) {
                        CellStyle originalStyle = row.getCell(0).getCellStyle();
                        newCell.setCellStyle(originalStyle);
                    }
                }
            }

            // 写回原文件
            try (FileOutputStream fos = new FileOutputStream("D:\\Users\\moxin\\Desktop\\生产BOM_new.xls")) {
                workbook.write(fos);
            }
        }
    }

    public static void main(String[] args) throws IOException {
        String filePath = "D:\\Users\\moxin\\Desktop\\生产BOM.xls"; // 替换为你的文件路径
        String newColumnName = "库存 - 用量"; // 新列名
        appendColumn(filePath, newColumnName);
        log.info("Done。");
    }

}
