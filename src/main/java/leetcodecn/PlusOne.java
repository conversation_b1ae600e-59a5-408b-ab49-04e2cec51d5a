package leetcodecn;

class PlusOne {


    //IMPORTANT!! Submit Code Region Begin(Do not remove this line)
    class Solution {
        public int[] plusOne(int[] digits) {
            for (int i = digits.length - 1; i >= 0; i--) {
                if (digits[i] < 9) {
                    digits[i]++;
                    return digits;
                } else {
                    digits[i] = 0;
                }
            }
            int[] newDigits = new int[digits.length + 1];
            newDigits[0] = 1;
            System.arraycopy(newDigits, 1, digits, 0, digits.length);
            return newDigits;
        }
    }
//IMPORTANT!! Submit Code Region End(Do not remove this line)

    public static void main(String[] args) {
        // add your test code
    }
}
