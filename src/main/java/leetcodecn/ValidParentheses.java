package leetcodecn;

import java.util.Stack;

class ValidParentheses {


//IMPORTANT!! Submit Code Region Begin(Do not remove this line)
class Solution {
    public boolean isValid(String s) {
        if (s.length() % 2 != 0) {
            return false;
        }

        Stack<Byte> stack = new Stack<>();

        byte[] bytes = s.getBytes();
        for (byte b : bytes) {
            switch (b) {
                case '(', '[', '{' -> stack.push(b);
                default -> {
                    if (stack.isEmpty()) {
                        return false;
                    }
                    switch (stack.pop()) {
                        case '(' -> {
                            if (b != ')') {
                                return false;
                            }
                        }
                        case '[' -> {
                            if (b != ']') {
                                return false;
                            }
                        }
                        case '{' -> {
                            if (b != '}') {
                                return false;
                            }
                        }
                    }
                }
            }
        }

        return stack.isEmpty();
    }
}
//IMPORTANT!! Submit Code Region End(Do not remove this line)

public static void main(String[] args) {
    // add your test code
}
}
