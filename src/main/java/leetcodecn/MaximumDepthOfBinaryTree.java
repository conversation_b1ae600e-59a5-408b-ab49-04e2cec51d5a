package leetcodecn;

class MaximumDepthOfBinaryTree {

    
//IMPORTANT!! Submit Code Region Begin(Do not remove this line)
/**
 * Definition for a binary tree node.
 * public class TreeNode {
 *     int val;
 *     TreeNode left;
 *     TreeNode right;
 *     TreeNode() {}
 *     TreeNode(int val) { this.val = val; }
 *     TreeNode(int val, TreeNode left, TreeNode right) {
 *         this.val = val;
 *         this.left = left;
 *         this.right = right;
 *     }
 * }
 */
class Solution {
    public int maxDepth(TreeNode root) {
        return deep(root, 0);
    }

    private int deep(TreeNode parentNode, int deep) {
        if (parentNode == null) {
            return deep;
        }
        return Math.max(deep(parentNode.left, deep + 1), deep(parentNode.right, deep + 1));
    }
}
//IMPORTANT!! Submit Code Region End(Do not remove this line)

public static void main(String[] args) {
    // add your test code
}
}
