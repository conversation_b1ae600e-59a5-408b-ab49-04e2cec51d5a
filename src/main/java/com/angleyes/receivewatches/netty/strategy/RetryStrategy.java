package com.angleyes.receivewatches.netty.strategy;

import com.angleyes.receivewatches.netty.config.TcpClientInit;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.EventLoop;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;


@Slf4j
@RequiredArgsConstructor
public class RetryStrategy {

    //启动引导类
    private final Bootstrap bootstrap;

    //最大重试次数
    @Setter
    private Integer retryMaxCount = -1;

    //最长间隔重试时间
    @Setter
    private int retryMaxTime = 128;

    //重试成功触发的事件
    @Setter
    private Consumer<Boolean> consumer;

    //初始重试次数
    private int currentRetryCount = 0;
    //初始重试间隔 1秒
    private long delay = 1L;

    public void processRetryConnect(ChannelHandlerContext ctx) {
        TcpClientInit.setGlobalChannel(null);
        if (Objects.isNull(ctx)) {
            log.error("RetryStrategy-处理器都不存在");
            //关闭整个客户端, 是整个netty应用停止
            bootstrap.config().group().shutdownGracefully();
            return;
        }

        if (retryMaxCount > -1 && currentRetryCount >= retryMaxCount) {
            log.error("RetryStrategy-重试已达最大次数,取消重试,关闭客户端");
            //关闭整个客户端, 是整个netty应用停止
            bootstrap.config().group().shutdownGracefully();
            return;
        }

        long delay = getDelay();
        currentRetryCount++;

        log.info("RetryStrategy-重连,当前次数:{},当前延迟重试间隔:{}秒", currentRetryCount, delay);
        EventLoop eventLoop = ctx.channel().eventLoop();
        eventLoop.schedule(() -> {
            try {
                ChannelFuture channelFuture = bootstrap.connect();
                channelFuture.addListener(future -> {
                    //触发事件
                    boolean futureSuccess = future.isSuccess();
                    //失败后重调递归调
                    if (!futureSuccess) {
                        this.processRetryConnect(ctx);
                    } else {
                        //成功后重置次数和延迟时间
                        log.info("RetryStrategy-重连成功");
                        this.currentRetryCount = 0;
                        this.delay = 1L;
                        TcpClientInit.setGlobalChannel(channelFuture.channel());
                        if (consumer != null) {
                            consumer.accept(Boolean.TRUE);
                        }
                    }
                });
                channelFuture.sync();
            } catch (Exception e) {
                log.error("TcpClientHandler-重连失败,原因: {}", e.getMessage());
            }
        }, delay, TimeUnit.SECONDS);
        //传递给下一个处理器
        ctx.fireChannelInactive();
    }

    private long getDelay() {
        if (currentRetryCount != 0) {
            if (delay < retryMaxTime) {
                delay *= 2;
            }
            if (delay > retryMaxTime) {
                delay = retryMaxTime;
            }
        }
        return delay;
    }
}
