package com.angleyes.receivewatches.mqtt.messaging.dto;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * The type Base message.
 *
 * @param <T> the type parameter
 * <AUTHOR>
 * @version 1.0
 * @since 2023 /1/31 21:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class BaseMessage<T> {

    /**
     * Of base message.
     *
     * @param <T>    the type parameter
     * @param sender the sender
     * @param data   the data
     * @return the base message
     */
    public static <T> BaseMessage<T> of(String sender, T data) {
        return new BaseMessage<>(System.currentTimeMillis(), sender, data);
    }

    /**
     * 消息创建时间
     */
    private long time;
    /**
     * 发送者id
     */
    private String sender;
    /**
     * 消息携带数据
     */
    private T data;

}
