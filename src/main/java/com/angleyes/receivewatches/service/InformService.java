package com.angleyes.receivewatches.service;

import com.angleyes.receivewatches.entity.Inform;
import com.angleyes.receivewatches.vo.FiremenVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 通知
 */

public interface InformService extends IService<Inform> {


    String selectFiremenId(String deviceid);

    FiremenVo selectFiremenIdOrName(String deviceid);

    void saveInform(String deviceId ,String type ,String content);

    void saveInform(String deviceId,String organizationId, String type, String content ,String UploadDate);

    void findByOrganizationIdIn();




}
