package com.angleyes.receivewatches.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.angleyes.receivewatches.dto.Rest;
import com.angleyes.receivewatches.dto.watches.WatchesMqttInfo;
import com.angleyes.receivewatches.entity.*;
import com.angleyes.receivewatches.event.*;
import com.angleyes.receivewatches.exception.WatchesException;
import com.angleyes.receivewatches.service.*;
import com.angleyes.receivewatches.util.CoordinateTransformUtil;
import com.angleyes.receivewatches.util.TimeUtils;
import com.angleyes.receivewatches.vo.FiremenVo;
import com.angleyes.receivewatches.vo.WatchesVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 *
 */

@RequiredArgsConstructor
@RestController
@RequestMapping("api/watches")
@Transactional(rollbackFor = Exception.class)
public class WatchesController {

    private final FallDetectionService fallDetectionService;
    private final GpsWatchesService gpsWatchesService;
    private final HealthService healthService;
    private final HeartRateService heartRateService;
    private final LowVoltageService lowVoltageService;
    private final SleepService sleepService;
    private final SosWatchesService sosWatchesService;
    private final WatchesService watchesService;
    private final InformService informService;

    private final WatchesMqttInfo watchesMqttInfo;

    private final ApplicationEventPublisher applicationEventPublisher;

    public static final String SOS = "SOS告警";
    public static final String HEART_RATE = "心率预警";
    public static final String LOW_VOLTAGE = "低电告警";
    public static final String FALL_DETECTION = "跌倒告警";

    public static final String OFF_LINE = "下线";

    public static final String DEVICE_ID = "Device-Id";

    public static final String APP_VISION = "App_Vision";

    @Value("${app.download-dir}")
    private String downloadDir;


    /**
     * 手表获取 MQTT 连接信息
     *
     * @return
     */
    @GetMapping("MqttInfo")
    public WatchesMqttInfo getMqttInfo() {
        return watchesMqttInfo.copy();
    }

    /**
     * 手表获取GPS配置信息
     *
     * @return
     */
    @GetMapping("settings")
    public WatchesVo getWatchesSettings(HttpServletRequest request) {
        String deviceId = request.getHeader(DEVICE_ID);
        String appVision = request.getHeader(APP_VISION);
        return watchesService.selectWatchesSettings(deviceId, appVision);
    }

    /**
     * 手表更新包写死地址。后续根据需求调整
     *
     * @param versionCode
     * @return
     */
    @GetMapping("update")
    public Rest<String> upload(
            @RequestParam(value = "versionName", required = false) String versionName,
            @RequestParam("versionCode") int versionCode,
            HttpServletRequest request) throws IOException, ParserConfigurationException, SAXException {
        String deviceId = request.getHeader(DEVICE_ID);
        if (versionName != null) {
            watchesService.updateVersion(deviceId, versionName);
        }

        int newVersion = -1;
        String downloadUrl = null;

        // 实例化一个文档构建器工厂
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        // 通过文档构建器工厂获取一个文档构建器
        DocumentBuilder builder = factory.newDocumentBuilder();
        // 通过文档通过文档构建器构建一个文档实例
        try (FileInputStream is = new FileInputStream(new File(downloadDir, "app/update.xml"))) {
            Document document = builder.parse(is);
            // 获取XML文件根节点
            Element root = document.getDocumentElement();
            // 获得所有子节点
            NodeList childNodes = root.getChildNodes();
            for (int j = 0; j < childNodes.getLength(); j++) {
                // 遍历子节点
                Node childNode = childNodes.item(j);
                if (childNode.getNodeType() == Node.ELEMENT_NODE) {
                    Element childElement = (Element) childNode;
                    if ("versionCode".equals(childElement.getNodeName())) {
                        newVersion = Integer.parseInt(childElement.getFirstChild().getNodeValue());
                    } else if (("downloadUrl".equals(childElement.getNodeName()))) {
                        downloadUrl = childElement.getFirstChild().getNodeValue();
                    }
                }
            }
        }

        if (newVersion == -1 || versionCode >= newVersion || downloadUrl == null) {
            return Rest.error(-1, "已经是最新版本");
        }

        return Rest.success(downloadUrl);
    }


    /**
     * 定时查询把最后上线时间比当前时间小2分钟以上的手表下线
     */
    @Scheduled(cron = "0 0/2 * * * ?")
    @GetMapping("offLine")
    public void offLine() {
        //System.err.println("--------------------------" + new Date() + "--------------------------" );
        Date date = new Date();
        Date offsetDate = DateUtil.offsetMinute(date, -12);
        LambdaQueryWrapper<Watches> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Watches::isOnline, true)
                .le(Watches::getOnlineAt, offsetDate)
                .orderByAsc(Watches::getOnlineAt);
        List<Watches> list = watchesService.list(wrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            list.forEach(watches -> {
                watches.setOnline(false);
                watches.setOfflineAt(date);
                informService.saveInform(watches.getDeviceId(), watches.getOrganizationId(), OFF_LINE, OFF_LINE, DateUtil.formatDateTime(date));
            });
            watchesService.updateBatchById(list);
        }
    }

    /**
     * 健康自动监测
     * 心率、全天血氧、压力、步数、消耗
     *
     * @param health
     * @param request
     */
    @PostMapping("health")
    public void health(@RequestBody List<Health> health, HttpServletRequest request) {
        String deviceId = request.getHeader(DEVICE_ID);
        watchesService.updateOnline(deviceId);
        FiremenVo firemenVo = informService.selectFiremenIdOrName(deviceId);

        setDeviceIdAndUploadDateAll(health, deviceId);
        health.forEach(h -> h.setId(null));
        boolean saveBatch = healthService.saveBatch(health);
        if (saveBatch) {
            applicationEventPublisher.publishEvent(new HealthEvent(deviceId, firemenVo, health));
        }
    }

    /**
     * GPS
     *
     * @param gpsWatches
     * @param request
     */
    @PostMapping("gps")
    public void gps(@RequestBody List<GpsWatches> gpsWatches, HttpServletRequest request) {
        String deviceId = request.getHeader(DEVICE_ID);
        FiremenVo firemenVo = informService.selectFiremenIdOrName(deviceId);

        setDeviceIdAndUploadDateAll(gpsWatches, deviceId);
        gpsWatches.forEach(i -> {
            i.setId(null);
            double[] doubles = CoordinateTransformUtil.wgs84togcj02(Convert.toDouble(i.getLongitude()), Convert.toDouble(i.getLatitude()));
            i.setGdLng(NumberUtil.roundStr(doubles[0], 6));
            i.setGdLat(NumberUtil.roundStr(doubles[1], 6));
        });
        boolean saveBatch = gpsWatchesService.saveBatch(gpsWatches);
        if (saveBatch) {
            applicationEventPublisher.publishEvent(new GpsEvent(deviceId, firemenVo, gpsWatches));
        }
    }


    /**
     * 睡眠
     *
     * @param sleeps
     * @param request
     */
    @PostMapping("sleep")
    public void sleep(@RequestBody List<Sleep> sleeps, HttpServletRequest request) {
        String deviceId = request.getHeader(DEVICE_ID);
        if (StrUtil.isEmpty(deviceId)) {
            throw new WatchesException("Device-Id:不能为空！");
        }
        if (CollUtil.isNotEmpty(sleeps)) {
            String firemenId = informService.selectFiremenId(deviceId);
            sleeps.forEach(i -> {
                i.setId(null);
                i.setDeviceId(deviceId);
                i.setFiremenId(firemenId);
                //入眠时间
                i.setFallAsleepDate(TimeUtils.stem(i.getFallAsleepTime()));
                //出眠时间
                i.setOutOfSleepDate(TimeUtils.stem(i.getOutOfSleepTime()));
                i.setUploadDate(TimeUtils.stem(i.getTimestamp()));
            });
        }
        sleepService.saveBatch(sleeps);
    }

    /**
     * SOS告警
     *
     * @param sos
     * @param request
     */
    @PostMapping("sos")
    public void sos(@RequestBody List<SosWatches> sos, HttpServletRequest request) {
        String deviceId = request.getHeader(DEVICE_ID);
        setDeviceIdAndUploadDateAll(sos, deviceId);
        sos.forEach(s -> s.setId(null));
        boolean saveBatch = sosWatchesService.saveBatch(sos);
        if (saveBatch) {
            informService.saveInform(deviceId, "SOS", SOS);
            applicationEventPublisher.publishEvent(new SosEvent(sos, deviceId));
        }


    }


    /**
     * 心率预警
     *
     * @param heartRates
     * @param request
     */
    @PostMapping("heartRate")
    public void heartRate(@RequestBody List<HeartRate> heartRates, HttpServletRequest request) {
        String deviceId = request.getHeader(DEVICE_ID);
        setDeviceIdAndUploadDateAll(heartRates, deviceId);
        heartRates.forEach(s -> s.setId(null));
        boolean saveBatch = heartRateService.saveBatch(heartRates);
        if (saveBatch) {
            informService.saveInform(deviceId, "心率", HEART_RATE);
            applicationEventPublisher.publishEvent(new HearRateEvent(heartRates, deviceId));
        }

    }


    /**
     * 低电告警
     *
     * @param lowVoltages
     * @param request
     */
    @PostMapping("lowVoltage")
    public void lowVoltage(@RequestBody List<LowVoltage> lowVoltages, HttpServletRequest request) {
        String deviceId = request.getHeader(DEVICE_ID);
        setDeviceIdAndUploadDateAll(lowVoltages, deviceId);
        lowVoltages.forEach(l -> l.setId(null));
        boolean saveBatch = lowVoltageService.saveBatch(lowVoltages);
        if (saveBatch) {
            informService.saveInform(deviceId, "低电", LOW_VOLTAGE);
            applicationEventPublisher.publishEvent(new LowVoltageEvent(lowVoltages, deviceId));
        }


    }


    /**
     * 跌倒检测
     *
     * @param fallDetections
     * @param request
     */
    @PostMapping("fallDetection")
    public void fallDetection(@RequestBody List<FallDetection> fallDetections, HttpServletRequest request) {
        String deviceId = request.getHeader(DEVICE_ID);
        setDeviceIdAndUploadDateAll(fallDetections, deviceId);
        fallDetections.forEach(f -> f.setId(null));
        boolean saveBatch = fallDetectionService.saveBatch(fallDetections);
        if (saveBatch) {
            informService.saveInform(deviceId, "跌倒", FALL_DETECTION);
            applicationEventPublisher.publishEvent(new FallDateEvent(fallDetections, deviceId));
        }
    }


    /**
     * 批量更新
     * 根据unix时间戳获取时间
     *
     * @param baseEntities
     * @param deviceId
     */
    private void setDeviceIdAndUploadDateAll(List<? extends BaseEntity> baseEntities, String deviceId) {
        if (StrUtil.isEmpty(deviceId)) {
            throw new WatchesException("Device-Id:不能为空！");
        }
        String firemenId = informService.selectFiremenId(deviceId);
        if (CollUtil.isNotEmpty(baseEntities)) {
            baseEntities.forEach(i -> {
                i.setDeviceId(deviceId);
                i.setFiremenId(firemenId);
                i.setUploadDate(TimeUtils.stem(i.getTimestamp()));
            });
        }
    }


    /**
     * 单条更新
     * 根据unix时间戳获取时间
     *
     * @param baseEntities
     * @param deviceId
     */
    private void setDeviceIdAndUploadDate(BaseEntity baseEntities, String deviceId) {
        if (StrUtil.isEmpty(deviceId)) {
            throw new WatchesException("Device-Id:不能为空！");
        }
        String firemenId = informService.selectFiremenId(deviceId);
        baseEntities.setDeviceId(deviceId);
        baseEntities.setFiremenId(firemenId);
        baseEntities.setUploadDate(TimeUtils.stem(baseEntities.getTimestamp()));

    }


}
