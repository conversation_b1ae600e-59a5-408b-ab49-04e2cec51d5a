package com.angleyes.smartwatches.data.watches.repository;

import com.angleyes.smartwatches.admin.tenant.query.WatchesQuery;
import com.angleyes.smartwatches.data.common.repository.CommonRepository;
import com.angleyes.smartwatches.data.watches.domain.LowVoltage;
import com.angleyes.smartwatches.data.watches.projections.DaysTotalProjections;
import com.angleyes.smartwatches.data.watches.projections.LowVoltageProjections;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

/**
 * 低电提醒
 *
 * <AUTHOR> ChenYe
 * @date 2023/3/9  16:47
 */
public interface LowVoltageRepository extends CommonRepository<LowVoltage> {

    /**
     * 近7日每日告警统计
     * 低电提醒
     *
     * @return
     */
    @Query(value = " SELECT DATE_FORMAT( f.upload_date, '%Y-%m-%d' ) days,count( 1 /*DISTINCT f.device_id*/ ) total " +
            " FROM low_voltage f inner join watches w on f.device_id = w.device_id " +
            " WHERE date_sub( curdate(), INTERVAL 7 DAY ) < date( f.upload_date )  " +
            " and w.organization_id in ( ?1 ) " +
            " GROUP BY DATE_FORMAT( f.upload_date, '%Y-%m-%d' ) ", nativeQuery = true)
    List<DaysTotalProjections> findLowVoltageDaysTotal(List<Long> ids);


    /**
     * 当日单个手表低电提醒统计
     *
     * @param deviceId
     * @param beginOfDay
     * @return
     */
    List<LowVoltage> findLowVoltagesByDeviceIdAndUploadDateAfter(String deviceId, Date beginOfDay);

    /**
     * 机构下手表当日低电提醒统计
     *
     * @param ids
     * @return
     */
    @Query("select count(lv) from LowVoltage lv left join Watches w on w.deviceId = lv.deviceId where w.organization.id in (?1) and lv.uploadDate >= current_date")
    long countTodayAlarm(List<Long> ids);

    @Query("select count(lv) from LowVoltage lv left join Watches w on w.deviceId = lv.deviceId where w.organization.id in (?1)")
    long countTotalAlarms(List<Long> orgIds);

    @Query("select count(lv) from LowVoltage lv left join Watches w on w.deviceId = lv.deviceId where w.organization.id in (?1) and lv.resolved is false")
    long countUntreatedAlarm(List<Long> orgIds);

    /**
     * 机构下低电提醒记录
     *
     * @param pageable
     * @param query
     * @return
     */
    @Query(value = " SELECT " +
            "  l.id,l.device_id deviceId,l.upload_date uploadDate,l.created_date createdDate,l.latitude,l.longitude,l.battery,l.resolved, " +
            " f.code,f.user_name uname,o.name oname,d.name dname " +
            " FROM " +
            " low_voltage l " +
            " INNER JOIN watches w ON w.device_id = l.device_id " +
            " LEFT JOIN firemen f ON l.firemen_id = f.id " +
            " LEFT JOIN organization o ON w.organization_id = o.id " +
            " LEFT JOIN department d ON f.department_id = d.id" +
            " where w.organization_id in (:#{#query.ids}) " +
            " and if(:#{#query.startTime} is not null && :#{#query.startTime} != '', l.upload_date >= :#{#query.startTime} , 1=1) " +
            " and if(:#{#query.endTime} is not null && :#{#query.endTime} != '', l.upload_date <= :#{#query.endTime} , 1=1) " +
            " and if(:#{#query.deviceId} is not null && :#{#query.deviceId} != '', w.device_id like CONCAT( '%',:#{#query.deviceId},'%') , 1=1) " +
            " and if(:#{#query.resolved} is not null, l.resolved = :#{#query.resolved}, 1=1) " +
            " order by l.upload_date desc ", nativeQuery = true)
    Page<LowVoltageProjections> pagedLowVoltage(Pageable pageable, @Param("query") WatchesQuery query);


    /**
     * 机构下低电提醒记录
     *
     * @param query
     * @return
     */
    @Query(value = " SELECT " +
            "  l.id,l.device_id deviceId,l.upload_date uploadDate,l.created_date createdDate,l.latitude,l.longitude,l.battery,l.resolved, " +
            " f.code,f.user_name uname,o.name oname,d.name dname " +
            " FROM " +
            " low_voltage l " +
            " INNER JOIN watches w ON w.device_id = l.device_id " +
            " LEFT JOIN firemen f ON l.firemen_id = f.id " +
            " LEFT JOIN organization o ON w.organization_id = o.id " +
            " LEFT JOIN department d ON f.department_id = d.id" +
            " where w.organization_id in (:#{#query.ids}) " +
            " and if(:#{#query.startTime} is not null && :#{#query.startTime} != '', l.upload_date >= :#{#query.startTime} , 1=1) " +
            " and if(:#{#query.endTime} is not null && :#{#query.endTime} != '', l.upload_date <= :#{#query.endTime} , 1=1) " +
            " and if(:#{#query.deviceId} is not null && :#{#query.deviceId} != '', w.device_id like CONCAT( '%',:#{#query.deviceId},'%') , 1=1) " +
            " and if(:#{#query.resolved} is not null, l.resolved = :#{#query.resolved}, 1=1) " +
            " order by l.upload_date desc ", nativeQuery = true)
    List<LowVoltageProjections> listLowVoltage(@Param("query") WatchesQuery query);

    default void solveAlarms(List<Long> ids, String solveResult) {
        findByIdIn(ids)
                .forEach(alarm -> {
                    alarm.setResolved(true);
                    alarm.setSolveResult(solveResult);
                });
    }
}
