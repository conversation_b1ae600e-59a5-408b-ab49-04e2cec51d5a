package com.angleyes.smartwatches.mqtt.config;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import static org.eclipse.paho.client.mqttv3.MqttConnectOptions.CLEAN_SESSION_DEFAULT;
import static org.eclipse.paho.client.mqttv3.MqttConnectOptions.MAX_INFLIGHT_DEFAULT;

/**
 * The type Mqtt properties.
 */
@Data
@ConfigurationProperties(prefix = "mqtt")
@Configuration
public class MqttProperties {

    public enum PersistenceType {
        MEMORY,

        FILE
    }

    private String[] serverUris;

    private String clientId;

    private int maxInflight = MAX_INFLIGHT_DEFAULT;

    private String userName;

    private String password;

    private boolean automaticReconnect;

    private boolean cleanSession = CLEAN_SESSION_DEFAULT;

    private PersistenceType persistenceType = PersistenceType.MEMORY;

    private int defaultQos;

    private String baseTopic;

    public String getWatchesTopic(String deviceId) {
        return StrUtil.format("watches/{}", deviceId);
    }

    public String getTcpTopic() {
        return StrUtil.format("{}/tcp", baseTopic);
    }


}
