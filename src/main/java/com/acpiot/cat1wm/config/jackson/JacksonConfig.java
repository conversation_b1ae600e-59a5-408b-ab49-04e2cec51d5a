package com.acpiot.cat1wm.config.jackson;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.hibernate6.Hibernate6Module;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import jakarta.persistence.Persistence;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_FORMATTER;
import static cn.hutool.core.date.DatePattern.NORM_DATE_FORMATTER;

/**
 * Created by moxin on 2020-12-29-0029
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Configuration
public class JacksonConfig {

    /**
     * Jackson 2 object mapper builder customizer jackson 2 object mapper builder customizer.
     *
     * @return the jackson 2 object mapper builder customizer
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {
        return builder -> {
            builder.failOnUnknownProperties(false);
            builder.featuresToDisable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

            // deserializers
            builder.deserializers(
                    new LocalDateDeserializer(NORM_DATE_FORMATTER),
                    new LocalDateTimeDeserializer(NORM_DATETIME_FORMATTER)
            );

            // serializers
            builder.serializers(
                    new LocalDateSerializer(NORM_DATE_FORMATTER),
                    new LocalDateTimeSerializer(NORM_DATETIME_FORMATTER)
            );

            builder.serializationInclusion(JsonInclude.Value.construct(
                    JsonInclude.Include.CUSTOM, JsonInclude.Include.USE_DEFAULTS, LazyFieldsFilter.class, Void.class));

            Hibernate6Module hm = new Hibernate6Module();
            // 禁用(表示要忽略@Transient字段属性,默认为true,设置为false禁用)
            hm.disable(Hibernate6Module.Feature.USE_TRANSIENT_ANNOTATION);
            builder.modulesToInstall(
                    hm
            );
        };
    }

    /**
     * The type Lazy fields filter.
     *
     * <AUTHOR>
     */
    @SuppressWarnings("com.haulmont.jpb.EqualsDoesntCheckParameterClass")
    public static class LazyFieldsFilter {
        @Override
        public boolean equals(Object obj) {
            return !Persistence.getPersistenceUtil().isLoaded(obj);
        }
    }

}
