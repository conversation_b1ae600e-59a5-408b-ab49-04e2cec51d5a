package com.acpiot.cat1wm.data.entity;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import pers.mx.fileserver.FileService;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 升级计划.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023 /1/28 09:39
 */
@Getter
@Setter
@NoArgsConstructor
@Entity
@DynamicInsert
@DynamicUpdate
public class UpgradePlan extends AbstractAuditable {

    private static final String PLAN_NAME_PREFIX_SEPARATOR = "__";
    private static final String PLAN_NAME_REGEX = ".*" + PLAN_NAME_PREFIX_SEPARATOR + "\\d+$";

    /**
     * 计划名称
     */
    @Column(nullable = false, unique = true, length = 64)
    private String planName;

    /**
     * 升级文件
     */
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    private UpgradeFile upgradeFile;

    /**
     * 升级任务序号
     */
    @Column(nullable = false)
    private int taskNo;

    /**
     * 每包字节长度
     */
    @Column(nullable = false)
    private int blockSize = 512;

    /**
     * 升级开始时间
     */
    @JsonFormat(pattern = "HH:mm")
    @Column(nullable = false)
    private LocalTime startTime = LocalTime.of(0, 0);

    /**
     * 升级结束时间
     */
    @JsonFormat(pattern = "HH:mm")
    @Column(nullable = false)
    private LocalTime endTime = LocalTime.of(23, 59);

    /**
     * 升级失效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Column
    private LocalDate endDate;

    public byte[] readFileBytes(FileService fileService) throws IOException {
        Path path = fileService.resolvePath(upgradeFile.getStoragePath());
        return Files.readAllBytes(path);
    }

    @JsonIgnore
    public String getPlanNamePrefix() {
        String prefix = planName;
        if (prefix.matches(PLAN_NAME_REGEX)) {
            prefix = StrUtil.subBefore(prefix, PLAN_NAME_PREFIX_SEPARATOR, true);
        }
        return prefix;
    }

    @JsonIgnore
    public Integer getPlanIdx() {
        if (planName.matches(PLAN_NAME_REGEX)) {
            String suffix = StrUtil.subAfter(planName, PLAN_NAME_PREFIX_SEPARATOR, true);
            return Integer.parseInt(suffix);
        }
        return null;
    }

    public static String buildPlanName(String prefix, int idx) {
        return prefix + PLAN_NAME_PREFIX_SEPARATOR + idx;
    }

}
