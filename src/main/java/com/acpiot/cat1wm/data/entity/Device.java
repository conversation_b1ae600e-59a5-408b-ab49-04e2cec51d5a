package com.acpiot.cat1wm.data.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Enumerated;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

import java.time.LocalDateTime;

/**
 * Created by moxin on 2023/8/21T9:28
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@Entity
public class Device extends AbstractAuditable {

    /**
     * 归属客户
     */
    @TenantId
    @Column(nullable = false, length = 24)
    private String tenantId;

    /**
     * IMEI
     */
    @Column(nullable = false, unique = true, length = 15, columnDefinition = "char(15)")
    private String imei;

    /**
     * 通讯方式
     */
    @Enumerated
    @Column(nullable = false)
    private ChannelType channelType = ChannelType.UDP;

    /**
     * 最后在线时间
     */
    @Column
    private LocalDateTime lastOnlineAt;

    /**
     * 备注
     */
    @Column
    private String remark;

    /**
     * 在线状态
     */
    @Column(nullable = false)
    private boolean online;

}
