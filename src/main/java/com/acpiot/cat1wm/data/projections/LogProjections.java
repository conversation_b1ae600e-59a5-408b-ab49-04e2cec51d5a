package com.acpiot.cat1wm.data.projections;

/**
 * Created by moxin on 2019-10-25-0025
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface LogProjections {

    interface Log extends AbstractAuditableProjection {

        String getDescription();

        String getUserAgent();

        String getIp();

        String getAddress();

        String getMethodName();

        int getCostTime();

        String getCreatedBy();

    }

    interface Args {

        String getArgs();

    }

    interface Exception {

        String getExceptionMsg();

    }

}
