package com.acpiot.cat1wm.protocol.util;

/**
 * Created by moxin on 2022-03-09-0009
 *
 * <AUTHOR> Email: <EMAIL>
 */
public class Crc16Util {

    private static final short[] CRC16_TABLE = {
            0x0000, 0x1189, 0x2312, 0x329B, 0x4624, 0x57AD, 0x6536, 0x74BF,
            (short) 0x8C48, (short) 0x9DC1, (short) 0xAF5A, (short) 0xBED3, (short) 0xCA6C, (short) 0xDBE5, (short) 0xE97E, (short) 0xF8F7,
            0x1081, 0x0108, 0x3393, 0x221A, 0x56A5, 0x472C, 0x75B7, 0x643E,
            (short) 0x9CC9, (short) 0x8D40, (short) 0xBFDB, (short) 0xAE52, (short) 0xDAED, (short) 0xCB64, (short) 0xF9FF, (short) 0xE876,
            0x2102, 0x308B, 0x0210, 0x1399, 0x6726, 0x76AF, 0x4434, 0x55BD,
            (short) 0xAD4A, (short) 0xBCC3, (short) 0x8E58, (short) 0x9FD1, (short) 0xEB6E, (short) 0xFAE7, (short) 0xC87C, (short) 0xD9F5,
            0x3183, 0x200A, 0x1291, 0x0318, 0x77A7, 0x662E, 0x54B5, 0x453C,
            (short) 0xBDCB, (short) 0xAC42, (short) 0x9ED9, (short) 0x8F50, (short) 0xFBEF, (short) 0xEA66, (short) 0xD8FD, (short) 0xC974,
            0x4204, 0x538D, 0x6116, 0x709F, 0x0420, 0x15A9, 0x2732, 0x36BB,
            (short) 0xCE4C, (short) 0xDFC5, (short) 0xED5E, (short) 0xFCD7, (short) 0x8868, (short) 0x99E1, (short) 0xAB7A, (short) 0xBAF3,
            0x5285, 0x430C, 0x7197, 0x601E, 0x14A1, 0x0528, 0x37B3, 0x263A,
            (short) 0xDECD, (short) 0xCF44, (short) 0xFDDF, (short) 0xEC56, (short) 0x98E9, (short) 0x8960, (short) 0xBBFB, (short) 0xAA72,
            0x6306, 0x728F, 0x4014, 0x519D, 0x2522, 0x34AB, 0x0630, 0x17B9,
            (short) 0xEF4E, (short) 0xFEC7, (short) 0xCC5C, (short) 0xDDD5, (short) 0xA96A, (short) 0xB8E3, (short) 0x8A78, (short) 0x9BF1,
            0x7387, 0x620E, 0x5095, 0x411C, 0x35A3, 0x242A, 0x16B1, 0x0738,
            (short) 0xFFCF, (short) 0xEE46, (short) 0xDCDD, (short) 0xCD54, (short) 0xB9EB, (short) 0xA862, (short) 0x9AF9, (short) 0x8B70,
            (short) 0x8408, (short) 0x9581, (short) 0xA71A, (short) 0xB693, (short) 0xC22C, (short) 0xD3A5, (short) 0xE13E, (short) 0xF0B7,
            0x0840, 0x19C9, 0x2B52, 0x3ADB, 0x4E64, 0x5FED, 0x6D76, 0x7CFF,
            (short) 0x9489, (short) 0x8500, (short) 0xB79B, (short) 0xA612, (short) 0xD2AD, (short) 0xC324, (short) 0xF1BF, (short) 0xE036,
            0x18C1, 0x0948, 0x3BD3, 0x2A5A, 0x5EE5, 0x4F6C, 0x7DF7, 0x6C7E,
            (short) 0xA50A, (short) 0xB483, (short) 0x8618, (short) 0x9791, (short) 0xE32E, (short) 0xF2A7, (short) 0xC03C, (short) 0xD1B5,
            0x2942, 0x38CB, 0x0A50, 0x1BD9, 0x6F66, 0x7EEF, 0x4C74, 0x5DFD,
            (short) 0xB58B, (short) 0xA402, (short) 0x9699, (short) 0x8710, (short) 0xF3AF, (short) 0xE226, (short) 0xD0BD, (short) 0xC134,
            0x39C3, 0x284A, 0x1AD1, 0x0B58, 0x7FE7, 0x6E6E, 0x5CF5, 0x4D7C,
            (short) 0xC60C, (short) 0xD785, (short) 0xE51E, (short) 0xF497, (short) 0x8028, (short) 0x91A1, (short) 0xA33A, (short) 0xB2B3,
            0x4A44, 0x5BCD, 0x6956, 0x78DF, 0x0C60, 0x1DE9, 0x2F72, 0x3EFB,
            (short) 0xD68D, (short) 0xC704, (short) 0xF59F, (short) 0xE416, (short) 0x90A9, (short) 0x8120, (short) 0xB3BB, (short) 0xA232,
            0x5AC5, 0x4B4C, 0x79D7, 0x685E, 0x1CE1, 0x0D68, 0x3FF3, 0x2E7A,
            (short) 0xE70E, (short) 0xF687, (short) 0xC41C, (short) 0xD595, (short) 0xA12A, (short) 0xB0A3, (short) 0x8238, (short) 0x93B1,
            0x6B46, 0x7ACF, 0x4854, 0x59DD, 0x2D62, 0x3CEB, 0x0E70, 0x1FF9,
            (short) 0xF78F, (short) 0xE606, (short) 0xD49D, (short) 0xC514, (short) 0xB1AB, (short) 0xA022, (short) 0x92B9, (short) 0x8330,
            0x7BC7, 0x6A4E, 0x58D5, 0x495C, 0x3DE3, 0x2C6A, 0x1EF1, 0x0F78
    };

    public static int crc16(byte[] bytes, int len) {
        int crc16 = 0xFFFF;
        for (int i = 0; i < len; i++) {
            crc16 = ((crc16 >> 8) ^ CRC16_TABLE[(crc16 ^ bytes[i]) & 0xFF]) & 0xFFFF;
        }
        crc16 ^= 0xFFFF;
        return crc16;
    }

    public static int crc16(byte[] bytes) {
        return crc16(bytes, bytes.length);
    }

}
