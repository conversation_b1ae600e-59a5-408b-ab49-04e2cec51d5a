package com.acpiot.cat1wm.protocol.command.impl;

import com.acpiot.cat1wm.protocol.command.BaseCommand;

import static com.acpiot.cat1wm.protocol.ProtocolConstant.CMD_READ_UPGRADE_FILE_INFO;

/**
 * 查询升级文件信息命令
 *
 * <AUTHOR>
 * @date 2023/7/25 14:45
 */
public enum C24Command implements BaseCommand {

    INSTANCE;

    @Override
    public byte getCmd() {
        return CMD_READ_UPGRADE_FILE_INFO;
    }

    @Override
    public String getDesc() {
        return "查询升级文件信息";
    }

}
