package com.acpiot.cat1wm.protocol.command.impl;

import com.acpiot.cat1wm.protocol.command.BaseCommand;
import com.acpiot.cat1wm.protocol.command.dto.param.C65Param;
import lombok.RequiredArgsConstructor;

import static com.acpiot.cat1wm.protocol.ProtocolConstant.CMD_RECHARGE;

/**
 * 充值
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class C65Command implements BaseCommand {

    private final C65Param param;

    @Override
    public byte getCmd() {
        return CMD_RECHARGE;
    }

    @Override
    public String getDesc() {
        return "充值";
    }

    @Override
    public String getParams() {
        return param.toString();
    }

    @Override
    public byte[] getData() {
        return param.toBytes();
    }
}
