package com.acpiot.cat1wm.protocol.command.upgrade.dto;

import cn.hutool.core.lang.Assert;
import com.acpiot.cat1wm.protocol.util.ByteBufWrapper;
import lombok.Data;

/**
 * 升级信息
 * Created by moxin on 2022-03-08-0008
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Data
public class UpgradeInfo {

    /**
     * 升级序号
     */
    private int seq;

    /**
     * 升级文件类型
     */
    private int fileType;

    /**
     * 升级块大小
     */
    private int blockSize;

    /**
     * 升级块总数
     */
    private int blockTotal;

    /**
     * 升级文件大小
     */
    private int fileSize;

    /**
     * 升级文件校验
     */
    private int fileCrc;

    public int calcBlockTotal() {
        Assert.isTrue(blockSize > 0);
        int total = fileSize / blockSize;
        if ((fileSize % blockSize) != 0) {
            total++;
        }
        return total;
    }

    public byte[] toBytes() {
        ByteBufWrapper buf = ByteBufWrapper.buffer(17);
        buf.writeIntLE(seq);
        buf.writeByte(fileType);
        buf.writeShortLE(blockSize);
        buf.writeShortLE(blockTotal);
        buf.writeIntLE(fileSize);
        buf.writeIntLE(fileCrc);
        return buf.array();
    }

}
