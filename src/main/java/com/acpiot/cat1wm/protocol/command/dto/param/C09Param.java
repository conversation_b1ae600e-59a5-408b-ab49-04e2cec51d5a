package com.acpiot.cat1wm.protocol.command.dto.param;

import com.acpiot.cat1wm.protocol.util.ByteBufWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import org.hibernate.validator.constraints.Range;

/**
 * 读曲线数据 命令参数
 * Created by moxin on 2023/8/17T17:26
 *
 * @param type      曲线种类 0:正反向曲线 1:正向曲线 2:反向曲线
 * @param density   曲线密度 1=96 点 2=48 点 3=24 点
 * @param curveTime 曲线时间 年月日
 * <AUTHOR>
 */
public record C09Param(
        @Schema(description = "曲线种类 1:正向脉冲数曲线 2:反向脉冲数曲线", requiredMode = Schema.RequiredMode.REQUIRED)
        @Range(min = 1, max = 2, message = "曲线种类 范围：1-2")
        int type,

        @Schema(description = "曲线密度 1=96 点 2=48 点 3=24 点", requiredMode = Schema.RequiredMode.REQUIRED)
        @Range(min = 1, max = 3, message = "曲线种类 范围：1-3")
        int density,

        @Schema(description = "曲线日期，格式：yyMMdd", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "曲线日期不能为空")
        @Pattern(regexp = "\\d{6}", message = "曲线日期格式不正确")
        String curveTime
) {

    /**
     * To bytes byte [ ].
     *
     * @return the byte [ ]
     */
    public byte[] toBytes() {
        ByteBufWrapper buf = ByteBufWrapper.buffer(6);
        buf.writeByte(type);
        buf.writeByte(density);
        buf.writeHex(curveTime + "00");
        return buf.array();
    }

}
