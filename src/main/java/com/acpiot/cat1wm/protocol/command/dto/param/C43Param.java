package com.acpiot.cat1wm.protocol.command.dto.param;

import com.acpiot.cat1wm.protocol.util.ByteBufWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Range;

/**
 * 设差值校表功能参数
 * Created by moxin on 2024/12/20T17:20
 *
 * <AUTHOR>
 */
public record C43Param(
        @Schema(description = "校表方向 0 加 1 减", requiredMode = Schema.RequiredMode.REQUIRED)
        @Range(min = 0, max = 1, message = "校表方向 范围：0-1")
        int direction,

        @Schema(description = "脉冲数差值", requiredMode = Schema.RequiredMode.REQUIRED)
        @Range(min = 0, max = Integer.MAX_VALUE, message = "脉冲数差值 范围：0-2147483647")
        int pulseDiff
) {

    /**
     * To bytes byte [ ].
     *
     * @return the byte [ ]
     */
    public byte[] toBytes() {
        ByteBufWrapper buf = ByteBufWrapper.buffer(8);
        buf.writeByte(direction);
        buf.writeIntLE(pulseDiff);
        return buf.array();
    }

}
