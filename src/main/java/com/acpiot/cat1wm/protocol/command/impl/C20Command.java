package com.acpiot.cat1wm.protocol.command.impl;

import com.acpiot.cat1wm.protocol.command.BaseCommand;
import com.acpiot.cat1wm.protocol.command.upgrade.dto.UpgradeInfo;
import lombok.RequiredArgsConstructor;

import static com.acpiot.cat1wm.protocol.ProtocolConstant.CMD_START_UPGRADE;

/**
 * 开始升级命令.
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class C20Command implements BaseCommand {

    private final UpgradeInfo upgradeInfo;

    @Override
    public byte getCmd() {
        return CMD_START_UPGRADE;
    }

    @Override
    public String getDesc() {
        return "开始升级";
    }

    @Override
    public byte[] getData() {
        return upgradeInfo.toBytes();
    }

}
