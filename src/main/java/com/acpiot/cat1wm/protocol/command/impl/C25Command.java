package com.acpiot.cat1wm.protocol.command.impl;

import com.acpiot.cat1wm.protocol.command.BaseCommand;

import static com.acpiot.cat1wm.protocol.ProtocolConstant.CMD_RESET_CAT1;

/**
 * 复位 CAT1
 *
 * <AUTHOR>
 * @date 2023/7/25 14:45
 */
public enum C25Command implements BaseCommand {

    INSTANCE;

    @Override
    public byte getCmd() {
        return CMD_RESET_CAT1;
    }

    @Override
    public String getDesc() {
        return "复位模组";
    }

    @Override
    public boolean isWaitResponse() {
        return false;
    }

}
