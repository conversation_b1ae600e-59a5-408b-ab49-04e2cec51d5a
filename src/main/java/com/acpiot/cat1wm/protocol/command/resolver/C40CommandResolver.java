package com.acpiot.cat1wm.protocol.command.resolver;

import com.acpiot.cat1wm.protocol.command.dto.data.C40Data;
import com.acpiot.cat1wm.protocol.util.ByteBufWrapper;
import org.springframework.stereotype.Component;

/**
 * 读 MQTT 参数信息
 * Created by moxin on 2024/12/23T14:23
 *
 * <AUTHOR>
 */
@Component
public class C40CommandResolver implements CommandResolver<C40Data> {
    @Override
    public C40Data resolver(ByteBufWrapper buf) {
        return new C40Data(buf.readAsciiStr(32), buf.readAsciiStr(50), buf.readAsciiStr(50), buf.readAsciiStr(50), buf.readUnsignedShortLE());
    }
}
