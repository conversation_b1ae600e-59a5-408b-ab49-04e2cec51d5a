package com.acpiot.cat1wm.protocol.command.impl;

import com.acpiot.cat1wm.protocol.command.BaseCommand;

import static com.acpiot.cat1wm.protocol.ProtocolConstant.CMD_READ_UPGRADE_CHANNEL;

/**
 * 查询升级通道命令
 *
 * <AUTHOR>
 * @date 2023/7/25 15:20
 */
public enum C52Command implements BaseCommand {

    INSTANCE;

    @Override
    public byte getCmd() {
        return CMD_READ_UPGRADE_CHANNEL;
    }

    @Override
    public String getDesc() {
        return "查询升级通道";
    }
}
