package com.acpiot.cat1wm.protocol.command.resolver;

import cn.hutool.core.util.StrUtil;
import com.acpiot.cat1wm.protocol.command.dto.data.ConfirmData;
import com.acpiot.cat1wm.protocol.util.ByteBufWrapper;
import org.springframework.stereotype.Component;

/**
 * 确认应答解析.
 *
 * <AUTHOR>
 * @date 2023 /7/24 10:44
 */
@Component
public class ConfirmCommandResolver implements CommandResolver<ConfirmData> {

    public static final String BEAN_NAME = StrUtil.lowerFirst(ConfirmCommandResolver.class.getSimpleName());

    @Override
    public ConfirmData resolver(ByteBufWrapper buf) {
        return new ConfirmData(Byte.toUnsignedInt(buf.readByte()));
    }

}
