package com.acpiot.cat1wm.protocol.command.impl;

import com.acpiot.cat1wm.protocol.command.BaseCommand;
import com.acpiot.cat1wm.protocol.command.dto.param.C17Param;
import lombok.RequiredArgsConstructor;

import static com.acpiot.cat1wm.protocol.ProtocolConstant.CMD_SET_WM_COUNT;

/**
 * 设水表计量参数.
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class C17Command implements BaseCommand {

    private final C17Param param;

    @Override
    public byte getCmd() {
        return CMD_SET_WM_COUNT;
    }

    @Override
    public String getDesc() {
        return "设水表计量参数";
    }

    @Override
    public String getParams() {
        return param.toString();
    }

    @Override
    public byte[] getData() {
        return param.toBytes();
    }

}
