package com.acpiot.cat1wm.protocol.command.upgrade;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import com.acpiot.cat1wm.admin.event.UpgradeSuccessEvent;
import com.acpiot.cat1wm.admin.event.UpgradeTaskDelEvent;
import com.acpiot.cat1wm.admin.service.UpgradeService;
import com.acpiot.cat1wm.data.entity.UpgradePlan;
import com.acpiot.cat1wm.data.entity.UpgradeTask;
import com.acpiot.cat1wm.protocol.PortType;
import com.acpiot.cat1wm.protocol.command.CmdExecutorCache;
import com.acpiot.cat1wm.protocol.command.event.CancelUpgradeEvent;
import com.acpiot.cat1wm.protocol.command.event.StartUpgradingEvent;
import com.acpiot.cat1wm.protocol.command.upgrade.dto.UpgradeData;
import com.acpiot.cat1wm.protocol.server.channel.ChannelManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;
import pers.mx.fileserver.FileService;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by moxin on 2023/7/27T17:59
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class UpgradeManager {

    /**
     * 最大升级数量
     */
    public static final int MAX_UPGRADE_NUM = 500;

    private final FileService fileService;
    private final UpgradeService upgradeService;
    private final CmdExecutorCache cmdExecutorCache;
    private final ChannelManager channelManager;
    private final ThreadPoolTaskScheduler upgradeScheduler;

    private final Map<UpgradePlan, UpgradeData> upgradeCache = new ConcurrentHashMap<>();

    /**
     * KEY: imei
     */
    private final TimedCache<String, UpgradeExecutor> upgradeExecutorMap = CacheUtil.newTimedCache(30 * 60 * 1000L, 50);

    /**
     * Onn start upgrading event.
     *
     * @param evt the evt
     */
    @EventListener
    public void onnStartUpgradingEvent(StartUpgradingEvent evt) {
        UpgradeTask upgradeTask = evt.upgradeTask();
        startUpgrade(upgradeTask);
    }

    /**
     * Onn upgrade task del event.
     *
     * @param evt the evt
     */
    @TransactionalEventListener
    public void onnUpgradeTaskDelEvent(UpgradeTaskDelEvent evt) {
        evt.upgradeTasks()
                .forEach(upgradeTask -> {
                    String imei = upgradeTask.getImei();
                    log.info("imei={}: 升级任务删除", imei);
                    removeUpgradeTask(imei);
                });
    }

    private void removeUpgradeTask(String imei) {
        UpgradeExecutor upgradeExecutor = upgradeExecutorMap.get(imei);
        if (upgradeExecutor != null) {
            upgradeExecutor.setRemoved(true);
            upgradeExecutorMap.remove(imei);
        }
    }

    @EventListener
    public void onCancelUpgradeEvent(CancelUpgradeEvent evt) {
        log.info("imei={}: 取消升级", evt.imei());
        removeUpgradeTask(evt.imei());
    }

    @TransactionalEventListener
    public void onUpgradeSuccessEvent(UpgradeSuccessEvent evt) {
        log.info("imei={}: 升级成功", evt.imei());
        upgradeExecutorMap.remove(evt.imei());
    }

    /**
     * 指定IMEI设备是否正在升级.
     *
     * @param imei the imei
     * @return the boolean
     */
    public boolean isUpgrading(String imei) {
        return upgradeExecutorMap.containsKey(imei);
    }

    /**
     * 是否需要排队升级
     *
     * @return boolean
     */
    public boolean isNeedWaitQueue() {
        return upgradeExecutorMap.size() >= MAX_UPGRADE_NUM;
    }

    private synchronized void startUpgrade(UpgradeTask upgradeTask) {
        UpgradePlan upgradePlan = upgradeTask.getUpgradePlan();
        UpgradeData upgradeData = upgradeCache.computeIfAbsent(upgradePlan, plan -> UpgradeData.of(fileService, plan));

        String imei = upgradeTask.getImei();
        if (isUpgrading(imei)) {
            log.warn("imei={}: 正在升级中", imei);
            return;
        }
        if (isNeedWaitQueue()) {
            log.info("imei={}: 当前升级队列已满", imei);
            return;
        }

        channelManager.getChannel(PortType.MAINTAIN, upgradeTask.getCode())
                .ifPresent(deviceChannel -> {
                    log.info("imei={}: 准备升级【{}】", imei, upgradePlan.getPlanName());

                    int taskNo = upgradeTask.getTaskNo();
                    if (taskNo == 0) {
                        taskNo = upgradePlan.getTaskNo();
                    }
                    UpgradeExecutor task = new UpgradeExecutor(
                            upgradeTask, upgradeData.getUpgradeInfo(taskNo), upgradeData.getFileBytes());
                    task.setCmdExecutorCache(cmdExecutorCache);
                    task.setChannel(deviceChannel);
                    task.setListener(new UpgradeTaskListener() {
                        @Override
                        public void onFail(UpgradeTask upgradeTask, String message) {
                            upgradeService.updateUpgradeFail(upgradeTask.getId(), message);
                            upgradeExecutorMap.remove(upgradeTask.getImei());

                            // 这种情况是因为:
                            // 2: MCU断电或者复位重启了，不再升级状态中
                            // 3: 升级序号和设备内的序号重复了
                            // 都需要换新的序号重新升级
                            if (message.contains("传输文件数据失败，结果码：") || !upgradeTask.isRetryExeUpdate()) {
                                log.info("imei={}: 准备重新升级，原因：{}", upgradeTask.getImei(), message);
                                upgradeService.genNewUpgradeTask(upgradeTask.getId());
                            }
                        }

                        @Override
                        public void onFinish(UpgradeTask upgradeTask, String message) {
                            upgradeService.updateUpgradeFinished(upgradeTask.getId(), message);
                        }

                        @Override
                        public void onSuccess(UpgradeTask upgradeTask, String version, String message) {
                            upgradeService.updateUpgradeSuccess(upgradeTask.getId(), version, message);
                            upgradeExecutorMap.remove(upgradeTask.getImei());
                        }

                        @Override
                        public void onProgress(UpgradeTask upgradeTask, int progress, String message) {
                            upgradeService.updateUpgradeProgress(upgradeTask.getId(), progress, message);
                        }

                        @Override
                        public void onRetryExeUpdate(UpgradeTask upgradeTask) {
                            upgradeService.updateRetryNum(upgradeTask.getId());
                        }
                    });
                    upgradeExecutorMap.put(imei, task);

                    upgradeScheduler.schedule(() -> {
                        log.info("imei={}: 开始升级", imei);
                        task.start();
                    }, Instant.now().plusSeconds(2));
                });
    }

}
