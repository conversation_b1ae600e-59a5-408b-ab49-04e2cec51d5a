package com.acpiot.cat1wm.protocol.mqtt;

import com.acpiot.cat1wm.config.mqtt.MqttPublisher;
import com.acpiot.cat1wm.protocol.Packet;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Created by moxin on 2024/8/26T15:38
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class MqttPacketPublisher {

    private static final String TOPIC = "wmudp/device/%s/down";

    private final MqttPublisher mqttPublisher;

    @SneakyThrows
    public void publish(String imei, Packet packet) {
        String topic = String.format(TOPIC, imei);
        log.info("推送Packet >> topic: {}, packet: {}", topic, packet);
        mqttPublisher.publish(topic, packet.toHexString(), 0);
    }

}
