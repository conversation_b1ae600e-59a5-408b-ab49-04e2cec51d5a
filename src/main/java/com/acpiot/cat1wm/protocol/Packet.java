package com.acpiot.cat1wm.protocol;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.cat1wm.protocol.exception.ProtocolException;
import com.acpiot.cat1wm.protocol.util.ByteBufWrapper;
import io.netty.buffer.ByteBuf;

import static cn.hutool.core.util.ArrayUtil.length;
import static com.acpiot.cat1wm.protocol.ProtocolConstant.PKT_END;
import static com.acpiot.cat1wm.protocol.ProtocolConstant.PKT_HEAD;
import static com.acpiot.cat1wm.protocol.util.CryptoUtil.*;
import static pers.mx.jupiter.common.utils.ByteUtils.sumCode;

/**
 * The type Packet.
 *
 * <AUTHOR>
 * @date 2023 /7/20 15:59
 */
public record Packet(String code, byte cmd, byte[] data) {

    public static Packet decode(ByteBuf buffer) {
        ByteBufWrapper buf = ByteBufWrapper.wrappedBuffer(buffer);

        Assert.isTrue(buf.readableBytes() >= 14, () -> new ProtocolException("数据包长度错误，至少14字节"));
        Assert.isTrue(buf.readByte() == PKT_HEAD, () -> new ProtocolException("报文头非0x68"));

        String code = buf.readHexStrLE(8);
        int payloadLen = buf.readUnsignedShortLE();

        byte cmd = buf.readByte();

        // 数据长度
        int dataLen = payloadLen - 1;
        byte[] data = null;
        if (dataLen > 0) {
            data = buf.readBytesExt(dataLen);
            if (isEncryptCmd(cmd)) {
                decrypt(data);
            }
        }

        // 验证校验和
        byte[] calcBytes = new byte[buf.readerIndex()];
        buf.getBytes(0, calcBytes);
        byte sumCode = sumCode(calcBytes);
        Assert.isTrue(buf.readByte() == sumCode, () -> new ProtocolException("报文校验和不正确"));
        Assert.isTrue(buf.readByte() == PKT_END, () -> new ProtocolException("报文尾非0x16"));

        return new Packet(code, cmd, data);
    }

    public static Packet decode(String hex) {
        byte[] bytes = HexUtil.decodeHex(hex);
        return decode(bytes);
    }

    public static Packet decode(byte[] bytes) {
        int bytesLen = bytes.length;
        Assert.isTrue(bytesLen >= 14, () -> new ProtocolException("报文长度不正确，最小长度位14字节"));

        // 验证报头报尾
        Assert.isTrue(bytes[0] == PKT_HEAD, () -> new ProtocolException("报文头非0x68"));
        Assert.isTrue(bytes[bytesLen - 1] == PKT_END, () -> new ProtocolException("报文尾非0x16"));
        // 验证校验和
        byte sum = sumCode(bytes, 0, bytesLen - 2);
        Assert.isTrue(bytes[bytesLen - 2] == sum, () -> new ProtocolException("报文校验和不正确"));

        ByteBufWrapper buf = ByteBufWrapper.wrappedBuffer(bytes);

        // 0x68 忽略
        buf.readByte();

        String code = buf.readHexStrLE(8);
        int payloadLen = buf.readUnsignedShortLE();

        byte cmd = buf.readByte();

        // 数据长度
        int dataLen = payloadLen - 1;
        byte[] data = null;
        if (dataLen > 0) {
            data = buf.readBytesExt(dataLen);
            if (isEncryptCmd(cmd)) {
                decrypt(data);
            }
        }

        return new Packet(code, cmd, data);
    }

    /**
     * 帧长
     * 命令+数据长度
     *
     * @return
     */
    private int payloadLen() {
        return length(data) + 1;
    }

    public byte[] toBytes() {
        Assert.isTrue(StrUtil.length(code) == 16, "设备编号 {} 长度错误", code);

        if (data != null) {
            if (isEncryptCmd(cmd)) {
                encrypt(data);
            }
        }

        int payloadLen = payloadLen();
        ByteBufWrapper buf = ByteBufWrapper.buffer(13 + payloadLen);
        buf.writeByte(PKT_HEAD);
        buf.writeHexLE(code);
        buf.writeShortLE(payloadLen);
        buf.writeByte(cmd);
        if (data != null) {
            buf.writeBytes(data);
        }
        buf.writeByte(sumCode(buf.array(), 0, buf.writerIndex()));
        buf.writeByte(PKT_END);
        return buf.array();
    }

    public String toHexString() {
        return HexUtil.encodeHexStr(toBytes(), false);
    }

    @Override
    public String toString() {
        return "Packet{" +
                "code=" + code +
                ", cmd=0x" + Integer.toHexString(cmd & 0xFF) +
                ", data=" + (data == null ? null : HexUtil.encodeHexStr(data, false)) +
                '}';
    }

}
