package com.acpiot.cat1wm.admin.service.impl;

import com.acpiot.cat1wm.admin.dto.UpgradeFileQueryParams;
import com.acpiot.cat1wm.admin.exception.BusinessException;
import com.acpiot.cat1wm.admin.service.UpgradeFileService;
import com.acpiot.cat1wm.data.entity.UpgradeFile;
import com.acpiot.cat1wm.data.entity.UpgradeFile_;
import com.acpiot.cat1wm.data.repository.UpgradeFileRepository;
import com.acpiot.cat1wm.data.repository.UpgradePlanRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.fileserver.FileService;
import pers.mx.jupiter.jpa.exception.FieldException;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/18 15:07
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
@RequiredArgsConstructor
public class UpgradeFileServiceImpl implements UpgradeFileService {

    private final UpgradePlanRepository upgradePlanRepository;
    private final UpgradeFileRepository upgradeFileRepository;
    private final FileService fileService;

    @Override
    @Transactional(readOnly = true)
    public Page<UpgradeFile> pagedUpgradeFiles(UpgradeFileQueryParams params) {
        params.initExtendFilters();
        return upgradeFileRepository.findAll(params);
    }

    @Override
    public void removeUpgradeFile(long id) {
        if (upgradePlanRepository.existsByUpgradeFileId(id)) {
            throw new BusinessException("该升级文件已被升级计划关联使用");
        }
        upgradeFileRepository.findById(id)
                .ifPresent(upgradeFile -> {
                    upgradeFileRepository.delete(upgradeFile);
                    try {
                        Path path = fileService.resolvePath(upgradeFile.getStoragePath());
                        Files.delete(path);
                    } catch (IOException e) {
                        log.error("删除文件失败", e);
                    }
                });
    }

    @Override
    public UpgradeFile addUpgradeFile(UpgradeFile upgradeFile) throws FieldException {
        checkFileName(upgradeFile);
        return upgradeFileRepository.save(upgradeFile);
    }

    private void checkFileName(UpgradeFile upgradeFile) throws FieldException {
        if (!upgradeFileRepository.isUnique(upgradeFile, UpgradeFile_.filename)) {
            throw new FieldException(UpgradeFile_.FILENAME, upgradeFile.getFilename(), "该文件已存在");
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<UpgradeFile> getUpgradeFileById(long id) {
        return upgradeFileRepository.findById(id);
    }

    @Override
    public void updateUpgradeFile(Long id, String versionName, String description) {
        upgradeFileRepository.findById(id)
                .ifPresent(upgradeFile -> {
                    upgradeFile.setVersionName(versionName);
                    upgradeFile.setDescription(description);
                });
    }

}
