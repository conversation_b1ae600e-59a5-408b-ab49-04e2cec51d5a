package com.acpiot.cat1wm.admin.dto;

import cn.hutool.core.util.StrUtil;
import com.acpiot.cat1wm.data.entity.UpgradePlan_;
import com.acpiot.cat1wm.data.entity.UpgradeTask;
import com.acpiot.cat1wm.data.entity.UpgradeTask_;
import lombok.Getter;
import lombok.Setter;
import pers.mx.jupiter.jpa.Filter;
import pers.mx.jupiter.jpa.PagingQueryParams;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/18 19:21
 */
@Getter
@Setter
public class UpgradeTaskQueryParams extends PagingQueryParams<UpgradeTask> {

    private long planId;
    private String search;

    public void initExtendFilters() {
        and(Filter.eq(StrUtil.format("{}.{}", UpgradeTask_.UPGRADE_PLAN, UpgradePlan_.ID), planId));
        if (StrUtil.isNotBlank(search)) {
            and(Filter.like(UpgradeTask_.IMEI, search));
        }
    }

}
