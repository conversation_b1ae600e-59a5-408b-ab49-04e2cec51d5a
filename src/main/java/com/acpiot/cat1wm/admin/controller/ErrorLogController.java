package com.acpiot.cat1wm.admin.controller;

import com.acpiot.cat1wm.admin.service.LogService;
import com.acpiot.cat1wm.data.entity.Log;
import com.acpiot.cat1wm.data.projections.LogProjections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import pers.mx.jupiter.jpa.PagingQueryParams;

import java.util.Optional;

/**
 * Created by moxin on 2020-05-13-0013
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Slf4j
@RequiredArgsConstructor
@Controller
@RequestMapping("/admin/log/error")
@PreAuthorize("hasRole('ADMIN')")
public class ErrorLogController {

    private final LogService logService;

    @GetMapping("list")
    public String list() {
        return "log/error/list";
    }

    @PostMapping("page")
    @ResponseBody
    public Page<LogProjections.Log> page(@RequestBody PagingQueryParams<Log> params) {
        return logService.pagedErrorLog(params);
    }

    @GetMapping("args")
    @ResponseBody
    public Optional<LogProjections.Args> getLogArgs(@RequestParam("id") Long id) {
        return logService.getLogArgs(id);
    }

    @GetMapping("exception")
    @ResponseBody
    public Optional<LogProjections.Exception> getLogException(@RequestParam("id") Long id) {
        return logService.getLogException(id);
    }

}
