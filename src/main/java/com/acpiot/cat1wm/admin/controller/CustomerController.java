package com.acpiot.cat1wm.admin.controller;

import com.acpiot.cat1wm.admin.service.CustomerService;
import com.acpiot.cat1wm.config.aspect.annotation.SystemLog;
import com.acpiot.cat1wm.data.entity.Customer;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import pers.mx.jupiter.jpa.exception.FieldException;
import pers.mx.jupiter.web.util.ResponseUtils;

import java.util.List;

/**
 * Created by moxin on 2021-04-21-0021
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Controller
@RequestMapping("/admin")
@PreAuthorize("hasRole('ADMIN')")
public class CustomerController {

    private final CustomerService customerService;

    @GetMapping("customers")
    @ResponseBody
    public List<Customer> getCustomers() {
        return customerService.getCustomers();
    }

    @SystemLog("添加授权客户")
    @PostMapping("add")
    @ResponseBody
    public ResponseEntity<?> addCustomer(@ModelAttribute("customer") Customer customer) {
        try {
            customerService.addCustomer(customer);
            return ResponseUtils.ok("创建成功");
        } catch (FieldException e) {
            return ResponseUtils.error(e.getMessage());
        }
    }

    @SystemLog("删除授权客户")
    @DeleteMapping("delete")
    @ResponseBody
    public ResponseEntity<?> deleteCustomer(@RequestParam("id") long id) {
        customerService.deleteCustomer(id);
        return ResponseUtils.ok("删除成功");
    }

    @SystemLog("启用授权客户")
    @PutMapping("enable")
    @ResponseBody
    public ResponseEntity<?> enableCustomer(@RequestParam("id") long id) {
        customerService.enableCustomer(id);
        return ResponseUtils.ok("启用成功");
    }

    @SystemLog("禁用授权客户")
    @PutMapping("disable")
    @ResponseBody
    public ResponseEntity<?> disableCustomer(@RequestParam("id") long id) {
        customerService.disableCustomer(id);
        return ResponseUtils.ok("禁用成功");
    }

    @SystemLog("编辑授权客户")
    @PutMapping("edit")
    @ResponseBody
    public ResponseEntity<?> updateCustomer(@RequestParam("id") long id, @ModelAttribute("customer") Customer customer) {
        try {
            customerService.updateCustomer(id, customer);
            return ResponseUtils.ok("修改成功");
        } catch (FieldException e) {
            return ResponseUtils.error(e.getMessage());
        }
    }

    @SystemLog("重置授权客户")
    @PutMapping("reset")
    @ResponseBody
    public ResponseEntity<?> resetToken(@RequestParam("id") long id,
                                        @RequestParam("validDays") int validDays) {
        customerService.resetToken(id, validDays);
        return ResponseUtils.ok("重新授权成功");
    }

}
