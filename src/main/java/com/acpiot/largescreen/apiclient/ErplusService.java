package com.acpiot.largescreen.apiclient;

import com.acpiot.largescreen.apiclient.dto.*;
import com.acpiot.largescreen.data.exception.BusinessErrCode;
import com.acpiot.largescreen.data.exception.BusinessException;
import com.dtflys.forest.Forest;
import com.dtflys.forest.exceptions.ForestNetworkException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ErplusService {

    private final ErplusProperties erplusProperties;
    private final ErplusApiClient erplusApiClient;

    private volatile boolean refreshToken;

    public List<InventoryDto> queryInventory(String productCode) {
        InventoryQueryRespDto<InventoryDto> respDto = null;

        int retry = 0;
        do {
            if (!refreshToken) {
                refreshToken();
            }
            try {
                respDto = erplusApiClient.queryInventories(1, 2, "all", 1,
                        new FilterBodyDto(List.of(new FilterCondition("productNo", "Search", List.of(productCode)))));
                break;
            } catch (ForestNetworkException e) {
                Integer statusCode = e.getStatusCode();
                if (statusCode != null && statusCode == 401) {
                    this.refreshToken = false;
                }
            }
        } while (retry++ < 3);

        if (respDto == null
                || !respDto.isSuccess()) {
            throw new BusinessException(BusinessErrCode.ERR_ERPPLUS_QUERY_EXCEPTION);
        }
        List<InventoryDto> items = respDto.getItems()
                .stream()
                .filter(item -> {
                    ProductTemplate productTemplate = item.getProductTemplate();
                    Warehouse warehouse = item.getWarehouse();
                    return Objects.equals(productCode, productTemplate.getProductNo()) && !warehouse.getName().contains("禁用");
                })
                // 深圳仓库放在前面，否则根据仓库ID排序
                .sorted((o1, o2) -> {
                    Warehouse warehouse1 = o1.getWarehouse();
                    Warehouse warehouse2 = o2.getWarehouse();
                    if (warehouse1.getName().contains("深圳")) {
                        return -1;
                    } else if (warehouse2.getName().contains("深圳")) {
                        return 1;
                    } else {
                        return Long.compare(warehouse1.getId(), warehouse2.getId());
                    }
                })
                .toList();
        if (items.isEmpty()) {
            throw new BusinessException(BusinessErrCode.ERR_ERPPLUS_NOT_EXISTS);
        }
        long count = items.stream()
                .mapToLong(inventoryDto -> inventoryDto.getProductTemplate().getId())
                .distinct()
                .count();
        if (count > 1) {
            throw new BusinessException(BusinessErrCode.ERR_ERPPLUS_NOT_UNIQUE);
        }
        return items;
    }

    private synchronized void refreshToken() {
        if (refreshToken) {
            return;
        }

        LoginRespDto loginRespDto = erplusApiClient.login(erplusProperties);
        Forest.config().setVariableValue("accessToken", loginRespDto.getAccessToken());
        refreshToken = true;
    }

}
