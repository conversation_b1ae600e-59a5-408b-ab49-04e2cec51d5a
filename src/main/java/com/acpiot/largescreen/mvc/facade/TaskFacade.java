package com.acpiot.largescreen.mvc.facade;

import com.acpiot.largescreen.mvc.dto.task.CreateTaskDto;
import com.acpiot.largescreen.mvc.dto.task.MarkTaskCompletionDto;
import com.acpiot.largescreen.mvc.dto.task.TaskDto;
import com.acpiot.largescreen.mvc.dto.task.UpdateTaskDto;

import java.util.List;

/**
 * 任务门面接口
 *
 * <AUTHOR>
 */
public interface TaskFacade {

    /**
     * 获取所有任务
     *
     * @param completed 是否已完成
     * @param year      年份
     * @param month     月份
     * @param keyword   关键字
     * @return 任务列表
     */
    List<TaskDto> getTasks(Boolean completed, Integer year, Integer month, String keyword);


    /**
     * 获取指定象限的任务
     *
     * @param quadrant 象限
     * @return 任务列表
     */
    List<TaskDto> getTasksByQuadrant(Integer quadrant);

    /**
     * 获取指定任务的子任务
     *
     * @param parentId 父任务ID
     * @return 子任务列表
     */
    List<TaskDto> getSubTasks(Long parentId);

    /**
     * 创建任务
     *
     * @param dto 创建任务DTO
     * @return 创建后的任务
     */
    TaskDto createTask(CreateTaskDto dto);

    /**
     * 更新任务
     *
     * @param dto 更新任务DTO
     * @return 更新后的任务
     */
    TaskDto updateTask(UpdateTaskDto dto);

    /**
     * 标记任务完成状态
     *
     * @param dto 标记任务完成状态DTO
     * @return 更新后的任务
     */
    TaskDto markTaskCompletion(MarkTaskCompletionDto dto);

    /**
     * 删除任务
     *
     * @param id 任务ID
     */
    void deleteTask(Long id);

    /**
     * 获取任务详情
     *
     * @param id 任务ID
     * @return 任务详情
     */
    TaskDto getTaskDetail(Long id);

    /**
     * 交换任务顺序
     *
     * @param draggedId 被拖动的任务ID
     * @param targetId  目标任务ID
     */
    void swapTaskOrder(Long draggedId, Long targetId);
}