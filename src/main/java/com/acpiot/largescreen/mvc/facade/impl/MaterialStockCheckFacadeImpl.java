package com.acpiot.largescreen.mvc.facade.impl;

import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.acpiot.largescreen.apiclient.ErplusService;
import com.acpiot.largescreen.apiclient.dto.InventoryDto;
import com.acpiot.largescreen.data.exception.BusinessException;
import com.acpiot.largescreen.mvc.facade.MaterialStockCheckFacade;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import pers.mx.fileserver.FileService;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MaterialStockCheckFacadeImpl implements MaterialStockCheckFacade {

    public static final int MAX_COLUMN_INDEX = 7;

    private final ErplusService erplusService;
    private final FileService fileService;

    private File materialDir;

    @PostConstruct
    void init() throws IOException {
        Path path = fileService.resolvePath("./download/material");
        Files.createDirectories(path);
        materialDir = path.toFile();
    }

    @Override
    public String[] listFileNames() {
        File[] files = materialDir.listFiles();
        if (files == null || files.length == 0) {
            return new String[0];
        }

        // 按照文件创建时间倒序排序（最新的在前面）
        return Arrays.stream(files)
                .filter(File::isFile) // 只包含文件，排除目录
                .sorted((f1, f2) -> Long.compare(f2.lastModified(), f1.lastModified())) // 倒序排序
                .map(File::getName)
                .toArray(String[]::new);
    }

    @Override
    public void deleteFile(String fileName) throws IOException {
        if (StrUtil.isBlank(fileName)) {
            throw new BusinessException("文件名不能为空");
        }

        // 防止路径遍历攻击
        if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
            throw new BusinessException("文件名包含非法字符");
        }

        File file = new File(materialDir, fileName);
        if (!file.exists()) {
            throw new BusinessException("文件不存在: {}", fileName);
        }

        if (!file.delete()) {
            throw new BusinessException("删除文件失败: {}", fileName);
        }

        log.info("成功删除文件: {}", fileName);
    }

    @Override
    public synchronized void checkMaterialStock(MultipartFile file) throws IOException {
        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            // 获取最大列数
            final int maxColumnIndex = MAX_COLUMN_INDEX;

            // 新列样式
            CellStyle newStyle = getNewColumnCellStyle(workbook);

            // 缓存物料号库存，避免重复查询，方便计算扣减库存
            final Map<String, Integer> materialStockMap = new HashMap<>();

            for (Sheet sheet : workbook) {
                // 获取最后一行的行号
                int lastRowNum = sheet.getLastRowNum();

                // 设置新列宽
                sheet.setColumnWidth(maxColumnIndex + 1, 15 * 256);

                // 倍数
                Integer multiple = null;

                // 在每行末尾添加新列
                for (int i = 0; i <= lastRowNum; i++) {
                    Row row = sheet.getRow(i);
                    if (row == null) {
                        continue;
                    }

                    // 剩余库存
                    String remainingStock = "";

                    if (i == 3) {
                        // 解析数量参数单元格
                        Cell cell = row.getCell(maxColumnIndex);
                        if (cell.getCellType() == CellType.NUMERIC) {
                            multiple = (int) cell.getNumericCellValue();
                        } else if (cell.getCellType() == CellType.STRING) {
                            String cellVal = cell.getStringCellValue();
                            if (StrUtil.isNotBlank(cellVal)) {
                                multiple = Integer.parseInt(cellVal);
                                log.info("数量参数: {}", multiple);
                            }
                        }
                        Assert.notNull(multiple, "第 {} 行 {} 列数量参数错误，请检查。", i + 1, maxColumnIndex);
                    } else if (i >= 5 && i < lastRowNum) { // 开始处理数据
                        // 料号
                        Cell materialCell = row.getCell(1);
                        if (materialCell == null) {
                            // 这里是空行，跳过
                            continue;
                        }

                        String materialCode = materialCell.getStringCellValue();
                        if (StrUtil.isNotBlank(materialCode)) {
                            Integer stock = null;
                            try {
                                stock = materialStockMap.computeIfAbsent(materialCode, this::getMaterialStock);
                            } catch (Exception e) {
                                log.error(StrUtil.format("查询库存失败，料号：{}", materialCode), e);
                                remainingStock = e.getMessage();
                            }

                            if (stock != null) {
                                // 用量
                                Cell cell = row.getCell(maxColumnIndex - 1);
                                Integer usage = null;
                                if (cell != null) {
                                    if (cell.getCellType() == CellType.NUMERIC) {
                                        usage = (int) cell.getNumericCellValue();
                                    } else if (cell.getCellType() == CellType.STRING) {
                                        String cellVal = cell.getStringCellValue();
                                        if (StrUtil.isNotBlank(cellVal)) {
                                            usage = Integer.parseInt(cellVal);
                                        }
                                    }
                                }
                                Assert.notNull(usage, "第 {} 行 {} 列用量数据错误，请检查。", i + 1, maxColumnIndex);

                                // 实际用量
                                assert usage != null && multiple != null;
                                int actualUsage = usage * multiple;

                                // 扣减库存
                                stock -= actualUsage;
                                remainingStock = String.valueOf(stock);

                                // 更新库存缓存
                                materialStockMap.put(materialCode, stock);
                            }
                        } else {
                            remainingStock = "料号列为空，请检查";
                        }
                    }

                    // 插入新列，并设置样式和值
                    Cell newCell = row.createCell(maxColumnIndex + 1);
                    newCell.setCellStyle(newStyle);
                    if (i == 0) {
                        newCell.setCellValue("库存减用量"); // 设置标题
                    } else if (i < 5 || i == lastRowNum) {
                        newCell.setCellValue(""); // 默认空值
                    } else {
                        newCell.setCellValue(remainingStock);
                    }
                }
            }

            // 写回原文件
            try (FileOutputStream fos = new FileOutputStream(new File(materialDir, getNewFileName(file.getOriginalFilename())))) {
                workbook.write(fos);
            }
        }
    }

    private String getNewFileName(String originalFilename) {
        String prefix = FileNameUtil.getPrefix(originalFilename);
        String suffix = FileNameUtil.getSuffix(originalFilename);
        int i = 1;
        while (true) {
            String newFileName = prefix + "_" + i + "." + suffix;
            File newFile = new File(materialDir, newFileName);
            if (!newFile.exists()) {
                return newFileName;
            }
            i++;
        }
    }

    private int getMaterialStock(String materialCode) {
        List<InventoryDto> inventoryDtos = erplusService.queryInventory(materialCode);
        if (inventoryDtos.size() > 1) {
            throw new BusinessException("料号 {} 存在多个库存记录，请检查。", materialCode);
        }
        return new BigDecimal(inventoryDtos.getFirst().getInventory()).intValue();
    }

    private CellStyle getNewColumnCellStyle(Workbook workbook) {
        CellStyle newStyle = workbook.createCellStyle();
        newStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        newStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        newStyle.setAlignment(HorizontalAlignment.CENTER);
        newStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        newStyle.setBorderBottom(BorderStyle.THIN);
        newStyle.setBorderTop(BorderStyle.THIN);
        newStyle.setBorderLeft(BorderStyle.THIN);
        newStyle.setBorderRight(BorderStyle.THIN);
        return newStyle;
    }

}
