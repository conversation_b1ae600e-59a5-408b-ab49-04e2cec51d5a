package com.acpiot.largescreen.data.service.impl;

import com.acpiot.largescreen.data.entity.Category;
import com.acpiot.largescreen.data.entity.Product;
import com.acpiot.largescreen.data.entity.Product_;
import com.acpiot.largescreen.data.exception.BusinessErrCode;
import com.acpiot.largescreen.data.exception.BusinessException;
import com.acpiot.largescreen.data.repository.ProductRepository;
import com.acpiot.largescreen.data.service.ProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.QueryParams;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class ProductServiceImpl implements ProductService {

    private final ProductRepository productRepository;

    @Override
    @Transactional(readOnly = true)
    public List<Product> getProductsBy(QueryParams<Product> params) {
        return productRepository.findAll(params);
    }

    @Override
    public void addProduct(Product product) {
        if (!productRepository.isUnique(product, Product_.code)) {
            throw new BusinessException(BusinessErrCode.ERR_PRODUCT_CODE_EXISTS);
        }

        product.setOrderNo((int) (productRepository.countByCategory(product.getCategory()) + 1));
        productRepository.save(product);
    }

    @Override
    public void deleteProduct(Long id) {
        productRepository.findByIdAndFetch(id, Product_.CATEGORY)
                .ifPresent(product -> {
                    productRepository.delete(product);

                    // 被删除的产品序号之后的产品序号统一往前移动一位
                    productRepository.findByCategoryAndOrderNoGreaterThanOrderByOrderNoAsc(product.getCategory(), product.getOrderNo())
                            .forEach(p -> p.setOrderNo(p.getOrderNo() - 1));
                });
    }

    @Override
    public void updateProduct(Long id, Product product) {
        if (!productRepository.isUnique(id, Map.of(Product_.code, product.getCode()))) {
            throw new BusinessException(BusinessErrCode.ERR_PRODUCT_CODE_EXISTS);
        }
        productRepository.findById(id)
                .ifPresent(dbProduct -> {
                    dbProduct.setCode(product.getCode());
                    dbProduct.setShortName(product.getShortName());
                    dbProduct.setName(product.getName());
                    dbProduct.setThreshold(product.getThreshold());
                    dbProduct.setStockStr(product.getStockStr());
                    dbProduct.setDisabled(product.isDisabled());
                    dbProduct.setFactory1Stock(product.getFactory1Stock());
                    dbProduct.setFactory2Stock(product.getFactory2Stock());
                    Category newCategory = product.getCategory();
                    if (!Objects.equals(newCategory.getId(), dbProduct.getCategory().getId())) {
                        dbProduct.setOrderNo((int) (productRepository.countByCategory(newCategory) + 1));
                        dbProduct.setCategory(newCategory);
                    }
                });
    }

    @Override
    public void swapProductOrder(Long draggedId, Long targetId) {
        Product draggedProduct = productRepository.findById(draggedId)
                .orElseThrow(() -> new BusinessException(BusinessErrCode.ERR_PRODUCT_NOT_EXISTS));
        Product targetProduct = productRepository.findById(targetId)
                .orElseThrow(() -> new BusinessException(BusinessErrCode.ERR_PRODUCT_NOT_EXISTS));
        if (!Objects.equals(draggedProduct.getCategory().getId(), targetProduct.getCategory().getId())) {
            throw new BusinessException(BusinessErrCode.ERR_PRODUCT_NOT_IN_CATEGORY);
        }

        int draggedOrderNo = draggedProduct.getOrderNo();
        int targetOrderNo = targetProduct.getOrderNo();
        draggedProduct.setOrderNo(targetOrderNo);
        targetProduct.setOrderNo(draggedOrderNo);
    }

    @Override
    public void updateProductStock(Long id, String name, String stockStr) {
        productRepository.findById(id)
                .ifPresent(product -> {
                    product.setName(name);
                    product.setStockStr(stockStr);
                    product.setDisabled(false);
                });
    }

    @Override
    public void disableProduct(Long id) {
        productRepository.findById(id)
                .ifPresent(product -> product.setDisabled(true));
    }
}
