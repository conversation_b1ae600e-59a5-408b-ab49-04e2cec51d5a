package com.acpiot.largescreen.data.service;

import com.acpiot.largescreen.data.entity.Product;
import pers.mx.jupiter.jpa.QueryParams;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProductService {
    List<Product> getProductsBy(QueryParams<Product> params);

    void addProduct(Product product);

    void deleteProduct(Long id);

    void updateProduct(Long id, Product product);

    void swapProductOrder(Long draggedId, Long targetId);

    void updateProductStock(Long id, String name, String stockStr);

    void disableProduct(Long id);
}
