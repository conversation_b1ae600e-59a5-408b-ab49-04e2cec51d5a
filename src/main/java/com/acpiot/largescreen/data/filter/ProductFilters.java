package com.acpiot.largescreen.data.filter;

import cn.hutool.core.util.StrUtil;
import com.acpiot.largescreen.data.entity.Category_;
import com.acpiot.largescreen.data.entity.Product_;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import pers.mx.jupiter.jpa.Filter;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ProductFilters {

    public static Filter byCategoryId(Long categoryId) {
        return Filter.eq(StrUtil.format("{}.{}", Product_.CATEGORY, Category_.ID), categoryId);
    }

}
