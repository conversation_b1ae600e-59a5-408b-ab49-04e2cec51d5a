package com.acpiot.protocolconverter.protocol.datastruct;

import cn.hutool.core.util.StrUtil;

import java.util.List;

/**
 * <AUTHOR>
 */
public record IpPort(String ip, int port) {

    public static IpPort of(String ipPort) {
        String[] split = ipPort.split(":");
        return new IpPort(split[0], Integer.parseInt(split[1]));
    }

    public static List<IpPort> parse(String serverAddrs) {
        if (StrUtil.isBlank(serverAddrs)) {
            return List.of();
        }
        return StrUtil.split(serverAddrs, ',')
                .stream()
                .map(IpPort::of)
                .toList();
    }

    @Override
    public String toString() {
        return StrUtil.format("{}:{}", ip, port);
    }
}
