package com.acpiot.protocolconverter.protocol.watermeter.cdsjudp;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "protocol.cdsj-udp")
public class CdsjUdpProperties {

    private String host;
    private int port;

}
