package com.acpiot.protocolconverter.protocol.watermeter.zywlonenet;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.RegexPool;
import cn.hutool.core.util.*;
import com.acpiot.protocolconverter.protocol.datastruct.Tlv;
import com.acpiot.protocolconverter.util.ByteBufferWrapper;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.nio.ByteOrder;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by moxin on 2024/10/24T11:08
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ZywlooTlvUtil {

    private static byte[] toBytes(Tlv tlv) {
        int dataLen = ArrayUtil.length(tlv.value());
        byte[] data = new byte[dataLen + 2];
        data[0] = (byte) tlv.type();
        data[1] = (byte) dataLen;
        if (dataLen > 0) {
            System.arraycopy(tlv.value(), 0, data, 2, dataLen);
        }
        return data;
    }

    private static Tlv decode(ByteBufferWrapper buf) {
        int type = buf.getUnsignedByte();
        int dataLen = buf.getUnsignedByte();
        byte[] data = null;
        if (dataLen > 0) {
            data = buf.getBytes(dataLen);
        }
        return new Tlv(type, data);
    }

    public static List<Tlv> decode(byte[] dataBytes) {
        List<Tlv> tlvs = new ArrayList<>();
        ByteBufferWrapper buf = ByteBufferWrapper.wrap(dataBytes);
        while (buf.hasRemaining()) {
            tlvs.add(decode(buf));
        }
        return tlvs;
    }

    public static byte[] encode(List<Tlv> tlvs) {
        ByteBufferWrapper dataBuf = ByteBufferWrapper.allocate(242);
        tlvs.forEach(tlv -> dataBuf.put(toBytes(tlv)));
        int dataLen = dataBuf.position();
        return Arrays.copyOfRange(dataBuf.array(), 0, dataLen);
    }

    /**
     * The constant METER_TYPE_WATER.
     */
    public static final Tlv METER_TYPE_WATER = ofMeterType(0xA1);

    /**
     * Of meter code tlv.
     *
     * @param meterCode the meter code
     * @return the tlv
     */
    public static Tlv ofMeterCode(String meterCode) {
        return new Tlv(0x01, HexUtil.decodeHex(StrUtil.padAfter(meterCode, 16, "0")));
    }

    /**
     * Of imei tlv.
     *
     * @param imei the imei
     * @return the tlv
     */
    public static Tlv ofImei(String imei) {
        return new Tlv(0x02, HexUtil.decodeHex(StrUtil.padAfter(imei, 16, "0")));
    }

    /**
     * Of imsi tlv.
     *
     * @param imsi the imsi
     * @return the tlv
     */
    public static Tlv ofImsi(String imsi) {
        return new Tlv(0x03, HexUtil.decodeHex(StrUtil.padAfter(imsi, 16, "0")));
    }

    /**
     * Of ip tlv.
     *
     * @param ip the ip
     * @return the tlv
     */
    public static Tlv ofIp(String ip) {
        Assert.isTrue(ReUtil.isMatch(RegexPool.IPV4, ip), "ip={} 格式不正确", ip);
        return new Tlv(0x04, ip.getBytes());
    }

    /**
     * Of port tlv.
     *
     * @param port the port
     * @return the tlv
     */
    public static Tlv ofPort(int port) {
        Assert.checkBetween(port, 0, 65535, "port={} 值不正确", port);
        return new Tlv(0x05, String.valueOf(port).getBytes());
    }

    /**
     * Of csq tlv.
     *
     * @param csq the csq
     * @return the tlv
     */
    public static Tlv ofCsq(int csq) {
        return new Tlv(0x06, HexUtil.decodeHex(String.valueOf(csq)));
    }

    /**
     * Of snr tlv.
     *
     * @param snr the snr
     * @return the tlv
     */
    public static Tlv ofSnr(int snr) {
        ByteBufferWrapper buf = ByteBufferWrapper.allocate(3);
        buf.putShortLe(Math.abs(snr));
        buf.put(snr < 0 ? 0x01 : 0x00);
        return new Tlv(0x07, buf.array());
    }

    /**
     * Of rsrp tlv.
     *
     * @param rsrp the rsrp
     * @return the tlv
     */
    public static Tlv ofRsrp(int rsrp) {
        ByteBufferWrapper buf = ByteBufferWrapper.allocate(3);
        buf.putShortLe(Math.abs(rsrp));
        buf.put(rsrp < 0 ? 0x01 : 0x00);
        return new Tlv(0x08, buf.array());
    }

    /**
     * Of rsrq tlv.
     *
     * @param rsrq the rsrq
     * @return the tlv
     */
    public static Tlv ofRsrq(int rsrq) {
        ByteBufferWrapper buf = ByteBufferWrapper.allocate(3);
        buf.putShortLe(Math.abs(rsrq));
        buf.put(rsrq < 0 ? 0x01 : 0x00);
        return new Tlv(0x09, buf.array());
    }

    /**
     * Of ecl tlv.
     *
     * @param ecl the ecl
     * @return the tlv
     */
    public static Tlv ofEcl(int ecl) {
        return new Tlv(0x0A, HexUtil.decodeHex(String.valueOf(ecl)));
    }

    /**
     * Of date time tlv.
     *
     * @param dateTime the date time
     * @return the tlv
     */
    public static Tlv ofDateTime(LocalDateTime dateTime) {
        return new Tlv(0x0B, HexUtil.decodeHex(LocalDateTimeUtil.format(dateTime, "ssmmHHddMMyy")));
    }

    /**
     * Of battery voltage tlv.
     *
     * @param batteryVoltage the battery voltage
     * @return the tlv
     */
    public static Tlv ofBatteryVoltage(BigDecimal batteryVoltage) {
        int voltage = batteryVoltage.multiply(new BigDecimal(100)).intValue();
        return new Tlv(0x0C, ByteUtil.shortToBytes((short) voltage, ByteOrder.LITTLE_ENDIAN));
    }

    /**
     * Of pulse equivalent tlv.
     *
     * @param pulseEquivalent the pulse equivalent
     * @return the tlv
     */
    public static Tlv ofPulseEquivalent(Integer pulseEquivalent) {
        short val = pulseEquivalent != null ? pulseEquivalent.shortValue() : 0x0001;
        return new Tlv(0x0D, ByteUtil.shortToBytes(val, ByteOrder.LITTLE_ENDIAN));
    }

    /**
     * Of report start time tlv.
     *
     * @param startTime   the start time
     * @param randomDelay the random delay
     * @return the tlv
     */
    public static Tlv ofReportStartTime(LocalTime startTime, int randomDelay) {
        ByteBufferWrapper buf = ByteBufferWrapper.allocate(4);
        buf.putHex(startTime.format(DateTimeFormatter.ofPattern("ssmmHH")));
        buf.put(randomDelay);
        return new Tlv(0x0E, buf.array());
    }

    /**
     * Of real time accumulation tlv.
     *
     * @param realTimeAccumulation the real time accumulation
     * @return the tlv
     */
    public static Tlv ofRealTimeAccumulation(int realTimeAccumulation) {
        return new Tlv(0x12, ByteUtil.intToBytes(realTimeAccumulation, ByteOrder.LITTLE_ENDIAN));
    }

    /**
     * 阀门状态
     *
     * @param valveState 00 开阀；01 关阀，如果不支持阀控，统一报 00
     * @return tlv tlv
     */
    public static Tlv ofValveState(int valveState) {
        return new Tlv(0x17, new byte[]{(byte) valveState});
    }

    /**
     * 磁干扰状态
     *
     * @param interferenceState 00：正常；01：存在故障
     * @return the tlv
     */
    public static Tlv ofInterferenceState(int interferenceState) {
        return new Tlv(0x19, new byte[]{(byte) interferenceState});
    }

    /**
     * 阀门故障
     *
     * @param state 00：正常；01：存在故障，不支持阀控统一报 00
     * @return the tlv
     */
    public static Tlv ofValveFault(int state) {
        return new Tlv(0x1A, new byte[]{(byte) state});
    }

    /**
     * 仪表类型
     *
     * @param meterType the meter type
     * @return the tlv
     */
    public static Tlv ofMeterType(int meterType) {
        return new Tlv(0x1B, new byte[]{(byte) meterType});
    }

    /**
     * 日冻结累计
     *
     * @param dailyFreezeAccumulation the daily freeze accumulation
     * @return the tlv
     */
    public static Tlv ofDailyFreezeAccumulation(int dailyFreezeAccumulation) {
        return new Tlv(0x1C, ByteUtil.intToBytes(dailyFreezeAccumulation, ByteOrder.LITTLE_ENDIAN));
    }

    /**
     * Of iccid tlv.
     *
     * @param iccid the iccid
     * @return the tlv
     */
    public static Tlv ofIccid(String iccid) {
        Assert.isTrue(StrUtil.length(iccid) == 20, "iccid长度必须是20位");
        return new Tlv(0x1D, HexUtil.decodeHex(iccid));
    }

    /**
     * 强磁攻击事件
     *
     * @param eventTime
     * @return
     */
    public static Tlv ofMagneticAttackEvent(String eventTime) {
        ByteBufferWrapper buf = ByteBufferWrapper.allocate(7);
        buf.putReverseHex(eventTime);
        buf.put(0x02);
        return new Tlv(0x01, buf.array());
    }

}
