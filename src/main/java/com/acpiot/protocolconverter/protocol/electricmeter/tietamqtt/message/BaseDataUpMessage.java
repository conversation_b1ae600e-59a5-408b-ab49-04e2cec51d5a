package com.acpiot.protocolconverter.protocol.electricmeter.tietamqtt.message;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public abstract class BaseDataUpMessage extends BaseTietaUpMessage {

    /**
     * 信号级别
     */
    private final int signallevel;

    public BaseDataUpMessage(int datatype, String meternum, int signallevel) {
        super(datatype, meternum);
        this.signallevel = signallevel;
    }

}
