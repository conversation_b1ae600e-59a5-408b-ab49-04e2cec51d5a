package com.acpiot.protocolconverter.protocol.electricmeter.listener;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.util.StrUtil;
import com.acpiot.protocolconverter.data.device.electricmeter.domain.ElectricMeter;
import com.acpiot.protocolconverter.data.device.electricmeter.service.ElectricMeterService;
import com.acpiot.protocolconverter.protocol.electricmeter.tietamqtt.TietaMqttClient;
import com.acpiot.protocolconverter.protocol.electricmeter.tietamqtt.TietaMqttClientManager;
import com.acpiot.protocolconverter.protocol.electricmeter.tietamqtt.command.TietaCommandTask;
import com.acpiot.protocolconverter.protocol.electricmeter.tietamqtt.command.impl.SetReportIntervalCommandTask;
import com.acpiot.protocolconverter.protocol.electricmeter.tietamqtt.message.CommandDownMessage;
import com.acpiot.protocolconverter.protocol.electricmeter.tietamqtt.message.DeviceRegisterMessage;
import com.acpiot.protocolconverter.protocol.electricmeter.tietamqtt.message.EnergyDataMessage;
import com.acpiot.protocolconverter.protocol.electricmeter.tietamqtt.message.PowerDataMessage;
import com.acpiot.protocolconverter.thirdservice.qgdw.Qgdw13761ApiClient;
import com.acpiot.protocolconverter.thirdservice.qgdw.dto.command.AfnFnData;
import com.acpiot.protocolconverter.thirdservice.qgdw.dto.command.ExecuteResult;
import com.acpiot.protocolconverter.thirdservice.qgdw.dto.command.FnResult;
import com.acpiot.protocolconverter.thirdservice.qgdw.dto.command.QgdwCommandResponse;
import com.acpiot.protocolconverter.thirdservice.qgdw.dto.command.h0c.Afn0CHF129Data;
import com.acpiot.protocolconverter.thirdservice.qgdw.dto.command.h0c.Afn0CHF25Data;
import com.acpiot.protocolconverter.thirdservice.qgdw.event.ElectricMeterCommandRespEvent;
import com.acpiot.protocolconverter.thirdservice.qgdw.event.ElectricMeterDataReportEvent;
import com.acpiot.protocolconverter.thirdservice.qgdw.event.ElectricMeterOnlineOfflineEvent;
import com.acpiot.protocolconverter.util.GlobalLockManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.locks.Lock;

/**
 * 电表数据事件监听器.
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ElectricMeterDataEventListener {

    private final TietaMqttClientManager tietaMqttClientManager;
    private final ElectricMeterService electricMeterService;
    private final ObjectMapper objectMapper;
    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    private final Qgdw13761ApiClient qgdw13761ApiClient;
    private final GlobalLockManager globalLockManager;

    private final TimedCache<String, Boolean> deviceStateCache = CacheUtil.newTimedCache(1);

    {
        deviceStateCache.setListener(this::handleOnlineOffline);
        deviceStateCache.schedulePrune(10);
    }

    private final TimedCache<Long, TietaCommandTask> commandTaskCache = CacheUtil.newTimedCache(60, 10);

    private TietaMqttClient getAndRegisterClient(ElectricMeter electricMeter) {
        TietaMqttClient client = tietaMqttClientManager.getClient(electricMeter);
        if (!client.isRegistered()) {
            // 如果没有注册，则发布注册消息
            Lock lock = globalLockManager.getLock(electricMeter.getModuleInfo().getUid());
            try {
                lock.lock();
                if (!client.isRegistered()) {
                    try {
                        client.publish(DeviceRegisterMessage.of(electricMeter));
                        client.setRegistered(true);
                    } catch (Exception e) {
                        log.error(StrUtil.format("meterCode={}: 发布注册消息失败", electricMeter.getMeterCode()), e);
                    }
                }
            } finally {
                lock.unlock();
            }
        }
        return client;
    }

    @Async
    @EventListener
    public void onElectricMeterOnlineOfflineEvent(ElectricMeterOnlineOfflineEvent evt) {
        String uid = evt.uid();
        boolean online = evt.online();

        Lock lock = globalLockManager.getLock(uid);
        try {
            lock.lock();
            Boolean b = deviceStateCache.get(uid);
            if (b == null || b == Boolean.FALSE) {
                deviceStateCache.put(uid, online);
            }
        } finally {
            lock.unlock();
        }
    }

    private void handleOnlineOffline(String uid, boolean online) {
        electricMeterService.updateOnline(uid, online)
                .ifPresent(electricMeter -> {
                    String meterCode = electricMeter.getMeterCode();
                    if (online) {
                        getAndRegisterClient(electricMeter);
                    } else {
                        // 电表离线后，销毁对应的客户端
                        tietaMqttClientManager.destroyClient(meterCode);
                    }
                });
    }

    @Async
    @EventListener
    public void onElectricMeterDataReportEvent(ElectricMeterDataReportEvent evt) {
        AfnFnData data = evt.data();
        if (data.afn() != 0x0C) {
            return;
        }

        List<FnResult<?>> fnResults = data.fnResults();
        String uid = evt.uid();
        electricMeterService.getElectricMeterByUid(uid)
                .ifPresent(electricMeter -> {
                    String meterCode = electricMeter.getMeterCode();

                    PowerDataMessage frozenDataMessage = null;
                    EnergyDataMessage realtimeDataMessage = null;

                    for (FnResult<?> fnResult : fnResults) {
                        Object fnData = fnResult.data();
                        switch (fnResult.fn()) {
                            case 25 -> {
                                Afn0CHF25Data fn25Data = objectMapper.convertValue(fnData, Afn0CHF25Data.class);
                                realtimeDataMessage = EnergyDataMessage.of(meterCode, fn25Data);
                            }
                            case 129 -> {
                                Afn0CHF129Data fn129Data = objectMapper.convertValue(fnData, Afn0CHF129Data.class);
                                frozenDataMessage = PowerDataMessage.ofRealtime(meterCode, fn129Data);
                            }
                        }
                    }

                    TietaMqttClient client = getAndRegisterClient(electricMeter);

                    // 电量实时数据上报
                    if (frozenDataMessage != null) {
                        try {
                            client.publish(frozenDataMessage);
                        } catch (Exception e) {
                            log.error(StrUtil.format("meterCode={}: 发送电量数据失败", meterCode), e);
                        }
                    }

                    // 电能数据上报
                    if (realtimeDataMessage != null) {
                        try {
                            client.publish(realtimeDataMessage);
                        } catch (Exception e) {
                            log.error(StrUtil.format("meterCode={}: 发送电能数据失败", meterCode), e);
                        }
                    }
                });
    }

    /**
     * 处理命令下发消息.
     *
     * @param evt
     */
    @Async
    @EventListener
    public void onCommandDownMessage(CommandDownMessage evt) {
        electricMeterService.getElectricMeterByMeterCode(evt.getMeternum())
                .ifPresent(electricMeter -> {
                    TietaCommandTask commandTask = switch (evt.getServercommand()) {
                        case 5 -> {
                            // 修改设备采集数据的时间间隔，数据为int类型，单位为分钟，范围为1-1440
                            int reportInterval = Integer.parseInt(evt.getServerdata());
                            yield new SetReportIntervalCommandTask(electricMeter, reportInterval,
                                    qgdw13761ApiClient, electricMeterService);
                        }
                        case 12 -> {
                            // 数据透传
                            log.warn("数据透传命令未实现，不清楚该命令的作用");
                            yield null;
                        }
                        default -> null;
                    };
                    if (commandTask != null) {
                        Long commandId = commandTask.execute();
                        if (commandId != null) {
                            commandTaskCache.put(commandId, commandTask);
                        }
                    }
                });
    }

    @Async
    @EventListener
    public void onElectricMeterCommandRespEvent(ElectricMeterCommandRespEvent evt) {
        QgdwCommandResponse commandResponse = evt.commandResponse();
        if (commandResponse.getExecuteState() == ExecuteResult.ExecuteState.COMPLETED) {
            TietaCommandTask commandTask = commandTaskCache.get(commandResponse.getCommandId());
            if (commandTask != null) {
                commandTask.success();
            }
        }
    }

}
