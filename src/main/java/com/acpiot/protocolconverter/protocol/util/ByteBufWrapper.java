package com.acpiot.protocolconverter.protocol.util;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.HexUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;

import static cn.hutool.core.util.PrimitiveArrayUtil.INDEX_NOT_FOUND;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ByteBufWrapper {

    public static ByteBufWrapper buffer(int capacity) {
        return new ByteBufWrapper(Unpooled.buffer(capacity));
    }

    public static ByteBufWrapper wrappedBuffer(ByteBuf buf) {
        return new ByteBufWrapper(Unpooled.wrappedBuffer(buf));
    }

    public static ByteBufWrapper wrappedBuffer(byte[] array) {
        return new ByteBufWrapper(Unpooled.wrappedBuffer(array));
    }

    @Delegate
    private final ByteBuf buf;

    public byte[] readBytesExt(int length) {
        byte[] bytes = new byte[length];
        buf.readBytes(bytes);
        return bytes;
    }

    public byte[] readReverseBytes(int length) {
        byte[] bytes = new byte[length];
        buf.readBytes(bytes);
        return ArrayUtil.reverse(bytes);
    }

    public void writeHexStr(String hexStr) {
        buf.writeBytes(HexUtil.decodeHex(hexStr));
    }

    public String readHexStr(int length) {
        return HexUtil.encodeHexStr(readBytesExt(length));
    }

    public void writeHexStrLe(String hexStr) {
        buf.writeBytes(ArrayUtil.reverse(HexUtil.decodeHex(hexStr)));
    }

    public String readHexStrLe(int length) {
        return HexUtil.encodeHexStr(readReverseBytes(length));
    }

    public void writeBCD(String format, int val) {
        writeHexStr(String.format(format, val));
    }

    public void writeBCDLe(String format, int val) {
        writeHexStrLe(String.format(format, val));
    }

    public String readAsciiStr(int length) {
        byte[] bytes = readBytesExt(length);
        int i = ArrayUtil.indexOf(bytes, (byte) 0);
        if (i != INDEX_NOT_FOUND) {
            return new String(bytes, 0, i);
        }
        return null;
    }

    public String readMac() {
        return String.format("%02X:%02X:%02X:%02X:%02X:%02X",
                readByte(), readByte(), readByte(), readByte(), readByte(), readByte());
    }

}
