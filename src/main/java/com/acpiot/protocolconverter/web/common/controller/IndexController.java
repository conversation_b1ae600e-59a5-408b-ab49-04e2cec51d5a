package com.acpiot.protocolconverter.web.common.controller;

import com.acpiot.protocolconverter.config.security.privilege.bean.Privilege;
import com.acpiot.protocolconverter.web.common.facade.IndexFacade;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.acpiot.protocolconverter.config.security.SecurityConstant.USER_MENU_PRIVILEGES;
import static com.acpiot.protocolconverter.config.security.privilege.PrivilegeService.findDefaultPrivilege;

/**
 * Created by moxin on 2019-07-15-0015
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Controller
@RequestMapping("/")
public class IndexController {

    private final IndexFacade indexFacade;

    /**
     * 默认进入首页
     *
     * @return
     */
    @GetMapping("")
    public String index() {
        return "redirect:index";
    }

    /**
     * 进入首页
     *
     * @param userPrivilege
     * @param map
     * @param response
     * @return
     */
    @SneakyThrows
    @GetMapping("index")
    public String index(@SessionAttribute(USER_MENU_PRIVILEGES) List<Privilege> userPrivilege, ModelMap map, HttpServletResponse response) {
        Privilege defaultPrivilege = findDefaultPrivilege(userPrivilege);
        if (defaultPrivilege == null) {
            // 默认权限为空，则进入空白首页
            defaultPrivilege = new Privilege();
            defaultPrivilege.setCode("");
        }
        map.addAttribute("defaultPrivilege", defaultPrivilege);
        return "index";
    }

    @GetMapping("account/password")
    public String minePassword() {
        return "account/password";
    }

    @PutMapping("account/password")
    @ResponseBody
    public void updatePassword(@RequestParam("oldPassword") String oldPassword,
                               @RequestParam("newPassword") String newPassword) {
        indexFacade.updatePassword(oldPassword, newPassword);
    }
}
