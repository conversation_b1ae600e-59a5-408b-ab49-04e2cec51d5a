package com.acpiot.protocolconverter.web.archive.electricmeter.controller;

import cn.idev.excel.FastExcel;
import cn.idev.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.acpiot.protocolconverter.data.device.electricmeter.domain.AddrRule;
import com.acpiot.protocolconverter.data.device.electricmeter.domain.ElectricMeter;
import com.acpiot.protocolconverter.data.device.electricmeter.domain.ElectricMeterType;
import com.acpiot.protocolconverter.web.archive.electricmeter.dto.ArchiveElectricMeterExcelDto;
import com.acpiot.protocolconverter.web.archive.electricmeter.facade.ArchiveElectricMeterFacade;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import pers.mx.jupiter.jpa.PagingQueryParams;
import pers.mx.jupiter.jpa.exception.FieldException;
import pers.mx.jupiter.web.util.ResponseUtils;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 电表档案管理控制器
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Controller
@RequestMapping("/archive/electricmeter")
public class ArchiveElectricMeterController {

    private final ArchiveElectricMeterFacade archiveElectricMeterFacade;
    private final ObjectMapper objectMapper;

    /**
     * 电表类型枚举.
     *
     * @return the electric meter type [ ]
     */
    @ModelAttribute("meterTypes")
    public ElectricMeterType[] meterTypes() {
        return ElectricMeterType.values();
    }

    @ModelAttribute("addrRules")
    public AddrRule[] addrRules() {
        return AddrRule.values();
    }

    /**
     * 跳转电表列表页面.
     *
     * @return the string
     */
    @RequestMapping("/list")
    public String list() {
        return "archive/electricmeter/list";
    }

    /**
     * 分页查询电表数据.
     *
     * @param params the params
     * @return the page
     */
    @PostMapping("page")
    @ResponseBody
    public Page<ElectricMeter> page(@RequestBody PagingQueryParams<ElectricMeter> params) {
        return archiveElectricMeterFacade.pagedElectricMeters(params);
    }

    /**
     * 跳转添加电表页面.
     *
     * @param electricMeter the electric meter
     * @return the string
     */
    @GetMapping("add")
    public String toAdd(@ModelAttribute("electricMeter") ElectricMeter electricMeter) {
        return "archive/electricmeter/add";
    }

    /**
     * 添加电表档案信息.
     *
     * @param electricMeter the electric meter
     * @param result        the result
     * @return the string
     */
    @PostMapping("add")
    public String add(@Validated @ModelAttribute("electricMeter") ElectricMeter electricMeter, BindingResult result) {
        if (result.hasErrors()) {
            return toAdd(electricMeter);
        }
        try {
            archiveElectricMeterFacade.addElectricMeter(electricMeter);
        } catch (FieldException e) {
            result.addError(new FieldError(result.getObjectName(), e.getFieldName(), e.getRejectedValue(),
                    true, null, null, e.getMessage()));
            return toAdd(electricMeter);
        }
        return "redirect:list";
    }

    /**
     * 跳转电表编辑页面.
     *
     * @param id  the id
     * @param map the map
     * @return the string
     */
    @GetMapping("edit")
    public String toEdit(@RequestParam("id") long id, ModelMap map) {
        return archiveElectricMeterFacade.getElectricMeterById(id)
                .map(meter -> toEdit(map, meter))
                .orElse("redirect:list");
    }

    private String toEdit(ModelMap map, ElectricMeter electricMeter) {
        map.addAttribute("electricMeter", electricMeter);
        return "archive/electricmeter/edit";
    }

    /**
     * 编辑电表档案信息.
     *
     * @param electricMeter the electric meter
     * @param result        the result
     * @param map           the map
     * @return the string
     */
    @PostMapping("edit")
    public String edit(@Validated @ModelAttribute("electricMeter") ElectricMeter electricMeter, BindingResult result, ModelMap map) {
        if (result.hasErrors()) {
            return toEdit(map, electricMeter);
        }
        try {
            archiveElectricMeterFacade.updateElectricMeter(electricMeter);
        } catch (FieldException e) {
            result.addError(new FieldError(result.getObjectName(), e.getFieldName(), e.getRejectedValue(),
                    true, null, null, e.getMessage()));
            return toEdit(map, electricMeter);
        }
        return "redirect:list";
    }

    /**
     * 删除电表注册信息.
     *
     * @param id the id
     */
    @DeleteMapping("deleteRegistration")
    @ResponseBody
    public void deleteRegistration(@RequestParam("id") long id) {
        archiveElectricMeterFacade.deleteRegistration(id);
    }

    /**
     * 注册电表信息.
     *
     * @param id the id
     */
    @PutMapping("registerMeter")
    @ResponseBody
    public void registerMeter(@RequestParam("id") long id) {
        archiveElectricMeterFacade.registerMeter(id);
    }

    /**
     * 删除电表档案.
     *
     * @param id the id
     * @return the response entity
     */
    @DeleteMapping("delete")
    @ResponseBody
    public ResponseEntity<?> delete(@RequestParam("id") long id) {
        archiveElectricMeterFacade.removeElectricMeters(List.of(id));
        return ResponseUtils.ok("删除成功");
    }

    /**
     * 批量删除电表档案.
     *
     * @param ids the ids
     * @return the response entity
     */
    @DeleteMapping("batchDel")
    @ResponseBody
    public ResponseEntity<?> delete(@RequestParam("ids[]") List<Long> ids) {
        archiveElectricMeterFacade.removeElectricMeters(ids);
        return ResponseUtils.ok("删除成功");
    }

    /**
     * 跳转批量导入电表页面.
     *
     * @return the string
     */
    @GetMapping("uploadAdd")
    public String toUploadAdd() {
        return "archive/electricmeter/upload-add";
    }

    /**
     * 批量导入电表档案.
     *
     * @param file the file
     * @throws IOException the io exception
     */
    @PostMapping("uploadAdd")
    @ResponseBody
    public void uploadExcel(@RequestParam("file") MultipartFile file) throws IOException {
        archiveElectricMeterFacade.importAddElectricMeters(file);
    }

    /**
     * 跳转批量导入编辑电表页面.
     *
     * @return the string
     */
    @GetMapping("uploadEdit")
    public String toUploadEdit() {
        return "archive/electricmeter/upload-edit";
    }

    /**
     * 批量导入编辑电表档案.
     *
     * @param file the file
     * @throws IOException the io exception
     */
    @PostMapping("uploadEdit")
    @ResponseBody
    public void uploadEditExcel(@RequestParam("file") MultipartFile file) throws IOException {
        archiveElectricMeterFacade.importEditElectricMeters(file);
    }

    /**
     * 导出电表档案信息
     *
     * @param params   the params
     * @param response the response
     * @throws IOException the io exception
     */
    @PostMapping("export")
    public void exportArchives(@RequestParam("params") String params, HttpServletResponse response) throws IOException {
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        PagingQueryParams<ElectricMeter> queryParams = objectMapper.readValue(
                params.replaceAll("'", "\""), new TypeReference<>() {
                });
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode("电表档案", StandardCharsets.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        List<ArchiveElectricMeterExcelDto> exportList = archiveElectricMeterFacade.getExportElectricMeters(queryParams);
        try (ServletOutputStream out = response.getOutputStream()) {
            FastExcel.write(out, ArchiveElectricMeterExcelDto.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("sheet1")
                    .doWrite(exportList);
        }
    }

}
