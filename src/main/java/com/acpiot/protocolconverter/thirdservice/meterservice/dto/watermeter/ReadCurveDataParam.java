package com.acpiot.protocolconverter.thirdservice.meterservice.dto.watermeter;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Created by moxin on 2022-01-26-0026
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReadCurveDataParam {

    private int type;

    private int density;

    @JsonFormat(pattern = "yyMMddHH")
    private Date endTime;

}
