package com.acpiot.protocolconverter.thirdservice.meterservice.dto.watermeter;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by moxin on 2021-01-28-0028
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceValveCtrlParam {

    /**
     * 阀门动作，true 表示开阀，false 表示关阀
     */
    private boolean action;

}
