package com.acpiot.protocolconverter.thirdservice.config.mqtt;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 华为云 mqtt 配置
 * Created by moxin on 2023/9/6T10:59
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "mqtt")
public class MqttProperties {

    private boolean enable;

    private Map<MqttServer, MqttParams> params;

    public MqttParams getParams(MqttServer server) {
        return params.get(server);
    }

}
