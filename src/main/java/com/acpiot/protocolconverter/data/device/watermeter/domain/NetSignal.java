package com.acpiot.protocolconverter.data.device.watermeter.domain;

import com.fasterxml.jackson.annotation.JsonAlias;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Data;

import java.io.Serializable;

/**
 * 网络信号信息
 * Created by moxin on 2020-08-05-0005
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Data
@Embeddable
public class NetSignal implements Serializable {

    /**
     * 信号等级
     */
    @Column
    private Integer csq;

    /**
     * 信号强度
     */
    @Column
    private Integer rssi;

    /**
     * 参考信号接收功率
     */
    @Column
    private Integer rsrp;

    /**
     * 信号与干扰加噪声比
     */
    @JsonAlias("sinr")
    @Column
    private Integer snr;

    /**
     * 无线信号覆盖等级
     */
    @Column
    private Integer ecl;

    /**
     * 物理小区标识
     */
    @Column
    private Integer pci;

    /**
     * 小区位置信息
     */
    @JsonAlias("cell_id")
    @Column
    private Integer cellId;

}
