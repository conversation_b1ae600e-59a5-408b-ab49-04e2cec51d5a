package com.acpiot.qgdw13761.api.dto;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 错误信息
 * Created by moxin on 2020-10-19-0019
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Getter
public enum ErrorCode {

    /**
     * IMEI号已存在
     */
    DEVICE_CODE_EXISTS(2020001, "设备编号已存在"),

    /**
     * IMEI号不存在
     */
    DEVICE_CODE_NOT_EXIST(2020002, "设备编号 {} 不存在或者归属于其他账号下");

    private final int code;
    private final String message;

}
