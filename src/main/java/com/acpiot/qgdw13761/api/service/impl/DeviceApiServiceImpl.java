package com.acpiot.qgdw13761.api.service.impl;

import cn.hutool.core.util.StrUtil;
import com.acpiot.qgdw13761.api.dto.AddDeviceReqDto;
import com.acpiot.qgdw13761.api.dto.BatchAddDeviceReqDto;
import com.acpiot.qgdw13761.api.exception.ApiException;
import com.acpiot.qgdw13761.api.service.DeviceApiService;
import com.acpiot.qgdw13761.data.domain.Customer;
import com.acpiot.qgdw13761.data.domain.Device;
import com.acpiot.qgdw13761.data.domain.Device_;
import com.acpiot.qgdw13761.data.event.DeviceSavedEvent;
import com.acpiot.qgdw13761.data.repository.CustomerRepository;
import com.acpiot.qgdw13761.data.repository.DeviceRepository;
import com.acpiot.qgdw13761.util.DeviceAddrUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static com.acpiot.qgdw13761.util.DeviceAddrUtil.checkAddDeviceReqDto;

/**
 * <AUTHOR>
 * @date 2023/7/7 14:39
 */
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class DeviceApiServiceImpl implements DeviceApiService {

    private final CustomerRepository customerRepository;
    private final DeviceRepository deviceRepository;
    private final ApplicationContext applicationContext;

    @Override
    public Device addDevice(AddDeviceReqDto reqDto, String tenantId) {
        checkAddDeviceReqDto(reqDto);

        Customer customer = customerRepository.findByTopicName(tenantId)
                .orElseThrow(() -> new ApiException(400, "客户 {} 不存在", tenantId));

        Device device = new Device();
        device.setUid(reqDto.getUid());
        device.setMeterCode(reqDto.getMeterCode());
        device.setTenantId(tenantId);

        if (!deviceRepository.isUnique(device, Device_.uid)) {
            throw new ApiException(400, "设备编号 {} 重复", reqDto.getUid());
        }

        deviceRepository.save(device);

        applicationContext.publishEvent(new DeviceSavedEvent(customer, List.of(device)));
        return device;
    }

    @Override
    public List<Device> batchAddDevices(BatchAddDeviceReqDto reqDto, String tenantId) {
        List<AddDeviceReqDto> dtoList = reqDto.getDevices();
        dtoList.forEach(DeviceAddrUtil::checkAddDeviceReqDto);

        Customer customer = customerRepository.findByTopicName(tenantId)
                .orElseThrow(() -> new ApiException(400, "客户 {} 不存在", tenantId));

        List<Device> devices = dtoList
                .stream()
                .map(dto -> {
                    Device device = new Device();
                    device.setUid(dto.getUid());
                    device.setMeterCode(dto.getMeterCode());
                    device.setTenantId(tenantId);
                    return device;
                })
                .toList();

        List<Device> existsDevices = deviceRepository.findByUidIn(devices.stream().map(Device::getUid).toList());
        if (!existsDevices.isEmpty()) {
            String existsCodes = dtoList.stream()
                    .filter(dto -> existsDevices.stream().anyMatch(device -> device.getUid().equals(dto.getUid())))
                    .map(dto -> StrUtil.isNotBlank(dto.getMeterCode()) ? dto.getMeterCode() : dto.getUid())
                    .collect(Collectors.joining(","));
            throw new ApiException(400, "设备编号或换算地址重复: {}", existsCodes);
        }

        deviceRepository.saveAll(devices);
        applicationContext.publishEvent(new DeviceSavedEvent(customer, devices));
        return devices;
    }

    @Override
    public void deleteDevices(Collection<String> uidColl) {
        deviceRepository.deleteByUidIn(uidColl);
    }

}
