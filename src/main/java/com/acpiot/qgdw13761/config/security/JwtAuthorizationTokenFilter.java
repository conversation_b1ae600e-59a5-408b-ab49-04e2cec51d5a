package com.acpiot.qgdw13761.config.security;

import com.acpiot.qgdw13761.admin.service.CustomerService;
import com.acpiot.qgdw13761.config.security.jwt.JwtTokenProvider;
import com.acpiot.qgdw13761.config.security.jwt.PayloadDto;
import com.acpiot.qgdw13761.data.config.TenantIdResolver;
import com.acpiot.qgdw13761.data.domain.Customer;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.security.web.servlet.util.matcher.PathPatternRequestMatcher;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Arrays;

import static com.acpiot.qgdw13761.config.security.SecurityConstant.TOKEN_HEADER;
import static com.acpiot.qgdw13761.config.security.SecurityConstant.TOKEN_PREFIX;

/**
 * Created by moxin on 2019-11-01-0001
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Component
public class JwtAuthorizationTokenFilter extends OncePerRequestFilter {

    private final JwtTokenProvider jwtTokenProvider;
    private final CustomerService customerService;
    private final TenantIdResolver tenantIdResolver;

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request,
                                    @NonNull HttpServletResponse response,
                                    @NonNull FilterChain filterChain) throws ServletException, IOException {
        boolean matches = Arrays.stream(new String[]{"/api/**"})
                .anyMatch(url -> PathPatternRequestMatcher.withDefaults().matcher(url).matches(request));
        if (matches) {
            String authHeader = request.getHeader(TOKEN_HEADER);
            if (authHeader != null
                    && authHeader.startsWith(TOKEN_PREFIX)) {
                String authToken = authHeader.substring(TOKEN_PREFIX.length());
                PayloadDto payloadDto = jwtTokenProvider.verifyToken(authToken);
                if (payloadDto != null) {
                    Customer customer = customerService.getCustomerByName(payloadDto.getUsername());
                    if (!customer.isDisabled()
                            && customer.getToken().equals(authToken)) {
                        String tenantId = customer.getTopicName();
                        CustomerUserDetails user = new CustomerUserDetails(customer);
                        UsernamePasswordAuthenticationToken authentication =
                                new UsernamePasswordAuthenticationToken(user, user.getPassword(), user.getAuthorities());
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                        tenantIdResolver.setCurrentTenant(tenantId);
                    }
                }
            }
        }
        filterChain.doFilter(request, response);
    }
}
