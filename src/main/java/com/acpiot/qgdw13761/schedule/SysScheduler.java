package com.acpiot.qgdw13761.schedule;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.acpiot.qgdw13761.admin.service.LogService;
import com.acpiot.qgdw13761.admin.service.ReportRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/7/14 15:35
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SysScheduler {

    private final LogService logService;
    private final ReportRecordService reportRecordService;

    @Async
    @Scheduled(cron = "0 0 0 * * ?")
    public void clearData() {
        TimeInterval timer = DateUtil.timer();
        log.info("开始删除历史数据");
        log.info(Thread.currentThread().toString());

        DateTime lastMonth = DateUtil.beginOfDay(DateUtil.date())
                .offset(DateField.MONTH, -1);
        logService.clearLogsBefore(lastMonth);
        reportRecordService.clearRecordBefore(lastMonth);

        log.info("删除历史数据完成，耗时：{}", timer.intervalPretty());
    }

}
