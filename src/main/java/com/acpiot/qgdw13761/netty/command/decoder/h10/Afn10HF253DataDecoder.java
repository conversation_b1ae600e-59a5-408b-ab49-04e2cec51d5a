package com.acpiot.qgdw13761.netty.command.decoder.h10;

import com.acpiot.qgdw13761.netty.command.decoder.DataDecoder;
import com.acpiot.qgdw13761.netty.command.decoder.h09.Afn09HF1DataDecoder;
import com.acpiot.qgdw13761.netty.command.decoder.h0c.Afn0CHF129DataDecoder;
import com.acpiot.qgdw13761.netty.command.dto.CardInfo;
import com.acpiot.qgdw13761.netty.command.dto.h09.Afn09HF1Data;
import com.acpiot.qgdw13761.netty.command.dto.h0c.Afn0CHF129Data;
import com.acpiot.qgdw13761.netty.command.dto.h10.Afn10HF253Data;
import com.acpiot.qgdw13761.netty.util.ProtocolByteBuf;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.stream.IntStream;

/**
 * Afn10HF253响应数据解析，航天中电定制
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/16 16:27
 */
@Component
@RequiredArgsConstructor
public class Afn10HF253DataDecoder implements DataDecoder<Afn10HF253Data> {

    private static final String INVALID_METER_CODE = "EEEEEEEEEEEE";
    private static final byte[] INVALID_BYTES_42 = new byte[42];

    private final Afn09HF1DataDecoder afn09HF1DataDecoder;
    private final Afn0CHF129DataDecoder afn0CHF129DataDecoder;

    @Override
    public Afn10HF253Data decode(ProtocolByteBuf buf) {
        // 端口，1个字节
        buf.readByte();
        // 数据内容长度，2个字节
        buf.readUnsignedShort();
        // 封装响应内容
        Afn10HF253Data data = new Afn10HF253Data();
        // flash 状态
        data.setFlash(buf.readIntLE() == 0);
        // flash 大小
        data.setFlashSize((int) buf.readUnsignedIntLE());
        // MAC地址
        data.setBleMac(buf.readMac());
        // IMEI
        data.setImei(buf.readAsciiStr(15));
        // 电表地址
        String meterCode = buf.readHexStrLE(6);
        boolean invalidCode = INVALID_METER_CODE.equalsIgnoreCase(meterCode);

        if (invalidCode) {
            buf.readBytes(INVALID_BYTES_42);
        } else {
            data.setMeterCode(meterCode);
            // 冻结日期
            data.setFreezeDate(buf.readHexStrLE(3));
            // 终端抄表时间
            data.setMeteringTime(buf.readHexStrLE(5));
            // 费率数
            int rateNum = buf.readByte();
            data.setRateNum(rateNum);

            // 日冻结正向有功总电能示值 + 费率值
            BigDecimal[] dayFrozenValues = IntStream.rangeClosed(0, rateNum)
                    .mapToObj(i -> buf.readBCDNumberLE(5, 4))
                    .toArray(BigDecimal[]::new);
            data.setDayFrozenValues(dayFrozenValues);

            // cat1模块时间
            data.setCat1ModuleTime(buf.readHexStrLE(6));
            // 电表运行状态字3
            int runState3 = buf.readUnsignedShortLE();
            data.setRunState3(runState3);
        }

        // 读4G信号数据
        int rsrp = buf.readIntLE();
        int snr = buf.readIntLE();
        int cellId = buf.readIntLE();
        data.setRsrp(rsrp);
        data.setSnr(snr);
        data.setCellId(cellId);
        // 读版本
        Afn09HF1Data afn09HF1Data = afn09HF1DataDecoder.decode(buf);
        data.setAfn09HF1Data(afn09HF1Data);

        // 兼容处理
        if (buf.isReadable(75)) {
            data.setCardInfo(new CardInfo(buf.readAsciiStr(40), buf.readAsciiStr(15), buf.readAsciiStr(20)));

            // 兼容处理，协议数据扩增
            if (buf.isReadable()) {
                Afn0CHF129Data afn0CHF129Data = afn0CHF129DataDecoder.decode(buf);
                data.setAfn0CHF129Data(afn0CHF129Data);
            }
        }

        return data;
    }
}
