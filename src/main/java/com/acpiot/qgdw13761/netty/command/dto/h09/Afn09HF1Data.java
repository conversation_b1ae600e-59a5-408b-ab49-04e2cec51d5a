package com.acpiot.qgdw13761.netty.command.dto.h09;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR> Email: <a href="mailto:<EMAIL>"><EMAIL></a>
 * @since 2022-12-22-0022 11:42
 */
@Data
public class Afn09HF1Data {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("ddMMyy");

    private String factoryCode;

    private String deviceNo;

    private String softwareVer;

    private String softwareReleaseDate;

    private String capacityInfoCode;

    private String protocolVer;

    private String hardwareVer;

    private String hardwareReleaseDate;

    public String getFormattedVer() {
        return StrUtil.format("{}-{}-{}-{}-{}-{}-{}-{}",
                factoryCode, deviceNo, softwareVer, softwareReleaseDate,
                capacityInfoCode, protocolVer, hardwareVer, hardwareReleaseDate
        );
    }
}
