package com.acpiot.qgdw13761.netty.command.dto;

import com.acpiot.qgdw13761.netty.PortType;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * The type Command param.
 *
 * @param <T> the type parameter
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CommandParam<T> {

    public static CommandParam<Void> of(String sessionId) {
        return new CommandParam<>(sessionId, null, 0);
    }

    public static <T> CommandParam<T> of(String sessionId, T data) {
        return new CommandParam<>(sessionId, data, 0);
    }

    public static <T> CommandParam<T> of(String sessionId, T data, int retryNum) {
        return new CommandParam<>(sessionId, data, retryNum);
    }

    public static CommandParam<Void> of(PortType portType, String channelId, String uid) {
        return new CommandParam<>(portType, channelId, uid, null, 0);
    }

    public static <T> CommandParam<T> of(PortType portType, String channelId, String uid, T data) {
        return new CommandParam<>(portType, channelId, uid, data, 0);
    }

    public static <T> CommandParam<T> of(PortType portType, String channelId, String uid, T data, int retryNum) {
        return new CommandParam<>(portType, channelId, uid, data, retryNum);
    }

    private String sessionId;

    private PortType portType;

    private String channelId;

    private String uid;

    private T data;

    private int retryNum;

    public CommandParam(String sessionId, T data, int retryNum) {
        this.sessionId = sessionId;
        this.data = data;
        this.retryNum = retryNum;
    }

    public CommandParam(PortType portType, String channelId, String uid, T data, int retryNum) {
        this.portType = portType;
        this.channelId = channelId;
        this.uid = uid;
        this.data = data;
        this.retryNum = retryNum;
    }
}
