package com.acpiot.qgdw13761.netty.command.afncommand.dataunit.h10;

import com.acpiot.qgdw13761.netty.protocol.dataunit.DataUnit;

/**
 * 读取自定义设备参数
 * Created by moxin on 2023/9/21T15:57
 *
 * <AUTHOR>
 */
public enum Afn10HF251DataUnit implements DataUnit {

    /**
     * Instance afn 10 hf 251 data unit.
     */
    INSTANCE;

    @Override
    public int getPn() {
        return 0;
    }

    @Override
    public int getFn() {
        return 251;
    }

    @Override
    public String getDesc() {
        return "读取自定义设备参数";
    }

}
