package com.acpiot.qgdw13761.netty.util;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.HexUtil;
import com.acpiot.qgdw13761.netty.command.dto.h04.IpPort;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

import java.math.BigDecimal;
import java.time.LocalDate;

import static cn.hutool.core.util.HexUtil.encodeHexStr;
import static com.acpiot.qgdw13761.netty.protocol.utils.DF3761Utils.*;

/**
 * <AUTHOR> Email: <a href="mailto:<EMAIL>"><EMAIL></a>
 * @since 2022-12-21-0021 16:52
 */
public class ProtocolByteBuf extends ByteBufWrapper {

    public static ProtocolByteBuf buffer(int initialCapacity) {
        return new ProtocolByteBuf(Unpooled.buffer(initialCapacity));
    }

    public static ProtocolByteBuf wrappedBuffer(byte[] arrays) {
        return new ProtocolByteBuf(Unpooled.wrappedBuffer(arrays));
    }

    public static ProtocolByteBuf wrappedBuffer(ByteBuf buf) {
        return new ProtocolByteBuf(buf);
    }

    protected ProtocolByteBuf(ByteBuf buf) {
        super(buf);
    }

    public int readPn() {
        byte[] bytes = readBytesExt(2);
        return bytes2pn(bytes);
    }

    public int readFn() {
        byte[] bytes = readBytesExt(2);
        return bytes2fn(bytes);
    }

    public void writeIp(String ip) {
        writeBytes(ip2Bytes(ip));
    }

    public String readIp() {
        byte[] bytes = readBytesExt(4);
        return bytes2Ip(bytes);
    }

    public IpPort readIpPort() {
        return new IpPort(readIp(), readUnsignedShortLE());
    }

    public BigDecimal readBCDNumber(int byteLen, int scale) {
        byte[] bytes = readBytesExt(byteLen);
        if (isInvalid(bytes)) {
            return null;
        }
        String val = encodeHexStr(bytes, false);
        return new BigDecimal(val).movePointLeft(scale);
    }

    public BigDecimal readBCDNumberLE(int byteLen, int scale) {
        byte[] bytes = readBytesExtLE(byteLen);
        if (isInvalid(bytes)) {
            return null;
        }
        String val = encodeHexStr(bytes, false);
        return new BigDecimal(val).movePointLeft(scale);
    }

    public BigDecimal readA5() {
        // 读取2个字节，小端
        byte[] bytes = readBytesExtLE(2);
        if (isInvalid(bytes)) {
            return null;
        }
        // 从高字节D7比特位解析数值符号位，0-正数，1-负数
        boolean isNegative = (bytes[0] & 0x80) == 0x80;
        // 将高字节去除符号位之后，计算数值
        bytes[0] = (byte) (bytes[0] & 0x7F);
        BigDecimal val = new BigDecimal(HexUtil.encodeHexStr(bytes, false)).movePointLeft(1);
        return isNegative ? BigDecimal.ZERO.subtract(val) : val;
    }

    public BigDecimal readA7() {
        byte[] bytes = readBytesExtLE(2);
        if (isInvalid(bytes)) {
            return null;
        }
        String hexStr = encodeHexStr(bytes, false);
        return new BigDecimal(hexStr).movePointLeft(1);
    }

    public BigDecimal readA9() {
        // 读取3个字节，小端
        byte[] bytes = readBytesExtLE(3);
        if (isInvalid(bytes)) {
            return null;
        }
        // 从高字节D7比特位解析数值符号位，0-正数，1-负数
        boolean isNegative = (bytes[0] & 0x80) == 0x80;
        // 将高字节去除符号位之后，计算数值
        bytes[0] = (byte) (bytes[0] & 0x7F);
        BigDecimal val = new BigDecimal(HexUtil.encodeHexStr(bytes, false)).movePointLeft(4);
        return isNegative ? BigDecimal.ZERO.subtract(val) : val;
    }

    public BigDecimal readA14() {
        // 读取5个字节，小端
        byte[] bytes = readBytesExtLE(5);
        if (isInvalid(bytes)) {
            return null;
        }
        return new BigDecimal(HexUtil.encodeHexStr(bytes, false)).movePointLeft(4);
    }

    /**
     * 格式: yyMMddHHmm
     *
     * @return
     */
    public String readA15Str() {
        return readHexStrLE(5);
    }

    public LocalDate readA20() {
        byte[] bytes = readBytesExt(3);
        if (isInvalid(bytes)) {
            return null;
        }
        String hexStr = encodeHexStr(bytes, false);
        return LocalDateTimeUtil.parseDate(hexStr, "ddMMyy");
    }

    public String readA20Str() {
        return readHexStrLE(3);
    }

    public BigDecimal readA25() {
        return readA9();
    }

    private boolean isInvalid(byte[] bytes) {
        for (byte b : bytes) {
            if ((b & 0xFF) == 0xFF || (b & 0xFF) == 0xEE) {
                return true;
            }
        }
        return false;
    }

    public String readMac() {
        byte[] bytes = readBytesExt(6);
        return String.format("%02X:%02X:%02X:%02X:%02X:%02X",
                bytes[5], bytes[4], bytes[3], bytes[2], bytes[1], bytes[0]);
    }
}
