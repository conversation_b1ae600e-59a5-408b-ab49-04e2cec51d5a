package com.acpiot.qgdw13761.netty.event;

import com.acpiot.qgdw13761.netty.PortType;
import com.acpiot.qgdw13761.netty.channel.DeviceSession;
import com.acpiot.qgdw13761.netty.command.dto.AfnFnData;

/**
 * 数据上报事件
 *
 * @param sessionId
 * @param uid
 * @param data
 * <AUTHOR>
 */
public record DataReportEvent(String sessionId, PortType portType, String uid, AfnFnData data) {

    public static DataReportEvent of(DeviceSession session, AfnFnData data) {
        return new DataReportEvent(session.getSessionId(), session.getPortType(), session.getUid(), data);
    }

}
