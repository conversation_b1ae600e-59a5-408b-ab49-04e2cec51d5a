package com.acpiot.qgdw13761.netty.protocol.cnst;

/**
 * 应用层功能码定义
 * Created by xin on 2015-10-23-0023.
 */
public class AFN {
    /**
     * 确认/否认
     */
    public static final byte H00 = 0x0;
    /**
     * 复位
     */
    public static final byte H01 = 0x01;
    /**
     * 链路接口检测
     */
    public static final byte H02 = 0x02;
    /**
     * 中继站命令
     */
    public static final byte H03 = 0x03;
    /**
     * 设置参数
     */
    public static final byte H04 = 0x04;
    /**
     * 控制命令
     */
    public static final byte H05 = 0x05;
    /**
     * 身份认证及密钥协商
     */
    public static final byte H06 = 0x06;
    /**
     * 备用
     */
    public static final byte H07 = 0x07;
    /**
     * 请求被级联终端主动上报
     */
    public static final byte H08 = 0x08;
    /**
     * 请求终端配置
     */
    public static final byte H09 = 0x09;
    /**
     * 查询参数
     */
    public static final byte H0A = 0x0A;
    /**
     * 请求任务数据
     */
    public static final byte H0B = 0x0B;
    /**
     * 请求1类数据（实时数据）
     */
    public static final byte H0C = 0x0C;
    /**
     * 请求2类数据（历史数据）
     */
    public static final byte H0D = 0x0D;
    /**
     * 请求3类数据（事件数据）
     */
    public static final byte H0E = 0x0E;
    /**
     * 文件传输
     */
    public static final byte H0F = 0x0F;
    /**
     * 数据转发，用于透抄电表。
     */
    public static final byte H10 = 0x10;

    /**
     * 判断指定该AFN的数据帧是否要包含密码
     *
     * @param afn
     * @return
     */
    public static boolean containsPW(int afn) {
        return afn == H04
                || afn == H05
                || afn == H06
                || afn == H0F
                || afn == H10;
    }
}
