<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/layout}">
<head>
  <meta charset="UTF-8">
  <title>新建后台管理员</title>
  <link rel="stylesheet" th:href="@{/webjars/select2/css/select2.min.css}">
  <link rel="stylesheet" th:href="@{/css/ly-select2.css}">
  <link rel="stylesheet" th:href="@{/css/reset-style.css}">
</head>
<body>

<th:block layout:fragment="page-content">
  <div class="row">
    <div class="col-lg-12">
      <div class="card table-card-30">
        <div class="card-header"><h4>新建后台管理员</h4></div>
        <div class="card-body" style="overflow: auto;">
          <form id="form" th:action="@{/sys/user/add}" method="post" th:object="${user}">
            <div style="overflow: auto">
              <div class="form-group line-group col-sm-12">
                <label for="name"><span class="text-danger">*</span>&nbsp;账号</label>
                <input class="form-control" type="text" th:field="*{name}" placeholder="账号" required maxlength="32">
                <label th:if="${#fields.hasErrors('name')}" th:errors="*{name}" class="text-danger"></label>
              </div>
            </div>
            <div class="form-group line-group col-sm-6">
              <label for="password"><span class="text-danger">*</span>&nbsp;密码</label>
              <input class="form-control" type="password" th:field="*{password}" placeholder="密码" required minlength="6" maxlength="18" autocomplete="new-password">
              <label th:if="${#fields.hasErrors('password')}" th:errors="*{password}" class="text-danger"></label>
            </div>
            <div class="form-group line-group col-sm-6">
              <label for="confirmPassword"><span class="text-danger">*</span>&nbsp;确认密码</label>
              <input class="form-control" type="password" id="confirmPassword" placeholder="确认密码" required minlength="6" maxlength="18"
                     data-rule-equalTo="#password" data-msg-equalTo="密码输入不一致">
            </div>
            <div class="form-group line-group col-md-6">
              <label for="role.id"><span class="text-danger">*</span>&nbsp;系统角色<small class="help-block" style="display: inline-block; margin: 0;"></small></label>
              <select style="width: 100%;" class="form-control select2" th:field="*{role.id}" required>
                <option value="">请选择</option>
                <option th:each="role : ${roles}" th:text="${role.name}" th:value="${role.id}"></option>
              </select>
              <label th:if="${#fields.hasErrors('role.id')}" th:errors="*{role.id}" class="text-danger"></label>
            </div>
            <div class="form-group line-group col-md-6">
              <label for="tenant.id"><span class="text-danger">*</span>&nbsp;所属客户<small class="help-block" style="display: inline-block; margin: 0;"></small></label>
              <select style="width: 100%;" class="form-control select2" th:field="*{tenant.id}" required>
                <option value="">请选择</option>
                <option th:each="tenant : ${tenants}" th:text="${tenant.name}" th:value="${tenant.id}"></option>
              </select>
              <label th:if="${#fields.hasErrors('tenant.id')}" th:errors="*{tenant.id}" class="text-danger"></label>
            </div>
            <div class="form-group line-group col-sm-12">
              <label for="remark">备注</label>
              <textarea rows="5" class="form-control" th:field="*{remark}" placeholder="请输入备注" maxlength="255">备注</textarea>
              <label th:if="${#fields.hasErrors('remark')}" th:errors="*{remark}" class="text-danger"></label>
            </div>
            <div class="col-sm-12">
              <th:block th:replace="fragments/form-footer :: submit-back-footer"/>
            </div>
          </form>

        </div>
      </div>
    </div>

  </div>
</th:block>

<th:block layout:fragment="page-script">
  <script th:src="@{/webjars/select2/js/select2.min.js}"></script>
  <script th:src="@{/webjars/select2/js/i18n/zh-CN.js}"></script>
  <script th:src="@{/webjars/jquery-validation/dist/jquery.validate.min.js}"></script>
  <script th:src="@{/webjars/jquery-validation/dist/localization/messages_zh.min.js}"></script>
  <script th:src="@{/js/common/jq-validation-bs.js}"></script>
  <script th:src="@{/js/sys/user/add.js}"></script>
</th:block>

</body>
</html>
