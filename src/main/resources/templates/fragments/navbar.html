<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
  <meta charset="UTF-8">
  <title>导航条</title>
</head>
<body>

<nav class="navbar navbar-expand-lg navbar-light bg-light" th:fragment="navbar(item)">
  <div class="collapse navbar-collapse">
    <ul class="navbar-nav mr-auto">
      <li class="nav-item" th:classappend="${item} eq 'customer' ? 'active' : ''" sec:authorize="hasRole('ROLE_ADMIN')">
        <a class="nav-link" th:href="@{/admin/index}">授权管理</a>
      </li>
      <li class="nav-item" th:classappend="${item} eq 'device' ? 'active' : ''" sec:authorize="hasRole('ROLE_ADMIN')">
        <a class="nav-link" th:href="@{/admin/device/list}">档案管理</a>
      </li>
      <li class="nav-item" th:classappend="${item} eq 'report' ? 'active' : ''" sec:authorize="hasAnyRole('ROLE_ADMIN', 'ROLE_MAINTAIN')">
        <a class="nav-link" th:href="@{/admin/report/list}">上报记录</a>
      </li>
      <li class="nav-item" th:classappend="${item} eq 'cmd' ? 'active' : ''" sec:authorize="hasAnyRole('ROLE_ADMIN', 'ROLE_MAINTAIN')">
        <a class="nav-link" th:href="@{/admin/device/cmd/list}">命令查询</a>
      </li>
      <li class="nav-item" th:classappend="${item} eq 'alarm' ? 'active' : ''" sec:authorize="hasAnyRole('ROLE_ADMIN', 'ROLE_MAINTAIN')">
        <a class="nav-link" th:href="@{/admin/alarm/list}">告警记录</a>
      </li>
      <li class="nav-item" th:classappend="${item} eq 'log-info' ? 'active' : ''" sec:authorize="hasRole('ROLE_ADMIN')">
        <a class="nav-link" th:href="@{/admin/log/info/list}">操作日志</a>
      </li>
      <li class="nav-item" th:classappend="${item} eq 'log-error' ? 'active' : ''" sec:authorize="hasRole('ROLE_ADMIN')">
        <a class="nav-link" th:href="@{/admin/log/error/list}">错误日志</a>
      </li>
      <li class="nav-item" th:classappend="${item} eq 'upgrade-file' ? 'active' : ''" sec:authorize="hasRole('ROLE_ADMIN')">
        <a class="nav-link" th:href="@{/admin/upgrade/file/list}">升级文件管理</a>
      </li>
      <li class="nav-item" th:classappend="${item} eq 'upgrade' ? 'active' : ''" sec:authorize="hasRole('ROLE_ADMIN')">
        <a class="nav-link" th:href="@{/admin/upgrade/list}">升级管理</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" th:href="@{/}" target="_blank">跳转API文档</a>
      </li>
    </ul>
  </div>
</nav>

</body>
</html>
