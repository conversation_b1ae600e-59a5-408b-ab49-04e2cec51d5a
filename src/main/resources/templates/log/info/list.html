<!doctype html>
<html lang="zh-CN"
      xmlns:th="http://www.thymeleaf.org">
<head>
  <!-- 必须的 meta 标签 -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <title>操作日志查询</title>
  <link rel="icon" th:href="@{/favicon.ico}" type="image/ico"
        href="../../../static/favicon.ico">

  <!-- Bootstrap 的 CSS 文件 -->
  <link rel="stylesheet" th:href="@{/webjars/bootstrap/css/bootstrap.min.css}"
        href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

  <!-- Bootstrap Icons -->
  <link rel="stylesheet" th:href="@{/webjars/bootstrap-icons/font/bootstrap-icons.css}"
        href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.4.1/font/bootstrap-icons.css">

  <link rel="stylesheet" th:href="@{/webjars/font-awesome/css/all.min.css}"
        href="https://use.fontawesome.com/releases/v5.6.3/css/all.css">

  <!-- Bootstrap Table -->
  <link rel="stylesheet" href="https://unpkg.com/bootstrap-table@1.18.3/dist/bootstrap-table.min.css"
        th:href="@{/webjars/bootstrap-table/dist/bootstrap-table.min.css}">
</head>
<body>

<th:block th:replace="~{fragments/navbar :: navbar('log-info')}"/>

<div class="container-fluid">

  <div class="row">
    <div class="col-lg">

      <div id="toolbar" class="btn-toolbar" role="toolbar">
        <form id="tableFilter" class="form-inline btn-group mr-2" role="form" onsubmit="return false;">
          <div class="input-group mr-2">
            <div class="input-group-prepend">
              <span class="input-group-text">日志描述</span>
            </div>
            <input type="text" class="form-control table-filter" placeholder="请输入日志描述"
                   data-filter="description" data-operator="like">
          </div>
          <div class="input-group mr-2">
            <div class="input-group-prepend">
              <span class="input-group-text">操作人</span>
            </div>
            <input type="text" class="form-control table-filter" placeholder="请输入创建者"
                   data-filter="createdBy" data-operator="eq">
          </div>
        </form>
      </div>
      <table id="table"
             data-toolbar="#toolbar"
             data-url="page"
             data-sort-order="desc"
             data-sort-name="createdDate"
             data-id-field="id"
             data-unique-id="id"
             data-cache="false"
             data-method="post"
             data-show-refresh="true"
             data-pagination="true"
             data-side-pagination="server"
             data-data-field="content"
             data-total-field="totalElements"
             data-page-size="10"
             data-detail-view="true"
             data-detail-formatter="detailFormatter">
        <thead>
        <tr>
          <th data-field="createdBy" data-sortable="true" data-width="120">操作人</th>
          <th data-field="ip" data-width="120">IP</th>
          <th data-field="address" data-width="150">IP来源</th>
          <th data-field="description" data-width="150">日志描述</th>
          <th data-field="userAgent" data-class="wrap">用户代理</th>
          <th data-field="costTime" data-width="100">请求耗时(毫秒)</th>
          <th data-field="createdDate" data-sortable="true" data-align="center" data-width="190">创建时间</th>
        </tr>
        </thead>
      </table>
    </div>
  </div>

</div>

<script th:src="@{/webjars/jquery/dist/jquery.min.js}"
    src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
<script th:src="@{/webjars/bootstrap/js/bootstrap.bundle.min.js}"
    src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

<script th:src="@{/js/common/global.js}"
        src="../../../static/js/common/global.js"></script>

<script th:src="@{/webjars/jsrender/jsrender.min.js}"
        src="https://cdnjs.cloudflare.com/ajax/libs/jsrender/1.0.11/jsrender.min.js"></script>
<script id="detailViewTmpl" type="text/x-jsrender">
  <table class="table">
    <tbody>
      <tr>
        <th style="width: 100px; border-width: 0;">请求方法:</th>
        <td style="border-width: 0;">{{:methodName}}</td>
      </tr>
      <tr>
        <th style="border-width: 0;">请求参数:</th>
        <td style="border-width: 0;">{{:args}}</td>
      </tr>
    </tbody>
  </table>
</script>

<!-- Bootstrap Table -->
<script th:src="@{/webjars/bootstrap-table/dist/bootstrap-table.min.js}"
        src="https://unpkg.com/bootstrap-table@1.18.3/dist/bootstrap-table.min.js"></script>
<script th:src="@{/webjars/bootstrap-table/dist/locale/bootstrap-table-zh-CN.min.js}"
        src="https://unpkg.com/bootstrap-table@1.18.3/dist/locale/bootstrap-table-zh-CN.min.js"></script>
<script th:src="@{/js/common/bootstrap-table-custom.js}"
        src="../../../static/js/common/bootstrap-table-custom.js"></script>
<script th:src="@{/js/common/query-filter.js}"
        src="../../../static/js/common/query-filter.js"></script>

<script th:src="@{/js/log/info/list.js}"
        src="../../../static/js/log/info/list.js"></script>

</body>
</html>
