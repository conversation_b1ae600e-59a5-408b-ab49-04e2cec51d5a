var $table = $('#table');

function operateFormatter(value, row, index) {
    var btnArray = ['<div class="btn-group" role="group">'];
    btnArray.push('<button type="button" class="btn btn-light btn-sm remove">删除</button>');
    btnArray.push('<button type="button" class="btn btn-light btn-sm download">下载</button>');
    btnArray.push('</div>');
    return btnArray.join('');
}

function fileTypeFormatter(value, row, index) {
    switch (value) {
        case '1':
            return '终端升级文件';
        case '2':
            return '远程（上行）通信模块升级文件';
        case '3':
            return '本地通信模块升级文件';
        case '4':
            return '采集器升级的采集器地址文件';
        case '5':
            return '采集器升级的采集器程序文件';
        case '6':
            return '采集器通信模块升级的地址文件';
        case '7':
            return '采集器通信模块升级的程序文件';
        default:
            return value;
    }
}

window.operateEvents = {
    'click .remove': function (e, value, row, index) {
        $table.deleteByUniqueId('id', row.id);
    },
    'click .download': function (e, value, row, index) {
        $table.optByUniqueId('id', row.id, 'download');
    }
};

$(function () {
    $table.bootstrapTable();
});
