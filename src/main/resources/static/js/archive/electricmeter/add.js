$(function () {
  $("#form").validate();
  submitOnce();
  $('.select2').on('change', function () {
    $(this).valid();
  }).select2({
    language: 'zh-CN'
  });

  let $addrRule = $('#moduleInfo\\.addrRule');
  $addrRule.on('change', function () {
    changeState($(this).val() !== 'FIXED');
    calculateAndFillUid();
  });

  changeState($addrRule.val() !== 'FIXED');

  $('#meterCode').on('blur', function () {
    calculateAndFillUid();
  });
});

/**
 * 计算并填充UID
 */
function calculateAndFillUid() {
  let addrRule = $('#moduleInfo\\.addrRule').val();
  let meterCode = $('#meterCode').val();
  if (addrRule === 'FIXED' || meterCode.length !== 12) {
    return;
  }

  let a2;
  let sub = meterCode.substring(8);
  switch (addrRule) {
    case 'CODE_CALCULATE':
      a2 = parseInt(sub) + 0xA000;
      break;
    case 'CODE_CUT':
      a2 = parseInt(sub, 16);
      break;
    default:
      throw new Error('未知的地址规则');
  }
  let uid = meterCode.substring(4, 8) + padPre('' + a2, 5, '0');
  $('#moduleInfo\\.uid').val(uid);
}

function changeState(readonly) {
  $('#moduleInfo\\.uid').prop('readonly', readonly);
}
