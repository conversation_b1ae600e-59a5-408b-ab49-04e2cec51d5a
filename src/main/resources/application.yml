logging:
  config: classpath:logback-spring.xml
  file:
    path: logs
  level:
    root: info

server:
  port: 8080
  servlet:
    context-path: /
  shutdown: graceful

spring:
  application:
    name: large-screen
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  datasource:
    url: jdbc:h2:file:./db/${spring.application.name}.db
    username: su
    password: htzd123
  h2:
    console:
      enabled: true
      path: /h2
      settings:
        web-allow-others: true
  jpa:
    open-in-view: false
    generate-ddl: true
    hibernate:
      ddl-auto: update
    properties:
      # 配置开启关键字转义
      globally_quoted_identifiers: true
      globally_quoted_identifiers_skip_column_definitions: true
      jdbc:
        batch_size: 30
  web:
    resources:
      cache:
        cachecontrol:
          max-age: 30d
      chain:
        enabled: true
        compressed: true
        strategy:
          content:
            enabled: true
  threads:
    virtual:
      enabled: true
  profiles:
    active: dev

forest:
  log-response-content: true
  variables:
    erplusUrl: https://www.erplus.co

erplus:
  client-id: 2_3t7k61w8zz28wc00w0o0swgo0ko48w08c8csk4ss444os4ccgo
  client-secret: 6o596th97340soswgw8ssswk8gk8cwoo40c4wwccs4g4wswk8
  username: 18680341271
  password: 1fpfRUvosCwj6###PC

decorator:
  datasource:
    p6spy:
      enable-logging: true
      logging: slf4j

file:
  resources:
    download: classpath:download
