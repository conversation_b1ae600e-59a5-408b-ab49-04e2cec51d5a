package com.acpiot.cps.common.sms.adapter;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.cps.common.msg.MsgTaskService;
import com.acpiot.cps.common.sms.properties.SmsProperties;
import com.acpiot.cps.common.sms.service.SmsService;
import com.acpiot.cps.common.utils.JsonUtils;
import com.acpiot.cps.data.archive.entity.Customer;
import com.acpiot.cps.data.archive.entity.Room;
import com.acpiot.cps.data.archive.repository.RoomRepository;
import com.acpiot.cps.data.revenue.entity.mongo.MsgTask;
import com.acpiot.cps.data.sys.dto.SmsReceiver;
import com.acpiot.cps.data.sys.entity.SmsConfig;
import com.acpiot.cps.data.sys.repository.SmsConfigRepository;
import com.acpiot.cps.dto.SmsResult;
import com.acpiot.cps.provider.RevenueConfigProvider;
import com.acpiot.cps.provider.dto.WarningAmountDto;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;

import static com.acpiot.cps.constant.CommonConstant.DEFAULT_SEND_END_HOUR;
import static com.acpiot.cps.constant.CommonConstant.DEFAULT_SEND_START_HOUR;

/**
 * 短信发送适配器，用于组装短信参数并发送短信
 * Created by zc on 2021/5/26 16:09
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class SmsServiceAdapter {

    private final RoomRepository roomRepository;
    private final SmsConfigRepository smsConfigRepository;

    private final MsgTaskService msgTaskService;
    private final RevenueConfigProvider revenueConfigProvider;

    private final ApplicationContext applicationContext;

    private SmsService getSmsService(SmsProperties smsProperties) {
        String prefix;
        SmsConfig.SmsPlatform smsPlatform = smsProperties.getSmsPlatform();
        if (smsPlatform == SmsConfig.SmsPlatform.ALI_YUN) {
            prefix = "ali";
        } else if (smsPlatform == SmsConfig.SmsPlatform.MAS_TEMP) {
            prefix = "masTemp";
        } else {
            return null;
        }
        return applicationContext.getBean(String.format("%sSmsServiceImpl", prefix), SmsService.class);
    }


    /**
     * 发送短信并返回结果
     *
     * @param smsReceiver
     * @return
     */
    private SmsResult sendSmsExecutorFunction(SmsReceiver smsReceiver, BiFunction<SmsProperties, SmsService, SmsResult> smsSender) {
        // 获取短信平台配置
        Optional<SmsConfig> smsConfig = smsConfigRepository.findFirstByCompanyIdAndEnableIsTrue(smsReceiver.getSmsConfigCompanyId());
        // 判断是否开启短信功能
        if (smsConfig.isEmpty()) {
            return SmsResult.returnNotEnableError();
        }
        SmsProperties properties = new SmsProperties(smsConfig.get());
        // 获取对应的service服务
        SmsService smsService = getSmsService(properties);
        if (smsService == null) {
            return SmsResult.returnNotSupportError();
        }
        return smsSender.apply(properties, smsService);
    }

    /**
     * 发送短信不返回结果
     *
     * @param smsReceiver
     * @return
     */
    private void sendSmsExecutorConsumer(SmsReceiver smsReceiver, BiConsumer<SmsProperties, SmsService> smsSender) {
        // 获取短信平台配置
        Optional<SmsConfig> smsConfig = smsConfigRepository.findFirstByCompanyIdAndEnableIsTrue(smsReceiver.getSmsConfigCompanyId());
        // 判断是否开启短信功能
        if (smsConfig.isEmpty()) {
            return;
        }
        SmsProperties properties = new SmsProperties(smsConfig.get());
        // 获取对应的service服务
        SmsService smsService = getSmsService(properties);
        if (smsService == null) {
            return;
        }
        smsSender.accept(properties, smsService);
    }

    /**
     * 发送充值成功通知
     *
     * @param customer   住户
     * @param roomNumber 房号
     * @param amount     充值金额
     * @param balance    账户余额
     */
    public void rechargeSuccess(Customer customer, String roomNumber, BigDecimal amount, BigDecimal balance) {
        // 构建短信参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("roomNumber", roomNumber);
        paramMap.put("amount", amount.setScale(2, RoundingMode.HALF_UP).toString());
        paramMap.put("balance", balance.setScale(2, RoundingMode.HALF_UP).toString());
        // 发送短信
        SmsReceiver smsReceiver = new SmsReceiver(customer);
        sendSmsExecutorConsumer(smsReceiver, (smsProperties, smsService) -> smsService.rechargeSuccess(smsReceiver, paramMap));
    }

    /**
     * 开阀通知
     *
     * @param customer   住户
     * @param deviceNo   设备编号
     * @param roomId     房间id
     * @param roomNumber 房号
     * @param balance    可用余额
     */
    public void openValve(Customer customer, String deviceNo, Long roomId, String roomNumber, BigDecimal balance) {
        SmsReceiver smsReceiver = new SmsReceiver(customer);
        sendSmsExecutorConsumer(smsReceiver, (smsProperties, smsService) -> {
            // 未开启模板消息时，不处理后续逻辑
            if (StrUtil.isBlank(smsProperties.getOpenValve())) {
                return;
            }
            // 如果不在发送时间内，需要添加到消息任务中
            String balanceVal = balance.setScale(2, RoundingMode.HALF_UP).toString();
            if (unableSendByTime(smsProperties)) {
                // 将要发送的短信添加到发送任务表中
                Map<String, Object> content = MapUtil.<String, Object>builder()
                        .put("customerId", customer.getId())
                        .put("deviceNo", deviceNo)
                        .put("roomNumber", roomNumber)
                        .put("balance", balanceVal).map();
                MsgTask msgTask = buildMsgTask(customer, roomId, "openValveCode", "开阀通知", content);
                msgTaskService.saveMsgTask(msgTask);
            } else {
                // 构建短信参数
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("deviceNo", deviceNo);
                paramMap.put("roomNumber", roomNumber);
                paramMap.put("balance", balanceVal);
                smsService.openValve(smsReceiver, paramMap);
            }
        });
    }

    /**
     * 关阀通知
     *
     * @param customer   住户
     * @param deviceNo   设备编号
     * @param roomId     房间id
     * @param roomNumber 房号
     * @param balance    可用余额
     */
    public void closeValve(Customer customer, String deviceNo, Long roomId, String roomNumber, BigDecimal balance) {
        SmsReceiver smsReceiver = new SmsReceiver(customer);
        sendSmsExecutorConsumer(smsReceiver, (smsProperties, smsService) -> {
            // 未开启模板消息时，不处理后续逻辑
            if (StrUtil.isBlank(smsProperties.getCloseValve())) {
                return;
            }
            // 如果不在发送时间内，需要添加到消息任务中
            if (unableSendByTime(smsProperties)) {
                // 将要短信添加到发送任务表中
                Map<String, Object> content = MapUtil.<String, Object>builder()
                        .put("customerId", customer.getId())
                        .put("deviceNo", deviceNo)
                        .put("roomNumber", roomNumber)
                        .put("balance", balance).map();
                MsgTask msgTask = buildMsgTask(customer, roomId, "closeValveCode", "关阀通知", content);
                msgTaskService.saveMsgTask(msgTask);
            } else {
                // 构建短信参数
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("deviceNo", deviceNo);
                paramMap.put("roomNumber", roomNumber);
                paramMap.put("balance", balance.setScale(2, RoundingMode.HALF_UP).toString());
                smsService.closeValve(smsReceiver, paramMap);
            }
        });
    }

    /**
     * 余额预警通知
     *
     * @param customer       住户
     * @param roomId         房间id
     * @param balance        可用余额
     * @param balanceWarning 系统预警值
     */
    public void balanceWarnNotify(Customer customer, Long roomId, BigDecimal balance, BigDecimal balanceWarning) {
        // 查询房间，不存在房间则返回
        Optional<Room> roomOptional = roomRepository.findByRoomId(roomId);
        if (roomOptional.isEmpty()) {
            return;
        }
        Room room = roomOptional.get();
        SmsReceiver smsReceiver = new SmsReceiver(customer);
        sendSmsExecutorConsumer(smsReceiver, (smsProperties, smsService) -> {
            // 未开启模板消息时，不处理后续逻辑
            if (StrUtil.isBlank(smsProperties.getBalanceWarnNotify())) {
                return;
            }
            Optional<MsgTask> currentMonthTask = msgTaskService.findCurrentMonthBalanceWarnTask(roomId, MsgTask.MsgType.SMS);
            Map<String, Object> content = MapUtil.<String, Object>builder()
                    .put("customerId", customer.getId())
                    .put("roomNumber", room.getRoomNumber())
                    .put("balance", balance)
                    .put("balanceWarning", balanceWarning).map();
            // 如果不在发送时间内，需要添加到消息任务中
            if (unableSendByTime(smsProperties)) {
                // 当月已缓存过任务，更新短信内容
                if (currentMonthTask.isPresent()) {
                    msgTaskService.updateContentById(currentMonthTask.get().getId(), JsonUtils.writeValueAsString(content));
                } else {
                    // 将要短信添加到发送任务表中
                    MsgTask msgTask = buildMsgTask(customer, roomId, "balanceWarnNotifyCode", "余额预警通知", content);
                    msgTask.setSendNum(0);
                    msgTaskService.saveMsgTask(msgTask);
                }
            } else {
                // 判断当月是否已经缓存过该任务，若未缓存该任务，需要发送后创建一条发送次数为1、上次发送时间为当前时间的任务
                if (currentMonthTask.isEmpty()) {
                    // 将短信任务添加到发送任务表中，已发送1次
                    MsgTask msgTask = buildMsgTask(customer, roomId, "balanceWarnNotifyCode", "余额预警通知", content);
                    msgTask.setLastSendTime(new Date());
                    msgTask.setSendNum(1);
                    msgTaskService.saveMsgTask(msgTask);
                    this.sendBalanceWarnNotify(smsReceiver, room.getRoomNumber(), balance, balanceWarning);
                } else {
                    // 任务内容无论如何都得更新
                    msgTaskService.updateContentById(currentMonthTask.get().getId(), JsonUtils.writeValueAsString(content));
                    WarningAmountDto warningAmountDto = revenueConfigProvider.getWarningAmountDto(roomOptional.get().getId());
                    MsgTask msgTask = currentMonthTask.get();
                    // 没有营收配置或者配置了营收配置，但是没有配置次数和间隔天数，仅执行一次任务，删除该任务
                    if (warningAmountDto == null) {
                        if (msgTask.getSendNum() == 0) {
                            this.sendBalanceWarnNotify(smsReceiver, room.getRoomNumber(), balance, balanceWarning);
                            // 发送完成后，更新任务状态
                            msgTaskService.updateSendNumAndLastSendTimeById(msgTask.getId(), 1, new Date());
                        }
                        return;
                    }
                    // 否则取出当前任务中的最后执行时间，判断是否在允许执行的时间内
                    int currentSendNum = msgTask.getSendNum();
                    Date lastSendTime = msgTask.getLastSendTime();
                    if (lastSendTime != null) {
                        // 发送间隔时间
                        int warningAmountDayInterval = warningAmountDto.getWarningAmountDayInterval();
                        // 配置的最大发送次数
                        int warningAmountTimes = warningAmountDto.getWarningAmountTimes();
                        Date now = new Date();
                        long dayInterval = DateUtil.betweenDay(lastSendTime, now, true);
                        // 未满足发送条件，直接返回，不进行发送
                        if (dayInterval < warningAmountDayInterval || currentSendNum >= warningAmountTimes) {
                            return;
                        }
                    }
                    this.sendBalanceWarnNotify(smsReceiver, room.getRoomNumber(), balance, balanceWarning);
                    // 发送完成后，更新任务状态
                    msgTaskService.updateSendNumAndLastSendTimeById(msgTask.getId(), currentSendNum + 1, new Date());
                }
            }
        });
    }

    /**
     * 发送余额预警短信
     *
     * @param smsReceiver
     * @param roomNumber
     * @param balance
     * @param balanceWarning
     */
    private void sendBalanceWarnNotify(SmsReceiver smsReceiver, String roomNumber, BigDecimal balance, BigDecimal balanceWarning) {
        // 构建短信参数并发送
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("roomNumber", roomNumber);
        paramMap.put("balance", balance.setScale(2, RoundingMode.HALF_UP).toString());
        paramMap.put("balanceWarning", balanceWarning.setScale(2, RoundingMode.HALF_UP).toString());
        sendSmsExecutorConsumer(smsReceiver, (smsProperties, smsService) -> smsService.balanceWarnNotify(smsReceiver, paramMap));
    }

    /**
     * 发送注册验证码
     *
     * @param smsReceiver 用户
     * @param code        验证码
     * @return
     */
    public SmsResult registerValidateCode(SmsReceiver smsReceiver, String code) {
        // 构建短信参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("code", code);
        // 发送短信
        return sendSmsExecutorFunction(smsReceiver, (smsProperties, smsService) -> smsService.registerValidateCode(smsReceiver, paramMap));
    }

    /**
     * 发送登录验证码
     *
     * @param smsReceiver 用户
     * @param code        验证码
     * @return
     */
    public SmsResult loginValidateCode(SmsReceiver smsReceiver, String code) {
        // 构建短信参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("code", code);
        // 发送短信
        return sendSmsExecutorFunction(smsReceiver, (smsProperties, smsService) -> smsService.loginValidateCode(smsReceiver, paramMap));
    }

    /**
     * 发送修改密码验证码
     *
     * @param smsReceiver 用户
     * @param code        验证码
     * @return
     */
    public SmsResult updatePasswordCode(SmsReceiver smsReceiver, String code) {
        // 构建短信参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("code", code);
        // 发送短信
        return sendSmsExecutorFunction(smsReceiver, (smsProperties, smsService) -> smsService.updatePasswordCode(smsReceiver, paramMap));
    }

    /**
     * 发送找回密码验证码
     *
     * @param smsReceiver 用户
     * @param code        验证码
     * @return
     */
    public SmsResult forgetPasswordCode(SmsReceiver smsReceiver, String code) {
        // 构建短信参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("code", code);
        // 发送短信
        return sendSmsExecutorFunction(smsReceiver, (smsProperties, smsService) -> smsService.forgetPasswordCode(smsReceiver, paramMap));
    }

    /**
     * 发送注册成功通知
     *
     * @param name     公司名
     * @param username 用户名
     * @param password 密码
     */
    public SmsResult registerSuccess(SmsReceiver smsReceiver, String name, String username, String password) {
        // 构建短信参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("name", name);
        paramMap.put("username", username);
        paramMap.put("password", password);
        // 发送短信
        return sendSmsExecutorFunction(smsReceiver, (smsProperties, smsService) -> smsService.registerSuccess(smsReceiver, paramMap));
    }

    /**
     * 发送注册失败通知
     *
     * @param name   公司名
     */
    public SmsResult registerFail(SmsReceiver smsReceiver, String name) {
        // 构建短信参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("name", name);
        // 发送短信
        return sendSmsExecutorFunction(smsReceiver, (smsProperties, smsService) -> smsService.registerFail(smsReceiver, paramMap));
    }

    /**
     * 当前时间是否可以发送短信
     *
     * @return
     */
    private boolean unableSendByTime(SmsProperties smsProperties) {
        // 优先取住户自定义的发送时间段
        Integer sendStartHour = smsProperties.getSendStartHour();
        Integer sendEndHour = smsProperties.getSendEndHour();
        // 住户未自定义发送时间段，取系统默认值
        if (sendEndHour == null) {
            sendStartHour = DEFAULT_SEND_START_HOUR;
            sendEndHour = DEFAULT_SEND_END_HOUR;
        }
        // 判断当前时间是否在可发送短信的时间范围内
        LocalDateTime localDateTime = LocalDateTime.now();
        int hour = localDateTime.getHour();
        return hour < sendStartHour || hour > sendEndHour;
    }

    /**
     * 构建短信通知记录
     *
     * @param customer
     * @param roomId
     * @param msgCode
     * @param msgName
     * @param content
     * @return
     */
    private MsgTask buildMsgTask(Customer customer, Long roomId, String msgCode, String msgName, Map<String, Object> content) {
        MsgTask msgTask = new MsgTask();
        msgTask.setMsgType(MsgTask.MsgType.SMS);
        msgTask.setMsgCode(msgCode);
        msgTask.setMsgName(msgName);
        msgTask.setRoomId(roomId);
        msgTask.setParams(JsonUtils.writeValueAsString(content));
        msgTask.setCompanyId(customer.getCompanyId());
        return msgTask;
    }
}