package com.acpiot.cps.common.wxmp.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * 微信公众号模板消息参数：充值成功通知参数
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class RechargeSuccessMsg extends CompanyMsg {

    /**
     * 房间id
     */
    private Long roomId;

    /**
     * 房号
     */
    private String roomNumber;

    /**
     * 充值时间
     */
    private String rechargeTime;

    /**
     * 充值金额
     */
    private String amount;

    /**
     * 实际可用余额
     */
    private String realBalance;

    public RechargeSuccessMsg(String companyId) {
        super(companyId);
    }

    /**
     * 校验参数
     *
     * @return
     */
    @Override
    @JsonIgnore
    public boolean isValid() {
        return !StringUtils.isAnyBlank(roomNumber, rechargeTime, amount, realBalance);
    }
}
