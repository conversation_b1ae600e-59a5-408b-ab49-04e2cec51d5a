package com.acpiot.cps.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.CalendarUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.acpiot.cps.adapay.dto.PaymentEvent;
import com.acpiot.cps.adapay.dto.RefundEvent;
import com.acpiot.cps.adapay.provider.AdapayProvider;
import com.acpiot.cps.adapay.res.QueryPaymentRes;
import com.acpiot.cps.common.exception.BusinessException;
import com.acpiot.cps.common.jpa.entity.BaseAuditingCompanyEntity;
import com.acpiot.cps.common.utils.CodeUtils;
import com.acpiot.cps.common.utils.LocalDateTimeUtils;
import com.acpiot.cps.data.archive.entity.*;
import com.acpiot.cps.data.archive.repository.RoomRepository;
import com.acpiot.cps.data.archive.repository.WmWaterMeterRepository;
import com.acpiot.cps.data.monitor.entity.QWmDayFreezeData;
import com.acpiot.cps.data.revenue.dto.SxyPayProperties;
import com.acpiot.cps.data.revenue.entity.*;
import com.acpiot.cps.data.revenue.enums.ApplyStatus;
import com.acpiot.cps.data.revenue.repository.*;
import com.acpiot.cps.dto.OweCheckTiming;
import com.acpiot.cps.event.revenue.OweCheckEvent;
import com.acpiot.cps.event.revenue.RechargeSuccessEvent;
import com.acpiot.cps.event.revenue.WaterMeterMasterChildBillEvent;
import com.acpiot.cps.provider.RevenueConfigProvider;
import com.acpiot.cps.service.PaymentEventService;
import com.acpiot.cps.sxypay.event.SplitOrderEvent;
import com.acpiot.cps.sxypay.event.WithdrawEvent;
import com.acpiot.cps.sxypay.service.SxyPayService;
import com.acpiot.cps.wxpay.properties.WxPayProperties;
import com.acpiot.cps.wxpay.service.WxPayService;
import com.acpiot.cps.wxpay.utils.WxPayUtils;
import com.alibaba.fastjson.JSONObject;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.upay.sdk.Constants;
import com.upay.sdk.entity.SplitDetail;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.refund.model.RefundNotification;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.util.ObjectUtil.defaultIfNull;
import static com.acpiot.cps.dto.OweCheckTiming.WX_MP_RECHARGE;

/**
 * Created by moxin on 2020-04-27-0027
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class PaymentEventServiceImpl implements PaymentEventService {

    private final RoomRepository roomRepository;
    private final RechargeRepository rechargeRepository;
    private final WxConfigRepository wxConfigRepository;
    private final AdapayConfigRepository adapayConfigRepository;
    private final ApplyRefundRepository applyRefundRepository;

    private final WxPayService wxPayService;
    private final AdapayProvider adapayProvider;
    private final RevenueConfigProvider revenueConfigProvider;
    private final SxyPayConfigRepository sxyPayConfigRepository;
    private final SxyPayService sxyPayService;
    private final ApplicationContext applicationContext;
    private final WmWaterMeterRepository wmWaterMeterRepository;
    private final JPAQueryFactory jpaQueryFactory;
    private final CommunityRevenueConfigRepository communityRevenueConfigRepository;

    @Value(value = "${pay.sxy.partnerId}")
    private String partnerId;

    @Override
    @Transactional(readOnly = true)
    public Recharge getRechargeByPayCode(String payCode) {
        return rechargeRepository.findByPayCode(payCode);
    }

    @Override
    @Transactional(readOnly = true)
    public WxPayProperties getWxPayProperties(String companyId) {
        return wxConfigRepository.findTopByCompanyId(companyId).filter(WxConfig::isEnablePay).map(WxPayProperties::new).orElse(null);
    }

    @Override
    public Map<String, String> wxPayCallback(Transaction transaction) {
        log.info("微信支付回调：requestBody={}", transaction);

        // 获取回调成功的参数
        String outTradeNo = transaction.getOutTradeNo();
        String transactionId = transaction.getTransactionId();
        String tradeStateDesc = transaction.getTradeStateDesc();
        String successTime = transaction.getSuccessTime();
        String openid = transaction.getPayer().getOpenid();
        int total = transaction.getAmount().getTotal();

        // 查询对应充值记录
        Recharge recharge = rechargeRepository.findByPayCodeFetchRoomAndCustomer(outTradeNo);
        if (recharge == null) {
            log.error("微信支付回调：未在业务系统中查询到对应充值记录，请联系管理员排查原因，outTradeNo={}，transactionId={}", outTradeNo, transactionId);
            return WxPayUtils.buildWxPayCallbackErrorResult("业务异常：业务系统不存在该充值记录");
        }
        // 判断金额是否与充值记录中的一致，如果不一致则表示出现系统异常
        int dbAmount = recharge.getAmount().multiply(new BigDecimal(100)).intValue();
        if (total != dbAmount) {
            log.error("微信支付回调：回调参数中的用户支付金额与业务充值记录中的金额不一致，请联系管理员排查原因，outTradeNo={}，transactionId={}", outTradeNo, transactionId);
            return WxPayUtils.buildWxPayCallbackErrorResult("业务异常：回调中的支付金额与业务充值记录金额不一致");
        }
        // 判断当前充值记录是否已经是最终状态，如果是最终状态而不是未支付状态则忽略处理回调
        if (recharge.getRechargeStatus() != Recharge.RechargeStatus.UNPAID) {
            log.info("微信支付回调：解析回调数据显示充值记录不是未支付状态，已忽略业务处理，outTradeNo={}，transactionId={}", outTradeNo, transactionId);
            return WxPayUtils.buildWxPayCallbackSuccessResult();
        }
        log.info("微信支付回调：检查业务充值记录状态正常，开始进行业务处理，outTradeNo={}，transactionId={}", outTradeNo, transactionId);

        // 统一处理充值失败的情况，判断到失败，则将充值记录设置为失败，返回微信支付已接收并处理成功
        if (WxPayUtils.isPayFailure(transaction)) {
            recharge.setOpenid(openid);
            recharge.setOrderCode(transactionId);
            recharge.setTradeStateDesc(tradeStateDesc);
            recharge.setRechargeStatus(Recharge.RechargeStatus.PAY_FAILED);
            log.info("微信支付回调：解析回调数据显示充值记录支付失败，已进行充值记录数据更新，outTradeNo={}，transactionId={}", outTradeNo, transactionId);
            return WxPayUtils.buildWxPayCallbackSuccessResult();
        }
        // 如果当前回调充值记录为未支付成功也未支付失败，则表示正处于支付过程中，忽略处理并返回微信支付处理结果
        if (!WxPayUtils.isPaySuccess(transaction)) {
            log.info("微信支付回调：解析回调数据显示充值记录为支付过程中，已忽略业务处理，outTradeNo={}，transactionId={}", outTradeNo, transactionId);
            return WxPayUtils.buildWxPayCallbackSuccessResult();
        }

        // 更新充值记录为支付成功，并更新相关字段
        recharge.setOpenid(openid);
        recharge.setOrderCode(transactionId);
        recharge.setTradeStateDesc(tradeStateDesc);
        recharge.setRechargeStatus(Recharge.RechargeStatus.PAY_SUCCESS);
        LocalDateTime payTime = LocalDateTime.parse(successTime, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX"));
        recharge.setPayTime(payTime);
        log.info("微信支付回调：解析回调数据显示充值记录支付成功，对应支付状态已更新，outTradeNo={}，transactionId={}", outTradeNo, transactionId);

        // 获取充值房间信息
        Room room = recharge.getRoom();
        Community community = room.getCommunity();
        boolean bluePrepay = recharge.isBluePrepay();
        // 充值成功后，需要给对应的房号下添加余额操作，判断是否由住户支付手续费
        boolean ownerPayPoundage = revenueConfigProvider.isOwnerPayPoundage(community.getId());
        if (ownerPayPoundage) {
            BigDecimal chargeRate = community.getCommunityRevenueConfig().getChargeRate();
            BigDecimal poundage = recharge.getAmount().multiply(chargeRate).setScale(2, RoundingMode.HALF_UP);
            if (poundage.compareTo(new BigDecimal("0.01")) < 0) {
                poundage = new BigDecimal("0.01");
            }
            recharge.setPoundage(poundage);
            // 更新充值后余额
            recharge.setAfterBalance(recharge.getBeforeBalance().add(recharge.getAmount().subtract(poundage)));
            // 非蓝牙预付费充值，需要更新房间余额
            if (!bluePrepay) {
                room.setBalance(room.getBalance().add(recharge.getAmount().subtract(poundage)));
            }
        } else {
            // 更新充值后余额
            recharge.setAfterBalance(recharge.getBeforeBalance().add(recharge.getAmount()));
            // 非蓝牙预付费充值，需要更新房间余额
            if (!bluePrepay) {
                room.setBalance(room.getBalance().add(recharge.getAmount()));
            }
        }
        // 如果房间的customer与本次充值记录的customer不一致，则需要更新房间住户
        if (!Objects.equals(room.getCustomer().getId(), recharge.getCustomer().getId())) {
            room.setCustomer(recharge.getCustomer());
        }

        // 发布充值成功事件
        applicationContext.publishEvent(new RechargeSuccessEvent(recharge.getId(), WX_MP_RECHARGE));

        log.info("微信支付回调：解析回调数据显示充值记录支付成功，对应房号余额已更新，outTradeNo={}，transactionId={}", outTradeNo, transactionId);

        return WxPayUtils.buildWxPayCallbackSuccessResult();
    }

    @Override
    public void syncRechargePayStatus(Long rechargeId) {
        Recharge recharge = rechargeRepository.findById(rechargeId).orElseThrow(() -> new BusinessException("不存在该充值记录"));
        if (recharge.getRechargeType() != Recharge.RechargeType.WECHAT_SUBSCRIPTION_RECHARGE) {
            throw new BusinessException("该充值记录不是微信支付记录，暂不支持同步支付状态");
        }
        WxPayProperties properties = getWxPayProperties(recharge.getCompanyId());
        if (properties == null) {
            throw new BusinessException("未开启微信支付通道，无法同步支付状态");
        }
        Transaction transaction = wxPayService.queryOrderDetailByOutTradeNo(properties, recharge.getPayCode());
        if (WxPayUtils.isPayFailure(transaction)) {
            log.warn("订单状态为支付失败，已更新为失败，outTradeNo={}", recharge.getPayCode());
            recharge.setRechargeStatus(Recharge.RechargeStatus.PAY_FAILED);
            return;
        }
        if (transaction.getTradeState() != Transaction.TradeStateEnum.SUCCESS) {
            log.warn("订单状态未支付失败也暂未成功，暂时忽略进一步处理，outTradeNo={}", recharge.getPayCode());
            return;
        }
        // 订单已成功，调用回调处理
        log.info("微信支付订单查询，查询结果为成功，调用微信支付回调方法处理，payCode={}", recharge.getPayCode());
        wxPayCallback(transaction);
        log.info("微信支付订单查询，查询结果为成功，调用微信支付回调方法处理结束，payCode={}", recharge.getPayCode());
    }

    @Override
    public void adaPayCallback(PaymentEvent paymentEvent) {
        // 获取订单号
        String orderNo = paymentEvent.getOrder_no();
        log.info("汇付天下第三方支付回调：开始处理，orderNo={}", orderNo);

        // 判断业务充值记录是否存在
        Recharge recharge = rechargeRepository.findByPayCodeFetchRoomAndCustomer(orderNo);
        if (recharge == null) {
            log.error("汇付天下第三方支付回调：未在业务系统中查询到对应充值记录，请联系管理员排查原因，orderNo={}", orderNo);
            return;
        }
        // 查询对应的公司配置
        AdapayConfig adapayConfig = adapayConfigRepository.findTopByCompanyId(recharge.getCompanyId()).orElse(null);
        if (adapayConfig == null) {
            log.error("汇付天下第三方支付回调：未在业务系统中查询到对应汇付支付配置，请联系管理员排查原因，orderNo={}", orderNo);
            return;
        }
        // 如果支付失败，更新订单状态并返回
        if (!paymentEvent.isPaySuccess()) {
            recharge.setRechargeStatus(Recharge.RechargeStatus.PAY_FAILED);
            log.error("汇付天下第三方支付回调：回调数据显示订单状态为失败，已更新订单状态，orderNo={}", orderNo);
            return;
        }
        // 判断金额是否与充值记录一致，如果不一致则表示出现系统异常
        String orderAmount = recharge.getAmount().setScale(2, RoundingMode.HALF_UP).toString();
        if (!ObjectUtil.equals(orderAmount, paymentEvent.getPay_amt())) {
            log.error("汇付天下第三方支付回调：回调参数中的用户支付金额与业务充值记录中的金额不一致，请联系管理员排查原因，orderNo={}", orderNo);
            return;
        }
        // 判断当前充值记录是否已经是最终状态，如果是最终状态而不是未支付状态则忽略处理回调
        if (recharge.getRechargeStatus() != Recharge.RechargeStatus.UNPAID) {
            log.info("汇付天下第三方支付回调：解析回调数据显示充值记录不是未支付状态，已忽略业务处理，orderNo={}", orderNo);
            return;
        }
        // 获取房间信息
        Optional<Room> roomOptional = roomRepository.findById(Objects.requireNonNull(recharge.getRoom().getId()));
        if (roomOptional.isEmpty()) {
            log.error("汇付天下第三方支付回调：未查询到对应房间，已忽略业务处理，orderNo={}", orderNo);
            return;
        }
        Room room = roomOptional.get();
        Community community = room.getCommunity();
        log.info("汇付天下第三方支付回调：检查业务充值记录状态正常，开始进行业务处理，orderNo={}", orderNo);

        // 更新充值记录为支付成功，并更新相关字段
        recharge.setPayTime(LocalDateTimeUtil.parse(paymentEvent.getEnd_time(), DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        recharge.setRechargeStatus(Recharge.RechargeStatus.PAY_SUCCESS);
        log.info("汇付天下第三方支付回调：收到支付成功通知，对应充值记录状态已更新，orderNo={}", orderNo);

        // 充值成功后，需要给对应的房号下添加余额操作，判断是否有住户支付手续费
        boolean bluePrepay = recharge.isBluePrepay();
        boolean ownerPayPoundage = revenueConfigProvider.isOwnerPayPoundage(community.getId());
        if (ownerPayPoundage) {
            BigDecimal chargeRate = community.getCommunityRevenueConfig().getChargeRate();
            BigDecimal poundage = recharge.getAmount().multiply(chargeRate).setScale(2, RoundingMode.HALF_UP);
            if (poundage.compareTo(new BigDecimal("0.01")) < 0) {
                poundage = new BigDecimal("0.01");
            }
            recharge.setPoundage(poundage);
            // 更新充值后余额
            recharge.setAfterBalance(recharge.getBeforeBalance().add(recharge.getAmount().subtract(poundage)));
            // 非蓝牙预付费充值，需要更新房间余额
            if (!bluePrepay) {
                room.setBalance(room.getBalance().add(recharge.getAmount().subtract(poundage)));
            }
        } else {
            // 更新充值后余额
            recharge.setAfterBalance(recharge.getBeforeBalance().add(recharge.getAmount()));
            // 非蓝牙预付费充值，需要更新房间余额
            if (!bluePrepay) {
                room.setBalance(room.getBalance().add(recharge.getAmount()));
            }
        }
        // 如果房间的customer与本次充值记录的customer不一致，则需要更新房间住户
        if (!Objects.equals(room.getCustomer().getId(), recharge.getCustomer().getId())) {
            room.setCustomer(recharge.getCustomer());
        }

        // 发布充值成功事件
        applicationContext.publishEvent(new RechargeSuccessEvent(recharge.getId(), WX_MP_RECHARGE));

        // 打印处理成功日志
        log.info("汇付天下第三方支付回调：数据处理成功，对应充值记录已更新，orderNo={}", orderNo);
    }

    @Override
    public void updateUnpaidRechargeForWechat() {
        // 查询出系统所有未支付的微信订单
        List<Recharge> recharges = rechargeRepository.findByRechargeStatusAndRechargeTypeInAndCreatedDateLessThanEqual(
                Recharge.RechargeStatus.UNPAID, Recharge.RechargeType.wechatType(), LocalDateTime.now().minusHours(24));
        if (CollUtil.isNotEmpty(recharges)) {
            // 按照公司进行分组，便于每个公司的数据只查询一次公司配置
            Map<String, List<Recharge>> dataMap = recharges.stream().collect(Collectors.groupingBy(p -> p.getRoom().getCompanyId()));
            dataMap.forEach((companyId, rechargeList) -> {
                // 查询当前公司的微信支付参数配置
                Optional<WxConfig> wxConfig = wxConfigRepository.findTopByCompanyId(companyId);
                if (wxConfig.isEmpty() || !wxConfig.get().isEnablePay()) {
                    log.error("微信支付订单定时查询，处理指定公司订单已忽略，该公司未开启微信支付能力，companyId={}", companyId);
                    return;
                }
                WxPayProperties properties = new WxPayProperties(wxConfig.get());
                // 调用微信商户订单号查单方法，查询订单的最终状态
                rechargeList.forEach(recharge -> {
                    try {
                        Transaction transaction = wxPayService.queryOrderDetailByOutTradeNo(properties, recharge.getPayCode());
                        // 因为定时器查询的是24小时之前的数据，如果24小时候还是过程状态，那么更新该订单为失败状态
                        if (!WxPayUtils.isPaySuccess(transaction)) {
                            log.warn("订单状态已过期，更新为失败，outTradeNo={}", recharge.getPayCode());
                            recharge.setRechargeStatus(Recharge.RechargeStatus.PAY_FAILED);
                        } else {
                            // 订单已成功，调用回调处理
                            log.info("微信支付订单定时查询，查询结果为成功，调用微信支付回调方法处理，payCode={}", recharge.getPayCode());
                            this.wxPayCallback(transaction);
                            log.info("微信支付订单定时查询，查询结果为成功，调用微信支付回调方法处理结束，payCode={}", recharge.getPayCode());
                        }
                    } catch (Exception e) {
                        recharge.setRechargeStatus(Recharge.RechargeStatus.PAY_FAILED);
                        log.error("定时查询微信支付未完成订单状态异常，已将订单状态更新为失败，outTradeNo={}", recharge.getPayCode(), e);
                    }
                });
            });
        }
    }

    @Override
    public void updateUnpaidPayIncomeForAdaPay() {
        // 查询出系统所有未支付的汇付微信支付订单
        List<Recharge> recharges = rechargeRepository.findByRechargeStatusAndRechargeTypeInAndCreatedDateLessThanEqual(
                Recharge.RechargeStatus.UNPAID, Recharge.RechargeType.adapayType(), LocalDateTime.now().minusHours(24));
        if (CollUtil.isNotEmpty(recharges)) {
            // 调用汇付微信商户订单号查单方法，查询订单的最终状态
            recharges.forEach(recharge -> {
                try {
                    QueryPaymentRes queryPaymentRes = adapayProvider.queryPayment(recharge.getCompanyId(), recharge.getOrderCode());
                    // 因为定时器查询的是24小时之前的数据，如果24小时候还是过程状态，那么更新该订单为失败状态
                    if (queryPaymentRes == null || !queryPaymentRes.isSuccess()) {
                        log.warn("汇付-订单状态已过期，更新为失败，outTradeNo={}", recharge.getPayCode());
                        recharge.setRechargeStatus(Recharge.RechargeStatus.PAY_FAILED);
                        return;
                    }
                    // 订单已成功，调用回调处理
                    log.info("汇付-微信支付订单定时查询，查询结果为成功，调用汇付微信支付回调方法处理，payCode={}", recharge.getPayCode());
                    this.adaPayCallback(queryPaymentRes.toSuccessPaymentEvent());
                    log.info("汇付-微信支付订单定时查询，查询结果为成功，调用农行微信支付回调方法处理结束，payCode={}", recharge.getPayCode());
                } catch (Exception e) {
                    recharge.setRechargeStatus(Recharge.RechargeStatus.PAY_FAILED);
                    log.error("汇付-定时查询农行微信支付未完成订单状态异常，已将订单状态更新为失败，outTradeNo={}", recharge.getPayCode(), e);
                }
            });
        }
    }

    @Override
    public Map<String, String> wxPayRefundCallback(RefundNotification refundNotification) {
        log.info("微信退款回调：requestBody={}", refundNotification);

        // 获取回调成功的参数
        String outTradeNo = refundNotification.getOutTradeNo();
        String outRefundNo = refundNotification.getOutRefundNo();
        String transactionId = refundNotification.getTransactionId();
        String successTime = refundNotification.getSuccessTime();
        int refund = refundNotification.getAmount().getRefund().intValue();

        // 查询对应充值记录
        Recharge recharge = rechargeRepository.findByPayCodeFetchRoomAndCustomer(outTradeNo);
        if (recharge == null) {
            log.error("微信退款回调：未在业务系统中查询到对应充值记录，请联系管理员排查原因，outTradeNo={}，transactionId={}", outTradeNo, transactionId);
            return WxPayUtils.buildWxPayCallbackErrorResult("业务异常：业务系统不存在该充值记录");
        }
        Optional<ApplyRefund> applyRefundOptional = applyRefundRepository.findByOutRefundNo(outRefundNo);
        if (applyRefundOptional.isEmpty()) {
            log.error("微信退款回调：未在业务系统中查询到对应退款申请记录，请联系管理员排查原因，refundOrderNo={}", outRefundNo);
            return WxPayUtils.buildWxPayCallbackErrorResult("业务异常：业务系统不存在充值记录对应的退款申请记录");
        }
        // 查询充值记录对应的正在退款中的退款申请记录
        ApplyRefund applyRefund = applyRefundOptional.get();
        if (applyRefund.getApplyStatus() != ApplyStatus.REFUNDING) {
            log.error("微信退款回调：未在业务系统中查询到充值对应的退款中的申请记录，请联系管理员排查原因，rechargeId={}", recharge.getId());
            return WxPayUtils.buildWxPayCallbackErrorResult("业务异常：业务系统不存在充值记录对应的退款中的申请记录");
        }
        // 判断金额是否与退款申请记录金额一致，如果不一致则表示出现系统异常
        int dbAmount = applyRefund.getAmount().multiply(new BigDecimal(100)).intValue();
        if (refund != dbAmount) {
            log.error("微信退款回调：回调参数中的退款金额与退款申请记录中的退款不一致，请联系管理员排查原因，outTradeNo={}，transactionId={}", outTradeNo, transactionId);
            return WxPayUtils.buildWxPayCallbackErrorResult("业务异常：回调中的退款金额与退款申请金额不一致");
        }
        log.info("微信退款回调：检查业务状态正常，开始进行业务处理，outTradeNo={}，transactionId={}", outTradeNo, transactionId);

        // 退款失败，直接返回
        if (WxPayUtils.isRefundFailure(refundNotification)) {
            log.info("微信退款回调：解析回调数据显示退款失败，已忽略业务处理，outTradeNo={}，transactionId={}", outTradeNo, transactionId);
            // 更新退款申请为审核失败
            applyRefund.setApplyStatus(ApplyStatus.AUDIT_FAIL);
            applyRefund.setFailReason(defaultIfNull(applyRefund.getFailReason(), "") + "微信退款回调：微信退款服务返回失败");
            return WxPayUtils.buildWxPayCallbackSuccessResult();
        }

        // 非成功状态，说明退款还在处理中
        if (!WxPayUtils.isRefundSuccess(refundNotification)) {
            log.info("微信退款回调：解析回调数据显示退款正在处理中，已忽略业务处理，outTradeNo={}，transactionId={}", outTradeNo, transactionId);
            return WxPayUtils.buildWxPayCallbackSuccessResult();
        }

        // 更新缴费记录相关字段
        List<ApplyRefund> applyWxRefundList = applyRefundRepository.findByRecharge_IdAndApplyStatus(recharge.getId(), ApplyStatus.AUDIT_SUCCESS);
        // 查询当前缴费记录已成功退款的申请记录，并计算当前缴费记录历史退款总金额
        BigDecimal refundAmount = applyRefund.getAmount();
        if (CollUtil.isNotEmpty(applyWxRefundList)) {
            BigDecimal reduce = applyWxRefundList.stream().map(ApplyRefund::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            refundAmount = refundAmount.add(reduce);
        }

        Date refundTime = CalendarUtil.parseByPatterns(successTime, "yyyy-MM-dd'T'HH:mm:ssXXX").getTime();

        // 更新退款总金额，实际缴费金额
        recharge.setRefundAmount(refundAmount);
        recharge.setRefundTime(LocalDateTimeUtils.toLocalDateTime(refundTime));
        recharge.setAmount(recharge.getOriginAmount().subtract(refundAmount));

        // 更新退款申请相关字段
        applyRefund.setRefundTime(LocalDateTimeUtils.toLocalDateTime(refundTime));
        applyRefund.setApplyStatus(ApplyStatus.AUDIT_SUCCESS);
        log.info("微信退款回调：解析回调数据显示退款成功，对应退款状态已更新，outTradeNo={}，transactionId={}", outTradeNo, transactionId);

        // 退款成功，更新房间余额，并刷新可用余额，如果是预付费充值退款，则不更新房间余额
        if (!applyRefund.isBluePrepay()) {
            Room room = recharge.getRoom();
            BigDecimal amount = applyRefund.getAmount();
            // 考虑手续费金额问题
            BigDecimal poundage = recharge.getPoundage();
            if (poundage != null) {
                // 如果当前缴费记录全额退款了，则需要减去手续费，否则不减手续费
                // 充值100，手续费0.6，如果第一次退款50，则余额减少50，第二次再退款50，则余额只需要减少49.4
                if (recharge.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                    amount = amount.subtract(poundage);
                }
            }
            room.setBalance(room.getBalance().subtract(amount));
            applicationContext.publishEvent(new OweCheckEvent(recharge.getRoom().getId(), OweCheckTiming.REFUND_RECHARGE));
        }

        log.info("微信退款回调：数据处理已完成，outTradeNo={}，transactionId={}", outTradeNo, transactionId);
        return WxPayUtils.buildWxPayCallbackSuccessResult();
    }

    @Override
    public void adaRefundCallback(RefundEvent refundEvent) {
        // 获取退款订单号
        String refundOrderNo = refundEvent.getRefund_order_no();
        String refund_amt = refundEvent.getRefund_amt();
        String succeed_time = refundEvent.getSucceed_time();
        log.info("汇付天下第三方退款回调：开始处理，refundOrderNo={}", refundOrderNo);

        // 查询退款申请记录
        Optional<ApplyRefund> applyRefundOptional = applyRefundRepository.findByOutRefundNo(refundOrderNo);
        if (applyRefundOptional.isEmpty()) {
            log.error("汇付天下第三方退款回调：未在业务系统中查询到对应退款申请记录，请联系管理员排查原因，refundOrderNo={}", refundOrderNo);
            return;
        }
        ApplyRefund applyRefund = applyRefundOptional.get();
        // 判断退款申请状态
        if (applyRefund.getApplyStatus() != ApplyStatus.REFUNDING) {
            log.error("汇付天下第三方退款回调：已忽略处理，当前退款申请状态不是退款中，refundOrderNo={}", refundOrderNo);
            return;
        }
        // 如果退款失败，更新申请记录的状态并返回
        if (!refundEvent.isRefundSuccess()) {
            applyRefund.setApplyStatus(ApplyStatus.AUDIT_FAIL);
            applyRefund.setFailReason(defaultIfNull(applyRefund.getFailReason(), "") + refundEvent.formatErrorInfo());
            log.error("汇付天下第三方退款回调：回调数据显示退款状态为失败，已更新退款申请状态，refundOrderNo={}", refundOrderNo);
            return;
        }
        // 比对退款金额
        String amountStr = NumberUtil.decimalFormat("0.00", applyRefund.getAmount().setScale(2, RoundingMode.HALF_UP));
        if (!Objects.equals(refund_amt, amountStr)) {
            applyRefund.setApplyStatus(ApplyStatus.AUDIT_FAIL);
            applyRefund.setFailReason(defaultIfNull(applyRefund.getFailReason(), "") + String.format("退款失败，实际退款金额=%s，申请退款金额=%s", refund_amt, amountStr));
            log.error("汇付天下第三方退款回调：{}，已更新退款申请状态，refundOrderNo={}", applyRefund.getFailReason(), refundOrderNo);
            return;
        }
        Recharge recharge = applyRefund.getRecharge();

        // 更新缴费记录相关字段
        List<ApplyRefund> applyWxRefundList = applyRefundRepository.findByRecharge_IdAndApplyStatus(recharge.getId(), ApplyStatus.AUDIT_SUCCESS);
        // 查询当前缴费记录已成功退款的申请记录，并计算当前缴费记录历史退款总金额
        BigDecimal refundAmount = applyRefund.getAmount();
        if (CollUtil.isNotEmpty(applyWxRefundList)) {
            BigDecimal reduce = applyWxRefundList.stream().map(ApplyRefund::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            refundAmount = refundAmount.add(reduce);
        }

        // 更新退款总金额，实际缴费金额
        recharge.setRefundAmount(refundAmount);
        recharge.setRefundTime(LocalDateTimeUtil.of(Long.parseLong(succeed_time)));
        recharge.setAmount(recharge.getOriginAmount().subtract(refundAmount));

        // 更新退款申请相关字段
        applyRefund.setRefundTime(recharge.getRefundTime());
        applyRefund.setApplyStatus(ApplyStatus.AUDIT_SUCCESS);
        log.info("汇付天下第三方退款回调：解析回调数据显示退款成功，对应退款状态已更新，refundOrderNo={}", refundOrderNo);

        // 退款成功，更新房间余额，并刷新可用余额，如果是预付费充值退款，则不更新房间余额
        if (!applyRefund.isBluePrepay()) {
            Room room = recharge.getRoom();
            BigDecimal amount = applyRefund.getAmount();
            // 考虑手续费金额问题
            BigDecimal poundage = recharge.getPoundage();
            if (poundage != null) {
                // 如果当前缴费记录全额退款了，则需要减去手续费，否则不减手续费
                // 充值100，手续费0.6，如果第一次退款50，则余额减少50，第二次再退款50，则余额只需要减少49.4
                if (recharge.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                    amount = amount.subtract(poundage);
                }
            }
            room.setBalance(room.getBalance().subtract(amount));
            applicationContext.publishEvent(new OweCheckEvent(recharge.getRoom().getId(), OweCheckTiming.REFUND_RECHARGE));
        }

        log.info("汇付天下第三方退款回调：数据处理已完成，refundOrderNo={}", refundOrderNo);
    }

    @Override
    @Transactional(readOnly = true)
    public SxyPayConfig getSxyPayConfigById(Long id) {
        return sxyPayConfigRepository.findById(id).orElseThrow(() -> new BusinessException("不存在该配置"));
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<SxyPayProperties> getEnabledSxyPayProperties(String companyId) {
        return sxyPayConfigRepository.getEnabledSxyPayProperties(companyId);
    }

    @Override
    public void sxySubMerchantApplyCallback(String companyId, JSONObject data) {
        // 订单号
        String requestId = data.getString("requestId");
        // 子商户审核状态
        String subMerchantReviewStatus = data.getString("subMerchantReviewStatus");
        // 子商户审核备注
        String subMerchantReviewRemarks = data.getString("subMerchantReviewRemarks");
        // 子商户商编
        String subMerchantId = data.getString("subMerchantId");
        // 电子签约链接
        String electronicContractingUrl = data.getString("electronicContractingUrl");
        // 核查状态
        String postReviewStatus = data.getString("postReviewStatus");
        // 核查补充链接
        String certificateSupplementUrl = data.getString("certificateSupplementUrl");
        // 核查备注
        String postReviewRemark = data.getString("postReviewRemark");
        log.info("首信易支付：子商户入网回调，requestId：{}，子商户审核状态：{}，子商户审核备注：{}，子商户商编：{}，电子签约链接：{}，核查状态：{}，核查补充链接：{}，核查备注：{}",
                requestId, subMerchantReviewStatus, subMerchantReviewRemarks, subMerchantId,
                electronicContractingUrl, postReviewStatus, certificateSupplementUrl, postReviewRemark);

        // 查询商户支付配置，更新审核状态相关信息
        Optional<SxyPayConfig> config = sxyPayConfigRepository.findByCompanyIdAndRequestId(companyId, requestId);
        if (config.isEmpty()) {
            log.error("首信易支付：子商户入网回调，处理失败，未查询到对应的商户支付配置，requestId：{}，companyId={}", requestId, companyId);
            return;
        }

        // 更新审核状态相关信息
        SxyPayConfig sxyPayConfig = config.get();
        sxyPayConfig.setMerchantId(subMerchantId);
        sxyPayConfig.setSubMerchantReviewStatus(subMerchantReviewStatus);
        sxyPayConfig.setSubMerchantReviewRemarks(subMerchantReviewRemarks);
        sxyPayConfig.setElectronicContractingUrl(electronicContractingUrl);
        sxyPayConfig.setPostReviewStatus(postReviewStatus);
        sxyPayConfig.setCertificateSupplementUrl(certificateSupplementUrl);
        sxyPayConfig.setPostReviewRemark(postReviewRemark);
    }

    @Override
    public void sxyCallback(String companyId, JSONObject data) {
        // 订单号
        String requestId = data.getString("requestId");
        // 订单金额
        long orderAmount = data.getLongValue("orderAmount");
        // 支付完成时间
        String completeDateTime = data.getString("completeDateTime");
        // 交易流水号
        String serialNumber = data.getString("serialNumber");
        // 可分账金额
        long orderSplitAmount = data.getLongValue("orderSplitAmount");
        // 错误信息
        String errorMessage = data.getString("errorMessage");
        // 交易状态
        String status = data.getString(Constants.STATUS);
        log.info("首信易支付：支付成功回调，requestId：{}，订单金额/分：{}，交易流水号：{}，可分账金额/分：{}，错误信息：{}，交易状态：{}",
                requestId, orderAmount, serialNumber, orderSplitAmount, errorMessage, status);
        // 支付平台计费成功前的通知，没有可分账金额字段，暂时不处理，处理后续有可分账金额时的通知
        if (orderSplitAmount == 0) {
            log.info("首信易支付：支付成功回调，暂时忽略处理支付平台计费前的通知，requestId：{}", requestId);
            return;
        }

        // 根据订单号查询交易记录
        Recharge dbRecharge = rechargeRepository.findByPayCodeFetchRoomAndCustomer(requestId);
        if (dbRecharge == null) {
            log.error("首信易支付：支付成功回调，处理失败，未查询到对应的交易订单，requestId：{}", requestId);
            return;
        }
        // 判断交易状态
        if (dbRecharge.getRechargeStatus() != Recharge.RechargeStatus.UNPAID) {
            log.info("首信易支付：支付成功回调，已忽略处理，订单已经处理，requestId：{}，订单状态：{}", requestId, dbRecharge.getRechargeStatus());
            return;
        }
        // 判断交易金额
        long amount = dbRecharge.getAmount().multiply(new BigDecimal(100)).longValue();
        if (amount != orderAmount) {
            log.error("首信易支付：支付成功回调，已忽略处理，订单金额异常，requestId：{}，订单金额/分：{}，支付金额/分：{}", requestId, amount, orderAmount);
            return;
        }
        // 判断状态是否成功
        if (!Constants.SUCCESS.equals(status)) {
            log.error("首信易支付：支付成功回调，已忽略处理，requestId：{}，状态异常：{}", requestId, status);
            // 更新支付状态为支付失败
            dbRecharge.setOrderCode(serialNumber);
            dbRecharge.setRechargeStatus(Recharge.RechargeStatus.PAY_FAILED);
            dbRecharge.setTradeStateDesc(errorMessage);
            return;
        }

        // 更新缴费记录为支付成功，并更新相关字段
        dbRecharge.setOrderCode(serialNumber);
        dbRecharge.setTradeStateDesc(errorMessage);
        dbRecharge.setRechargeStatus(Recharge.RechargeStatus.PAY_SUCCESS);

        LocalDateTime payTime = LocalDateTimeUtil.parse(completeDateTime, "yyyy-MM-dd HH:mm:ss");
        dbRecharge.setPayTime(payTime);
        // 将可分账金额转换为BigDecimal类型保存至缴费记录中，用于后续实现分账功能
        dbRecharge.setOrderSplitAmount(new BigDecimal(orderSplitAmount).movePointLeft(2));

        // 获取房间相关信息
        Room room = dbRecharge.getRoom();
        Community community = room.getCommunity();

        // 充值成功后，需要给对应的户号下添加余额操作，判断是否有户主支付手续费
        boolean bluePrepay = dbRecharge.isBluePrepay();
        if (revenueConfigProvider.isOwnerPayPoundage(community.getId())) {
            BigDecimal chargeRate = community.getCommunityRevenueConfig().getChargeRate();
            BigDecimal poundage = dbRecharge.getAmount().multiply(chargeRate).setScale(2, RoundingMode.HALF_UP);
            if (poundage.compareTo(new BigDecimal("0.01")) < 0) {
                poundage = new BigDecimal("0.01");
            }
            // 缴费记录中保存手续费
            dbRecharge.setPoundage(poundage);
            // 更新充值后余额
            dbRecharge.setAfterBalance(dbRecharge.getBeforeBalance().add(dbRecharge.getAmount().subtract(poundage)));
            // 非蓝牙预付费充值，需要更新房间余额
            if (!bluePrepay) {
                room.setBalance(room.getBalance().add(dbRecharge.getAmount().subtract(poundage)));
            }
        } else {
            // 更新充值后余额
            dbRecharge.setAfterBalance(dbRecharge.getBeforeBalance().add(dbRecharge.getAmount()));
            // 非蓝牙预付费充值，需要更新房间余额
            if (!bluePrepay) {
                room.setBalance(room.getBalance().add(dbRecharge.getAmount()));
            }
        }

        // 如果房间的customer与本次充值记录的customer不一致，则需要更新房间住户
        if (!Objects.equals(room.getCustomer().getId(), dbRecharge.getCustomer().getId())) {
            room.setCustomer(dbRecharge.getCustomer());
        }

        // 构建分账明细参数，缓存起来用于后续可能的退款，以及当下的分账参数
        SxyPayProperties sxyPayProperties = sxyPayConfigRepository.getEnabledSxyPayProperties(companyId)
                .orElseThrow(() -> new BusinessException("易支付参数未配置"));
        List<SplitDetail> details = new ArrayList<>();
        // 先将订单金额转换为分再进行后续计算
        BigDecimal amountVal = dbRecharge.getAmount().multiply(new BigDecimal("100"));
        // 计算平台手续费，不足1分钱按照1分钱计算
        BigDecimal poundage = amountVal.multiply(new BigDecimal(sxyPayProperties.getChargeRate()));
        if (poundage.compareTo(new BigDecimal("1")) < 0) {
            poundage = new BigDecimal("1");
        }
        // 计算真实收款单位可分账金额：支付平台返回的可分账金额 - 平台手续费
        long realAmount = amountVal.subtract(poundage).longValue();
        // 计算平台手续费收益：支付平台返回的可分账金额 - 收款单位可分账金额
        long profit = dbRecharge.getOrderSplitAmount().multiply(new BigDecimal("100")).longValue() - realAmount;
        // 平台商户分账，用于收取平台手续费
        if (profit > 0) {
            SplitDetail self = new SplitDetail();
            self.setSubSplitRequestId(CodeUtils.buildSxyRequestId());
            self.setSplitAccType("MERCHANT_ACC");// 固定传：商户
            self.setSplitAccId(partnerId);// 固定传主商户，用于分配手续费收益
            self.setSplitAmount(profit);// 分配平台手续费收益
            self.setRemark("平台手续费");
            details.add(self);
        }
        // 真实收款单位分账
        if (realAmount > 0) {
            SplitDetail real = new SplitDetail();
            real.setSubSplitRequestId(CodeUtils.buildSxyRequestId());
            real.setSplitAccType("MERCHANT_ACC");// 固定传：商户
            real.setSplitAccId(sxyPayProperties.getMerchantId());// 传分账商户，用于分配用户支付款项
            real.setSplitAmount(realAmount);// 分配真实收款单位可分账金额，填写“-1”时为剩余未分账金额全额分账
            real.setRemark("收款单位实际收入");
            details.add(real);
        }
        dbRecharge.setSplitDetails(details);

        // 发布充值成功事件
        applicationContext.publishEvent(new RechargeSuccessEvent(dbRecharge.getId(), OweCheckTiming.WX_MP_RECHARGE));
        // 支付成功后，发布订单分账事件触发实时分账
        applicationContext.publishEvent(new SplitOrderEvent(this, requestId, details));
    }

    @Override
    public void sxySplitOrderCallback(String companyId, JSONObject data) {
        // 订单号
        String requestId = data.getString("requestId");
        // 分账状态
        String splitStatus = data.getString("splitStatus");
        // 分账明细订单号
        String subSplitRequestId = data.getString("subSplitRequestId");
        log.info("首信易支付：订单分账回调，requestId：{}，subSplitRequestId：{}，分账状态：{}", requestId, subSplitRequestId, splitStatus);

        // 判断分账状态是否完成，仅完成后处理后续逻辑
        if (!Objects.equals(splitStatus, "FINISHED")) {
            log.info("首信易支付：订单分账回调，暂忽略处理，正在分账中，requestId：{}，分账状态：{}", requestId, splitStatus);
            return;
        }
        // 根据订单号查询交易记录
        Recharge dbRecharge = rechargeRepository.findBySplitRequestIdFetchRoomAndCustomer(requestId);
        if (dbRecharge == null) {
            log.error("首信易支付：订单分账回调，处理失败，未查询到对应的交易订单，requestId：{}", requestId);
            return;
        }
        // 如果已经分账完成，则忽略处理
        if (dbRecharge.getSplitStatus() == Recharge.SplitStatus.FINISHED) {
            log.info("首信易支付：订单分账回调，已忽略处理，订单已经分账完成，requestId：{}", requestId);
            return;
        }

        // 更新分账状态
        dbRecharge.setSplitStatus(Recharge.SplitStatus.valueOf(splitStatus));

        // 分账成功后，发布提现事件，触发实现提现
        applicationContext.publishEvent(new WithdrawEvent(this, dbRecharge.getPayCode()));
    }

    @Override
    public void sxyWithdrawCallback(String companyId, JSONObject data) {
        // 订单号
        String requestId = data.getString("requestId");
        // 提现金额
        String withdrawAmount = data.getString("withdrawAmount");
        // 实际到账金额
        String receivedAmount = data.getString("receivedAmount");
        // 提现状态
        String withdrawStatus = data.getString("withdrawStatus");
        // 错误信息
        String errorMessage = data.getString("errorMessage");
        log.info("首信易支付：订单提现回调，requestId：{}，提现金额/分：{}，实际到账金额/分：{}，提现状态：{}，错误信息：{}",
                requestId, withdrawAmount, receivedAmount, withdrawStatus, errorMessage);

        // 根据订单号查询交易记录
        Recharge dbRecharge = rechargeRepository.findByWithdrawRequestIdFetchRoomAndCustomer(requestId);
        if (dbRecharge == null) {
            log.error("首信易支付：订单提现回调，处理失败，未查询到对应的交易订单，requestId：{}", requestId);
            return;
        }
        // 判断提现状态是否合法
        if (!List.of("SUCCESS", "FAIL").contains(withdrawStatus)) {
            log.error("首信易支付：订单提现回调，处理失败，提现状态异常，requestId：{}，提现状态：{}", requestId, withdrawStatus);
            return;
        }

        // 更新提现状态
        dbRecharge.setWithdraw(Objects.equals(withdrawStatus, "SUCCESS"));
    }

    @Override
    public void sxyRefundCallback(String companyId, JSONObject data) {
        // 订单号，请求退款的requestId，对应ApplyWxRefund.outRefundNo字段
        String requestId = data.getString("requestId");
        // 退款金额
        long amount = data.getLongValue("amount");
        // 交易流水号
        String serialNumber = data.getString("serialNumber");
        // 退款状态
        String status = data.getString("status");
        // 退款完成时间
        String completeDateTime = data.getString("completeDateTime");
        // 错误信息
        String errorMessage = data.getString("errorMessage");
        log.info("首信易支付：退款回调，requestId：{}，退款金额/分：{}，交易流水号：{}，退款状态：{}，退款完成时间：{}，错误信息：{}",
                requestId, amount, serialNumber, status, completeDateTime, errorMessage);

        // 查询对应的退款申请记录
        Optional<ApplyRefund> optional = applyRefundRepository.findByOutRefundNo(requestId);
        if (optional.isEmpty()) {
            log.error("首信易支付：未在业务系统中查询到对应的退款申请记录，请联系管理员排查原因，requestId={}", requestId);
            return;
        }
        // 查询对应的充值交易记录
        ApplyRefund applyRefund = optional.get();
        // 判断该申请记录是否处于审核中状态
        if (applyRefund.getApplyStatus() != ApplyStatus.REFUNDING) {
            log.error("首信易支付：退款申请记录不处于退款中状态，请联系管理员排查原因，requestId={}，applyWxRefundId={}", requestId, applyRefund.getId());
            return;
        }
        // 判断金额是否与退款申请记录金额一致，如果不一致则表示出现系统异常
        long dbAmount = applyRefund.getAmount().multiply(new BigDecimal(100)).longValue();
        if (amount != dbAmount) {
            log.error("首信易支付：回调参数中的退款金额与退款申请记录中的退款不一致，请联系管理员排查原因，requestId={}，applyWxRefundId={}", requestId, applyRefund.getId());
            return;
        }
        Optional<Recharge> dbRechargeOpt = rechargeRepository.findById(Objects.requireNonNull(applyRefund.getRecharge().getId()));
        if (dbRechargeOpt.isEmpty()) {
            log.error("首信易支付：退款回调，处理失败，未查询到对应的交易订单，requestId：{}", requestId);
            return;
        }
        Recharge dbRecharge = dbRechargeOpt.get();
        // 判断当前账单是否已经是支付成功状态，如果不是则忽略处理回调
        if (dbRecharge.getRechargeStatus() != Recharge.RechargeStatus.PAY_SUCCESS) {
            log.info("首信易支付：解析回调数据显示账单不是支付成功状态，已忽略业务处理，requestId={}，payIncomeId={}", requestId, dbRecharge.getId());
            return;
        }
        // 退款失败
        if (!"SUCCESS".equals(status)) {
            log.info("首信易支付：解析回调数据显示退款失败，已忽略业务处理，requestId={}，退款状态={}", requestId, status);
            // 更新退款申请为审核失败
            applyRefund.setApplyStatus(ApplyStatus.AUDIT_FAIL);
            applyRefund.setFailReason(defaultIfNull(applyRefund.getFailReason(), "") + "易支付退款回调：" + errorMessage);
            return;
        }

        // 更新缴费记录相关字段
        List<ApplyRefund> applyWxRefundList = applyRefundRepository.findByRecharge_IdAndApplyStatus(dbRecharge.getId(), ApplyStatus.AUDIT_SUCCESS);
        // 查询当前缴费记录已成功退款的申请记录，并计算当前缴费记录历史退款总金额
        BigDecimal refundAmount = applyRefund.getAmount();
        if (CollUtil.isNotEmpty(applyWxRefundList)) {
            BigDecimal reduce = applyWxRefundList.stream().map(ApplyRefund::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            refundAmount = refundAmount.add(reduce);
        }

        LocalDateTime refundTime = LocalDateTimeUtil.parse(completeDateTime, "yyyy-MM-dd HH:mm:ss");

        // 更新退款总金额，实际缴费金额
        dbRecharge.setRefundAmount(refundAmount);
        dbRecharge.setRefundTime(refundTime);
        dbRecharge.setAmount(dbRecharge.getOriginAmount().subtract(refundAmount));

        // 更新退款申请相关字段
        applyRefund.setRefundTime(refundTime);
        applyRefund.setApplyStatus(ApplyStatus.AUDIT_SUCCESS);

        // 退款成功，更新房间余额，并刷新可用余额，如果是预付费充值退款，则不更新房间余额
        if (!applyRefund.isBluePrepay()) {
            Room room = dbRecharge.getRoom();
            BigDecimal amountBigDecimal = applyRefund.getAmount();
            // 考虑手续费金额问题
            BigDecimal poundage = dbRecharge.getPoundage();
            if (poundage != null) {
                // 如果当前缴费记录全额退款了，则需要减去手续费，否则不减手续费
                // 充值100，手续费0.6，如果第一次退款50，则余额减少50，第二次再退款50，则余额只需要减少49.4
                if (dbRecharge.getMaxRefundAmount().compareTo(dbRecharge.getRefundAmount()) == 0) {
                    amountBigDecimal = amountBigDecimal.subtract(poundage);
                }
            }
            room.setBalance(room.getBalance().subtract(amountBigDecimal));
            applicationContext.publishEvent(new OweCheckEvent(dbRecharge.getRoom().getId(), OweCheckTiming.REFUND_RECHARGE));
        }
    }

    @Override
    public void updateUnpaidPayIncomeForSxyPay() {
        // 查询出系统所有未支付的易支付订单
        List<Recharge> payIncomes = rechargeRepository.findByRechargeStatusAndRechargeTypeInAndCreatedDateLessThanEqual(
                Recharge.RechargeStatus.UNPAID, List.of(Recharge.RechargeType.SXY_RESERVE), LocalDateTime.now().minusHours(24));
        if (CollUtil.isNotEmpty(payIncomes)) {
            // 按照公司进行分组，便于每个公司的数据只查询一次公司配置
            Map<String, List<Recharge>> dataMap = payIncomes.stream().collect(Collectors.groupingBy(BaseAuditingCompanyEntity::getCompanyId));
            dataMap.forEach((companyId, incomes) -> {
                // 查询当前公司的易支付参数配置
                Optional<SxyPayProperties> optional = sxyPayConfigRepository.getEnabledSxyPayProperties(companyId);
                if (optional.isEmpty()) {
                    log.error("易支付-微信支付订单定时查询，处理指定公司订单已忽略，该公司未开启易支付能力，companyId={}", companyId);
                    return;
                }
                // 调用易支付订单查询方法，查询订单的最终状态
                incomes.forEach(payIncome -> {
                    try {
                        JSONObject result = sxyPayService.queryOrderDetailByRequestId(optional.get(), payIncome.getPayCode());
                        this.sxyCallback(companyId, result);
                    } catch (Exception e) {
                        payIncome.setRechargeStatus(Recharge.RechargeStatus.PAY_FAILED);
                        log.error("易支付-定时查询微信支付未完成订单异常，已将订单状态更新为失败，requestId={}", payIncome.getPayCode(), e);
                    }
                });
            });
        }
    }

    @Override
    public void calcMasterChildBillAuto() {
        // 查询所有开了户且配置了自动结算的定点上报类型根节点水表
        List<WmWaterMeter> rootWaterMeterList = wmWaterMeterRepository.findAll((root, query, cb) -> cb.and(cb.isTrue(root.get(WmWaterMeter_.timedReport)),
                cb.isNotNull(root.get(WmWaterMeter_.settlePeriodStartDateInMonth)), cb.isNotNull(root.get(WmWaterMeter_.chargeScheme)),
                cb.isNotNull(root.get(WmWaterMeter_.room)), cb.isNotNull(root.get(WmWaterMeter_.room).get(Room_.customer)),
                cb.isTrue(root.get(WmWaterMeter_.master)), cb.isFalse(root.get(WmWaterMeter_.child))));

        LocalDate today = LocalDate.now();
        YearMonth currentMonth = YearMonth.from(today);
        YearMonth previousMonth = currentMonth.minusMonths(1);
        int previousMonthDays = previousMonth.lengthOfMonth();

        for (WmWaterMeter rootWaterMeter : rootWaterMeterList) {
            int settlePeriodStartDateInMonth = rootWaterMeter.getSettlePeriodStartDateInMonth();
            int settlePeriodEndDateInMonth = rootWaterMeter.getSettlePeriodEndDateInMonth();
            YearMonth currentYearMonth;

            // 当前事件在当月小于7，说明该周期结算可能是上月结算，再结合结算周期范围结束时间是否大于上月总天数-6，精确锁定是否是上月结算
            if (today.getDayOfMonth() < 7 && settlePeriodEndDateInMonth > previousMonthDays - 6) {
                currentYearMonth = previousMonth;
            } else {
                currentYearMonth = currentMonth;
            }
            LocalDate startDate = currentYearMonth.atDay(Math.min(settlePeriodStartDateInMonth, currentYearMonth.lengthOfMonth()));
            LocalDate endDate = currentYearMonth.atDay(Math.min(settlePeriodEndDateInMonth, currentYearMonth.lengthOfMonth()));
            LocalDate endCheckDate = endDate.plusDays(6);
            // 该水表不在账单计算周期内，跳过该账单结算
            if (today.isBefore(startDate) || today.isAfter(endCheckDate)) {
                continue;
            }

            // 如果本期已结算过账单，跳过该账单结算
            LocalDate lastBillDate = rootWaterMeter.getLastBillDate();
            if (lastBillDate != null && !lastBillDate.isBefore(startDate) && !lastBillDate.isAfter(endDate)) {
                continue;
            }

            // 找到总表和子表在这个结算时间范围内全部上传了数据的日期
            try {
                List<Long> childWaterMeterIdsByRootParentId = getChildWaterMeterIdsByRootParentId(Objects.requireNonNull(rootWaterMeter.getId()));
                if (CollUtil.isEmpty(childWaterMeterIdsByRootParentId)) {
                    continue;
                }
                List<Long> allWaterMeterIds = new ArrayList<>(childWaterMeterIdsByRootParentId);
                allWaterMeterIds.add(rootWaterMeter.getId());
                QWmDayFreezeData wmDayFreezeData = QWmDayFreezeData.wmDayFreezeData;
                int allWaterMeterIdSize = allWaterMeterIds.size();
                LocalDate currentDate = startDate;
                while (!currentDate.isAfter(endDate)) {
                    // 查询当日是否全部上报，如果全部上报，发送结算账单事件并跳出循环
                    Long currentDateCount = jpaQueryFactory.select(wmDayFreezeData.count()).from(wmDayFreezeData)
                            .where(wmDayFreezeData.waterMeter.id.in(allWaterMeterIds), wmDayFreezeData.freezeDate.eq(currentDate)).fetchOne();
                    if (Objects.equals(currentDateCount, (long) allWaterMeterIdSize)) {
                        // 增加流量校验逻辑
                        validateFlowMatch(rootWaterMeter.getId(), childWaterMeterIdsByRootParentId, currentDate);
                        applicationContext.publishEvent(new WaterMeterMasterChildBillEvent(allWaterMeterIds, currentDate));
                        break;
                    }
                    currentDate = currentDate.plusDays(1);
                }
            } catch (Exception e) {
                log.warn("跳过总子表自动结算账单", e);
            }
        }
    }

    private List<Long> getChildWaterMeterIdsByRootParentId(long rootParentId) {
        QWmWaterMeter wmWaterMeter = QWmWaterMeter.wmWaterMeter;
        Set<Long> result = new HashSet<>();
        Queue<Long> parentQueue = new LinkedList<>();
        // 初始将根父ID加入队列
        parentQueue.offer(rootParentId);

        while (!parentQueue.isEmpty()) {
            Long currentParentId = parentQueue.poll();
            // 查询当前层级的子水表ID
            List<WmWaterMeter> waterMeters = jpaQueryFactory
                    .select(wmWaterMeter)
                    .from(wmWaterMeter)
                    .where(wmWaterMeter.parent.id.eq(currentParentId))
                    .fetch();

            Optional<WmWaterMeter> unSurportWaterMeterOptional = waterMeters.stream().filter(waterMeter
                    -> !waterMeter.isTimedReport() || waterMeter.getChargeScheme() == null || waterMeter.getRoom() == null
                    || waterMeter.getRoom().getCustomer() == null).findFirst();
            if (unSurportWaterMeterOptional.isPresent()) {
                throw new BusinessException("该水表ID:{}不满足结算条件（非定点上报、未开户或未设置水价），整条总子表树不进行账单结算", unSurportWaterMeterOptional.get().getId());
            }

            List<Long> waterMeterIds = waterMeters.stream().map(WmWaterMeter::getId).toList();

            // 将新发现的子节点加入结果集和队列
            for (Long childId : waterMeterIds) {
                if (result.add(childId)) {
                    // 将子节点作为新的父节点继续查询
                    parentQueue.offer(childId);
                }
            }
        }
        return new ArrayList<>(result);
    }


    private void validateFlowMatch(Long rootWaterMeterId, List<Long> childWaterMeterIds, LocalDate localDate) {
        WmWaterMeter rootWaterMeter = wmWaterMeterRepository.findOne((root, query, cb) -> {
            root.fetch(WmWaterMeter_.community);
            return cb.equal(root.get(WmWaterMeter_.id), rootWaterMeterId);
        }).orElseThrow(() -> new BusinessException("总表ID:{}不存在", rootWaterMeterId));

        List<WmWaterMeter> childWaterMeters = wmWaterMeterRepository.findAll((root, query, cb) -> {
            root.fetch(WmWaterMeter_.community);
            return root.get(WmWaterMeter_.id).in(new ArrayList<>(childWaterMeterIds));
        });

        LocalDate lastBillDate = rootWaterMeter.getLastBillDate();
        Community community = rootWaterMeter.getCommunity();

        if (CollUtil.isNotEmpty(childWaterMeters)) {
            for (WmWaterMeter meter : childWaterMeters) {
                if (!Objects.equals(community.getId(), meter.getCommunity().getId())) {
                    throw new BusinessException("该子表:{}, 所属小区与总表:{}不一致", meter.getCode(), rootWaterMeter.getCode());
                }
                if (lastBillDate == null && meter.getLastBillDate() != null || lastBillDate != null && meter.getLastBillDate() == null) {
                    throw new BusinessException("该子表:{}, 结算状态必须与总表:{}相同", meter.getCode(), rootWaterMeter.getCode());
                }
                // 如果上次账单日期不为空，校验是否所有水表都是同一个账单日
                if (lastBillDate != null && !lastBillDate.isEqual(meter.getLastBillDate())) {
                    throw new BusinessException("该子表:{}, 与总表:{}上次账单日期必须是同一天", meter.getCode(), rootWaterMeter.getCode());
                }
            }
        }

        CommunityRevenueConfig communityRevenueConfig = communityRevenueConfigRepository.findByCommunity_IdAndEnabledIsTrue(community.getId());
        if (communityRevenueConfig == null) {
            throw new BusinessException("该小区:{}未配置小区营收配置，无法计算水表流量信息，总表:{}", community.getName(), rootWaterMeter.getCode());
        }
        LocalDate enabledBillDate = communityRevenueConfig.getEnabledBillDate();
        // 获取上次账单日期
        boolean isUseEnabledBillDate = false;
        if (lastBillDate == null || enabledBillDate.isAfter(lastBillDate)) {
            // 如果水表上次计算账单日期为null，或者启用计算账单日期在上次账单计算之后，则设置上次计算账单日期为启用账单计算日期
            lastBillDate = enabledBillDate;
            isUseEnabledBillDate = true;
        }
        // 未使用启用结算日期的情况需要判断待结算日期是否在上次账单日期之前，则无法计算水表流量信息
        if (!isUseEnabledBillDate && !localDate.isAfter(lastBillDate)) {
            throw new BusinessException("总表：{}待计算账单日期：{}不在上次账单日期：{}之后，无法计算水表流量信息", rootWaterMeter.getCode(), localDate, lastBillDate);
        }

        BigDecimal rootFlowSum = getFlowSum(List.of(rootWaterMeter), localDate, lastBillDate, isUseEnabledBillDate);
        BigDecimal childFlowSum = getFlowSum(childWaterMeters, localDate, lastBillDate, isUseEnabledBillDate);
        if (rootFlowSum.compareTo(childFlowSum) < 0) {
            throw new BusinessException("总表：{}流量：{}与子表流量：{}不匹配，无法自动结算总子表账单", rootWaterMeter.getCode(), rootFlowSum, childFlowSum);
        }
    }

    /**
     * 获取水表总流量
     *
     * @param wmWaterMeters
     * @param localDate
     * @param lastBillDate
     * @param isUseEnabledBillDate
     * @return
     */
    private BigDecimal getFlowSum(List<WmWaterMeter> wmWaterMeters, LocalDate localDate, LocalDate lastBillDate, boolean isUseEnabledBillDate) {
        QWmDayFreezeData dayFreezeData = QWmDayFreezeData.wmDayFreezeData;
        BigDecimal dayFlowSum = jpaQueryFactory.select(dayFreezeData.dayFlow.sum())
                .from(dayFreezeData).where(dayFreezeData.waterMeter.id.in(wmWaterMeters.stream().map(WmWaterMeter::getId).collect(Collectors.toList())),
                        isUseEnabledBillDate ? dayFreezeData.freezeDate.goe(lastBillDate) : dayFreezeData.freezeDate.gt(lastBillDate), dayFreezeData.freezeDate.loe(localDate))
                .fetchOne();
        return defaultIfNull(dayFlowSum, BigDecimal.ZERO);
    }
}
