package com.acpiot.cps.schedule.job;

import com.acpiot.cps.common.msg.MsgTaskProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.quartz.QuartzJobBean;

/**
 * 短信发送定时任务
 */
@Slf4j
@RequiredArgsConstructor
public class SendSmsJob extends QuartzJobBean {

    private final MsgTaskProvider msgTaskProvider;

    @Override
    protected void executeInternal(@NonNull JobExecutionContext context) {
        // 从定时器任务中取出公司id条件
        JobDataMap jobDataMap = context.getJobDetail().getJobDataMap();
        String companyId = (String) jobDataMap.get("companyId");
        msgTaskProvider.executeSmsTask(companyId);
    }
}
