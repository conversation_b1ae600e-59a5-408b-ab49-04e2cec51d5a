package com.acpiot.cps.schedule.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.StrUtil;
import com.acpiot.cps.service.BillEventService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.time.LocalDate;
import java.util.List;

import static com.acpiot.cps.constant.CommonConstant.DF;
import static com.acpiot.cps.constant.CommonConstant.SF;

/**
 * 计算账单定时任务
 */
@Slf4j
@RequiredArgsConstructor
public class CalcBillingJob extends QuartzJobBean {

    private final BillEventService billEventService;

    @Override
    protected void executeInternal(@NonNull JobExecutionContext context) {
        // 从定时器任务中取出公司id条件
        TimeInterval timer = DateUtil.timer();
        JobDataMap jobDataMap = context.getJobDetail().getJobDataMap();
        String companyId = (String) jobDataMap.get("companyId");
        // 取出账单类型条件
        String type = (String) jobDataMap.get("type");
        LocalDate billDate = LocalDate.now().minusDays(1);
        log.info("开始计算账单，companyId={}，type={}", companyId, type);
        switch (type) {
            case SF -> {
                // 查询可以生成水费账单的小区
                List<Long> communityIds = billEventService.getCalcBillCommunityIds(billDate, companyId);
                communityIds.forEach(communityId -> {
                    List<Long> waterMeterIds = billEventService.getCalcBillWaterMeters(communityId);
                    log.info(StrUtil.format("账单日 {} 小区 {} 水表 {} 账单计算开始", billDate, communityId, waterMeterIds));
                    waterMeterIds.forEach(waterMeterId -> {
                        try {
                            billEventService.calcWmBill(billDate, waterMeterId);
                        } catch (Exception e) {
                            log.error(StrUtil.format("账单日 {} 小区 {} 水表 {} 账单计算错误", billDate, communityId, waterMeterId), e);
                        }
                    });
                });
            }
            case DF -> {
                // 查询可以生成电费账单的小区
                List<Long> communityIds = billEventService.getCalcBillCommunityIds(billDate, companyId);
                communityIds.forEach(communityId -> {
                    List<Long> electricMeterIds = billEventService.getCalcBillElectricMeters(communityId);
                    electricMeterIds.forEach(electricMeterId -> {
                        try {
                            billEventService.calcEmBill(billDate, electricMeterId);
                        } catch (Exception e) {
                            log.error(StrUtil.format("账单日 {} 小区 {} 电表 {} 账单计算错误", billDate, communityId, electricMeterId), e);
                        }
                    });
                });
            }
        }
        log.info("计算账单结束，companyId={}，type={}，耗时={}", companyId, type, timer.intervalPretty());
    }
}
