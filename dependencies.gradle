dependencyManagement {

    dependencies {
        dependency("io.hypersistence:hypersistence-utils-hibernate-63:3.9.10")
        dependency("com.github.gavlyukovskiy:p6spy-spring-boot-starter:1.11.0")
        dependency("com.nimbusds:nimbus-jose-jwt:10.2")

        dependency('com.github.xiaoymin:knife4j-openapi3-jakarta-spring-boot-starter:4.5.0')
        dependency('org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.5')

        dependencySet(group: 'pers.mx.jupiter', version: '3.1.11u2') {
            entry 'jupiter-common'
            entry 'jupiter-jpa'
            entry 'jupiter-web'
        }
        dependency("pers.mx.fileserver:fileserver-spring-boot-starter:2.1.0")

        dependencySet(group: 'cn.hutool', version: '5.8.38') {
            entry 'hutool-core'
            entry 'hutool-log'
            entry 'hutool-setting'
            entry 'hutool-extra'
            entry 'hutool-cache'
            entry 'hutool-poi'
            entry 'hutool-crypto'
        }

        dependency('net.dreamlu:mica-ip2region:3.4.5')
        dependency('org.apache.poi:poi-ooxml:5.4.1')

        dependency('com.dtflys.forest:forest-core:1.6.4')

        dependency('org.webjars.bowergithub.jquery:jquery-dist:3.7.1')
        dependency('org.webjars:bootstrap:4.6.2')
        dependency('org.webjars.npm:bootstrap-icons:1.11.3')
        dependency('org.webjars:font-awesome:6.7.1')
        dependency('org.webjars.bowergithub.wenzhixin:bootstrap-table:1.22.3')
        dependency('org.webjars.bowergithub.hhurz:tableexport.jquery.plugin:1.26.0')
        dependency('org.webjars.bowergithub.jquery-validation:jquery-validation:1.20.0')
        dependency('org.webjars.bowergithub.craftpip:jquery-confirm:3.3.4')
        dependency('org.webjars.bowergithub.borismoore:jsrender:1.0.12')
    }

}
