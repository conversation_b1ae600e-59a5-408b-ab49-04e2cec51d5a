package com.acpiot.cps.mvc.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.acpiot.cps.data.archive.entity.Room;
import com.acpiot.cps.mvc.service.BuildingUnitService;
import com.acpiot.cps.mvc.service.RoomService;
import com.acpiot.cps.security.util.WechatSecurityContextUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 房间控制器层
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/auth/room")
public class RoomController {

    private final RoomService roomService;
    private final BuildingUnitService buildingUnitService;

    @GetMapping("getCurrentCustomerRoomList")
    public List<Room> getCurrentCustomerRoomList() {
        // 查询当前微信用户绑定的所有房间
        List<Room> rooms = roomService.findByOpenId(WechatSecurityContextUtils.getOpenid());
        if (CollectionUtil.isNotEmpty(rooms)) {
            buildingUnitService.buildBuildingUnitName(rooms);
        }
        return rooms;
    }

    @PostMapping("unbindRoom")
    public void unbindRoom(@RequestParam("roomId") Long roomId) {
        roomService.unbindRoom(roomId);
    }
}
