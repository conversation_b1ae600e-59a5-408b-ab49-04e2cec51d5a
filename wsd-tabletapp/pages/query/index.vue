<template>
    <view class="page">
        <view :style="{ height: iStatusBarHeight + 'px'}"></view>
        <top-bar></top-bar>
        <view class="main-content bg-white">
            <u-subsection active-color="#ffffff" :list="[{name:'抄表数据'}, {name:'预置命令'}]" :current="current" @change="change"></u-subsection>
            <swiper :current="current" style="height: 100%!important;" disable-touch="true">
              <swiper-item class="swiper-item">
                <meter-data ref="meterData"></meter-data>
              </swiper-item>
              <swiper-item class="swiper-item">
                    <test-prepare-cmd ref="prepareCmd"></test-prepare-cmd>
                </swiper-item>
            </swiper>
        </view>
    </view>
</template>
<script>
export default {
    data() {
        return {
            iStatusBarHeight: 0,
            current: 0
        };
    },
    onLoad() {
        this.iStatusBarHeight = uni.getSystemInfoSync().statusBarHeight - 5;
    },

    onShow() {
		this.$refs.meterData && this.$refs.meterData.getTestBatch();
		this.$refs.prepareCmd && this.$refs.prepareCmd.getTestBatch();	
    },

    methods: {
        change(index) {
            this.current = index
        },
    },
};
</script>

<style lang="scss" scoped>
.active {
    color: blue;
    font-weight: bold;
}
</style>
