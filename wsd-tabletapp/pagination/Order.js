"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Order = void 0;
const Direction_1 = require("./Direction");
/**
 * 排序字段封装
 */
class Order {
    constructor(property, direction) {
        this.property = property;
        this.direction = direction;
    }
    static asc(property) {
        return new Order(property, Direction_1.Direction.asc);
    }
    static desc(property) {
        return new Order(property, Direction_1.Direction.desc);
    }
}
exports.Order = Order;
