package com.acpiot.microservice.meterservice.config.mqtt;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.integration.mqtt.event.MqttConnectionFailedEvent;
import org.springframework.integration.mqtt.event.MqttMessageDeliveredEvent;

import java.util.concurrent.Semaphore;

/**
 * The type Mqtt publisher.
 *
 * <AUTHOR> Email: <a href="mailto:<EMAIL>"><EMAIL></a>
 * @since 2022 -11-09-0009 15:31
 */
@Slf4j
public class MessagePublisher {

    private final MqttOutboundConfiguration.MqttGateway mqttGateway;
    private final Semaphore semaphore;
    private final int maxInflight;

    public MessagePublisher(MqttOutboundConfiguration.MqttGateway mqttGateway, int maxInflight) {
        this.mqttGateway = mqttGateway;
        this.semaphore = new Semaphore(maxInflight);
        this.maxInflight = maxInflight;
    }

    /**
     * On mqtt message delivered event.
     *
     * @param evt the evt
     */
    @EventListener
    public void onMqttMessageDeliveredEvent(MqttMessageDeliveredEvent evt) {
        semaphore.release();
    }

    @EventListener
    public void onMqttConnectionFailedEvent(MqttConnectionFailedEvent evt) {
        log.error("MQTT连接失败或断开，{}", evt);
        semaphore.release(maxInflight);
    }

    /**
     * 往主题发布消息
     *
     * @param topic   the topic
     * @param content the content
     */
    public void publish(String topic, String content) throws Exception {
        semaphore.acquire();
        try {
            log.info("推送消息 -> topic: {}, content: {}", topic, content);
            mqttGateway.sendToMqtt(content, topic);
        } catch (Exception e) {
            semaphore.release();
            throw e;
        }
    }

    /**
     * 往主题发布消息
     *
     * @param topic   the topic
     * @param qos     the qos
     * @param content the content
     */
    public void publish(String topic, int qos, String content) throws Exception {
        semaphore.acquire();
        try {
            log.info("推送消息 -> topic: {}, content: {}", topic, content);
            mqttGateway.sendToMqtt(content, qos, topic);
        } catch (Exception e) {
            semaphore.release();
            throw e;
        }
    }

}
