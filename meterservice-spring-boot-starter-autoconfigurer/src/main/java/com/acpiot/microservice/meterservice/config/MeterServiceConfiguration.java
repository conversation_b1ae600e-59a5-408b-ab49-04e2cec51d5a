package com.acpiot.microservice.meterservice.config;

import com.acpiot.microservice.meterservice.apiclient.*;
import com.acpiot.microservice.meterservice.apiclient.customized.WmDcSjzApiClient;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.logging.HttpLoggingInterceptor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.util.concurrent.TimeUnit;

/**
 * 表计服务API接口定义
 * Created by moxin on 2020-11-12-0012
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Slf4j
@RequiredArgsConstructor
@Configuration
@EnableConfigurationProperties(MeterServiceProperties.class)
public class MeterServiceConfiguration {

    private final ObjectMapper objectMapper;
    private final MeterServiceProperties meterServiceProperties;

    @Bean
    public DeviceApiClient deviceApiClient() {
        return createApiClient(DeviceApiClient.class, null);
    }

    @Bean
    public DeviceCommandApiClient deviceCommandApiClient() {
        return createApiClient(DeviceCommandApiClient.class, null);
    }

    @Bean
    public WaterMeterApiClient waterMeterApiClient() {
        return createApiClient(WaterMeterApiClient.class, "v2");
    }

    @Bean
    public WmCollectorApiClient waterCollectorApiClient() {
        return createApiClient(WmCollectorApiClient.class, "v2");
    }

    @Bean
    public ElectricMeterApiClient electricMeterApiClient() {
        return createApiClient(ElectricMeterApiClient.class, "v2");
    }

    @Bean
    public WmDcSjzApiClient wmDcSjzApiClient() {
        return createApiClient(WmDcSjzApiClient.class, null);
    }

    @Bean
    public HeatValveApiClient heatValveApiClient() {
        return createApiClient(HeatValveApiClient.class, null);
    }

    @Bean
    public HtwmudpApiClient htwmudpApiClient() {
        return createApiClient(HtwmudpApiClient.class, null);
    }

    private <T> T createApiClient(Class<T> clazz, String apiVersion) {
        HttpLoggingInterceptor interceptor = new HttpLoggingInterceptor(log::info);
        interceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(meterServiceProperties.getConnectTimeout(), TimeUnit.SECONDS)
                .readTimeout(meterServiceProperties.getReadTimeout(), TimeUnit.SECONDS)
                .writeTimeout(meterServiceProperties.getWriteTimeout(), TimeUnit.SECONDS)
                .addInterceptor(interceptor)
                .addInterceptor(chain -> {
                    Request.Builder builder = chain.request().newBuilder();
                    builder.addHeader("Authorization", "Bearer " + meterServiceProperties.getToken());
                    builder.addHeader("User-Agent", "meter service api client");
                    if (apiVersion != null && !apiVersion.trim().isEmpty()) {
                        builder.addHeader("X-API-VERSION", apiVersion);
                    }
                    return chain.proceed(builder.build());
                })
                .followRedirects(false)
                .build();

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(meterServiceProperties.getBaseUrl())
                .client(client)
                .addConverterFactory(JacksonConverterFactory.create(objectMapper))
                .build();
        return retrofit.create(clazz);
    }

}
