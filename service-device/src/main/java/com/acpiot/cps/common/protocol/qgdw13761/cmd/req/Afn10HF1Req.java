package com.acpiot.cps.common.protocol.qgdw13761.cmd.req;

import com.acpiot.cps.common.protocol.wrapper.ProtocolByteBuf;
import com.acpiot.cps.common.protocol.qgdw645.Packet;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

/**
 * 透明转发命令参数.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023 /1/21 16:30
 */
@NoArgsConstructor
@Data
public class Afn10HF1Req {

    /**
     * The enum Packet timeout unit.
     */
    public enum PacketTimeoutUnit {

        /**
         * 10 ms.
         */
        TEN_MS,

        /**
         * 秒.
         */
        SECOND
    }

    /**
     * 终端通信端口号，范围1-31，默认设置为31
     */
    private int port = 31;

    /**
     * 透明转发通信控制字，设置为默认参数
     */
    private TransportCtrl transportCtrl = new TransportCtrl();

    /**
     * 透明转发接收等待报文超时时间单位，默认设置为秒
     */
    private PacketTimeoutUnit packetTimeoutUnit = PacketTimeoutUnit.SECOND;

    /**
     * 透明转发接收等待报文超时时间，默认设置为3秒
     */
    @Range(min = 0, max = 127)
    private int packetTimeout = 3;

    /**
     * 透明转发接收等待字节超时时间，默认设置人100毫秒
     */
    private int byteTimeout = 100;

    /**
     * 透明转发内容，通过645协议帧结构组包传入
     */
    private byte[] content;

    /**
     * 通过传入645协议帧结构组包
     *
     * @param packet
     */
    public Afn10HF1Req(Packet packet) {
        packet.setPrefix(true);
        this.content = packet.toBytes();
    }

    public byte[] toBytes() {
        int len = content.length;
        ProtocolByteBuf buf = ProtocolByteBuf.buffer(6 + len);
        buf.writeByte(port);
        buf.writeByte(transportCtrl.toByte());
        int pktTimeout = packetTimeout;
        if (packetTimeoutUnit == PacketTimeoutUnit.SECOND) {
            pktTimeout |= 0b1000_0000;
        }
        buf.writeByte(pktTimeout);
        buf.writeByte(byteTimeout);
        buf.writeShortLE(len);
        buf.writeBytes(content);
        return buf.array();
    }
}
