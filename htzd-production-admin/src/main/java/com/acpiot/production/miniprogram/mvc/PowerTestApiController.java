package com.acpiot.production.miniprogram.mvc;

import com.acpiot.production.admin.annotation.SystemLog;
import com.acpiot.production.admin.utils.GlobalLockUtil;
import com.acpiot.production.data.common.entity.PowerTestData;
import com.acpiot.production.miniprogram.dto.QueryModuleTestResultReqDto;
import com.acpiot.production.miniprogram.dto.QueryModuleTestResultRespDto;
import com.acpiot.production.miniprogram.dto.UpdateTestCurrentReqDto;
import com.acpiot.production.miniprogram.dto.powertest.PowerTestDataDto;
import com.acpiot.production.miniprogram.service.PowerTestApiService;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

/**
 * Created by moxin on 2024/3/7T17:50
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/auth/powerTest")
public class PowerTestApiController {

    private final PowerTestApiService powerTestApiService;
    private final GlobalLockUtil globalLockUtil;

    @SystemLog(description = "模块测试-上传测试数据")
    @PostMapping("upload")
    public void upload(@RequestBody PowerTestDataDto dto) throws JsonProcessingException {
        String name = dto.getName();
        List<PowerTestData> list = dto.getTestDataList()
                .stream()
                .map(testData -> {
                    PowerTestData powerTestData = new PowerTestData();
                    powerTestData.setRecordName(name);
                    powerTestData.setDeviceNo(testData.getDeviceNo());
                    powerTestData.setPos(testData.getPos());
                    powerTestData.setAverage(testData.getAverage());
                    return powerTestData;
                })
                .toList();
        powerTestApiService.savePowerTestData(list);
    }

    /**
     * 更新设备测试电流
     */
    @SystemLog(description = "模块测试-更新测试电流")
    @PostMapping("updateTestCurrent")
    public void updateTestCurrent(@Valid @RequestBody UpdateTestCurrentReqDto reqDto) {
        reqDto.getTestList()
                .forEach(dto -> {
                    synchronized (globalLockUtil.getLock(dto.getImei())) {
                        powerTestApiService.updateTestCurrent(dto);
                    }
                });
    }

    /**
     * 查询模块测试结果
     *
     * @param reqDto
     * @return
     */
    @PostMapping("queryModuleTestResult")
    public List<QueryModuleTestResultRespDto> queryModuleTestResult(@Valid @RequestBody QueryModuleTestResultReqDto reqDto) {
        return powerTestApiService.queryModuleTestResult(reqDto.getTestDate(), reqDto.getImeiList());
    }

}
