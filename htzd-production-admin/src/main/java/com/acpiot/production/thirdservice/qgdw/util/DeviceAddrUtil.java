package com.acpiot.production.thirdservice.qgdw.util;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;

/**
 * Created by moxin on 2023/9/19T17:57
 *
 * <AUTHOR>
 */
public class DeviceAddrUtil {

    /**
     * Meter code to uid string.
     *
     * @param meterCode the meter code
     * @param calcRule  地址计算规则，0-表地址后8位计算；1-表地址后8位截取
     * @return the string
     */
    public static String meterCodeToUid(String meterCode, int calcRule) {
        Assert.isTrue(meterCode.length() == 12);
        Assert.checkBetween(calcRule, 0, 1);

        String sub = meterCode.substring(8);
        int a2 = switch (calcRule) {
            case 0 -> Integer.parseInt(sub) + 0xA000;
            case 1 -> Integer.parseInt(sub, 16);
            default -> throw new IllegalArgumentException("Unexpected value: " + calcRule);
        };

        return StrUtil.format("{}{}",
                meterCode.substring(4, 8), StrUtil.padPre(String.valueOf(a2), 5, "0"));
    }

}
