package com.acpiot.production.admin.mvc.production.controller;

import com.acpiot.microservice.meterservice.dto.production.SetBaseValueParam;
import com.acpiot.microservice.meterservice.dto.watermeter.SetMeterNumberParam;
import com.acpiot.microservice.meterservice.dto.watermeter.ValveCtrlParam;
import com.acpiot.production.admin.annotation.SystemLog;
import com.acpiot.production.admin.mvc.production.dto.WmPagingQueryParam;
import com.acpiot.production.admin.mvc.production.service.PresetService;
import com.acpiot.production.data.production.entity.WaterMeter;
import com.acpiot.production.data.production.entity.WmCommand;
import com.acpiot.production.data.production.entity.WmTestBatch;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import pers.mx.jupiter.jpa.PagingQueryParams;
import pers.mx.jupiter.web.util.ResponseUtils;

import java.math.BigDecimal;

import static com.acpiot.production.admin.constant.SessionConstant.SESSION_ATTR_TEST_BATCH;
import static com.acpiot.production.admin.utils.ApiClientCaller.buildCommandReqDto;
import static com.acpiot.production.admin.utils.AppUtils.toBaseValue;

/**
 * 预置命令测试模式
 * Created by moxin on 2021-12-06-0006
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Controller
@Validated
@RequestMapping("/production/watermeter/{testBatchId}/preset")
@SessionAttributes(names = {SESSION_ATTR_TEST_BATCH}, types = {WmTestBatch.class})
public class PresetController {

    private final PresetService presetService;

    @GetMapping("/")
    public String toPreset() {
        return "production/watermeter/preset";
    }

    @PostMapping("page")
    @ResponseBody
    public Page<WaterMeter> page(@ModelAttribute(SESSION_ATTR_TEST_BATCH) WmTestBatch testBatch,
                                 @RequestBody WmPagingQueryParam params) {
        return presetService.pagedWaterMeters(testBatch, params);
    }

    @SystemLog(description = "预置抄表命令")
    @PostMapping("meteringData")
    @ResponseBody
    public ResponseEntity<?> meteringData(@ModelAttribute(SESSION_ATTR_TEST_BATCH) WmTestBatch testBatch,
                                          @RequestParam("ids[]") long[] ids) {
        presetService.saveCommand(testBatch, ids, WmCommand.WmCmdType.METERING_DATA,
                waterMeter -> buildCommandReqDto(waterMeter.getImei(), true));
        return ResponseUtils.ok("预置抄表命令成功，等待触摸上报");
    }

    @SystemLog(description = "预置开阀命令")
    @PostMapping("openValve")
    @ResponseBody
    public ResponseEntity<?> openValve(@ModelAttribute(SESSION_ATTR_TEST_BATCH) WmTestBatch testBatch,
                                       @RequestParam("ids[]") long[] ids) {
        presetService.saveCommand(testBatch, ids, WmCommand.WmCmdType.OPEN_VALVE,
                waterMeter -> buildCommandReqDto(waterMeter.getImei(), true, new ValveCtrlParam(true)));
        return ResponseUtils.ok("预置开阀命令成功，等待触摸上报");
    }

    @SystemLog(description = "预置关阀命令")
    @PostMapping("closeValve")
    @ResponseBody
    public ResponseEntity<?> closeValve(@ModelAttribute(SESSION_ATTR_TEST_BATCH) WmTestBatch testBatch,
                                        @RequestParam("ids[]") long[] ids) {
        presetService.saveCommand(testBatch, ids, WmCommand.WmCmdType.CLOSE_VALVE,
                waterMeter -> buildCommandReqDto(waterMeter.getImei(), true, new ValveCtrlParam(false)));
        return ResponseUtils.ok("预置关阀命令成功，等待触摸上报");
    }

    @SystemLog(description = "预置读卡信息/表号命令")
    @PostMapping("readCardInfo")
    @ResponseBody
    public ResponseEntity<?> readCardInfo(@ModelAttribute(SESSION_ATTR_TEST_BATCH) WmTestBatch testBatch,
                                          @RequestParam("ids[]") long[] ids) {
        presetService.saveCommand(testBatch, ids, WmCommand.WmCmdType.READ_CARD_INFO,
                waterMeter -> buildCommandReqDto(waterMeter.getImei(), true));
        return ResponseUtils.ok("预置读卡信息/表号命令成功，等待触摸上报");
    }

    @SystemLog(description = "预置读数清零命令")
    @PostMapping("resetValue")
    @ResponseBody
    public ResponseEntity<?> resetValue(@ModelAttribute(SESSION_ATTR_TEST_BATCH) WmTestBatch testBatch,
                                        @RequestParam("ids[]") long[] ids) {
        presetService.saveCommand(testBatch, ids, WmCommand.WmCmdType.SET_BASE_VALUE,
                waterMeter -> {
                    SetBaseValueParam param = new SetBaseValueParam();
                    param.setFactor(testBatch.getFactor());
                    param.setBaseValue("00000000");
                    return buildCommandReqDto(waterMeter.getImei(), true, param);
                });
        return ResponseUtils.ok("预置读数清零命令成功，等待触摸上报");
    }

    @SystemLog(description = "预置设置表号命令")
    @PostMapping("setMeterCode")
    @ResponseBody
    public ResponseEntity<?> setMeterCode(@ModelAttribute(SESSION_ATTR_TEST_BATCH) WmTestBatch testBatch,
                                          @RequestParam("meterId") long id,
                                          @RequestParam("meterCode") String meterCode) {
        presetService.saveCommand(testBatch, new long[]{id}, WmCommand.WmCmdType.SET_METER_CODE,
                waterMeter -> {
                    SetMeterNumberParam param = new SetMeterNumberParam();
                    param.setMeterNumber(meterCode);
                    return buildCommandReqDto(waterMeter.getImei(), true, param);
                });
        return ResponseUtils.ok("预置设置表号命令成功，等待触摸上报");
    }

    @SystemLog(description = "预置校表命令")
    @PostMapping("setValue")
    @ResponseBody
    public ResponseEntity<?> setValue(@ModelAttribute(SESSION_ATTR_TEST_BATCH) WmTestBatch testBatch,
                                      @RequestParam("meterId") long id,
                                      @RequestParam("baseValue") BigDecimal value) {
        presetService.saveCommand(testBatch, new long[]{id}, WmCommand.WmCmdType.SET_BASE_VALUE,
                waterMeter -> {
                    SetBaseValueParam param = new SetBaseValueParam();
                    int factor = testBatch.getFactor();
                    param.setFactor(factor);
                    param.setBaseValue(toBaseValue(value, factor));
                    return buildCommandReqDto(waterMeter.getImei(), true, param);
                });
        return ResponseUtils.ok("预置校表命令成功，等待触摸上报");
    }

    @PostMapping("pagedCmd")
    @ResponseBody
    public Page<WmCommand> pagedCmd(@RequestBody PagingQueryParams<WmCommand> params) {
        return presetService.pagedHistoryCmd(params);
    }

    @SystemLog(description = "预置命令测试-标记成功")
    @PutMapping("markSuccess")
    @ResponseBody
    public ResponseEntity<?> markSuccess(@ModelAttribute(SESSION_ATTR_TEST_BATCH) WmTestBatch testBatch,
                                         @RequestParam("ids[]") long[] ids) {
        presetService.markSuccess(testBatch, ids);
        return ResponseUtils.ok("操作成功");
    }

    @SystemLog(description = "预置命令测试-标记失败")
    @PutMapping("markFail")
    @ResponseBody
    public ResponseEntity<?> markFail(@ModelAttribute(SESSION_ATTR_TEST_BATCH) WmTestBatch testBatch,
                                      @RequestParam("ids[]") long[] ids) {
        presetService.markFail(testBatch, ids);
        return ResponseUtils.ok("操作成功");
    }

}
