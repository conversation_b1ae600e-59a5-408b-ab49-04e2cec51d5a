package com.acpiot.production.admin.service.impl;

import com.acpiot.production.admin.event.ExitProductionEvent;
import com.acpiot.production.admin.event.PresetTestEvent;
import com.acpiot.production.admin.event.StartModuleTestEvent;
import com.acpiot.production.admin.event.wm.FirstCloseValveEvent;
import com.acpiot.production.admin.listener.dto.FactoryResult;
import com.acpiot.production.admin.service.ProductionService;
import com.acpiot.production.data.common.entity.ModuleState;
import com.acpiot.production.data.common.entity.NbSignal;
import com.acpiot.production.data.production.entity.*;
import com.acpiot.production.data.production.repository.WaterMeterRepository;
import com.acpiot.production.data.production.repository.WmPackBoxRepository;
import com.acpiot.production.data.production.repository.WmTestBatchRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * Created by moxin on 2020-12-03-0003
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class ProductionServiceImpl implements ProductionService {

    public static final String CMD_WAIT_REPORT = "等待触摸上报";
    public static final String CMD_WAIT_BLE_TEST = "等待蓝牙测试通过";

    private final ApplicationContext applicationContext;
    private final WaterMeterRepository waterMeterRepository;
    private final WmTestBatchRepository wmTestBatchRepository;
    private final WmPackBoxRepository wmPackBoxRepository;

    @Override
    @Transactional(readOnly = true)
    public Optional<WaterMeter> getWaterMeterByImei(String imei) {
        return waterMeterRepository.findByImei(imei);
    }

    @Override
    public void handleDeviceOnlineOffline(String imei, boolean online) {
        getWaterMeterByImei(imei)
                .ifPresent(waterMeter -> {
                    waterMeter.setModuleState(ModuleState.ACTIVATED);
                    waterMeter.setOnline(online);

                    if (online) {
                        WmTestInfo testInfo = waterMeter.getTestInfo();
                        if (testInfo.isLocalTestStep()) {
                            if (waterMeter.checkCode()) {
                                if (testInfo.getTestState() != WmTestState.FAILURE) {
                                    waterMeter.setTestInfo(new WmTestInfo(testInfo.getProductionStep(), WmTestState.SUCCESS, null, null));
                                    waterMeter.setTestResult(TestResult.SUCCESS);
                                }
                            } else {
                                if (!waterMeter.isFirstCloseValve()) {
                                    if (waterMeter.getTestBatch().isFirstCloseValve()) {
                                        waterMeter.setFirstCloseValve(true);
                                        // 下发关阀命令
                                        applicationContext.publishEvent(new FirstCloseValveEvent(waterMeter));
                                    }
                                }
                            }
                        } else {
                            if (waterMeter.getTestBatch().isCustomProtocol()) {

                            } else {
                                checkAndStartModuleTest(waterMeter);
                            }
                        }
                    }
                });
    }

    private void checkAndStartModuleTest(WaterMeter waterMeter) {
        if (!waterMeter.getTestBatch().isModuleTest()) {
            // 未开启自动模块测试
            return;
        }
        if (waterMeter.getTestResult() != null) {
            // 已成表测试汇总，不自动开启模块测试
            return;
        }
        WmTestInfo testInfo = waterMeter.getTestInfo();
        if (testInfo.getProductionStep() != ProductionStep.MODULE_TEST) {
            // 不处于模块测试，不自动开始测试
            return;
        }
        WmTestState testState = testInfo.getTestState();
        if (testState != null
                && testState != WmTestState.FAILURE) {
            // 不处于等待测试时或者测试失败，则不自动开始测试
            return;
        }
        applicationContext.publishEvent(new StartModuleTestEvent(this, waterMeter));
    }

    @Override
    public void updateFactoryResult(String imei, FactoryResult factoryResult) {
        getWaterMeterByImei(imei)
                .ifPresent(waterMeter -> {
                    waterMeter.setFixtureFlag(factoryResult.fixtureFlag());
                    waterMeter.setCompletionFlag(factoryResult.completionFlag());
                });
    }

    @Override
    public void updateMeterData(String imei, MeterData meterData, boolean report) {
        updateMeterData(imei, meterData, report, false);
    }

    @Override
    public void updateMeterData(String imei, MeterData meterData, boolean report, boolean storageError) {
        getWaterMeterByImei(imei)
                .ifPresent(waterMeter -> {
                    WmTestBatch testBatch = waterMeter.getTestBatch();
                    if (!testBatch.isValve()) {
                        meterData.setValveStatus(null);
                    }
                    meterData.setReport(report);
                    waterMeter.setMeterData(meterData);

                    if (report) {
                        checkOtherTest(waterMeter, meterData, storageError);
                    }
                });
    }

    /**
     * 检查其它测试流程中是否要对该数据进行处理
     *
     * @param waterMeter
     * @param meterData
     * @param storageError 是否存储器异常
     */
    private void checkOtherTest(WaterMeter waterMeter, MeterData meterData, boolean storageError) {
        TestResult testResult = waterMeter.getTestResult();
        if (testResult != null) {
            // 已经归档，不修改测试状态
            return;
        }
        WmTestInfo testInfo = waterMeter.getTestInfo();
        ProductionStep productionStep = testInfo.getProductionStep();
        if (productionStep == null) {
            return;
        }

        WmTestBatch testBatch = waterMeter.getTestBatch();
        TestPlan testPlan = testBatch.getTestPlan();
        TestSchema testSchema = testPlan.getTestSchema();

        if (testSchema != TestSchema.DEFAULT
                && productionStep == ProductionStep.MODULE_TEST
                && testInfo.getTestState() != WmTestState.TESTING) {
            // 如果验证表号有效，则切换到对应成表产测方案
            if (waterMeter.checkCode()) {
                productionStep = ProductionStep.valueOf(testSchema.name());
                testInfo = new WmTestInfo(productionStep, null, null, null);
                waterMeter.setTestInfo(testInfo);
            }
        }

        WmTestState testState = testInfo.getTestState();

        if (!testBatch.getVoltageRange().checkVoltage(meterData.getBatteryVoltage())) {
            // 如果是模块测试阶段，且测试成功，则忽略电压异常
            if (!(productionStep == ProductionStep.MODULE_TEST
                    && testState == WmTestState.SUCCESS)) {
                // 电池电压异常
                waterMeter.setTestInfo(new WmTestInfo(productionStep, WmTestState.FAILURE, "数据上报", "电池电压异常"));
                return;
            }
        }

        if (testInfo.isLocalTestStep()) {
            if (waterMeter.checkCode()) {
                // 小无线测试，标记测试成功
                waterMeter.setTestInfo(new WmTestInfo(productionStep, WmTestState.SUCCESS, "信息上报成功", null));
                waterMeter.setTestResult(TestResult.SUCCESS);
            }
        } else {
            switch (productionStep) {
                case MODULE_TEST -> {
                    if (testBatch.isValidReport()
                            && testState != WmTestState.SUCCESS) {
                        if (testBatch.isModuleTest()) {
                            // 配置了模块测试，则检查是否在等待触摸上报
                            if (Objects.equals(CMD_WAIT_REPORT, testInfo.getCmd())
                                    && testState == WmTestState.TESTING) {
                                if (storageError) {
                                    waterMeter.setTestInfo(new WmTestInfo(productionStep, WmTestState.FAILURE,
                                            "触摸上报", "存储异常，请检查"));
                                } else {
                                    validMeterData(waterMeter, meterData);
                                }
                            }
                        } else {
                            // 没有配置模块测试，则测试成功
                            validMeterData(waterMeter, meterData);
                        }
                    }
                }
                case PREPARE -> {
                    if (testState == WmTestState.TESTING
                            && testPlan.hasValveTest()) {
                        if (meterData.getValveStatus() == ValveStatus.OPENED) {
                            waterMeter.setTestInfo(new WmTestInfo(productionStep, WmTestState.SUCCESS, "阀门自检",
                                    null));
                        } else {
                            waterMeter.setTestInfo(new WmTestInfo(productionStep, WmTestState.FAILURE, "阀门自检",
                                    "阀门自检失败"));
                        }
                    }
                }
                case PRESET -> {
                    if (testSchema == TestSchema.PRESET) {
                        // 开始预置命令测试
                        applicationContext.publishEvent(new PresetTestEvent(this, waterMeter));
                    }
                }
            }
        }
    }

    private void validMeterData(WaterMeter waterMeter, MeterData meterData) {
        WmTestBatch testBatch = waterMeter.getTestBatch();
        NbSignal nbSignal = meterData.getNbSignal();
        Integer convertRssi = nbSignal.getConvertRssi();
        if (convertRssi == null
                || convertRssi < testBatch.getRssiThreshold()
                || nbSignal.getSinr() < testBatch.getSnrThreshold()) {
            waterMeter.setTestInfo(new WmTestInfo(ProductionStep.MODULE_TEST, WmTestState.FAILURE, "触摸上报",
                    "RSSI(SNR)值小于设定的RSSI(SNR)阈值"));
            return;
        }
        if (!testBatch.validVersion(meterData.getMeterVersion())) {
            waterMeter.setTestInfo(new WmTestInfo(ProductionStep.MODULE_TEST, WmTestState.FAILURE, "触摸上报",
                    "版本不匹配"));
            return;
        }
        if (meterData.getValue().compareTo(testBatch.getMinValue()) < 0) {
            // 如果水表读数小于设置的最小读数，则测试失败
            waterMeter.setTestInfo(new WmTestInfo(ProductionStep.MODULE_TEST, WmTestState.FAILURE,
                    "数据上报", "水表读数 小于 批次设置最小读数"));
            return;
        }
        if (testBatch.isValidBle()
                && !waterMeter.isBleTestPass()) {
            waterMeter.setTestInfo(new WmTestInfo(ProductionStep.MODULE_TEST, WmTestState.TESTING, CMD_WAIT_BLE_TEST, null));
        } else {
            waterMeter.setTestInfo(new WmTestInfo(ProductionStep.MODULE_TEST, WmTestState.SUCCESS, "触摸上报", null));
        }
    }

    @Override
    public void updateProductionMode(String imei, ProductionMode productionMode) {
        waterMeterRepository.findByImei(imei)
                .ifPresent(waterMeter -> waterMeter.setProductionMode(productionMode));
    }

    @Override
    public void updateTestInfo(String imei, WmTestInfo testInfo) {
        waterMeterRepository.findByImei(imei).ifPresent(waterMeter -> waterMeter.setTestInfo(testInfo));
    }

    @Override
    public void updateModuleTestFinish(String imei, String cmd) {
        waterMeterRepository.findByImei(imei)
                .ifPresent(waterMeter -> {
                    WmTestBatch testBatch = waterMeter.getTestBatch();
                    if (testBatch.isValidBle()
                            && !waterMeter.isBleTestPass()) {
                        waterMeter.setTestInfo(new WmTestInfo(ProductionStep.MODULE_TEST, WmTestState.TESTING, CMD_WAIT_BLE_TEST, null));
                    } else {
                        waterMeter.setTestInfo(new WmTestInfo(ProductionStep.MODULE_TEST, WmTestState.SUCCESS, cmd, null));
                    }
                });
    }

    @Override
    public void updateTestSuccess(String imei) {
        waterMeterRepository.findByImei(imei)
                .ifPresent(waterMeter -> {
                    WmTestInfo testInfo = waterMeter.getTestInfo();
                    testInfo.setTestState(WmTestState.SUCCESS);
                    testInfo.setFailMessage(null);
                });
    }

    @Override
    public void updateTestResult(String imei, TestResult testResult) {
        waterMeterRepository.findByImei(imei)
                .ifPresent(waterMeter -> {
                    waterMeter.setTestResult(testResult);
                    applicationContext.publishEvent(new ExitProductionEvent(this, waterMeter));
                });
    }

    @Override
    public void updateCardInfo(String imei, String imsi, String iccid) {
        getWaterMeterByImei(imei)
                .ifPresent(waterMeter -> {
                    waterMeter.setImsi(imsi);
                    waterMeter.setIccid(iccid);
                });
    }

    @Override
    public void updateValveStatus(String imei, ValveStatus valveStatus) {
        getWaterMeterByImei(imei)
                .ifPresent(waterMeter -> {
                    MeterData meterData = waterMeter.getMeterData();
                    if (meterData == null) {
                        meterData = new MeterData();
                    }
                    meterData.setValveStatus(valveStatus);
                    waterMeter.setMeterData(meterData);
                });
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TestPlan> getTestPlan(long testBatchId) {
        return wmTestBatchRepository.getTestPlan(testBatchId);
    }

    @Override
    public void updateBoxNum(Set<Long> boxIds) {
        List<WmPackBox> packBoxes = wmPackBoxRepository.findByIdIn(boxIds);
        packBoxes.forEach(wmPackBox -> {
            int num = (int) waterMeterRepository.countByPackBoxId(Objects.requireNonNull(wmPackBox.getId()));
            wmPackBox.setPackNum(num);
        });
    }

}
