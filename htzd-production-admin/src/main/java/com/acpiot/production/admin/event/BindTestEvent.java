package com.acpiot.production.admin.event;

import com.acpiot.production.data.production.entity.WaterMeter;
import com.acpiot.production.data.production.entity.WmTestBatch;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Collection;
import java.util.List;

/**
 * 绑定表号事件
 * Created by moxin on 2020-11-26-0026
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Getter
public class BindTestEvent extends ApplicationEvent {

    private final WmTestBatch testBatch;
    private final List<WaterMeter> waterMeters;

    /**
     * Create a new {@code ApplicationEvent}.
     *
     * @param source      the object on which the event initially occurred or with
     *                    which the event is associated (never {@code null})
     * @param testBatch
     * @param waterMeters
     */
    public BindTestEvent(Object source, WmTestBatch testBatch, WaterMeter... waterMeters) {
        super(source);
        this.testBatch = testBatch;
        this.waterMeters = List.of(waterMeters);
    }

    public BindTestEvent(Object source, WmTestBatch testBatch, Collection<WaterMeter> waterMeters) {
        super(source);
        this.testBatch = testBatch;
        this.waterMeters = List.copyOf(waterMeters);
    }

}
