package com.acpiot.production.admin.mvc.em_production.service;

import com.acpiot.production.data.em_production.entity.EmTestBatch;
import org.springframework.data.domain.Page;
import pers.mx.jupiter.jpa.PagingQueryParams;
import pers.mx.jupiter.jpa.exception.FieldException;

import java.util.Optional;

/**
 * Created by moxin on 2021-05-19-0019
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface EmTestBatchService {
    Page<EmTestBatch> pagedTestBatches(PagingQueryParams<EmTestBatch> params);

    void removeTestBatch(long id);

    void removeTestBatches(long[] ids);

    EmTestBatch addTestBatch(EmTestBatch testBatch) throws FieldException;

    Optional<EmTestBatch> getTestBatchById(long id);

    void updateTestBatch(EmTestBatch testBatch) throws FieldException;
}
