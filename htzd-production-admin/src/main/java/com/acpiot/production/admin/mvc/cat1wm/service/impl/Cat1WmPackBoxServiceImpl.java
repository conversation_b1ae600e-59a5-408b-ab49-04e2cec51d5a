package com.acpiot.production.admin.mvc.cat1wm.service.impl;

import cn.hutool.core.util.StrUtil;
import com.acpiot.production.data.exception.BusinessException;
import com.acpiot.production.admin.mvc.cat1wm.dto.Cat1WmPackPagingQuery;
import com.acpiot.production.admin.mvc.cat1wm.service.Cat1WmPackBoxService;
import com.acpiot.production.admin.mvc.common.dto.SearchPagingQueryParam;
import com.acpiot.production.data.cat1wm.entity.*;
import com.acpiot.production.data.cat1wm.repository.Cat1WaterMeterRepository;
import com.acpiot.production.data.cat1wm.repository.Cat1WmPackBoxRepository;
import com.acpiot.production.data.cat1wm.repository.Cat1WmTestBatchRepository;
import com.acpiot.production.data.common.entity.Customer;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.mx.jupiter.jpa.Filter;

import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.IntStream;

import static com.acpiot.production.admin.config.security.SecurityContextUtils.filterByCustomer;
import static com.acpiot.production.admin.config.security.SecurityContextUtils.filterByTestBatchIn;

/**
 * Created by moxin on 2020-12-07-0007
 *
 * <AUTHOR> Email: <EMAIL>
 */
@RequiredArgsConstructor
@Service
@Transactional(rollbackFor = Exception.class)
public class Cat1WmPackBoxServiceImpl implements Cat1WmPackBoxService {

    private final Cat1WmTestBatchRepository cat1WmTestBatchRepository;
    private final Cat1WmPackBoxRepository catWmPackBoxRepository;
    private final Cat1WaterMeterRepository cat1WaterMeterRepository;

    @Override
    @Transactional(readOnly = true)
    public Page<Cat1WmPackBox> pagedPackBoxes(SearchPagingQueryParam<Cat1WmPackBox> params) {
        String search = params.getSearch();
        if (StrUtil.isNotBlank(search)) {
            params.and(Filter.like(Cat1WmPackBox_.BOX_NAME, search.trim()));
        }
        filterByCustomer(Cat1WmPackBox_.CUSTOMER).ifPresent(params::and);
        filterByTestBatchIn(Cat1WmPackBox_.TEST_BATCH).ifPresent(params::and);
        params.fetch(Cat1WmPackBox_.CUSTOMER).fetch(Cat1WmPackBox_.TEST_BATCH);
        return catWmPackBoxRepository.findAll(params);
    }

    @Override
    public void generateBox(long batchId) {
        Cat1WmTestBatch testBatch = cat1WmTestBatchRepository.findById(batchId)
                .orElseThrow(() -> new BusinessException("该批次不存在"));
        int boxMaxNum = testBatch.getBoxMaxNum();
        if (boxMaxNum <= 0) {
            throw new BusinessException("请设置该批次打包箱最大装载数量");
        }

        // 批次下的模块数量
        int moduleCount = (int) cat1WaterMeterRepository.countByTestBatch(testBatch);

        // 计算需要的打包箱数量
        int boxNum = moduleCount / boxMaxNum;
        if (moduleCount % boxMaxNum != 0) {
            boxNum++;
        }

        List<Cat1WmPackBox> packBoxes = catWmPackBoxRepository.findByTestBatchIdOrderByOrderNo(batchId);
        if (boxNum <= packBoxes.size()) {
            throw new BusinessException("打包箱数量已达到该批次下的最大数量");
        }

        String name = testBatch.getName();
        Customer customer = testBatch.getCustomer();
        List<Cat1WmPackBox> wmPackBoxes = IntStream.rangeClosed(1, boxNum)
                .filter(i -> packBoxes.stream().noneMatch(packBox -> packBox.getOrderNo() == i))
                .mapToObj(i -> {
                    Cat1WmPackBox packBox = new Cat1WmPackBox();
                    packBox.setCustomer(customer);
                    packBox.setTestBatch(testBatch);
                    packBox.setBoxName(StrUtil.format("{}-{}", name, i));
                    packBox.setOrderNo(i);
                    return packBox;
                })
                .toList();
        catWmPackBoxRepository.saveAll(wmPackBoxes);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Cat1WmPackBox> getPackBoxesByTestBatch(long batchId) {
        return catWmPackBoxRepository.findByTestBatchIdOrderByOrderNo(batchId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Cat1WaterMeter> pagedWaterMeters(Cat1WmPackPagingQuery param) {
        param.initExtFilters();
        return cat1WaterMeterRepository.findAll(param);
    }

    @Override
    public void removePackBox(long id) {
        if (cat1WaterMeterRepository.existsByPackBoxId(id)) {
            throw new BusinessException("该打包箱下有水表，不能删除");
        }
        catWmPackBoxRepository.deleteById(id);
    }

    @Override
    public boolean packWaterMeter(long boxId, String deviceNo) {
        return packModule(boxId, deviceNo, null);
    }

    private boolean packModule(long boxId, String deviceNo, Consumer<Cat1WaterMeter> consumer) {
        Cat1WmPackBox packBox = catWmPackBoxRepository.findById(boxId)
                .orElseThrow(() -> new BusinessException("该打包箱不存在"));
        Cat1WmTestBatch testBatch = packBox.getTestBatch();
        int boxMaxNum = testBatch.getBoxMaxNum();
        if (boxMaxNum <= 0) {
            throw new BusinessException("该批次未设置最大装箱数量，请先进行设置");
        }

        Cat1WaterMeter dbWaterMeter = cat1WaterMeterRepository.findByDeviceNo(testBatch, deviceNo)
                .orElseThrow(() -> new BusinessException("该设备号不存在"));
        Cat1WmTestInfo testInfo = dbWaterMeter.getModuleTestInfo();
        if (testInfo == null
                || testInfo.getTestState() != Cat1WmTestState.SUCCESS) {
            throw new BusinessException("该模块未测试成功，请检查");
        }
        if (testBatch.isCheckTestCurrent()) {
            if (dbWaterMeter.getTestCurrent() == null) {
                throw new BusinessException("该模块未测试电流，请检查");
            }
        }
        Cat1WmPackBox meterBox = dbWaterMeter.getPackBox();
        if (meterBox != null) {
            if (!Objects.equals(meterBox.getId(), packBox.getId())) {
                throw new BusinessException("该水表已打包到 {} 包装箱，请检查", meterBox.getBoxName());
            } else {
                return false;
            }
        }
        if (packBox.getPackNum() >= boxMaxNum) {
            throw new BusinessException("该打包箱已装满，请重新选择新的打包箱");
        }

        dbWaterMeter.setPackBox(packBox);
        packBox.setPackNum((int) cat1WaterMeterRepository.countByPackBox(packBox));
        if (consumer != null) {
            consumer.accept(dbWaterMeter);
        }
        return true;
    }

    @Override
    public boolean packModuleAndUpdate(long boxId, String imei, String assetNo) {
        return packModule(boxId, imei, waterMeter -> {
            if (!cat1WaterMeterRepository.isUnique(waterMeter, Cat1WaterMeter_.assetNo)) {
                throw new BusinessException("该资产号已和其它水表绑定");
            }
            waterMeter.setAssetNo(assetNo);
        });
    }

    @Override
    public void unpackWaterMeter(long boxId, long meterId) {
        Cat1WmPackBox packBox = catWmPackBoxRepository.findById(boxId)
                .orElseThrow(() -> new BusinessException("该打包箱不存在"));
        Cat1WaterMeter waterMeter = cat1WaterMeterRepository.findById(meterId)
                .orElseThrow(() -> new BusinessException("该水表模块不存在"));
        Cat1WmPackBox oldBox = waterMeter.getPackBox();
        if (!Objects.equals(packBox, oldBox)) {
            throw new BusinessException("该水表模块不在该打包箱中");
        }
        waterMeter.setPackBox(null);
        packBox.setPackNum((int) cat1WaterMeterRepository.countByPackBox(packBox));
    }

}
