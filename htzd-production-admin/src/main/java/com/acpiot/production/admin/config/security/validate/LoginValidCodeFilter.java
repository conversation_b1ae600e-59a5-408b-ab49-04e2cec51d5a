package com.acpiot.production.admin.config.security.validate;

import com.acpiot.production.admin.config.properties.SecurityProperties;
import com.acpiot.production.admin.config.security.constant.SecurityConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import java.io.IOException;

/**
 * 验证码过滤器
 *
 * <AUTHOR> E-mail: <EMAIL>
 * @version 18/4/13 上午9:55
 */
@RequiredArgsConstructor
@Slf4j
@Component
public class LoginValidCodeFilter extends OncePerRequestFilter {

    private final SecurityProperties securityProperties;
    private final AuthenticationFailureHandler failureHandler;

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request,
                                    @NonNull HttpServletResponse response,
                                    @NonNull FilterChain filterChain) throws ServletException, IOException {
        // 判断URL是否需要验证
        boolean action = new AntPathRequestMatcher(securityProperties.getLoginProcessUrl()).matches(request);

        // 拦截请求
        if (action) {
            try {
                validate(request);
            } catch (ValidateCodeException exception) {
                //返回错误信息给 失败处理器
                failureHandler.onAuthenticationFailure(request, response, exception);
                return;
            }
        }

        // 不做任何处理，调用后面的 过滤器
        filterChain.doFilter(request, response);
    }

    private void validate(HttpServletRequest request) throws ServletRequestBindingException {
        HttpSession session = request.getSession();

        // 从request 请求中 取出 验证码
        String codeInRequest = ServletRequestUtils.getStringParameter(request, securityProperties.getCodeParameter());
        try {
            ValidCode validCode = (ValidCode) session.getAttribute(SecurityConstant.VALID_CODE);
            if (validCode == null
                    || !validCode.verify(codeInRequest)) {
                throw new ValidateCodeException("验证码不匹配");
            }
        } finally {
            // 把对应 的 session信息  删掉
            session.removeAttribute(SecurityConstant.VALID_CODE);
        }
    }
}
