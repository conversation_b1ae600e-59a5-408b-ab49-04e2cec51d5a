<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/layout}">
<head>
  <title>新建测试批次</title>
  <link rel="stylesheet" type="text/css" th:href="@{/webjars/light-year-admin/js/bootstrap-select/bootstrap-select.css}">
</head>
<body>

<th:block layout:fragment="page-content">
  <div class="row">
    <div class="col-lg-12">
      <div class="card full-card">
        <div class="card-header"><div class="card-title">新建测试批次</div></div>
        <div class="card-body">

          <form id="form" class="my-inline-form" th:action="@{/production/testbatch/add}" method="post" th:object="${testBatch}">
            <div class="form-group">
              <label for="customer.id"><span class="text-danger">*</span>&nbsp;所属客户</label>
              <select class="form-control selectpicker" th:field="*{customer.id}" data-live-search="true" required>
                <option value="">请选择</option>
                <option th:each="customer : ${customers}" th:value="${customer.id}" th:text="${customer.name}"></option>
              </select>
              <label th:if="${#fields.hasErrors('customer.id')}" th:errors="*{customer.id}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="testPlan.id"><span class="text-danger">*</span>&nbsp;选择测试计划</label>
              <select class="form-control selectpicker" th:field="*{testPlan.id}" required>
                <option value="">请选择</option>
                <option th:each="testPlan : ${testPlans}" th:value="${testPlan.id}" th:text="${testPlan.planName}"></option>
              </select>
              <label th:if="${#fields.hasErrors('testPlan.id')}" th:errors="*{testPlan.id}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="name"><span class="text-danger">*</span>&nbsp;测试批次名称</label>
              <input class="form-control" type="text" th:field="*{name}" placeholder="测试批次名称" required maxlength="64">
              <label th:if="${#fields.hasErrors('name')}" th:errors="*{name}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="meterVersion"><span class="text-danger">*</span>&nbsp;模块版本</label>
              <input class="form-control" type="text" th:field="*{meterVersion}" placeholder="模块版本" required minlength="32" maxlength="255">
              <label th:if="${#fields.hasErrors('meterVersion')}" th:errors="*{meterVersion}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="factor"><span class="text-danger">*</span>&nbsp;计量小数点位数</label>
              <input class="form-control" type="number" th:field="*{factor}" placeholder="计量小数点位数" required min="0" max="4">
              <label th:if="${#fields.hasErrors('factor')}" th:errors="*{factor}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="productionTimeout"><span class="text-danger">*</span>&nbsp;产测超时时间（天）</label>
              <input class="form-control" type="number" th:field="*{productionTimeout}" placeholder="产测超时时间（天）" required min="1" max="30">
            </div>
            <div class="form-group">
              <label for="rssiThreshold"><span class="text-danger">*</span>&nbsp;RSSI阈值</label>
              <input class="form-control" type="number" th:field="*{rssiThreshold}" placeholder="RSSI阈值" required min="-120" max="0">
            </div>
            <div class="form-group">
              <label for="snrThreshold"><span class="text-danger">*</span>&nbsp;SNR阈值</label>
              <input class="form-control" type="number" th:field="*{snrThreshold}" placeholder="SNR阈值" required min="-100" max="100">
            </div>
            <div class="form-group">
              <label for="minValue"><span class="text-danger">*</span>&nbsp;最小表读数</label>
              <input class="form-control" type="number" th:field="*{minValue}" placeholder="最小表读数" required min="0" max="999999.999" step="0.001">
            </div>
            <div class="form-group">
              <label for="iotPlatform"><span class="text-danger">*</span>&nbsp;IoT平台</label>
              <select class="form-control selectpicker" th:field="*{iotPlatform}" required>
                <option value="">请选择</option>
                <option th:each="iotPlatform : ${iotPlatforms}" th:value="${iotPlatform}" th:text="#{'IoTPlatform_' + ${iotPlatform}}"></option>
              </select>
              <label th:if="${#fields.hasErrors('iotPlatform')}" th:errors="*{iotPlatform}" class="text-danger"></label>
            </div>
            <div class="form-group full-row" style="margin-bottom: 0">
              <label><span class="text-danger">*</span> 模块测试开关</label>
              <div class="form-row">
                <div class="form-group">
                  <label>模块测试</label>
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" th:field="*{moduleTest}" class="custom-control-input">
                    <label class="custom-control-label" for="moduleTest1">是</label>
                  </div>
                </div>
                <div class="form-group">
                  <label>触摸上报</label>
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" th:field="*{validReport}" class="custom-control-input">
                    <label class="custom-control-label" for="validReport1">验证</label>
                  </div>
                </div>
                <div class="form-group">
                  <label>夹具产测标志</label>
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" th:field="*{validFixtureFlag}" class="custom-control-input">
                    <label class="custom-control-label" for="validFixtureFlag1">验证</label>
                  </div>
                </div>
                <div class="form-group">
                  <label>eDRX周期</label>
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" th:field="*{configEdrx}" class="custom-control-input">
                    <label class="custom-control-label" for="configEdrx1">设置</label>
                  </div>
                </div>
                <div class="form-group">
                  <label>抄读冻结</label>
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" th:field="*{validFrozenData}" class="custom-control-input">
                    <label class="custom-control-label" for="validFrozenData1">验证</label>
                  </div>
                </div>
                <div class="form-group">
                  <label>IMEI做资产号</label>
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" th:field="*{imeiAsAssetCode}" class="custom-control-input">
                    <label class="custom-control-label" for="imeiAsAssetCode1">是</label>
                  </div>
                </div>
                <div class="form-group">
                  <label>BLE</label>
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" th:field="*{validBle}" class="custom-control-input">
                    <label class="custom-control-label" for="validBle1">验证</label>
                  </div>
                </div>
                <div class="form-group">
                  <label>BLE</label>
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" th:field="*{customProtocol}" class="custom-control-input">
                    <label class="custom-control-label" for="customProtocol">验证</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label for="updateCardInfo"><span class="text-danger">*</span>&nbsp;小程序更新卡信息</label>
              <div class="custom-control custom-checkbox">
                <input type="checkbox" th:field="*{updateCardInfo}" class="custom-control-input">
                <label class="custom-control-label" for="updateCardInfo1">更新</label>
              </div>
            </div>
            <div class="form-group">
              <label for="checkTestCurrent1"><span class="text-danger">*</span>&nbsp;检查测试电流</label>
              <div class="custom-control custom-checkbox">
                <input type="checkbox" th:field="*{checkTestCurrent}" class="custom-control-input">
                <label class="custom-control-label" for="checkTestCurrent1">检查</label>
              </div>
            </div>
            <div class="form-group">
              <label for="validApn">校验APN</label>
              <input class="form-control" type="text" th:field="*{validApn}" placeholder="APN，无需校验则不用填写" maxlength="15">
              <label th:if="${#fields.hasErrors('validApn')}" th:errors="*{validApn}" class="text-danger"></label>
            </div>
            <div class="form-group full-row" style="margin-bottom: 0">
              <label><span class="text-danger">*</span> 成表测试开关</label>
              <div class="form-row">
                <div class="form-group">
                  <label>表底数</label>
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" th:field="*{resetValue}" class="custom-control-input">
                    <label class="custom-control-label" for="resetValue1">重置</label>
                  </div>
                </div>
                <div class="form-group">
                  <label>资产号作为表号</label>
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" th:field="*{reuseAssetNo}" class="custom-control-input">
                    <label class="custom-control-label" for="reuseAssetNo1">是</label>
                  </div>
                </div>
                <div class="form-group">
                  <label>成表产测标志</label>
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" th:field="*{setFactoryFlag}" class="custom-control-input">
                    <label class="custom-control-label" for="setFactoryFlag1">重置</label>
                  </div>
                </div>
                <div class="form-group">
                  <label>成表产测结束标志</label>
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" th:field="*{setFactoryTestFinish}" class="custom-control-input">
                    <label class="custom-control-label" for="setFactoryTestFinish1">设置</label>
                  </div>
                </div>
                <div class="form-group">
                  <label>上电关阀</label>
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" th:field="*{firstCloseValve}" class="custom-control-input">
                    <label class="custom-control-label" for="firstCloseValve1">是</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label for="boxMaxNum"><span class="text-danger">*</span>&nbsp;打包箱最大装载数量</label>
              <input class="form-control" type="number" th:field="*{boxMaxNum}" placeholder="打包箱最大装载数量" required min="0" max="65535">
            </div>
            <div class="form-group">
              <label for="edrxPeriod2"><span class="text-danger">*</span>&nbsp;eDRX周期</label>
              <div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" th:field="*{edrxPeriod}" id="edrxPeriod1" value="ONE_MINUTE">
                  <label class="form-check-label" for="edrxPeriod1">81.92s</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" th:field="*{edrxPeriod}" id="edrxPeriod2" value="TEN_MINUTES">
                  <label class="form-check-label" for="edrxPeriod2">655.36s</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" th:field="*{edrxPeriod}" id="edrxPeriod3" value="TWENTY_MINUTES">
                  <label class="form-check-label" for="edrxPeriod3">1310.72s</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" th:field="*{edrxPeriod}" id="edrxPeriod4" value="POWER_OFF">
                  <label class="form-check-label" for="edrxPeriod4">断电</label>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label><span class="text-danger">*</span>&nbsp;移动运营商检测</label>
              <div>
                <div class="form-check form-check-inline" th:each="mobileOperator,stat : ${mobileOperators}">
                  <input class="form-check-input" type="radio" th:field="*{mobileOperator}" th:value="${mobileOperator}">
                  <label class="form-check-label" th:for="'mobileOperator' + ${stat.count}" th:text="#{'MobileOperator_' + ${mobileOperator}}">不限制</label>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label><span class="text-danger">*</span>&nbsp;是否有阀</label>
              <div class="custom-control custom-checkbox">
                <input type="checkbox" th:field="*{valve}" class="custom-control-input">
                <label class="custom-control-label" for="valve1">是</label>
              </div>
            </div>
            <div class="form-group" style="margin-bottom: 0">
              <label>电压范围</label>
              <div class="form-row">
                <div class="form-group">
                  <label for="voltageRange.minVoltage"><span class="text-danger">*</span>&nbsp;最小电压</label>
                  <input class="form-control" type="number" th:field="*{voltageRange.minVoltage}" placeholder="最小电压" required min="0" step="0.01">
                  <label th:if="${#fields.hasErrors('voltageRange.minVoltage')}" th:errors="*{voltageRange.minVoltage}" class="text-danger"></label>
                </div>
                <div class="form-group">
                  <label for="voltageRange.maxVoltage"><span class="text-danger">*</span>&nbsp;最大电压</label>
                  <input class="form-control" type="number" th:field="*{voltageRange.maxVoltage}" placeholder="最大电压" required min="0" step="0.01">
                  <label th:if="${#fields.hasErrors('voltageRange.maxVoltage')}" th:errors="*{voltageRange.maxVoltage}" class="text-danger"></label>
                </div>
              </div>
            </div>
            <div class="form-group full-row">
              <label for="taskLinkUrl">关联任务</label>
              <input type="url" class="form-control" th:field="*{taskLinkUrl}" maxlength="1024">
              <label th:if="${#fields.hasErrors('taskLinkUrl')}" th:errors="*{taskLinkUrl}" class="text-danger"></label>
            </div>
            <div class="form-group full-row">
              <label for="description">备注</label>
              <textarea class="form-control" th:field="*{description}" rows="3" style="width: 100%" maxlength="255"></textarea>
              <label th:if="${#fields.hasErrors('description')}" th:errors="*{description}" class="text-danger"></label>
            </div>
            <th:block th:replace="~{fragments/form-footer :: submit-back-footer}"/>
          </form>

        </div>
      </div>
    </div>

  </div>
</th:block>

<th:block layout:fragment="page-script">
  <script type="text/javascript" th:src="@{/webjars/light-year-admin/js/bootstrap-select/bootstrap-select.min.js}"></script>
  <script type="text/javascript" th:src="@{/webjars/light-year-admin/js/bootstrap-select/i18n/defaults-zh_CN.min.js}"></script>
  <script type="text/javascript" th:src="@{/webjars/jquery-validation/dist/jquery.validate.min.js}"></script>
  <script type="text/javascript" th:src="@{/webjars/jquery-validation/dist/localization/messages_zh.min.js}"></script>
  <script type="text/javascript" th:src="@{/js/common/jq-validation-bs.js}"></script>
  <script type="text/javascript" th:src="@{/webjars/jsencrypt/bin/jsencrypt.min.js}"></script>
  <script type="text/javascript" th:src="@{/js/production/testbatch/add.js}"></script>
</th:block>

</body>
</html>
