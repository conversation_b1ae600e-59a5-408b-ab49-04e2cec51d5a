<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security"
      layout:decorate="~{layout/layout}">
<head>
  <title>测试批次管理</title>

  <!--对话框-->
  <link rel="stylesheet" th:href="@{/webjars/light-year-admin/js/jquery-confirm/jquery-confirm.min.css}">
  <link rel="stylesheet" th:href="@{/webjars/bootstrap-table/dist/bootstrap-table.min.css}">

  <link rel="stylesheet" type="text/css" th:href="@{/webjars/light-year-admin/js/bootstrap-select/bootstrap-select.css}">
</head>
<body>

<th:block layout:fragment="page-content">
  <div class="row">
    <div class="col-lg-12">
      <div class="card">
        <div class="card-body">

          <form id="tableFilter" class="form-inline" role="form">
            <div class="form-group mr-sm-2">
              <div class="input-group">
                <div class="input-group-prepend">
                  <div class="input-group-text">所属客户</div>
                </div>
                <select class="form-control selectpicker table-filter" id="select-customer" data-live-search="true" data-filter="customer.id" data-operator="eq">
                  <option value="">请选择</option>
                  <option th:each="customer : ${customers}" th:value="${customer.id}" th:text="${customer.name}"></option>
                </select>
              </div>
            </div>
            <div class="form-group mr-sm-2">
              <div class="input-group">
                <div class="input-group-prepend">
                  <div class="input-group-text">批次名称</div>
                </div>
                <input type="text" class="form-control table-filter" data-filter="name" data-operator="like" data-ignorecase="true" placeholder="请输入测试批次名称"/>
              </div>
            </div>
            <div class="form-group">
              <button class="btn btn-primary mr-sm-2" type="button" onclick="$('#table').reloadTable();">查询</button>
              <button class="btn btn-secondary" type="button" onclick="clearFilters();">清除</button>
            </div>
          </form>

        </div>
      </div>

      <div class="card table-card">
        <div class="card-body">

          <div id="toolbar" class="toolbar-btn-action">
            <a class="btn btn-primary m-r-5" th:href="@{/production/testbatch/add}" sec:authorize-url="/production/testbatch/add"><i class="mdi mdi-plus"></i> 新增</a>
            <a class="btn btn-danger m-r-5" href="javascript:$('#table').deleteTableSelections('id');" sec:authorize-url="/production/testbatch/batchDel**"><i class="mdi mdi-window-close"></i> 删除</a>
          </div>

          <input type="hidden" id="hasEdit" value="true" sec:authorize-url="/production/testbatch/edit**">
          <input type="hidden" id="hasDel" value="true" sec:authorize-url="/production/testbatch/delete**">

          <table id="table"
                 class="f-table-r stripe-table"
                 data-icons-prefix="mdi"
                 data-icons="icons"
                 data-url="page"
                 data-sort-order="desc"
                 data-sort-name="createdDate"
                 data-toolbar="#toolbar"
                 data-id-field="id"
                 data-unique-id="id"
                 data-click-to-select="true"
                 data-striped="true"
                 data-cache="false"
                 data-method="post"
                 data-show-columns="false"
                 data-show-refresh="true"
                 data-pagination="true"
                 data-side-pagination="server"
                 data-data-field="content"
                 data-total-field="totalElements"
                 data-page-size="20"
                 data-detail-view="true"
                 data-detail-formatter="detailFormatter">
            <thead>
            <tr>
              <th rowspan="2" data-checkbox="true">选择</th>
              <th rowspan="2" data-field="name" data-sortable="true" data-class="nowrap">批次名称</th>
              <th rowspan="2" data-field="testPlan.planName" data-sortable="true">关联测试计划</th>
              <th rowspan="2" data-field="createdDate" data-sortable="true" data-align="center" data-class="nowrap td-time">创建时间</th>
              <th rowspan="2" data-field="iotPlatform" data-sortable="true" data-align="center" data-class="nowrap" data-formatter="iotPlatformFormatter">IoT平台</th>
              <th rowspan="2" data-field="valve" data-sortable="true" data-align="center" data-formatter="booleanFormatter">是否有阀</th>
              <th rowspan="2" data-field="edrxPeriod" data-sortable="true" data-align="center" data-formatter="edrxPeriodFormatter">eDRX周期</th>
              <th rowspan="2" data-field="meterVersion" data-sortable="true">模块版本</th>
              <th rowspan="2" data-field="factor" data-sortable="true" data-align="center">计量小数点位数</th>
              <th rowspan="2" data-field="productionTimeout" data-sortable="true" data-align="center">产测超时时间（天）</th>
              <th rowspan="2" data-field="rssiThreshold" data-sortable="true" data-align="center">RSSI阈值</th>
              <th rowspan="2" data-field="snrThreshold" data-sortable="true" data-align="center">SNR阈值</th>
              <th rowspan="2" data-field="voltageRange" data-sortable="true" data-align="center" data-class="nowrap" data-formatter="voltageRangeFormatter">电压范围</th>
              <th rowspan="2" data-field="minValue" data-sortable="true" data-align="center">最小表读数</th>
              <th colspan="7" data-align="center">模块测试开关</th>
              <th rowspan="2" data-field="validApn" data-sortable="true" data-align="center">校验APN</th>
              <th colspan="5" data-align="center">成表测试开关</th>
              <th rowspan="2" data-field="mobileOperator" data-sortable="true" data-formatter="mobileOperatorFormatter">移动运营商检测</th>
              <th rowspan="2" data-field="boxMaxNum" data-sortable="true">打包箱最大装载数量</th>
              <th rowspan="2" data-field="updateCardInfo" data-sortable="true" data-formatter="booleanFormatter">是否小程序更新卡信息</th>
              <th rowspan="2" data-field="checkTestCurrent" data-sortable="true" data-formatter="booleanFormatter">是否检查电流</th>
              <th rowspan="2" data-field="operate" data-align="center" data-width="80" data-click-to-select="false" data-formatter="operateFormatter" data-events="operateEvents">操作</th>
            </tr>
            <tr>
              <th data-field="moduleTest" data-sortable="true" data-align="center" data-formatter="booleanFormatter">进行模块测试</th>
              <th data-field="validReport" data-sortable="true" data-align="center" data-formatter="booleanFormatter">验证触摸上报</th>
              <th data-field="validFixtureFlag" data-sortable="true" data-align="center" data-formatter="booleanFormatter">验证夹具产测标志</th>
              <th data-field="configEdrx" data-sortable="true" data-align="center" data-formatter="booleanFormatter">设置eDRX周期</th>
              <th data-field="validFrozenData" data-sortable="true" data-align="center" data-formatter="booleanFormatter">验证抄读冻结</th>
              <th data-field="imeiAsAssetCode" data-sortable="true" data-align="center" data-formatter="booleanFormatter">IMEI做资产号</th>
              <th data-field="validBle" data-sortable="true" data-align="center" data-formatter="booleanFormatter">验证BLE</th>
              <th data-field="customProtocol" data-sortable="true" data-align="center" data-formatter="booleanFormatter">验证BLE</th>

              <th data-field="resetValue" data-sortable="true" data-align="center" data-formatter="booleanFormatter">重置表底数</th>
              <th data-field="reuseAssetNo" data-sortable="true" data-align="center" data-formatter="booleanFormatter">使用资产号为表号</th>
              <th data-field="setFactoryFlag" data-sortable="true" data-align="center" data-formatter="booleanFormatter">重置成表产测标志</th>
              <th data-field="setFactoryTestFinish" data-sortable="true" data-align="center" data-formatter="booleanFormatter">设置成表产测结束标志</th>
              <th data-field="firstCloseValve" data-sortable="true" data-align="center" data-formatter="booleanFormatter">是否上电关阀</th>
            </tr>
            </thead>
          </table>

        </div>
      </div>
    </div>
  </div>
</th:block>

<th:block layout:fragment="page-script">
  <script th:src="@{/webjars/jsrender/jsrender.min.js}"></script>
  <script id="detailViewTmpl" type="text/x-jsrender">
    <table class="table table-bordered">
      <tbody>
        <tr>
          <th style="width: 120px;">创建人:</th>
          <td>{{:createdBy}}</td>

          <th style="width: 120px;">修改人:</th>
          <td>{{:lastModifiedBy}}</td>

          <th style="width: 120px;">更新时间:</th>
          <td>{{:lastModifiedDate}}</td>
        </tr>
        <tr>
          <th>关联任务:</th>
          <td>
            {{if taskLinkUrl}}
              <a href="{{:taskLinkUrl}}" target="_blank">点击跳转</a>
            {{else}}
              未关联
            {{/if}}
          </td>

          <th>备注:</th>
          <td colspan="3">{{:description}}</td>
        </tr>
      </tbody>
    </table>
  </script>

  <script type="text/javascript" th:src="@{/webjars/light-year-admin/js/bootstrap-select/bootstrap-select.min.js}"></script>
  <script type="text/javascript" th:src="@{/webjars/light-year-admin/js/bootstrap-select/i18n/defaults-zh_CN.min.js}"></script>

  <!--对话框-->
  <script th:src="@{/webjars/light-year-admin/js/jquery-confirm/jquery-confirm.min.js}"></script>
  <script th:src="@{/js/common/confirm-extend.js}"></script>
  <script th:src="@{/webjars/bootstrap-table/dist/bootstrap-table.min.js}"></script>
  <script th:src="@{/webjars/bootstrap-table/dist/locale/bootstrap-table-zh-CN.min.js}"></script>
  <script th:src="@{/js/common/bootstrap-table-custom.js}"></script>
  <script th:src="@{/js/common/query-filter.js}"></script>

  <script th:src="@{/js/common/enum-formatter.js}"></script>
  <script th:src="@{/js/production/testbatch/list.js}"></script>
</th:block>
</body>
</html>
