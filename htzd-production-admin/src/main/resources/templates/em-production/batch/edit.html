<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/layout}">
<head>
  <title>编辑电表产测批次</title>
  <link rel="stylesheet" type="text/css" th:href="@{/webjars/light-year-admin/js/bootstrap-select/bootstrap-select.css}">
</head>
<body>

<th:block layout:fragment="page-content">
  <div class="row">
    <div class="col-lg-12">
      <div class="card full-card">
        <div class="card-header"><div class="card-title">编辑测试批次</div></div>
        <div class="card-body">

          <form id="form"  class="my-inline-form" th:action="@{/em-production/batch/edit}" method="post" th:object="${testBatch}">
            <input type="hidden" th:field="*{id}">
            <div class="form-group">
              <label for="name"><span class="text-danger">*</span>&nbsp;测试批次名称</label>
              <input class="form-control" type="text" th:field="*{name}" placeholder="测试批次名称" required maxlength="64">
              <label th:if="${#fields.hasErrors('name')}" th:errors="*{name}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="meterVersion"><span class="text-danger">*</span>&nbsp;模块版本</label>
              <input class="form-control" type="text" th:field="*{meterVersion}" placeholder="模块版本" required minlength="22" maxlength="22">
              <label th:if="${#fields.hasErrors('meterVersion')}" th:errors="*{meterVersion}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="validFixtureFlag1"><span class="text-danger">*</span>&nbsp;验证夹具测试结果</label>
              <div class="form-controls">
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" th:field="*{validFixtureFlag}" value="true" required>
                  <label class="form-check-label" for="validFixtureFlag1">验证</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" th:field="*{validFixtureFlag}" value="false" required>
                  <label class="form-check-label" for="validFixtureFlag2">跳过</label>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label for="setFactoryFlag1"><span class="text-danger">*</span>&nbsp;设置成表产测标志</label>
              <div class="form-controls">
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" th:field="*{setFactoryFlag}" value="true" required>
                  <label class="form-check-label" for="setFactoryFlag1">设置</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" th:field="*{setFactoryFlag}" value="false" required>
                  <label class="form-check-label" for="setFactoryFlag2">跳过</label>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label for="rssiThreshold"><span class="text-danger">*</span>&nbsp;RSSI阈值</label>
              <input class="form-control" type="number" th:field="*{rssiThreshold}" placeholder="RSSI阈值" required min="-120" max="0">
            </div>
            <div class="form-group">
              <label for="snrThreshold"><span class="text-danger">*</span>&nbsp;SNR阈值</label>
              <input class="form-control" type="number" th:field="*{snrThreshold}" placeholder="SNR阈值" required min="-100" max="100">
            </div>
            <div class="form-group">
              <label for="imeiAsBoxCode1"><span class="text-danger">*</span>&nbsp;IMEI作为表壳号</label>
              <div class="custom-control custom-checkbox">
                <input type="checkbox" th:field="*{imeiAsBoxCode}" class="custom-control-input">
                <label class="custom-control-label" for="imeiAsBoxCode1">是</label>
              </div>
            </div>
            <div class="form-group">
              <label><span class="text-danger">*</span>&nbsp;移动运营商检测</label>
              <div>
                <div class="form-check form-check-inline" th:each="mobileOperator,stat : ${mobileOperators}">
                  <input class="form-check-input" type="radio" th:field="*{mobileOperator}" th:value="${mobileOperator}">
                  <label class="form-check-label" th:for="'mobileOperator' + ${stat.count}" th:text="#{'MobileOperator_' + ${mobileOperator}}">不限制</label>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label for="iotPlatform"><span class="text-danger">*</span>&nbsp;IoT平台</label>
              <select class="form-control selectpicker" th:field="*{iotPlatform}" required>
                <option value="">请选择</option>
                <option th:each="iotPlatform : ${iotPlatforms}" th:value="${iotPlatform}" th:text="#{'IoTPlatform_' + ${iotPlatform}}"></option>
              </select>
              <label th:if="${#fields.hasErrors('iotPlatform')}" th:errors="*{iotPlatform}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="boxMaxNum"><span class="text-danger">*</span>&nbsp;打包箱最大装载数量</label>
              <input class="form-control" type="number" th:field="*{boxMaxNum}" placeholder="打包箱最大装载数量" required min="0" max="65535">
            </div>
            <div class="form-group full-row">
              <label for="taskLinkUrl">关联任务</label>
              <input type="url" class="form-control" th:field="*{taskLinkUrl}" maxlength="1024">
              <label th:if="${#fields.hasErrors('taskLinkUrl')}" th:errors="*{taskLinkUrl}" class="text-danger"></label>
            </div>
            <div class="form-group full-row">
              <label for="description">备注</label>
              <textarea class="form-control" th:field="*{description}" rows="3" style="width: 100%" maxlength="255"></textarea>
              <label th:if="${#fields.hasErrors('description')}" th:errors="*{description}" class="text-danger"></label>
            </div>
            <th:block th:replace="~{fragments/form-footer :: submit-back-footer}"/>
          </form>

        </div>
      </div>
    </div>

  </div>
</th:block>

<th:block layout:fragment="page-script">
  <script type="text/javascript" th:src="@{/webjars/light-year-admin/js/bootstrap-select/bootstrap-select.min.js}"></script>
  <script type="text/javascript" th:src="@{/webjars/light-year-admin/js/bootstrap-select/i18n/defaults-zh_CN.min.js}"></script>
  <script type="text/javascript" th:src="@{/webjars/jquery-validation/dist/jquery.validate.min.js}"></script>
  <script type="text/javascript" th:src="@{/webjars/jquery-validation/dist/localization/messages_zh.min.js}"></script>
  <script type="text/javascript" th:src="@{/js/common/jq-validation-bs.js}"></script>
  <script type="text/javascript" th:src="@{/js/em-production/batch/edit.js}"></script>
</th:block>

</body>
</html>
