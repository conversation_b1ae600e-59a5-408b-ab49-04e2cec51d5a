<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/layout}">
<head>
  <title>新建Cat.1水表产测批次</title>
  <link rel="stylesheet" type="text/css" th:href="@{/webjars/light-year-admin/js/bootstrap-select/bootstrap-select.css}">

  <style>
      #reportParam\.interval + .bootstrap-select {
          width: 80px;
      }
      .bootstrap-select > .dropdown-toggle {
          height: calc(1.5em + .75rem + 3px);
      }
  </style>
</head>
<body>

<th:block layout:fragment="page-content">
  <div class="row">
    <div class="col-lg-12">
      <div class="card full-card">
        <div class="card-header"><div class="card-title">新建测试批次</div></div>
        <div class="card-body">

          <form id="form"   class="my-inline-form" th:action="@{/cat1wm/batch/add}" method="post" th:object="${testBatch}">
            <div class="form-group">
              <label for="customer.id"><span class="text-danger">*</span>&nbsp;所属客户</label>
              <select class="form-control selectpicker" th:field="*{customer.id}" data-live-search="true" required>
                <option value="">请选择</option>
                <option th:each="customer : ${customers}" th:value="${customer.id}" th:text="${customer.name}"></option>
              </select>
              <label th:if="${#fields.hasErrors('customer.id')}" th:errors="*{customer.id}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="name"><span class="text-danger">*</span>&nbsp;测试批次名称</label>
              <input class="form-control" type="text" th:field="*{name}" placeholder="测试批次名称" required maxlength="64">
              <label th:if="${#fields.hasErrors('name')}" th:errors="*{name}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="meterVersion"><span class="text-danger">*</span>&nbsp;模块版本</label>
              <input class="form-control" type="text" th:field="*{meterVersion}" placeholder="模块版本" required maxlength="255">
              <label th:if="${#fields.hasErrors('meterVersion')}" th:errors="*{meterVersion}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="minValue"><span class="text-danger">*</span>&nbsp;最小表读数</label>
              <input class="form-control" type="number" th:field="*{minValue}" placeholder="最小表读数" required min="0" max="999999.999" step="0.001">
            </div>
            <!--<div class="form-group">
              <label for="master.ip">主站IP或域名</label>
              <input class="form-control" type="text" th:field="*{master.ip}" placeholder="主站IP或域名" required maxlength="50" data-rule-ipOrDomain="true">
              <label th:if="${#fields.hasErrors('master.ip')}" th:errors="*{master.ip}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="master.port">主站端口</label>
              <input class="form-control" type="number" th:field="*{master.port}" placeholder="主站端口" required min="1" max="65535">
              <label th:if="${#fields.hasErrors('master.port')}" th:errors="*{master.port}" class="text-danger"></label>
            </div>-->
            <div class="form-group">
              <label for="imeiAsAssetCode1"><span class="text-danger">*</span>&nbsp;IMEI做资产号</label>
              <div class="custom-control custom-checkbox">
                <input type="checkbox" th:field="*{imeiAsAssetCode}" class="custom-control-input">
                <label class="custom-control-label" for="imeiAsAssetCode1">是</label>
              </div>
            </div>
            <div class="form-group">
              <label for="packAssetCode1"><span class="text-danger">*</span>&nbsp;打包时更新资产号</label>
              <div class="custom-control custom-checkbox">
                <input type="checkbox" th:field="*{packAssetCode}" class="custom-control-input">
                <label class="custom-control-label" for="packAssetCode1">是</label>
              </div>
            </div>
            <div class="form-group full-row" style="margin-bottom: 0">
              <label>电压范围</label>
              <div class="form-row">
                <div class="form-group">
                  <label for="voltageRange.minVoltage"><span class="text-danger">*</span>&nbsp;最小电压</label>
                  <input class="form-control" type="number" th:field="*{voltageRange.minVoltage}" placeholder="最小电压" required min="0" step="0.01">
                  <label th:if="${#fields.hasErrors('voltageRange.minVoltage')}" th:errors="*{voltageRange.minVoltage}" class="text-danger"></label>
                </div>
                <div class="form-group">
                  <label for="voltageRange.maxVoltage"><span class="text-danger">*</span>&nbsp;最大电压</label>
                  <input class="form-control" type="number" th:field="*{voltageRange.maxVoltage}" placeholder="最大电压" required min="0" step="0.01">
                  <label th:if="${#fields.hasErrors('voltageRange.maxVoltage')}" th:errors="*{voltageRange.maxVoltage}" class="text-danger"></label>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label for="validBle1"><span class="text-danger">*</span>&nbsp;BLE</label>
              <div class="custom-control custom-checkbox">
                <input type="checkbox" th:field="*{validBle}" class="custom-control-input">
                <label class="custom-control-label" for="validBle1">验证</label>
              </div>
            </div>
            <div class="form-group">
              <label><span class="text-danger">*</span>&nbsp;移动运营商检测</label>
              <div>
                <div class="form-check form-check-inline" th:each="mobileOperator,stat : ${mobileOperators}">
                  <input class="form-check-input" type="radio" th:field="*{mobileOperator}" th:value="${mobileOperator}">
                  <label class="form-check-label" th:for="'mobileOperator' + ${stat.count}" th:text="#{'MobileOperator_' + ${mobileOperator}}">不限制</label>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label for="boxMaxNum"><span class="text-danger">*</span>&nbsp;打包箱最大装载数量</label>
              <input class="form-control" type="number" th:field="*{boxMaxNum}" placeholder="打包箱最大装载数量" required min="0" max="65535">
            </div>
            <div class="form-group">
              <label for="reportMode"><span class="text-danger">*</span>&nbsp;上报模式</label>
              <select class="form-control selectpicker" th:field="*{reportMode}" data-live-search="true" required>
                <option th:each="reportMode : ${reportModes}" th:value="${reportMode}" th:text="#{'Cat1WmReportMode_' + ${reportMode}}"></option>
              </select>
              <label th:if="${#fields.hasErrors('reportMode')}" th:errors="*{reportMode}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="moduleType"><span class="text-danger">*</span>&nbsp;模组类型</label>
              <select class="form-control selectpicker" th:field="*{moduleType}" data-live-search="true" required>
                <option th:each="moduleType : ${moduleTypes}" th:value="${moduleType}" th:text="#{'UdpModuleType_' + ${moduleType}}"></option>
              </select>
              <label th:if="${#fields.hasErrors('moduleType')}" th:errors="*{moduleType}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="channelType"><span class="text-danger">*</span>&nbsp;通道类型</label>
              <select class="form-control selectpicker" th:field="*{channelType}" data-live-search="true" required>
                <option th:each="channelType : ${channelTypes}" th:value="${channelType}" th:text="${channelType}"></option>
              </select>
              <label th:if="${#fields.hasErrors('channelType')}" th:errors="*{channelType}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="platformTransparent1"><span class="text-danger">*</span>&nbsp;是否平台透传</label>
              <div class="custom-control custom-checkbox">
                <input type="checkbox" th:field="*{platformTransparent}" class="custom-control-input">
                <label class="custom-control-label" for="platformTransparent1">是</label>
              </div>
            </div>
            <div class="form-group">
              <label for="edrxPeriod"><span class="text-danger">*</span>&nbsp;eDRX周期：</label>
              <select class="form-control selectpicker" th:field="*{edrxPeriod}" data-live-search="true" required>
                <option th:each="edrxPeriod : ${edrxPeriods}" th:value="${edrxPeriod}" th:text="${edrxPeriod.desc}"></option>
              </select>
              <label th:if="${#fields.hasErrors('moduleType')}" th:errors="*{moduleType}" class="text-danger"></label>
            </div>
            <div class="form-group">
              <label for="reportParam.interval"><span class="text-danger">*</span>&nbsp;上报间隔：</label>
              <div style="display: flex;">
                <input class="form-control" type="number" th:field="*{reportParam.interval}" placeholder="范围：0~255" required min="0" max="255">
                <select th:field="*{reportParam.unit}" class="form-control selectpicker">
                  <option value="0">天</option>
                  <option value="1">小时</option>
                  <option value="2">分钟</option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label for="reportParam.range"><span class="text-danger">*</span>&nbsp;日上报随机时段：</label>
              <div class="row">
                <div class="col-lg-9">
                  <select th:field="*{reportParam.range}" class="form-control selectpicker" th:disabled="*{reportParam.unit != 0}">
                    <option value="0">0~X 点</option>
                    <option value="1">X~18 点</option>
                  </select>
                </div>
                <div class="col-lg-3">
                  <input type="number" class="form-control" th:field="*{reportParam.XHour}" placeholder="请输入X" min="5" max="12" th:disabled="*{reportParam.unit != 0}">
                </div>
              </div>
            </div>
            <div class="form-group">
              <label for="updateCardInfo1"><span class="text-danger">*</span>&nbsp;小程序更新卡信息</label>
              <div class="custom-control custom-checkbox">
                <input type="checkbox" th:field="*{updateCardInfo}" class="custom-control-input">
                <label class="custom-control-label" for="updateCardInfo1">更新</label>
              </div>
            </div>
            <div class="form-group">
              <label for="checkTestCurrent1"><span class="text-danger">*</span>&nbsp;检查测试电流</label>
              <div class="custom-control custom-checkbox">
                <input type="checkbox" th:field="*{checkTestCurrent}" class="custom-control-input">
                <label class="custom-control-label" for="checkTestCurrent1">检查</label>
              </div>
            </div>
            <div class="form-group">
              <label for="validFixtureFlag1"><span class="text-danger">*</span>&nbsp;验证夹具产测标志</label>
              <div class="custom-control custom-checkbox">
                <input type="checkbox" th:field="*{validFixtureFlag}" class="custom-control-input">
                <label class="custom-control-label" for="validFixtureFlag1">验证</label>
              </div>
            </div>
            <div class="form-group">
              <label for="clearZero1"><span class="text-danger">*</span>&nbsp;是否清零</label>
              <div class="custom-control custom-checkbox">
                <input type="checkbox" th:field="*{clearZero}" class="custom-control-input">
                <label class="custom-control-label" for="clearZero1">是</label>
              </div>
            </div>
            <div class="form-group">
              <label for="integerDigits"><span class="text-danger">*</span>&nbsp;整数位数</label>
              <input class="form-control" type="number" th:field="*{integerDigits}" placeholder="整数位数" required min="3" max="8">
            </div>
            <div class="form-group">
              <label for="decimalDigits"><span class="text-danger">*</span>&nbsp;小数位数</label>
              <input class="form-control" type="number" th:field="*{decimalDigits}" placeholder="小数位数" required min="0" max="5">
            </div>
            <div class="form-group">
              <label title="定制协议只验证上线，即设备上线则认为模块测试通过">定制协议</label>
              <div class="custom-control custom-checkbox">
                <input type="checkbox" th:field="*{customProtocol}" class="custom-control-input">
                <label class="custom-control-label" for="customProtocol1">是</label>
              </div>
            </div>
            <div class="form-group">
              <label for="taskLinkUrl">关联任务</label>
              <input type="url" class="form-control" th:field="*{taskLinkUrl}" maxlength="1024">
              <label th:if="${#fields.hasErrors('taskLinkUrl')}" th:errors="*{taskLinkUrl}" class="text-danger"></label>
            </div>
            <div class="form-group full-row">
              <label for="description">备注</label>
              <textarea class="form-control" th:field="*{description}" rows="3" style="width: 100%" maxlength="255"></textarea>
              <label th:if="${#fields.hasErrors('description')}" th:errors="*{description}" class="text-danger"></label>
            </div>
            <th:block th:replace="~{fragments/form-footer :: submit-back-footer}"/>
          </form>

        </div>
      </div>
    </div>

  </div>
</th:block>

<th:block layout:fragment="page-script">
  <script type="text/javascript" th:src="@{/webjars/light-year-admin/js/bootstrap-select/bootstrap-select.min.js}"></script>
  <script type="text/javascript" th:src="@{/webjars/light-year-admin/js/bootstrap-select/i18n/defaults-zh_CN.min.js}"></script>
  <script type="text/javascript" th:src="@{/webjars/jquery-validation/dist/jquery.validate.min.js}"></script>
  <script type="text/javascript" th:src="@{/webjars/jquery-validation/dist/localization/messages_zh.min.js}"></script>
  <script type="text/javascript" th:src="@{/js/common/jq-validation-bs.js}"></script>
  <script type="text/javascript" th:src="@{/js/cat1wm/batch/add.js}"></script>
</th:block>

</body>
</html>
