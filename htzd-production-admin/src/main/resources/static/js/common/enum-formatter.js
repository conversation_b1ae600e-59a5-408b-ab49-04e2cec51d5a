/**
 * 格式化性别显示
 * @param value
 * @param row
 * @param index
 * @returns {string}
 */
function genderFormatter(value, row, index) {
  switch (value) {
    case 'UNKNOWN':
      return '保密';

    case 'MALE':
      return '男';

    case 'FEMALE':
      return '女';

    default:
      return value;
  }
}

/**
 * 通用boolean类型格式化
 * @param value
 * @param row
 * @param index
 * @returns {string|*}
 */
function booleanFormatter(value, row, index) {
  switch (value) {
    case true:
      return '<i class="mdi mdi-check text-success"></i>';
    case false:
      return '<i class="mdi mdi-close text-danger"></i>';
    default:
      return value;
  }
}

/**
 * 异常状态格式化
 * @param value
 * @param row
 * @param index
 * @returns {string|*}
 */
function exceptionFormatter(value, row, index) {
  switch (value) {
    case true:
      return '<i class="mdi mdi-check text-danger"></i>';
    default:
      return null;
  }
}

/**
 * 水表事件类型格式化
 * @param value
 * @param row
 * @param index
 * @returns {string|*}
 */
function meterEventTypeFormatter(value, row, index) {
  switch (value) {
    case 'VALVE_FAULT':
      return '阀门故障';
    case 'BATTERY_UNDER_VOLTAGE':
      return '电池低电压报警';
    case 'ONLINE_OFFLINE':
      return '设备上下线';
    case 'CLOCK_OFFSET':
      return '传输延时';

    default:
      return value;
  }
}

/**
 * 水表命令格式化
 * @param value
 * @param row
 * @param index
 * @returns {string|*}
 */
function meterCmdTypeFormatter(value, row, index) {
  switch (value) {
    case 'METERING_DATA':
      return '抄表';
    case 'OPEN_VALVE':
      return '开阀';
    case 'CLOSE_VALVE':
      return '关阀';

    default:
      return value;
  }
}

/**
 * 格式化显示阀门状态
 * @param value
 * @param row
 * @param index
 */
function valveStatusFormatter(value, row, index) {
  switch (value) {
    case 'OPENED':
      return '<i class="mdi mdi-lock-open text-success" title="阀门已打开"></i>';
    case 'CLOSED':
      return '<i class="mdi mdi-lock text-warning" title="阀门已关闭"></i>';
    case 'ALARM':
      return '<i class="mdi mdi-alert text-danger" title="阀门故障"></i>';

    default:
      return value;
  }
}

/**
 * 格式话显示电池是否欠压
 * @param value
 * @param row
 * @param index
 * @returns {string|*}
 */
function batteryUnderVoltageFormatter(value, row, index) {
  if (value === true) {
    return '<span class="text-danger">欠压</span>';
  } else if (value === false) {
    return '<span class="text-success">正常</span>';
  } else {
    return value;
  }
}

/**
 * 格式化显示NB设备状态
 * @param value
 * @param row
 * @param index
 * @returns {string|*}
 */
function moduleStateFormatter(value, row, index) {
  switch (value) {
    case 'UNREGISTERED':
      return '<span class="text-danger">未注册</span>';
    case 'UNACTIVATED':
      return '<span class="text-default">未激活</span>';
    case 'ACTIVATED':
      return '<span class="text-success">已激活</span>';

    default:
      return value;
  }
}

/**
 * 格式化显示产测模式
 * @param value
 * @param row
 * @param index
 * @returns {string|*}
 */
function productionModeFormatter(value, row, index) {
  switch (value) {
    case 'NORMAL':
      return '正常模式';
    case 'MODULE_TEST':
      return '夹具产测模式';
    case 'METER_TEST':
      return '成表产测模式';
    case 'STORAGE':
      return '仓储模式';

    default:
      return value;
  }
}

/**
 * 格式化显示产测步骤
 * @param value
 * @param row
 * @param index
 * @returns {string|*}
 */
function productionStepFormatter(value, row, index) {
  switch (value) {
    case 'BIND_TEST':
      return '绑定测试';
    case 'VALVE_TEST':
      return '开关阀测试';
    case 'METERING_TEST':
      return '吹风测试';
    case 'BASE_VALUE_TEST':
      return '重置表底数';
    case 'MODULE_TEST':
      return '模块测试';
    case 'PREPARE':
      return '产测准备';
    case 'PRESET':
      return '预置命令测试';
    case 'RF_FSK':
      return '小无线测试';
    case 'INFRARED':
      return '红外测试';
    case 'BLE':
      return '蓝牙测试';
    default:
      return value;
  }
}

/**
 * 格式化显示eDRX周期
 * @param value
 * @param row
 * @param index
 * @returns {string|*}
 */
function edrxPeriodFormatter(value, row, index) {
  switch (value) {
    case 'DEFAULT':
      return '默认';
    case 'ONE_MINUTE':
      return '81.92s';
    case 'TEN_MINUTES':
      return '655.36s';
    case 'TWENTY_MINUTES':
      return '1310.72s';
    case 'POWER_OFF':
      return '断电';

    default:
      return value;
  }
}

/**
 * 是否在线格式化显示
 * @param value
 * @param row
 * @param index
 * @returns {string|string}
 */
function onlineFormatter(value, row, index) {
  if (row.moduleState === 'UNREGISTERED') {
    return '-';
  }
  return value ? '<span class="text-success">在线</span>' : '<span class="text-danger">离线</span>';
}

function rsrpFormatter(value, row, index) {
  if (value === undefined || value === null) {
    return value;
  }
  if (value < -17 || value > 97) {
    return "无效值";
  }
  return -140 + value;
}

function rssiFormatter(value, row, index) {
  if (value === undefined || value === null) {
    return value;
  }

  if (value < 0 || value > 31) {
    return "无效值";
  }
  return -113 + 2 * value;
}

function wmTestStateFormatter(value, row, index) {
  switch (value) {
    case 'TESTING':
      return '测试中';
    case 'SUCCESS':
      return '<span class="text-success">测试成功</span>';
    case 'FAILURE':
      return '<span class="text-danger">测试失败</span>';
    case 'MARK_SUCCESS':
      return '<span class="text-success">标记成功</span>';
    case 'MARK_FAIL':
      return '<span class="text-danger">标记失败</span>';
    case 'EXIT_SUCCESS':
      return '<span class="text-success">退出产测成功</span>';
    case 'EXIT_FAIL':
      return '<span class="text-warning">退出产测失败</span>';

    default:
      return value;
  }
}

function hvTestStateFormatter(value, row, index) {
  switch (value) {
    case 'TESTING':
      return '测试中';
    case 'SUCCESS':
      return '<span class="text-success">测试成功</span>';
    case 'FAILURE':
      return '<span class="text-danger">测试失败</span>';
    case 'MARK_SUCCESS':
      return '<span class="text-success">标记成功</span>';
    case 'MARK_FAIL':
      return '<span class="text-danger">标记失败</span>';
    case 'EXIT_SUCCESS':
      return '<span class="text-success">退出产测成功</span>';
    case 'EXIT_FAIL':
      return '<span class="text-warning">退出产测失败</span>';

    default:
      return value;
  }
}

function testResultFormatter(value, row, index) {
  switch (value) {
    case 'SUCCESS':
      return '<span class="text-success">测试成功</span>';
    case 'FAIL':
      return '<span class="text-danger">测试失败</span>';

    default:
      return value;
  }
}

/**
 * 电表或DTU测试步骤格式化
 * @param value
 * @param row
 * @param index
 * @returns {string|*}
 */
function testStepFormatter(value, row, index) {
  switch (value) {
    case 'READ_TEST_RESULT':
      return '读取夹具测试结果';
    case 'READ_VERSION':
      return '读版本';
    case 'READ_DATA':
      return '抄表';
    case 'READ_SIMCARD':
      return '读卡信息';
    case 'READ_NB_SIGNAL':
      return '读NB信号';
    case 'TRANSPORT_DATA':
      return '透传命令';
    case 'RESET_FACTORY_RESULT':
      return '重置产测标志';
    case 'VALID_RESET_FACTORY_RESULT':
      return '验证重置产测标志';
    case 'SET_FACTORY_COMPLETED':
      return '设置产测标志';
    case 'VALID_SET_FACTORY_COMPLETED':
      return '验证产测标志';

    default:
      return value;
  }
}

/**
 * cat1电表测试步骤格式化
 * @param value
 * @param row
 * @param index
 * @returns {string|*}
 */
function cat1TestStepFormatter(value, row, index) {
  switch (value) {
    case 'SET_MASTER_IP_PORT':
      return '写主站ip端口';
    case 'READ_MASTER_IP_PORT':
      return '读取主IP端口';
    case 'READ_CARD_INFO':
      return '读卡信息';
    case 'SET_HEARTBEAT':
      return '设置心跳周期';
    case 'READ_HEARTBEAT':
      return '读取心跳周期';
    case 'SET_CUSTOM_PARAM':
      return '设置自定义设备参数';
    case 'READ_CUSTOM_PARAM':
      return '读取自定义设备参数';
    case 'SET_ADDR':
      return '设置终端逻辑地址';
    case 'READ_ADDR':
      return '读取终端逻辑地址';
    default:
      return value;
  }
}

/**
 * 产测状态格式化显示
 * @param value
 * @param row
 * @param index
 * @returns {string}
 */
function testStateFormatter(value, row, index) {
  switch (value) {
    case 'NOT_START':
      return '未开始';
    case 'TESTING':
      return '测试中';
    case 'SUCCESS':
      return '<span class="text-success">测试成功</span>';
    case 'FAILURE':
      return '<span class="text-danger">测试失败</span>';

    default:
      return value;
  }
}

function managerFormatter(value, row, index) {
  if (value) {
    return '<span class="text-success">是</span>';
  } else {
    return '<span class="text-danger">否</span>';
  }
}

function imsiFormatter(value, row, index) {
  if (!value) {
    return value;
  }
  return value + '【' + imsi2MobileOperator(value) + '】';
}

const CMCC = ['46000', '46002', '46004', '46007', '46008', '46024'],
  CTCC = ['46003', '46005', '46011'],
  CUCC = ['46001', '46006'];

function imsi2MobileOperator(imsi) {
  var prefix = imsi.substr(0, 5);
  if (CMCC.includes(prefix)) {
    return '移动';
  } else if (CTCC.includes(prefix)) {
    return '电信';
  } else if (CUCC.includes(prefix)) {
    return '联通';
  } else {
    return '未知';
  }
}

/**
 * 测试方案
 * @param value
 * @param row
 * @param index
 * @returns {string|*}
 */
function testSchemaFormatter(value, row, index) {
  switch (value) {
    case 'DEFAULT':
      return 'NB流程测试';
    case 'PRESET':
      return 'NB预置命令';
    case 'RF_FSK':
      return '小无线';
    case 'INFRARED':
      return '红外';
    case 'BLE':
      return '蓝牙';

    default:
      return value;
  }
}

function mobileOperatorFormatter(value, row, index) {
  switch (value) {
    case 'NONE':
      return '不限制';
    case 'CMCC':
      return '移动';
    case 'CTCC':
      return '电信';
    case 'CUCC':
      return '联通';

    default:
      return value;
  }
}

/**
 * 预置命令模式下 水表操作命令格式化显示
 * @param value
 * @param row
 * @param index
 */
function wmCmdTypeFormatter(value, row, index) {
  switch (value) {
    case 'METERING_DATA':
      return '预置抄表';
    case 'OPEN_VALVE':
      return '预置开阀';
    case 'CLOSE_VALVE':
      return '预置关阀';
    case 'READ_CARD_INFO':
      return '预置读卡信息/表号';
    case 'SET_BASE_VALUE':
      return '预置设置表底数';
    case 'SET_METER_CODE':
      return '预置设置表号';

    default:
      return value;
  }
}

/**
 * 预置命令模式下 执行状态格式化显示
 * @param value
 * @param row
 * @param index
 * @returns {string|*}
 */
function execStateFormatter(value, row, index) {
  switch (value) {
    case 'NOT_CREATED':
      return '等待触摸上报';
    case 'EXECUTING':
      return '执行中';
    case 'SUCCESS':
      return '执行成功';
    case 'FAILURE':
      return '执行失败';

    default:
      return value;
  }
}

/**
 * IoT平台格式化显示
 * @param value
 * @param row
 * @param index
 * @returns {string|*}
 */
function iotPlatformFormatter(value, row, index) {
  switch (value) {
    case 'CTWING':
      return '电信 CTWing';
    case 'ONENET':
      return '移动 OneNet';

    default:
      return value;
  }
}

function moduleTestStateFormatter(value, row, index) {
  switch (value) {
    case 'TESTING':
      return '测试中';
    case 'SUCCESS':
      return '<span class="text-success">测试成功</span>';
    case 'FAILURE':
      return '<span class="text-danger">测试失败</span>';
    case 'MARK_SUCCESS':
      return '<span class="text-success">标记成功</span>';
    case 'MARK_FAIL':
      return '<span class="text-danger">标记失败</span>';
    case 'EXIT_SUCCESS':
      return '<span class="text-success">退出产测成功</span>';
    case 'EXIT_FAIL':
      return '<span class="text-warning">退出产测失败</span>';

    default:
      return value;
  }
}

/**
 * 失败信息展示
 * @param value
 * @param row
 * @param index
 * @returns {string|*}
 */
function failMessageFormatter(value, row, index) {
  if (!value) {
    return value;
  }
  return `<div class="text-danger text-clip" title="${value}">${value}</div>`;
}

function deviceTypeFormatter(value, row, index) {
  switch (value) {
    case 'WATER_METER':
      return '水表';
    case 'ELECTRIC_METER':
      return '电表';
    default:
      return value;
  }
}

function cat1ElectricMeterCmdTypeFormatter(value, row, index) {
  switch (value) {
    case 'READ_DATA':
      return '读数据';
    case 'SET_MASTER_IP_PORT':
      return '写主站ip端口';
    default:
      return value;
  }
}

function cat1ElectricMeterCommandStateFormatter(value, row, index) {
  switch (value) {
    case 'SAVED':
      return '已保存';
    case 'COMPLETED':
      return '已完成';
    case 'FAILED':
      return '失败';
    default:
      return value;
  }
}

function cat1WmTestStateFormatter(value, row, index) {
  switch (value) {
    case 'TESTING':
      return '<span class="text-default">测试中</span>';
    case 'SUCCESS':
      return '<span class="text-success">测试成功</span>';
    case 'FAIL':
      return '<span class="text-danger">测试失败</span>';
    default:
      return value;
  }
}

function cat1AddrRuleFormatter(value, row, index) {
  switch (value) {
    case 'METER_CODE_CALC':
      return '表号后8位计算';
    case 'METER_CODE_SUB':
      return '表号后8位截取';
    case 'FIXED':
      return '固定地址';
    default:
      return value;
  }
}

function cat1FuncSetFormatter(value, row, index) {
  switch (value) {
    case 0:
      return '航天中电';
    case 1:
      return '杭州华立';
    case 2:
      return '电瓦特';
    case 3:
      return '无上报';
    case 4:
      return '万康';
    default:
      return value;
  }
}

/**
 * 上报模式格式化显示
 * @param value
 * @param row
 * @param index
 */
function reportModeFormatter(value, row, index) {
  switch (value) {
    case 0:
      return '常规上报';
    case 1:
      return '曲线上报';
    case 2:
      return '7日冻结上报';
    default:
      return value;
  }
}


/**
 * UDP协议模组类型格式化显示
 * @param value
 * @param row
 * @param index
 */
function udpModuleTypeFormatter(value, row, index) {
  switch (value) {
    case 'CAT_1':
      return 'Cat.1';
    case 'NB':
      return 'NB';
    default:
      return value;
  }
}


function voltageRangeFormatter(valeu, row, index) {
  var voltageRange = row.voltageRange;
  return `[${voltageRange.minVoltage}, ${voltageRange.maxVoltage}]`;
}
