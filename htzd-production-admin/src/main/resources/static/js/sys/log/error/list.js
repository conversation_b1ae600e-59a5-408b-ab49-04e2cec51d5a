var $table = $('#table');

function detailFormatter(index, row, element) {
  return $.templates("#detailViewTmpl").render(row);
}

function operateFormatter(value, row, index) {
  return [
    '<div class="btn-group">',
    '<a class="btn btn-xs btn-default details" href="#" title="查看详情" data-toggle="modal" data-target=".modal-exception"><i class="mdi mdi-view-headline"></i></a>',
    '</div>'
  ].join('');
}

window.operateEvents = {
  'click .details': function (e, value, row, index) {
    $('#exception-msg').html('<pre>' + value + '</pre>');
  }
};

function deleteAll() {
  var data = $table.bootstrapTable('getData');
  if (data.length <= 0) {
    $.notifyInfo('当前无日志记录');
    return;
  }

  $.warnConfirm('确认清空日志吗？', function () {

    $.confirm({
      title: '危险操作',
      type: 'red',
      content: '' +
        '<form action="" class="formName">' +
        '<div class="form-group">' +
        '<input type="password" placeholder="请输入密码" class="password form-control" required />' +
        '</div>' +
        '</form>',
      buttons: {
        formSubmit: {
          text: '提交',
          btnClass: 'btn-red',
          action: function () {
            var password = this.$content.find('.password').val();
            if (!password) {
              $.notifyInfo('请输入密码');
              return false;
            }

            var encrypt = new JSEncrypt();
            encrypt.setPublicKey($("#publicKey").val());

            $.ajax({
              type: 'delete',
              url: 'deleteAll',
              data: {
                password: encrypt.encrypt(password)
              },
              success: function () {
                $.notifyInfo('清空成功');
                $table.reloadTable();
              }
            })
          }
        },
        cancel: {
          text: '取消'
        }
      }
    });

  });
}

$(function () {
  $table.bootstrapTable({
    queryParams: function (params) {
      return $('#tableFilter').appendFilters(params);
    }
  });
});