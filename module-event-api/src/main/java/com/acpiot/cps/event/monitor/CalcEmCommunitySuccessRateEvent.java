package com.acpiot.cps.event.monitor;

import com.acpiot.cps.event.MqttMessageEvent;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * 计算小区水表成功率事件
 *
 * <AUTHOR>
 * @since 2023/5/29
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
public class CalcEmCommunitySuccessRateEvent extends MqttMessageEvent {

    /**
     * 小区id
     */
    private Long communityId;

    /**
     * 计算日期
     */
    private LocalDate date;

    public CalcEmCommunitySuccessRateEvent(Long communityId, LocalDate date) {
        this.communityId = communityId;
        this.date = date;
    }
}
