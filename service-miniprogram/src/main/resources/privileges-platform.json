[{"name": "综合能源预付费小程序", "code": "miniprogram", "type": "BUTTON", "children": [{"name": "管理", "code": "miniprogram-manage", "type": "BUTTON", "children": [{"name": "水表监控", "code": "miniprogram-manage-wmMonitor", "type": "BUTTON", "children": [{"name": "水表查询", "code": "miniprogram-manage-wmMonitor-page", "type": "BUTTON"}, {"name": "远程抄表", "code": "miniprogram-manage-wmMonitor-meteringData", "type": "BUTTON"}, {"name": "远程开阀", "code": "miniprogram-manage-wmMonitor-openValue", "type": "BUTTON"}, {"name": "远程关阀", "code": "miniprogram-manage-wmMonitor-closeValve", "type": "BUTTON"}, {"name": "水表详细信息", "code": "miniprogram-manage-wmMonitor-detail", "type": "BUTTON", "children": [{"name": "房间详情", "code": "miniprogram-manage-wmMonitor-detail-roomDetail", "type": "BUTTON"}, {"name": "抄表数据", "code": "miniprogram-manage-wmMonitor-detail-meterData", "type": "BUTTON"}, {"name": "日冻结数据", "code": "miniprogram-manage-wmMonitor-detail-freezeData", "type": "BUTTON"}, {"name": "水表事件", "code": "miniprogram-manage-wmMonitor-detail-meterEvents", "type": "BUTTON"}, {"name": "历史命令", "code": "miniprogram-manage-wmMonitor-detail-meterCmdLogs", "type": "BUTTON"}, {"name": "业务阀控记录", "code": "miniprogram-manage-wmMonitor-detail-serviceValveCtrlRecord", "type": "BUTTON"}]}]}, {"name": "电表监控", "code": "miniprogram-manage-emMonitor", "type": "BUTTON", "children": [{"name": "电表查询", "code": "miniprogram-manage-emMonitor-page", "type": "BUTTON"}, {"name": "远程抄表", "code": "miniprogram-manage-emMonitor-meteringData", "type": "BUTTON"}, {"name": "远程合闸", "code": "miniprogram-manage-emMonitor-onSwitch", "type": "BUTTON"}, {"name": "远程拉闸", "code": "miniprogram-manage-emMonitor-offSwitch", "type": "BUTTON"}, {"name": "电表详细信息", "code": "miniprogram-manage-emMonitor-detail", "type": "BUTTON", "children": [{"name": "房间详情", "code": "miniprogram-manage-emMonitor-detail-roomDetail", "type": "BUTTON"}, {"name": "抄表数据", "code": "miniprogram-manage-emMonitor-detail-meterData", "type": "BUTTON"}, {"name": "日冻结数据", "code": "miniprogram-manage-emMonitor-detail-freezeData", "type": "BUTTON"}, {"name": "电表事件", "code": "miniprogram-manage-emMonitor-detail-meterEvents", "type": "BUTTON"}, {"name": "历史命令", "code": "miniprogram-manage-emMonitor-detail-meterCmdLogs", "type": "BUTTON"}, {"name": "业务闸控记录", "code": "miniprogram-manage-emMonitor-detail-serviceSwitchCtrlRecord", "type": "BUTTON"}]}]}, {"name": "集中器维护", "code": "miniprogram-manage-concentratorMonitor", "type": "BUTTON", "children": [{"name": "档案维护", "code": "miniprogram-manage-concentratorMonitor-archive", "type": "BUTTON"}, {"name": "其他操作", "code": "miniprogram-manage-concentratorMonitor-other", "type": "BUTTON"}, {"name": "集中器事件", "code": "miniprogram-manage-concentratorMonitor-event", "type": "BUTTON"}]}, {"name": "历史命令", "code": "miniprogram-manage-cmdLog", "type": "BUTTON", "children": [{"name": "水表历史命令", "code": "miniprogram-manage-cmdLog-pageWater", "type": "BUTTON"}, {"name": "电表历史命令", "code": "miniprogram-manage-cmdLog-pageElectric", "type": "BUTTON"}]}, {"name": "抄表成功率", "code": "miniprogram-manage-readMeterRate", "type": "BUTTON", "children": [{"name": "集中器抄表成功率", "code": "miniprogram-manage-readMeterRate-concentrator", "type": "BUTTON", "children": [{"name": "水表集中器抄表成功率", "code": "miniprogram-manage-readMeterRate-pageWater", "type": "BUTTON"}, {"name": "电表集中器抄表成功率", "code": "miniprogram-manage-readMeterRate-pageElectric", "type": "BUTTON"}]}, {"name": "小区抄表成功率", "code": "miniprogram-manage-readMeterRate-community", "type": "BUTTON", "children": [{"name": "水表小区抄表成功率", "code": "miniprogram-manage-readMeterRate-community-pageWater", "type": "BUTTON"}, {"name": "电表小区抄表成功率", "code": "miniprogram-manage-readMeterRate-community-pageElectric", "type": "BUTTON"}]}]}, {"name": "抄表数据", "code": "miniprogram-manage-meterData", "type": "BUTTON", "children": [{"name": "水表抄表数据", "code": "miniprogram-manage-meterData-pageWater", "type": "BUTTON"}, {"name": "电表抄表数据", "code": "miniprogram-manage-meterData-pageElectric", "type": "BUTTON"}]}, {"name": "冻结数据", "code": "miniprogram-manage-freezeData", "type": "BUTTON", "children": [{"name": "水表冻结数据", "code": "miniprogram-manage-freezeData-pageWater", "type": "BUTTON"}, {"name": "电表冻结数据", "code": "miniprogram-manage-freezeData-pageElectric", "type": "BUTTON"}]}, {"name": "现金预存", "code": "miniprogram-manage-reserve", "type": "BUTTON"}, {"name": "欠费查询", "code": "miniprogram-manage-owe<PERSON><PERSON>", "type": "BUTTON"}, {"name": "账单查询", "code": "miniprogram-manage-bill", "type": "BUTTON"}, {"name": "缴费记录", "code": "miniprogram-manage-recharge", "type": "BUTTON"}, {"name": "客户查询", "code": "miniprogram-manage-customer", "type": "BUTTON"}, {"name": "小区查询", "code": "miniprogram-manage-community", "type": "BUTTON"}, {"name": "房间查询", "code": "miniprogram-manage-room", "type": "BUTTON"}]}, {"name": "本地抄表", "code": "miniprogram-readmeter", "type": "BUTTON", "children": [{"name": "蓝牙水表抄表与维护", "code": "miniprogram-readmeter-bluetoothWaterMeter", "type": "BUTTON"}, {"name": "UDP直连水表抄表与维护", "code": "miniprogram-readmeter-cat1WaterMeter", "type": "BUTTON", "children": [{"name": "维护升级IP端口", "code": "miniprogram-readmeter-cat1WaterMeter-setUpgradeIpAndPort", "type": "BUTTON"}]}, {"name": "4G电表抄表与维护", "code": "miniprogram-readmeter-cat1ElectricMeter", "type": "BUTTON", "children": [{"name": "维护IP端口", "code": "miniprogram-readmeter-cat1ElectricMeter-setStandbyIpAndPort", "type": "BUTTON"}]}, {"name": "集中器现场运维", "code": "miniprogram-readmeter-concentratorMaintain", "type": "BUTTON"}]}]}]