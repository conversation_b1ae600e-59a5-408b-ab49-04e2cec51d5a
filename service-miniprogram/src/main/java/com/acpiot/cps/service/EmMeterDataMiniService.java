package com.acpiot.cps.service;

import com.acpiot.cps.common.mongodb.filter.MongoPagingQueryParams;
import com.acpiot.cps.data.monitor.entity.mongo.EmMeterData;
import org.springframework.data.domain.Page;

/**
 * 电表历史数据服务接口
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
public interface EmMeterDataMiniService {

    /**
     * 分页查询
     *
     * @return 电表历史数据列表
     */
    Page<EmMeterData> pageEmMeterData(MongoPagingQueryParams params);
}