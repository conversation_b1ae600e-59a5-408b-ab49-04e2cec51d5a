package com.acpiot.cps.dto;

import com.acpiot.cps.common.serializer.CompanyId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 抄表成功率查询dto
 * Created by YoungLu on 2023/07/21
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReadMeterRateDto {

    /**
     * 公司id
     */
    @CompanyId
    private String companyId;

    /**
     * 小区ID
     */
    private Long communityId;

    /**
     * 小区名称
     */
    private String communityName;

    /**
     * 时间范围内，每日抄表成功率详情
     */
    private Map<String, DailyReadMeterRate> data;

}