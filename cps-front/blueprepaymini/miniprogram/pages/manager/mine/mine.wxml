<!--pages/manager/mine/mine.wxml-->
<wxs src="../../../pipes/enumFormat.wxs" module="enumFormat"></wxs>
<view class="page-content">
    <status-bar></status-bar>
    <view class="page-body">
        <view class="header flex-center">
            <view class="avatar-wrapper">
                <button open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar"></button>
                <image mode="aspectFit" src="{{avatarUrl}}" alt="" srcset="" />
            </view>
            <view class="name">{{nickName}}</view>
        </view>

        <view class="count flex-center">
            <view>
                <view class="val">{{reportDataDto.totalAmount || 0}}</view>
                <view class="label">累计收入(元)</view>
            </view>
            <view>
                <view class="val">{{reportDataDto.waterMeterTotal || 0}}</view>
                <view class="label">水表数量(个)</view>
            </view>
            <view>
                <view class="val">{{reportDataDto.electricMeterTotal || 0}}</view>
                <view class="label">电表数量(个)</view>
            </view>
        </view>

        <view class="card change flex-center">

            <van-icon size="54rpx" name="/assets/svg/manager.svg" />
            <view style="color: #ffffff;font-size: 32rpx; font-weight: 550; line-height: 38rpx;margin-left: 16rpx;flex: 1;">当前是管理端</view>
            <view class="change-btn" bind:tap="change">切换用户端</view>
        </view>

        <view class="card" style="margin-bottom: 28rpx;padding: 16rpx;">
            <van-cell center use-label-slot is-link link-type="navigateTo" url="/pages/manager/manager-package/pages/mine/account/account">
                <van-icon slot="icon" size="48rpx" name="/assets/svg/account.svg" />
                <view slot="title" class="list-title">收款账户</view>
                <van-tag wx:if="{{!bankInfoDto.id}}" color="rgba(243, 167, 63, 0.1)" text-color="#FF9500">未配置</van-tag>
                <van-tag wx:elif="{{bankInfoDto.subMerchantReviewStatus !== 'SUCCESS'}}" color="rgba(228, 61, 51, 0.1)" text-color="{{bankInfoDto.unsupportedReview?'#336AFF':'#E43D33'}} ">
                    {{bankInfoDto.subMerchantReviewStatus ? enumFormat('subMerchantReviewStatus', bankInfoDto.subMerchantReviewStatus) : '审核中'}}
                </van-tag>
            </van-cell>
            <van-cell center use-label-slot is-link url="/pages/manager/manager-package/pages/rooms/rooms">
                <van-icon slot="icon" size="48rpx" name="/assets/svg/room.svg" />
                <view slot="title" class="list-title">房间管理</view>
            </van-cell>
            <van-cell center use-label-slot is-link url="/pages/manager/manager-package/pages/price/price?type=water">
                <van-icon slot="icon" size="48rpx" name="/assets/svg/water-price.svg" />
                <view slot="title" class="list-title">水价管理</view>
            </van-cell>
            <!-- <van-cell size="large" center use-label-slot is-link url="/pages/manager/price/price?type=electric">
                <van-icon slot="icon" size="54rpx" color="#888888" class-prefix="icon" name="dian" />
                <view slot="title" style="padding-left: 10rpx;">电价管理</view>
            </van-cell> -->
            <!-- <van-cell size="large" center use-label-slot>
                <van-switch checked="{{ customerPay }}" size="36rpx" bind:change="onChange" />
                <van-icon slot="icon" size="52rpx" color="#888888" class-prefix="icon" name="fuwufeihuiyuanjia" />
                <view slot="title" style="padding-left: 10rpx;">用户承担服务费</view>
            </van-cell> -->
            <!-- <van-cell size="large" center use-label-slot is-link url="/pages/common/analysis/analysis">
                <van-icon slot="icon" size="50rpx" color="#888888" class-prefix="icon" name="fenxi1" />
                <view slot="title" style="padding-left: 10rpx;">用量分析</view>
            </van-cell> -->
            <!-- <van-cell size="large" center use-label-slot is-link>
                <van-icon slot="icon" size="46rpx" color="#888888" class-prefix="icon" name="jiaocheng" />
                <view slot="title" style="padding-left: 10rpx;">使用教程</view>
            </van-cell>
            <van-cell size="large" center use-label-slot is-link>
                <van-icon slot="icon" size="50rpx" color="#888888" class-prefix="icon" name="changjianwenti" />
                <view slot="title" style="padding-left: 10rpx;">常见问题</view>
            </van-cell> -->
            <van-cell center use-label-slot is-link url="/pages/common/auth-manage/auth-manage">
                <van-icon slot="icon" size="48rpx" color="#3372ff" name="label" />
                <view slot="title" class="list-title">权限管理</view>
            </van-cell>
            <van-cell center use-label-slot border="{{false}}" is-link url="/pages/common/about/about">
                <van-icon slot="icon" size="48rpx" name="/assets/svg/about.svg" />
                <view slot="title" class="list-title">关于</view>
            </van-cell>
            
            <!-- <van-cell size="large" center use-label-slot is-link url="pass-change/pass-change">
                <van-icon slot="icon" size="52rpx" color="#888888" class-prefix="icon" name="xiugaimima" />
                <view slot="title" style="padding-left: 10rpx;">修改密码</view>
            </van-cell> -->

        </view>

        <view class="card out" bind:tap="out">
            退出登录
        </view>

        <footer></footer>
    </view>  
</view>

<tab-bar index="2"></tab-bar>