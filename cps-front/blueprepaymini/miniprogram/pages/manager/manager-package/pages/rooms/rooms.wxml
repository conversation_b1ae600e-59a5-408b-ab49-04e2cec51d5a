<!--pages/manager/rooms/rooms.wxml-->
<view class="page-content" >
    <status-bar action="back" title="房间管理"></status-bar>
    <view class="page-body" >
        <view wx:if="{{choose}}" class="notice-bar" style="text-align: center;">
            请选择房间
        </view>
        <view wx:for="{{content}}" wx:key="id" class="room card">
            <view class="header flex-center">
                <view class="flex-center">
                    <van-icon size="48rpx" style="margin-right: 16rpx;" name="/assets/svg/room.svg"></van-icon>
                    {{item.roomNumber}}
                </view>
                <van-button wx:if="{{choose}}" data-room="{{item}}" bind:tap="select" block round type="info" size="small" custom-class="add-btn" >选择
                </van-button>
                <van-icon wx:else bind:tap="edit" data-id="{{item.id}}" data-name="{{item.roomNumber}}" size="48rpx" name="/assets/svg/edit.svg"></van-icon>
            </view>
            <view class="body card flex-center">
                <view>
                    <view class="value">{{item.realBalance}}</view>
                    <view class="label">房间余额(元)</view>
                </view>
                <view>
                    <view class="value">{{item.waterMeterNums}}</view>
                    <view class="label">水表数量</view>
                </view>
                <view>
                    <view class="value">{{item.electricMeterNums}}</view>
                    <view class="label">电表数量</view>
                </view>
            </view>
        </view>
        <list-bottom total="{{totalElements}}" loaded="{{currentTotal}}" loading="{{loading}}" />
        <view>
            <view style="height: 120rpx;width: 100%;"></view>
        </view>
    </view>

</view>

<view class="btns-bottom">
    <view bind:tap="add" class="btn">新建房间</view>
</view>