<!--pages/manager/price/price.wxml-->
<view class="page-content" >
    <status-bar action="back" title="{{title}}"></status-bar>
    <view class="page-body">
        <view wx:if="{{choose}}" class="notice-bar" style="text-align: center;">
            请选择水价
        </view>
        
        <block wx:for="{{schemeList}}" wx:key="id">
            <block wx:if="{{item.schemeType === 'LADDER'}}">
                <view wx:if="{{item.priceData.ladders.length === 1}}" class="card" bind:tap="click" data-scheme="{{item}}">
                    <van-cell border="{{false}}" center is-link="{{!choose}}" use-label-slot>
                        <view slot="title" class="name">{{item.schemeName}}</view>
                        <view class="value">
                            <text class="val">{{item.priceData.ladderPrices[0]}}</text>
                            <text> 元/{{unit}}</text>
                            <van-button wx:if="{{choose}}" style="margin-left: 10rpx;" data-scheme="{{item}}" bind:tap="select" block round type="info" size="small" custom-class="add-btn">选择</van-button>
                        </view>
                    </van-cell>
                </view>

                <view wx:else class="card" bind:tap="click" data-index="{{index}}">
                    <van-cell title="{{item.schemeName}}" border="{{false}}" is-link="{{!choose}}" clickable use-label-slot>
                        <view slot="label" style="padding-top: 10rpx;">
                            <block wx:for="{{item.priceData.ladders}}" wx:for-item="price" wx:for-index="index_" wx:key="index_">
                                <van-cell wx:if="{{index_ < item.priceData.ladders.length - 1}}" custom-class="inner-cell" title="{{price}}~{{item.priceData.ladders[index_+1]}}{{unit}}" border="{{false}}" center use-label-slot>
                                    <text>
                                        <text style=" font-size: 34rpx;color: #1989fa">{{item.priceData.ladderPrices[index_]}}</text>
                                        <text style=" font-size: 30rpx">元/{{unit}}</text>
                                    </text>
                                </van-cell>
                                <van-cell wx:else custom-class="inner-cell" title="{{price}}{{unit}}以上" border="{{false}}" center use-label-slot>
                                    <text>
                                        <text style=" font-size: 34rpx;color: #1989fa">{{item.priceData.ladderPrices[index_]}}</text>
                                        <text style=" font-size: 30rpx">元/{{unit}}</text>
                                    </text>
                                </van-cell>
                            </block>
                        </view>
                    </van-cell>
                </view>
            </block>

            <view wx:else class="card" bind:tap="click" data-index="{{index}}">
                <van-cell title="{{item.schemeName}}" border="{{false}}" is-link="{{!choose}}" clickable use-label-slot>
                    <view slot="label" style="padding-top: 10rpx;">
                        <van-cell custom-class="inner-cell" title="尖" border="{{false}}" center use-label-slot>
                            <text class="default">
                                <text class="val">{{item.priceData.tip}}</text>
                                <text>元/kWh</text>
                            </text>
                        </van-cell>
                        <van-cell custom-class="inner-cell" title="峰" border="{{false}}" center use-label-slot>
                            <text class="default">
                                <text class="val">{{item.priceData.peak}}</text>
                                <text>元/kWh</text>
                            </text>
                        </van-cell>
                        <van-cell custom-class="inner-cell" title="平" border="{{false}}" center use-label-slot>
                            <text class="default">
                                <text class="val">{{item.priceData.flat}}</text>
                                <text>元/kWh</text>
                            </text>
                        </van-cell>
                        <van-cell custom-class="inner-cell" title="谷" border="{{false}}" center use-label-slot>
                            <text class="default">
                                <text class="val">{{item.priceData.valley}}</text>
                                <text>元/kWh</text>
                            </text>
                        </van-cell>
                    </view>
                </van-cell>
            </view>
        </block>
        <footer wx:if="{{schemeList.length}}"></footer>
        <van-empty wx:else description="暂无方案" />
        <view>
            <view style="height: 120rpx;width: 100%;"></view>
        </view>
    </view>
</view>

<view class="btns-bottom">
    <view bind:tap="add" class="btn">新增{{type === 'water' ? '水价' : '电价'}}</view>
</view>

<van-dialog z-index="{{99}}" use-slot title="结算配置" show="{{ show }}" show-cancel-button bind:confirm="confirm">
    <view style="padding: 20rpx;">
        <van-cell size="large" title="启用日期" is-link value="{{date}}" bind:tap="selectDate" />
        <van-cell size="large" border="{{false}}" title="结算周期" is-link value="{{periods[periodIndex]}}" bind:tap="selectPeriod" />
    </view>
</van-dialog>


<van-popup show="{{ periodShow }}" position="bottom" close-on-click-overlay bind:click-overlay="closePicker">
    <van-picker default-index="{{ 0 }}" show-toolbar title="选择结算周期" columns="{{ periods }}" bind:confirm="periodConfirm" bind:cancel="closePicker" />
</van-popup>

<van-popup show="{{ dateShow }}" position="bottom" close-on-click-overlay bind:click-overlay="closePicker">
    <van-datetime-picker type="date" value="{{ currentDate }}" min-date="{{ currentDate }}" bind:confirm="dateConfirm" bind:cancel="closePicker" />
</van-popup>