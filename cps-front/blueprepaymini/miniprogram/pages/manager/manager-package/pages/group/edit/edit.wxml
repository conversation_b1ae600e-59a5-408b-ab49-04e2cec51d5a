<!--pages/manager/group/edit/edit.wxml-->
<view class="page-content">
    <status-bar action="back" title="{{id ? '编辑' : '新增'}}分组"></status-bar>
    <view class="card" >
        <van-field type="text" input-align="right" maxlength="12" custom-style="background:transparent;font-size:28rpx" placeholder-style="font-size:30rpx;color:#bbbbbb" input-class="custom-input" center model:value="{{ name }}" placeholder="请填写分组名称" border="{{ false }}" >
                <view slot="label" class="label">分组名称</view>
        </van-field>
    </view>

    <view class="changes" >组内设备({{totalElements}}<text style="color: #f73311;" wx:if="{{removeDevices.length}}">-{{removeDevices.length}}</text><text style="color: #07c160;" wx:if="{{addDevices.length}}">+{{addDevices.length}}</text>)</view>

    <view class="card add" bind:tap="add">
            <van-icon size="36rpx" style="margin-right: 16rpx;" color="#1D1F24" name="plus" />添加设备
        </view>
    <scroll-view style="flex: 1;height: 100px" scroll-y="true"  refresher-triggered="{{refresherTriggered}}" refresher-enabled="{{false}}" bindscrolltolower="onReachBottom">
        <view wx:for="{{addDevices}}" class="card item {{item.meterType}}"  wx:key="id">
            <van-icon size="60rpx" class-prefix="icon" name="/assets/svg/{{item.meterType==='WATER'?'water-meter':'electricity-meter'}}.svg" />
            <view class="info">
                <view class="name">{{item.roomNumber || '未绑定房间'}}</view>
                <view class="code"><text>表号：</text> {{item.code}}</view>
            </view>
            <van-icon bind:tap="removeAdd" data-index="{{index}}" size="46rpx" color="#ee0a24" name="clear" />
            <van-icon style="position: absolute;left: 0; top: 0;" color="#07c160" size="70rpx" class-prefix="icon" name="iconfontzhizuobiaozhun023113" />
        </view>
        <view wx:for="{{content}}" class="item {{item.meterType}}"  wx:key="id">
            <van-icon size="60rpx" class-prefix="icon" name="/assets/svg/{{item.meterType==='WATER'?'water-meter':'electricity-meter'}}.svg" />
            <view class="info">
                <view class="name">{{item.roomNumber || '未绑定房间'}}</view>
                <view class="code"><text>表号：</text> {{item.code}}</view>
            </view>
            <van-icon bind:tap="remove" data-index="{{index}}" size="46rpx" color="#ee0a24" name="clear" />
        </view>

        <list-bottom wx:if="{{addDevices.length === 0 || totalElements > currentTotal}}" type="device" total="{{totalElements}}" loaded="{{currentTotal}}" loading="{{loading}}" />
        <view style="height: 150rpx;"></view>
    </scroll-view>

    <view class="btns-bottom">
            <view bind:tap="save" class="btn">保存</view>
        </view>


</view>