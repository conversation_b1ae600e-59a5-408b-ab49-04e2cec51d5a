<!--pages/manager/group/devices/devices.wxml-->

<view class="page-content list-page">
    <status-bar action="back" title="分组设备"></status-bar>
    <van-search shape="round" background="transparent" model:value="{{ search }}" bind:change="searchOn" placeholder="搜索设备" />
    <scroll-view style="flex: 1;height: 100px;" scroll-y="true" refresher-triggered="{{refresherTriggered}}" refresher-enabled bindrefresherrefresh="onPullDownRefresh" bindscrolltolower="onReachBottom">
        <van-checkbox-group value="{{ result }}" bind:change="onChange">
            <view wx:for="{{content}}" class="card flex-center {{item.meterType}}" style="margin-top: 16rpx!important;" wx:key="id" bind:tap="toggle" data-index="{{ index }}">
                <view class="flex-center">
                    <van-icon size="64rpx" style="margin-right: 8rpx;" class-prefix="icon" name="/assets/svg/{{item.meterType==='WATER'?'water-meter':'electricity-meter'}}.svg" />
                    <view class="info">
                        <view class="name">{{item.roomNumber || '未绑定房间'}}</view>
                        <view class="code"><text>表号：</text> {{item.code}}</view>
                    </view>
                </view>


                <van-checkbox catch:tap="noop" class="checkboxes-{{ index }}" name="{{item.id}}" data-index="{{index}}" icon-size="46rpx"> </van-checkbox>

            </view>

        </van-checkbox-group>
        <list-bottom type="device" total="{{totalElements}}" loaded="{{currentTotal}}" loading="{{loading}}" />
    </scroll-view>
    <view class="footer">
        <view class="count">已选择 {{result.length}}</view>
        <view style="width: 200rpx;" bind:tap="comfirm" class="btn">确定</view>
    </view>
</view>