<!--components/list-bottom/index.wxml-->


<view wx:if="{{total === 0 && loading}}" class="loading-wrap" style="height:{{ initialHeight }}">
    <van-loading class="cont" size="80rpx" vertical text-size="30rpx">加载中...</van-loading>
</view>
<van-empty wx:elif="{{total === 0}}" description="{{type === 'device' ? '暂无设备' : '暂无数据'}}">
    <view wx:if="{{type === 'device' && showScan}}" bind:tap="scan" class="btn"><van-icon style="margin-right:8rpx" name="scan" size="42rpx" ></van-icon>  扫码添加</view>
</van-empty>
<footer wx:else></footer>
