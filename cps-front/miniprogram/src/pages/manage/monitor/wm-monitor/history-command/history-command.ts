import {Pagination} from "../../../../../pagination/Pagination";
import {Direction} from "../../../../../pagination/Direction";
import moment from "../../../../../utils/moment";
import {Operator} from "../../../../../pagination/Operator";
//混入对象
import {page} from "../../../../../behaviors/behavior";

Page({

    /**
     * 混入分页组件公共函数，导入该文件后必须在当前组件中创建【pagination】对象，如下所示
     */
    behaviors: [page],

    /**
     * 分页查询对象
     */
    pagination: null,

    /**
     * 页面的初始数据
     */
    data: {
        activeNames: 0,
        meterId: null,
        loading: false,
        sortOpt: [
            {text: '全部命令类型', value: ''},
            {text: '抄表', value: 'METERING_DATA'},
            {text: '开阀', value: 'OPEN_VALVE'},
            {text: '关阀', value: 'CLOSE_VALVE'},
            {text: '设置日流量阈值', value: 'SET_DAILY_FLOW'},
            {text: '查询日流量阈值', value: 'QUERY_DAILY_FLOW'},
            {text: '设置上报间隔', value: 'SET_REPORT_INTERVAL'},
            {text: '查询上报间隔', value: 'QUERY_REPORT_INTERVAL'},
            {text: '读正向累计读数曲线', value: 'READ_FORWARD_CURVE_DATA'},
            {text: '读反向累计读数曲线', value: 'READ_BACKWARD_CURVE_DATA'},
            {text: '设置基表底数', value: 'SET_BASE_VALUE'},
            {text: '读参数信息', value: 'READ_PARAM'},
            {text: '读MCU参数信息', value: 'READ_MCU_PARAM'},
            {text: '设置上报参数', value: 'SET_REPORT_PARAMS'},
            {text: '设置IP信息', value: 'SET_IP'},
            {text: '设置上报模式', value: 'SET_REPORT_MODE'},
            {text: '设置关阀后密集上报参数', value: 'SET_VALVE_CLOSED_REPORT_PARAM'},
            {text: '设置基表参数', value: 'SET_BASE_PARAM'},
            {text: '设定制功能配置项', value: 'SET_FUNC_VAL'}
        ]
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options: any) {
        this.setData({
            meterId: options.meterId
        });
        this.pagination = new Pagination<any>('auth/monitorDetail/wm/pagedMeterCmdLogs?meterId=' + options.meterId, 20);
        this.pagination.setSort('createdDate', Direction.desc);
        // @ts-ignore
        this.loadData();
    },

    /**
     * 选择时间区间并查询
     * @param event
     */
    dateChange(event: any) {
        const [start, end] = event.detail;
        // 清除掉之前的时间条件
        this.pagination.removeFilter('createdDate');
        if (start && end) {
            // 应用时间条件查询
            const startTime = moment(start).startOf('day').format('yyyy-MM-DD HH:mm:ss');
            const endTime = moment(end).endOf('day').format('yyyy-MM-DD HH:mm:ss');;
            this.pagination.addFilter('createdDate', [startTime, endTime], Operator.dateBetween, false);
        }
        // @ts-ignore
        this.loadData();
    },

    /**
     * 选择命令类型并查询
     * @param event
     */
    cmdTypeChange(event: any) {
        const cmdType = event.detail;
        // 清除掉之前的时间条件
        this.pagination.removeFilter('cmdType');
        if (cmdType) {
            // 应用抄表类型条件查询
            this.pagination.addFilter('cmdType', cmdType);
        }
        // @ts-ignore
        this.loadData();
    },

    onActiveChange(event: any) {
        this.setData({
            activeNames: event.detail,
        });
    },
})