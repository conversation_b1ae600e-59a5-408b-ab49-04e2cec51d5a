import{InfraredWaterMultiPacketHandler}from"./handler/InfraredWaterMultiPacketHandler";import{InfraredWaterSession}from"./worker/InfraredWaterSession";import{InfraredWaterBlueSession}from"./worker/InfraredWaterBlueSession";import stringUtils from"../../utils/StringUtils";import infraredWaterMonitorCodec from"./handler/InfraredWaterMonitorCodec";import infraredWaterMonitorHandler from"./handler/InfraredWaterMonitorHandler";import{DeviceValveCtrlWorker}from"./worker/impl/DeviceValveCtrlWorker";import{ReadDeviceDataWorker}from"./worker/impl/ReadDeviceDataWorker";import{ReadDeviceFunctionWorker}from"./worker/impl/ReadDeviceFunctionWorker";import{WriteDeviceFunctionWorker}from"./worker/impl/WriteDeviceFunctionWorker";import{WriteDeviceIdWorker}from"./worker/impl/WriteDeviceIdWorker";import{ReadDeviceIdWorker}from"./worker/impl/ReadDeviceIdWorker";import{ReadDeviceInfoWorker}from"./worker/impl/ReadDeviceInfoWorker";import{ReadDeviceParamsWorker}from"./worker/impl/ReadDeviceParamsWorker";import{ReadMeasuringConfigWorker}from"./worker/impl/ReadMeasuringConfigWorker";import{ReadDeviceNbSignalWorker}from"./worker/impl/ReadDeviceNbSignalWorker";import{WriteDeviceParamsWorker}from"./worker/impl/WriteDeviceParamsWorker";import{WriteMeasuringConfigWorker}from"./worker/impl/WriteMeasuringConfigWorker";import{WriteMeterBaseValueWorker}from"./worker/impl/WriteMeterBaseValueWorker";import{DeviceNbCtrlWorker}from"./worker/impl/DeviceNbCtrlWorker";import{ReadNbStateWorker}from"./worker/impl/ReadNbStateWorker";import{DeviceMcuCtrlWorker}from"./worker/impl/DeviceMcuCtrlWorker";import{DeviceReportWorker}from"./worker/impl/DeviceReportWorker";import{ReadNoMagnetismInfoWorker}from"./worker/impl/ReadNoMagnetismInfoWorker";import{ReadProductionTestDataWorker}from"./worker/impl/ReadProductionTestDataWorker";import{LocateMeterWorker}from"./worker/impl/LocateMeterWorker";import{WriteIpInfoWorker}from"./worker/impl/WriteIpInfoWorker";import{ReadIpInfoWorker}from"./worker/impl/ReadIpInfoWorker";import{OpenUpgradeChannelWorker}from"./worker/impl/OpenUpgradeChannelWorker";import httpUtils from"../../utils/HttpUtils";import commonUtils from"../../utils/CommonUtils";import bluetoothUtils from"../../utils/BluetoothUtils";import{WriteMQTTParamWorker}from"./worker/impl/WriteMQTTParamWorker";import{ReadMQTTParamWorker}from"./worker/impl/ReadMQTTParamWorker";import{MQTTParam}from"./handler/process/bean/MQTTParam";import{HeatValveCalibrationWorker}from"./worker/impl/HeatValveCalibrationWorker";import{SetValveRateWorker}from"./worker/impl/SetValveRateWorker";import{Read7DayFreezeDataWorker}from"./worker/impl/Read7DayFreezeDataWorker";class InfraredProtocolSDK{constructor(t){if(this.authorizationUrl="https://www.acp-iot.com/production-test/api/auth/device/scanCode",this.authorizationResult=!1,this.targetDeviceId="FFFFFFFFFFFFFFFF",this.defaultTargetDeviceId="FFFFFFFFFFFFFFFF",!(t.token&&t.deviceId&&t.serviceId&&t.writeId&&t.notifyId))throw new Error("请提供必填参数以便成功初始化SDK");var e=new InfraredWaterBlueSession(t.deviceId,t.serviceId,t.writeId);this.session=new InfraredWaterSession(t.deviceId,e,e=>{e=stringUtils.buffer2hex(e);t.messageCallback&&t.messageCallback("send",e)},e=>{t.cmdResCallback&&t.cmdResCallback(e)}),this.multiPacketHandler=new InfraredWaterMultiPacketHandler(e=>{infraredWaterMonitorCodec.decode(e)?(e=infraredWaterMonitorHandler.handle(e))?this.session.handleProcess(e):console.log("接收到报文，但是monitorHandler.handle失败"):console.log("接收到报文，但是monitorCodec.decode失败")}),this.messageCallback=t.messageCallback,bluetoothUtils.startMessageListener(t.deviceId,t.serviceId,t.notifyId,e=>{var t=stringUtils.buffer2hex(e.value),e=InfraredProtocolSDK.currentProtocolSDKs.get(e.deviceId);e&&(e.messageCallback&&e.messageCallback("receive",t),e.multiPacketHandler.receivePacket(t))}),this.deviceId=t.deviceId,InfraredProtocolSDK.currentProtocolSDKs.set(this.deviceId,this);let r=this;httpUtils.requestFormUrl({url:"https://www.acp-iot.com/appid.json",params:{},success:e=>{-1===e.map(e=>e.appId).indexOf(commonUtils.getAppId())?r.verifyCurrentDevicePrivilege(t.token,t.success,t.failure):(this.authorizationResult=!0,t.success&&t.success())},method:"GET"})}static setNormalTimeout(e){InfraredWaterSession.normalTimeout=e}static getNormalTimeout(){return InfraredWaterSession.normalTimeout}getTargetDeviceId(){return this.targetDeviceId}dispose(){this.session.dispose(),this.multiPacketHandler.dispose(),InfraredProtocolSDK.currentProtocolSDKs.delete(this.deviceId)}sendOpenValveCommand(t,r){this.authorization(()=>{var e=new DeviceValveCtrlWorker(1);e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送开阀命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("开阀响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("开阀失败")}).catch(e=>{console.log("开阀超时，res="+e),r("开阀超时，res="+e)})},r)}sendCloseValveCommand(t,r){this.authorization(()=>{var e=new DeviceValveCtrlWorker(0);e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送关阀命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("关阀响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("关阀失败")}).catch(e=>{console.log("关阀超时，res="+e),r("关阀超时，res="+e)})},r)}sendWashValveCommand(t,r){this.authorization(()=>{var e=new DeviceValveCtrlWorker(2);e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送洗阀命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("洗阀响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("洗阀失败")}).catch(e=>{r("洗阀超时，err="+e)})},r)}sendSingleReadMeterCommand(t,r){this.authorization(()=>{var e=new ReadDeviceDataWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送单播抄表命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("抄表响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("单播抄表失败")}).catch(e=>{r("单播抄表超时，err="+e)})},r)}sendReadDeviceFunctionCommand(t,r){this.authorization(()=>{var e=new ReadDeviceFunctionWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送读功能选项命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("读功能选项响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("读功能选项失败")}).catch(e=>{console.log("读功能选项超时，res="+e),r("读功能选项超时，res="+e)})},r)}sendWriteDeviceFunctionCommand(t,r,i,o,s,a,c){this.authorization(()=>{var e=new WriteDeviceFunctionWorker(t,r,i,o,s);e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送写功能选项命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("写功能选项响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),a(e)):c("写功能选项失败")}).catch(e=>{console.log("写功能选项超时，res="+e),c("写功能选项超时，res="+e)})},c)}sendWriteDeviceIdCommand(t,r,i){this.authorization(()=>{var e=new WriteDeviceIdWorker(t);e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送配置表号命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("配置表号响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),r(e)):i("写表号失败")}).catch(e=>{console.log("写表号超时，res="+e),i("写表号超时，res="+e)})},i)}sendReadDeviceIdCommand(t,r){this.authorization(()=>{var e=new ReadDeviceIdWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送读表号命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("读表号响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("读表号失败")}).catch(e=>{console.log("读表号超时，res="+e),r("读表号超时，res="+e)})},r)}sendReadDeviceInfoCommand(t,r){this.authorization(()=>{var e=new ReadDeviceInfoWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送读设备信息命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("读设备信息响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("读设备信息失败")}).catch(e=>{console.log("读设备信息超时，res="+e),r("读设备信息超时，res="+e)})},r)}sendReadDeviceParamsCommand(t,r){this.authorization(()=>{var e=new ReadDeviceParamsWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送读设备参数命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("读设备参数响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("读设备参数失败")}).catch(e=>{console.log("读设备参数超时，res="+e),r("读设备参数超时，res="+e)})},r)}sendReadMeasuringConfigCommand(t,r){this.authorization(()=>{var e=new ReadMeasuringConfigWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送读取计量配置信息指令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("读计量配置信息响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("读计量配置信息失败")}).catch(e=>{console.log("读计量配置信息超时，res="+e),r("读计量配置信息超时，res="+e)})},r)}sendReadDeviceNbSignalCommand(t,r,i){this.authorization(()=>{var e=new ReadDeviceNbSignalWorker(t);e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送读信号命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("读信号响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),r(e)):i("读信号失败")}).catch(e=>{console.log("读信号超时，res="+e),i("读信号超时，res="+e)})},i)}sendWriteDeviceParamsCommand(t,r,i,o,s){this.authorization(()=>{var e=new WriteDeviceParamsWorker(t,r,i);e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送写设备参数命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("写设备参数响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),o(e)):s("写设备参数失败")}).catch(e=>{console.log("写设备参数超时，res="+e),s("写设备参数超时，res="+e)})},s)}sendWriteMeasuringConfigCommand(t,r,i,o,s){this.authorization(()=>{var e=new WriteMeasuringConfigWorker(t,r,i);e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送写计量配置命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("写计量配置响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),o(e)):s("写计量配置失败")}).catch(e=>{console.log("写计量配置超时，res="+e),s("写计量配置超时，res="+e)})},s)}sendWriteMeterBaseValueCommand(t,r,i,o,s,a,c){this.authorization(()=>{var e=new WriteMeterBaseValueWorker(t,r,i,o,c);e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送写基表读数命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("写基表读数响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),s(e)):a("写基表读数失败")}).catch(e=>{console.log("写基表读数超时，res="+e),a("写基表读数超时，res="+e)})},a)}sendDeviceNbOpenCommand(r,i){this.authorization(()=>{var e=new DeviceNbCtrlWorker(1);e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送水表开NB命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("启用NB响应结果，res="+JSON.stringify(e));var t=e;e.isResult()&&1==t.getState()?(this.targetDeviceId=t.getTargetDeviceId(),r(t)):i("水表启用NB失败")}).catch(e=>{console.log("水表启用NB超时，res="+e),i("水表启用NB超时，res="+e)})},i)}sendDeviceNbCloseCommand(r,i){this.authorization(()=>{var e=new DeviceNbCtrlWorker(0);e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送水表关NB命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("禁用NB响应结果，res="+JSON.stringify(e));var t=e;e.isResult()&&0==t.getState()?(this.targetDeviceId=t.getTargetDeviceId(),r(t)):i("禁用NB失败")}).catch(e=>{console.log("禁用NB超时，res="+e),i("禁用NB超时，res="+e)})},i)}sendDeviceNbResetCommand(r,i){this.authorization(()=>{var e=new DeviceNbCtrlWorker(2);e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送水表复位NB命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("复位NB响应结果，res="+JSON.stringify(e));var t=e;e.isResult()&&1==t.getState()?(this.targetDeviceId=t.getTargetDeviceId(),r(t)):i("复位NB失败")}).catch(e=>{console.log("复位NB超时，res="+e),i("复位NB超时，res="+e)})},i)}sendReadNbStateCommand(r,i){this.authorization(()=>{var e=new ReadNbStateWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送读NB状态命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("读NB状态响应结果，res="+JSON.stringify(e));var t=e;e.isResult()?(this.targetDeviceId=t.getTargetDeviceId(),r(t)):i("读NB状态失败")}).catch(e=>{console.log("读NB状态超时，res="+e),i("读NB状态超时，res="+e)})},i)}sendDeviceMcuCtrlCommand(t,r){this.authorization(()=>{var e=new DeviceMcuCtrlWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送MCU控制命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("MCU控制响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("MCU控制失败")}).catch(e=>{console.log("MCU控制超时，res="+e),r("MCU控制超时，res="+e)})},r)}sendReportDeviceInfoCommand(t,r){this.authorization(()=>{var e=new DeviceReportWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送上报信息命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("上报信息响应结果，res=："+JSON.stringify(e));e.isResult()?(this.targetDeviceId=e.getTargetDeviceId(),t(e)):r("上报信息失败",e.getErrorCode())}).catch(e=>{console.log("上报指令下发超时，res="+e),r("上报指令下发超时，res="+e)})},r)}sendReadNoMagnetismInfoCommand(t,r){this.authorization(()=>{var e=new ReadNoMagnetismInfoWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送读无磁数据命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("读无磁数据响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("读无磁数据失败")}).catch(e=>{console.log("读无磁数据超时，res="+e),r("读无磁数据超时，res="+e)})},r)}sendReadProductionTestDataCommand(t,r){this.authorization(()=>{var e=new ReadProductionTestDataWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送一键读取命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("一键读取响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("一键读取失败")}).catch(e=>{console.log("一键读取指令下发超时，res="+e),r("一键读取指令下发超时，res="+e)})},r)}sendLocateMeterCommand(t,r){this.authorization(()=>{var e=new LocateMeterWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送寻表命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("寻表响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("寻表失败")}).catch(e=>{console.log("寻表超时，res="+e),r("寻表超时，res="+e)})},r)}sendWriteIpInfoCommand(t,r,i,o,s,a,c,n,l){r&&50<r.length?l("主用网络地址长度超限，最大50位"):o&&50<o.length?l("备用网络地址长度超限，最大50位"):a&&50<a.length?l("升级网络地址长度超限，最大50位"):this.authorization(()=>{var e=new WriteIpInfoWorker(t,r,i,o,s,a,c);e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送设置网络地址命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("写设置网络地址响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),n(e)):l("设置网络地址失败")}).catch(e=>{console.log("设置网络地址超时，res="+e),l("设置网络地址超时，res="+e)})},l)}sendReadIpInfoCommand(t,r){this.authorization(()=>{var e=new ReadIpInfoWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送读网络地址命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("读网络地址响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("读网络地址失败")}).catch(e=>{console.log("读网络地址超时，res="+e),r("读网络地址超时，res="+e)})},r)}sendOpenUpgradeChannelCommand(t,r){this.authorization(()=>{var e=new OpenUpgradeChannelWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送开启升级通道命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("开启升级通道响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("开启升级通道失败")}).catch(e=>{console.log("开启升级通道超时，res="+e),r("开启升级通道超时，res="+e)})},r)}sendWriteMQTTParamCommand(t,r,i,o,s,a,c,n,l,d){this.authorization(()=>{var e=new MQTTParam,e=(e.setAuthSign(t),e.setUsername(r),e.setPassword(i),e.setTopicParamSign(o),e.setUploadTopic(s),e.setDownloadTopic(a),e.setHeartBeatSign(c),e.setHeartBeatPeriod(n),new WriteMQTTParamWorker(e));e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送设置MQTT参数成功，设备："+this.targetDeviceId)}).then(e=>{console.log("设置MQTT参数响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),l(e)):d("设置MQTT参数失败")}).catch(e=>{console.log("设置MQTT参数超时，res="+e),d("设置MQTT参数超时，res="+e)})},d)}sendReadMQTTParamCommand(t,r){this.authorization(()=>{var e=new ReadMQTTParamWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送读MQTT参数命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("读MQTT参数响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("读MQTT参数失败")}).catch(e=>{console.log("读MQTT参数超时，res="+e),r("读MQTT参数超时，res="+e)})},r)}sendHeatValveCalibrationCommand(t,r){this.authorization(()=>{var e=new HeatValveCalibrationWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送暖通阀开度校准命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("暖通阀开度校准响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("暖通阀开度校准失败")}).catch(e=>{console.log("暖通阀开度校准超时，res="+e),r("暖通阀开度校准超时，res="+e)})},r)}sendSetValveRateCommand(t,r,i){this.authorization(()=>{var e=new SetValveRateWorker(t);e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送暖通阀开度调节命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("暖通阀开度调节响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),r(e)):i("暖通阀开度调节失败")}).catch(e=>{console.log("暖通阀开度调节超时，res="+e),i("暖通阀开度调节超时，res="+e)})},i)}read7DayFreezeDataCommand(t,r){this.authorization(()=>{var e=new Read7DayFreezeDataWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送读7天冻结数据命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("读7天冻结数据响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),t(e)):r("读7天冻结数据失败")}).catch(e=>{console.log("读7天冻结数据超时，res="+e),r("读7天冻结数据超时，res="+e)})},r)}authorization(e,t){this.authorizationResult?e():t("无权限操作此设备")}verifyCurrentDevicePrivilege(t,r,i){var e=new ReadDeviceIdWorker;e.setTargetDeviceId(this.defaultTargetDeviceId),this.session.executeWorker(e,()=>{console.log("发送读设备信息命令成功，设备："+this.targetDeviceId)}).then(e=>{console.log("读设备信息响应结果，res="+JSON.stringify(e)),e.isResult()?(this.targetDeviceId=(e=e).getTargetDeviceId(),this.validDeviceCode(e.getTargetDeviceId(),e.getDeviceId(),t,r,i)):(this.authorizationResult=!1,i&&i())}).catch(e=>{console.log("读设备信息超时，res="+e),this.authorizationResult=!1,i&&i()})}validDeviceCode(e,t,r,i,o){16===e.length&&(e=e.substring(1)).startsWith("0")&&(e=e.substring(1)),httpUtils.requestFormUrl({url:this.authorizationUrl,params:{deviceCode:e},success:e=>{this.authorizationResult=!!e.result,this.authorizationResult?i&&i():o&&o()},failure:e=>{this.authorizationResult=!1,o&&o()},method:"GET",token:r})}}InfraredProtocolSDK.currentProtocolSDKs=new Map;let sdkDataType;export{InfraredProtocolSDK,sdkDataType};