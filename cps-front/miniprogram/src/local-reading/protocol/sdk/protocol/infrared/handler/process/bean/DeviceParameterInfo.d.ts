import { InfraredBaudRate } from '../../../InfraredWaterCmdConstants';
/**
 * 设备参数信息
 */
export declare class DeviceParameterInfo {
    /**
     * 用于映射至产测结果页面上的字段名列表
     */
    KEY_FIELDS: string[];
    /**
     * 获取当前数据值列表，与key一一对应
     */
    getValues(): any[];
    /**
     * 红外波特率
     */
    private infraredBaudRate;
    /**
     * 阀门超时时间，单位：秒
     */
    private valveTimeout;
    /**
     * 洗阀时间，单位：天，取值范围：10到60，0表示不洗阀
     */
    private washValveTime;
    /**
     * 洗阀倒计时，单位：天
     */
    private washValveCountdown;
    static transferDeviceParameterInfo(data: any): DeviceParameterInfo;
    getInfraredBaudRate(): InfraredBaudRate;
    setInfraredBaudRate(value: InfraredBaudRate): void;
    getValveTimeout(): number;
    setValveTimeout(value: number): void;
    getWashValveTime(): number;
    setWashValveTime(value: number): void;
    getWashValveCountdown(): number;
    setWashValveCountdown(value: number): void;
}
