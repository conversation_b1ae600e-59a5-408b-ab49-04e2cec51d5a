class IpInfo{constructor(){this.KEY_FIELDS=["ipChannel","masterIp","masterPort","standbyIp","standbyPort","upgradeIp","upgradePort","originalMasterIp","originalMasterPort","originalStandbyIp","originalStandbyPort","originalUpgradeIp","originalUpgradePort"],this.ipChannel=null,this.masterIp=null,this.masterPort=null,this.standbyIp=null,this.standbyPort=null,this.upgradeIp=null,this.upgradePort=null}getValues(){return[this.ipChannel,this.masterIp,this.masterPort,this.standbyIp,this.standbyPort,this.upgradeIp,this.upgradePort,this.masterIp,this.masterPort,this.standbyIp,this.standbyPort,this.upgradeIp,this.upgradePort]}static transferIpInfo(t){var r=new IpInfo;return r.ipChannel=t.ipChannel,r.masterIp=t.masterIp,r.masterPort=t.masterPort,r.standbyIp=t.standbyIp,r.standbyPort=t.standbyPort,r.upgradeIp=t.upgradeIp,r.upgradePort=t.upgradePort,r}getIpChannel(){return this.ipChannel}setIpChannel(t){this.ipChannel=t}getMasterIp(){return this.masterIp}setMasterIp(t){this.masterIp=t}getMasterPort(){return this.masterPort}setMasterPort(t){this.masterPort=t}getStandbyIp(){return this.standbyIp}setStandbyIp(t){this.standbyIp=t}getStandbyPort(){return this.standbyPort}setStandbyPort(t){this.standbyPort=t}getUpgradeIp(){return this.upgradeIp}setUpgradeIp(t){this.upgradeIp=t}getUpgradePort(){return this.upgradePort}setUpgradePort(t){this.upgradePort=t}}export{IpInfo};