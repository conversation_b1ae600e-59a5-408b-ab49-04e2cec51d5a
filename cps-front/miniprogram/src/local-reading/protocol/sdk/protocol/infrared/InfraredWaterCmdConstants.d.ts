/**
 * 起始字符
 */
export declare const START_POS: number;
/**
 * 结束字符
 */
export declare const END_POS: number;
/**
 * 帧类型
 */
export declare enum PacketType {
    /**
     * 命令帧
     */
    COMMAND = 0,
    /**
     * 应答帧
     */
    RESPONSE = 1
}
/**
 * 命令码
 */
export declare enum CommandCode {
    /**
     * 读设备ID
     */
    CODE_03 = 3,
    /**
     * 写设备ID
     */
    CODE_04 = 4,
    /**
     * 设置表计底度
     */
    CODE_05 = 5,
    /**
     * 读表计数据
     */
    CODE_23 = 35,
    /**
     * 读NB信号
     */
    CODE_24 = 36,
    /**
     * 读设备信息
     */
    CODE_25 = 37,
    /**
     * 读设备参数
     */
    CODE_26 = 38,
    /**
     * 读功能选项
     */
    CODE_27 = 39,
    /**
     * 一键读取
     */
    CODE_28 = 40,
    /**
     * 读NB状态
     */
    CODE_29 = 41,
    /**
     * 读无磁信号
     */
    CODE_2A = 42,
    /**
     * 读计量配置
     */
    CODE_2B = 43,
    /**
     * 读7日冻结数据
     */
    CODE_2C = 44,
    /**
     * 阀门控制
     */
    CODE_32 = 50,
    /**
     * 写设备参数
     */
    CODE_33 = 51,
    /**
     * 写功能选项
     */
    CODE_34 = 52,
    /**
     * NB控制
     */
    CODE_35 = 53,
    /**
     * MCU控制
     */
    CODE_36 = 54,
    /**
     * 数据上报
     */
    CODE_37 = 55,
    /**
     * 写计量配置
     */
    CODE_38 = 56,
    /**
     * 蓝牙寻表
     */
    CODE_39 = 57,
    /**
     * 写IP信息
     */
    CODE_3A = 58,
    /**
     * 读IP信息
     */
    CODE_3B = 59,
    /**
     * 开启升级通道
     */
    CODE_3C = 60,
    /**
     * 设置MQTT参数
     */
    CODE_40 = 64,
    /**
     * 读取MQTT参数
     */
    CODE_41 = 65,
    /**
     * 暖通阀开度校准
     */
    CODE_42 = 66,
    /**
     * 暖通阀开度调节
     */
    CODE_43 = 67
}
/**
 * 红外波特率
 ZERO = 1200,
 ONE = 2400,
 TWO = 4800,
 THREE = 9600,
 FOUR = 19200,
 FIVE = 38400,
 SIX = 57600,
 SEVEN = 115200,
 */
export declare enum InfraredBaudRate {
    ZERO = 0,
    ONE = 1,
    TWO = 2,
    THREE = 3,
    FOUR = 4,
    FIVE = 5,
    SIX = 6,
    SEVEN = 7
}
