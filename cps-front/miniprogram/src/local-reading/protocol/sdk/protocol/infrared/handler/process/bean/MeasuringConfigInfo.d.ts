/**
 * 计量配置信息
 */
export declare class MeasuringConfigInfo {
    /**
     * 用于映射至产测结果页面上的字段名列表
     */
    KEY_FIELDS: string[];
    MEASURING_TYPE_ARRAY: string[];
    MEASURING_EXTEND_ARRAY1: string[];
    MEASURING_EXTEND_ARRAY2: string[];
    COUNTING_DIRECTION_ARRAY: string[];
    /**
     * 获取当前数据值列表，与key一一对应
     */
    getValues(): any[];
    /**
     * 出厂计量方式
     */
    private defaultMeasuringType;
    /**
     * 出厂计量扩展
     */
    private defaultMeasuringExtend;
    /**
     * 出厂计数方向
     */
    private defaultCountingDirection;
    /**
     * 当前计量方式
     */
    private measuringTypeIndex;
    /**
     * 当前计量扩展（光电）
     */
    private measuringExtendIndex1;
    /**
     * 当前计量扩展（无磁）
     */
    private measuringExtendIndex2;
    /**
     * 当前计数方向
     */
    private countingDirectionIndex;
    static transferMeasuringConfigInfo(data: any): MeasuringConfigInfo;
    getDefaultMeasuringType(): string;
    setDefaultMeasuringType(value: string): void;
    getDefaultMeasuringExtend(): string;
    setDefaultMeasuringExtend(value: string): void;
    getDefaultCountingDirection(): string;
    setDefaultCountingDirection(value: string): void;
    getMeasuringTypeIndex(): number;
    setMeasuringTypeIndex(value: number): void;
    getMeasuringExtendIndex1(): number;
    setMeasuringExtendIndex1(value: number): void;
    getMeasuringExtendIndex2(): number;
    setMeasuringExtendIndex2(value: number): void;
    getCountingDirectionIndex(): number;
    setCountingDirectionIndex(value: number): void;
}
