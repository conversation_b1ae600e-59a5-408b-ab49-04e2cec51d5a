import{InfraredWaterAbstractMonitorProcess}from"../InfraredWaterAbstractMonitorProcess";import stringUtils from"../../../../utils/StringUtils";import protocolUtils from"../../../ProtocolUtils";class Read7DayFreezeDataProcess extends InfraredWaterAbstractMonitorProcess{constructor(){super(...arguments),this.freezeData=[]}process(e){var r=new Uint8Array(e);let t=0;t+=1;var a=e.slice(t,t+2);for(this.date=stringUtils.buffer2hex(new Uint8Array(a).reverse()),t+=2;this.freezeData.length<7;)r.slice(t,t+8).every(e=>238===e||255===e)?this.freezeData.push({forward:null,backward:null}):this.freezeData.push({forward:protocolUtils.parseMeteringValueScale(e.slice(t,t+4)),backward:protocolUtils.parseMeteringValueScale(e.slice(t+4,t+8))}),t+=8;console.log("解析7日冻结数据结果：",this.freezeData),this.setResult(!0)}getDate(){return this.date}getFreezeData(){return this.freezeData}}export{Read7DayFreezeDataProcess};