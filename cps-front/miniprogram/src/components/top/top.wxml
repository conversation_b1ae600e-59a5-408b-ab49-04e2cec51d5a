<!--components/top.wxml-->
<view style="height:130rpx"></view>
<view style="box-shadow: 0 0 8px 0px #86868673;position: fixed; top:0;left: 0; width: 100vw; z-index: 3;">
    <view class="top-tool">
        <block wx:if="{{sortOpt && sortOpt.length}}">
            <view class="sort-wrap">
                <van-dropdown-menu custom-class="dropdown-menu" active-color="#1989fa">
                    <van-dropdown-item value="{{sortDefault}}" options="{{ sortOpt }}" bind:change="dropdownMenuChangeForSort"/>
                </van-dropdown-menu>
            </view>
            <view class="line"></view>
        </block>

        <block wx:if="{{filterDate}}">
            <view class="filter-wrap">
                <filter-date input-align="center" value="{{date}}" bind:change="dateChange" defaultDate="{{defaultDate}}"/>
            </view>
        </block>
        <block wx:else>
            <view class="filter-wrap" bindtap="showFilter">
                <van-icon name="filter-o"/>
                <text>筛选</text>
            </view>
        </block>
    </view>
    <view class="list-total" wx:if="{{queryDate || total}}">
        <text wx:if="{{queryDate}}">{{queryDate}}</text>
        <text wx:if="{{queryDate && total}}">；</text>
        <text wx:if="{{total}}">共计 {{total}} 条，已加载 {{loaded}} 条</text>
    </view>
</view>

<van-popup custom-style="background-color:transparent;height:auto" show="{{ filterShow }}" position="top" close-on-click-overlay="true" bind:click-overlay="closeFilter">
    <view style="height: 100vh;display: flex;flex-direction: column;" catch:touchmove="noop">
        <view style="flex: 0;">
            <view class="filter-group">
                <slot></slot>
            </view>
            <view class="filter-btn-group">
                <van-button icon="replay" custom-class='btn-reset' type="info" size="normal" block bindtap="reset">重置</van-button>
                <view style="width: 20rpx;"></view>
                <view style="flex: 1;">
                    <van-button icon="search" custom-class='btn-sub' type="primary" size="normal" block bindtap="query">查询</van-button>
                </view>
            </view>
        </view>
        <view style="flex: 1;" catchtap="closeFilter"></view>
    </view>
</van-popup>