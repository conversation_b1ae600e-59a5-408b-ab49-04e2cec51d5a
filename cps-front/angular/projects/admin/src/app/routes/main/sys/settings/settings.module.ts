import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {WebsiteComponent} from './website/website.component';
import {RouterModule, Routes} from '@angular/router';
import {ContactComponent} from './contact/contact.component';
import {NzSpinModule} from "ng-zorro-antd/spin";
import {NzCardModule} from "ng-zorro-antd/card";
import {NzFormModule} from "ng-zorro-antd/form";
import {RendererModule} from "@web-ui";
import {ComponentsModule} from "../../../../components/components.module";
import {ReactiveFormsModule} from '@angular/forms';
import {NzIconModule} from 'ng-zorro-antd/icon';
import {NzButtonModule} from 'ng-zorro-antd/button';
import {NzDividerModule} from 'ng-zorro-antd/divider';
import {NzUploadModule} from 'ng-zorro-antd/upload';

const routes: Routes = [
    {path: '', redirectTo: 'website'},
    {
        path: 'website',
        component: WebsiteComponent,
        data: {keep: true, title: '网站设置', guard: 'sys-settings-website-updateWebsite'}
    },
    {
        path: 'contact',
        component: ContactComponent,
        data: {keep: true, title: '联系方式', guard: 'sys-settings-contact-updateContact'}
    }
];

@NgModule({
    declarations: [
        WebsiteComponent, ContactComponent
    ],
    imports: [
        RouterModule.forChild(routes),
        CommonModule,
        NzSpinModule,
        NzCardModule,
        NzFormModule,
        RendererModule,
        ComponentsModule,
        ReactiveFormsModule,
        NzIconModule,
        NzButtonModule,
        NzDividerModule,
        NzUploadModule
    ]
})
export class SettingsModule {
}
