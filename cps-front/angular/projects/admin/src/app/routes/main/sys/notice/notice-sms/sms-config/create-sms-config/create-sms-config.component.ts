import {Component, Inject, OnInit} from '@angular/core';
import {NzMessageService} from "ng-zorro-antd/message";
import {Validators} from "@angular/forms";
import {Router} from '@angular/router';
import {FormComponent} from "@core";
import {SmsConfig} from '@lib';
import {SmsConfigService} from '../../../../../../../service/sms/sms-config.service';
import {CompanyService} from "../../../../../../../service/sys/company.service";
import { SmsPlatforms } from "../../../../../../../components/EnumOptions";

@Component({
    selector: 'app-create-sms-config',
    templateUrl: './create-sms-config.component.html',
    styleUrls: ['./create-sms-config.component.scss']
})
export class CreateSmsConfigComponent extends FormComponent implements OnInit {

    title: string = '新建短信配置';
    treeData: any[] = [];
    companyId: string;

    constructor(@Inject('apiUrl') public apiUrl, public messageService: NzMessageService,
                public router: Router, public smsConfigService: SmsConfigService,
                private companyService: CompanyService) {
        super();
    }

    ngOnInit() {
        this.formGroup = this.buildForm({
            company: [null, [Validators.required], this.field({
                label: '所属公司',
                type: 'treeSelect',
                allowClear: false,
                expandChange: () => {},
                treeData: this.treeData,
                selectChange: (event: string, treeSelect: any) => this.companyChange(event, treeSelect)
            })],
            // 短信能力开关
            enable: [true, [Validators.required], this.field({
                label: '短信能力开关', type: 'radio', optionItems: [{label: '是', value: true}, {label: '否', value: false}]
            })],
            smsPlatform: [null, [Validators.required], this.field({
                label: '短信平台',
                type: 'select',
                optionItems: SmsPlatforms,
                selectChange: (event: string) => this.changeSmsPlatform(event)
            })],
            // 短信产品域名
            domain: [null, [Validators.required, Validators.maxLength(64)], this.field({
                label: '短信产品域名', type: 'text', maxlength: 64
            })],
            ecName: [null, [Validators.maxLength(64)], this.field({
                label: '用户企业名称', type: 'text', maxlength: 64
            })],
            // accessKeyId
            accessKeyId: [null, [Validators.required, Validators.maxLength(32)], this.field({
                label: 'accessKeyId', type: 'text', maxlength: 32
            })],
            // accessKeySecret
            accessKeySecret: [null, [Validators.required, Validators.maxLength(32)], this.field({
                label: 'accessKeySecret', type: 'text', maxlength: 32
            })],
            // 签名
            signName: [null, [Validators.required, Validators.maxLength(64)], this.field({
                    label: '签名', type: 'text', maxlength: 64
                }
            )],
            sendStartHour: [null, [Validators.min(0), Validators.max(23)], this.field({
                label: '发送短信开始时间(小时)', type: 'number', max: 23, min: 0, tooltip: '如不设置，则为系统默认：8点'
            })],
            sendEndHour: [null, [Validators.min(0), Validators.max(23)], this.field({
                label: '发送短信结束时间(小时)', type: 'number', max: 23, min: 0, tooltip: '如不设置，则为系统默认：22点', divider: {enabled: true, text: '短信模板编码'}
            })],
            // 注册验证模板编码
            registerValidateCode: [null, [Validators.required, Validators.maxLength(32)], this.field({
                label: '注册验证模板编码', type: 'text', maxlength: 32
            })],
            // 登录验证码模板编码
            loginValidateCode: [null, [Validators.required, Validators.maxLength(32)], this.field({
                label: '登录验证模板编码', type: 'text', maxlength: 32
            })],
            // 修改密码验证码模板编码
            updatePasswordCode: [null, [Validators.required, Validators.maxLength(32)], this.field({
                label: '修改密码模板编码', type: 'text', maxlength: 32
            })],
            // 找回密码验证码模板编码
            forgetPasswordCode: [null, [Validators.required, Validators.maxLength(32)], this.field({
                label: '找回密码模板编码', type: 'text', maxlength: 32
            })],
            // 充值成功通知短信模板编码
            rechargeSuccessCode: [null, [Validators.required, Validators.maxLength(32)], this.field({
                label: '充值成功模板编码', type: 'text', maxlength: 32
            })],
            // 开阀通知模板编码
            openValveCode: [null, [Validators.required, Validators.maxLength(32)], this.field({
                label: '开阀通知模板编码', type: 'text', maxlength: 32
            })],
            // 关阀通知模板编码
            closeValveCode: [null, [Validators.required, Validators.maxLength(32)], this.field({
                label: '关阀通知模板编码', type: 'text', maxlength: 32
            })],
            // 余额预警通知编码
            balanceWarnNotifyCode: [null, [Validators.required, Validators.maxLength(32)], this.field({
                label: '余额预警模板编码', type: 'text', maxlength: 32
            })],
            registerSuccessCode: [null, [Validators.required, Validators.maxLength(32)], this.field({
                label: '注册成功通知编码', type: 'text', maxlength: 32
            })],
            registerFailCode: [null, [Validators.required, Validators.maxLength(32)], this.field({
                label: '注册失败通知编码', type: 'text', maxlength: 32
            })]
        });
        this.companyService.companyTree().subscribe(companies => {
            if (companies && companies.length > 0) {
                this.treeData.push(...companies);
            }
        });
    }

    /**
     * 表单提交
     * @param formValue 表单数据
     */
    onValidSuccess(formValue: any): any {
        if (!this.submitted) {
            this.submitted = true;
            const sendStartHour = formValue.sendStartHour;
            const sendEndHour = formValue.sendEndHour;
            if (sendStartHour && sendEndHour && sendStartHour >= sendEndHour) {
                this.messageService.error("发送短信开始时间必须小于发送短信结束时间");
                this.submitted = false;
                return;
            }
            const smsConfig: SmsConfig = formValue;
            smsConfig.companyId = this.companyId;
            this.smsConfigService.addSmsConfig(smsConfig).subscribe({
                next: () => {
                    this.messageService.create('success', '保存成功');
                    this.submitted = false;
                    this.goBack();
                },
                error: () => {
                    this.submitted = false;
                }
            });
        }
    }

    /**
     * 返回上个视图
     */
    goBack() {
        this.router.navigate(['/main/sys/notice/notice-sms'], {replaceUrl: true});
    }

    companyChange(event, treeSelect) {
        let selectedNode = treeSelect.getSelectedNodeList()[0];
        this.companyId = selectedNode.origin.companyId;
    }

    /**
    * 切换短信平台时相关参数校验条件修改
    */
    changeSmsPlatform(event: string) {
        if (event === 'ALI_YUN') {
            this.formFields.find(item => item.fieldName === 'ecName').category = 'other';
            this.formGroup.patchValue({ecName:null});
            this.updateValidators(this.formGroup, 'ecName', []);
            let index = 10;
            while (index <= this.formFields.length-1) {
                this.formFields[index].required = true;
                this.updateValidators(this.formGroup, this.formFields[index].fieldName, [Validators.required]);
                index++;
            }
        } else if (event === 'MAS_TEMP') {
            this.formFields.find(item => item.fieldName === 'ecName').category = 'default';
            this.formFields.find(item => item.fieldName === 'ecName').required = true;
            this.updateValidators(this.formGroup, 'ecName', [Validators.required]);
            let index = 10;
            while (index <= this.formFields.length-1) {
                this.formFields[index].required = false;
                this.updateValidators(this.formGroup, this.formFields[index].fieldName, []);
                index++;
            }
        }
    }
}
