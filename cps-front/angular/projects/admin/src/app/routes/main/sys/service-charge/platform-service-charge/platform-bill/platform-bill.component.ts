import {Component, Inject, OnInit} from '@angular/core';
import {AbstractPageComponent} from "../../../../../../components/AbstractPageComponent";
import {PlatformServiceBill} from "@lib";
import {HttpClient} from "@angular/common/http";
import {NzMessageService} from "ng-zorro-antd/message";
import {NzModalService} from "ng-zorro-antd/modal";
import {AuthService} from "../../../../../../auth/auth.service";
import {NzDrawerService} from "ng-zorro-antd/drawer";
import {Operator, Order} from "@core";
import {BusinessUtils} from "../../../../../../utils/BusinessUtils";
import {ViewPlatformBillComponent} from "./view-platform-bill/view-platform-bill.component";
import {platformChargeMode, wrap} from "../../../../../../components/EnumOptions";

@Component({
    selector: 'app-platform-bill',
    templateUrl: './platform-bill.component.html',
    styleUrls: ['./platform-bill.component.scss']
})
export class PlatformBillComponent extends AbstractPageComponent<PlatformServiceBill> implements OnInit {

    constructor(http: HttpClient, @Inject('apiUrl') public apiUrl: any,
                messageService: NzMessageService, modalService: NzModalService,
                private authService: AuthService, public drawerService: NzDrawerService) {
        super(http, apiUrl + 'auth/sys/platformServiceCharge/bill/page', messageService, modalService);
    }

    ngOnInit(): void {
        this.pagination.queryForm = this.buildQueryForm({
            chargeMode: [null, this.filter({
                label: '收费模式',
                field: 'chargeMode',
                type: 'select',
                optionItems: wrap(platformChargeMode),
                operator: Operator.eq
            })],
            payTime: [null, this.filter({
                label: '支付时间', field: 'payTime', type: 'range', submitted: false, allowClear: false
            })],
        });
        this.pagination.setDefaultSort('createdDate', Order.desc);
        this.pagination.reload();
    }

    /**
     * 查询数据前处理特殊逻辑
     */
    override beforeDataLoad() {
        // @ts-ignore
        BusinessUtils.handleCreatedDateRangeCondition(this.pagination, 'payTime', 'payTime');
    }

    viewDetail(platformServiceBill: PlatformServiceBill) {
        const drawerRef = this.drawerService.create<ViewPlatformBillComponent, {
            platformServiceBill: PlatformServiceBill
        }, string>({
            nzTitle: '平台服务费账单详情',
            nzWidth: 650,
            nzContent: ViewPlatformBillComponent,
            nzContentParams: {
                platformServiceBill: platformServiceBill
            }
        });
    }
}
