import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ChangeEmCat1ElectricModuleComponent } from './change-em-cat1-electric-module.component';

describe('ChangeEmCat1ElectricModuleComponent', () => {
  let component: ChangeEmCat1ElectricModuleComponent;
  let fixture: ComponentFixture<ChangeEmCat1ElectricModuleComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ChangeEmCat1ElectricModuleComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ChangeEmCat1ElectricModuleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
