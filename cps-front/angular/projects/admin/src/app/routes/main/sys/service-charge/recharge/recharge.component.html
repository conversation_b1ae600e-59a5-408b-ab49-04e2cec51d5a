<nz-spin [nzTip]="tip" [nzSpinning]="spinning" style="height: 100%;">
	<!--页头查询部分-->
	<app-query-card [pagination]="pagination"></app-query-card>
	<nz-card class="table-card">
		<!--表格内容部分-->
		<nz-table #basicTable nzBordered nzShowPagination nzShowSizeChanger
		          nzFrontPagination="false" nzSize="small"
		          [nzScroll]="{ x: '900px',y:'calc(100vh - 310px)' }"
		          [nzLoading]="pagination.loading"
		          [nzData]="pagination.content"
		          [nzTotal]="pagination.totalElements"
		          [nzShowTotal]="totalTemplate"
		          [(nzPageIndex)]="pagination.pageIndex"
		          [(nzPageSize)]="pagination.pageSize" [nzPageSizeOptions]="[20,50,100,200]"
		          (nzPageIndexChange)="pagination.refresh()"
		          (nzPageSizeChange)="pagination.refresh()">
			<ng-template #totalTemplate let-total let-range="range">
				显示第 {{range[0]}} 到第 {{range[1]}} 条记录，总共 {{total}} 条记录
			</ng-template>
			<thead nzSingleSort>
			<tr>
				<th [nzWidth]="'160px'" nzAlign="center" nzShowSort
				    (nzSortOrderChange)="pagination.setSort('name', $event)">公司名称
				</th>
				<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
				    (nzSortOrderChange)="pagination.setSort('companyId', $event)">公司ID
				</th>
				<th [nzWidth]="'120px'" nzAlign="center">联系电话</th>
				<th [nzWidth]="'140px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('emServiceChargeRule.ruleName', $event)">电表服务费规则</th>
				<th [nzWidth]="'140px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('platformServiceChargeRule.ruleName', $event)">平台服务费规则</th>
				<th [nzWidth]="'120px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('serviceBalance', $event)">服务费余额</th>
				<th [nzWidth]="'120px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('cumulativeRechargeAmount', $event)">累计充值金额</th>
				<th [nzWidth]="'120px'" nzAlign="center">数据推送</th>
				<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
				    (nzSortOrderChange)="pagination.setSort('enabled', $event)">启用/禁用
				</th>
				<th [nzWidth]="'140px'" nzAlign="center" nzShowSort
				    (nzSortOrderChange)="pagination.setSort('onlyUseMini', $event)">仅使用小程序
				</th>
				<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
				    (nzSortOrderChange)="pagination.setSort('createdDate', $event)">创建时间
				</th>
				<th nzAlign="center" nzRight nzWidth="100px">操作</th>
			</tr>
			</thead>
			<tbody>
            <ng-template #free><nz-tag nzColor="green">免服务费</nz-tag></ng-template>
			<tr *ngFor="let data of basicTable['data']">
				<td nzEllipsis nzAlign="center" [nzTooltipTitle]="data.name" nzTooltipPlacement="bottom" nz-tooltip>
					<a (click)="viewDetail(data)">{{ data?.name }}</a>
				</td>
				<td nzAlign="center">{{ data?.companyIdVal }}</td>
				<td nzAlign="center">{{ data?.phone }}</td>
				<td nzAlign="center">
					<ng-container *ngIf="data?.emServiceChargeRule;else free">
						{{data?.emServiceChargeRule?.ruleName}}
					</ng-container>
				</td>
                <td nzAlign="center">
                    <ng-container *ngIf="data?.platformServiceChargeRule;else free">
                        {{data?.platformServiceChargeRule?.ruleName}}
                    </ng-container>
                </td>
				<td nzAlign="center">{{ data?.serviceBalance }}</td>
				<td nzAlign="center">{{ data?.cumulativeRechargeAmount }}</td>
				<td nzAlign="center">
					<nz-tag [nzColor]="data?.enablePush ? 'success' : 'error'">
						<span>{{ data?.enablePush ? '开启' : '关闭' }}</span>
					</nz-tag>
				</td>
				<td nzAlign="center">
					<nz-tag [nzColor]="data?.enabled ? 'success' : 'error'">
						<span>{{ data?.enabled ? '启用' : '禁用' }}</span>
					</nz-tag>
				</td>
				<td nzAlign="center">{{data?.onlyUseMini ? '是' : '否'}}</td>
				<td nzAlign="center">{{ data?.createdDate }}</td>
				<td nzAlign="center" nzRight>
                    <ng-container *aclIf="'sys-serviceCharge-recharge-add'">
                        <a *ngIf="data?.emServiceChargeRule || data?.platformServiceChargeRule" routerLink="create-recharge" [queryParams]="{companyId:data.companyIdVal}">
                            <span nz-icon nzType="pay-circle" nzTheme="outline" nzTooltipTitle="充值"
                                  nzTooltipPlacement="bottom" nz-tooltip></span>
                        </a>
                    </ng-container>

				</td>
			</tr>
			</tbody>
		</nz-table>
	</nz-card>
</nz-spin>

