import { ComponentFixture, TestBed } from '@angular/core/testing';

import { UploadConcentratorComponent } from './upload-concentrator.component';

describe('UploadConcentratorComponent', () => {
  let component: UploadConcentratorComponent;
  let fixture: ComponentFixture<UploadConcentratorComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ UploadConcentratorComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(UploadConcentratorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
