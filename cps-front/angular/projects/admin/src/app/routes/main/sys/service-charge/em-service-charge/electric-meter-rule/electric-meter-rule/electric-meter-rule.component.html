<nz-spin [nzTip]="tip" [nzSpinning]="spinning" style="height: 100%;">
    <!--页头查询部分-->
    <app-query-card [pagination]="pagination"></app-query-card>
    <nz-card class="table-card">
        <div class="table-top-wrap">
            <button nz-button routerLink="create" *aclIf="'sys-serviceCharge-emServiceCharge-rule-add'" [nzType]="'primary'"
                    class="mb-sm mr-sm">
                <span nz-icon nzType="plus" nzTheme="outline"></span>
                新建
            </button>
        </div>
        <!--表格内容部分-->
        <nz-table #basicTable nzBordered nzShowPagination nzShowSizeChanger
                  nzFrontPagination="false" nzSize="small"
                  [nzScroll]="{ x: '900px',y:'calc(100vh - 310px)' }"
                  [nzLoading]="pagination.loading"
                  [nzData]="pagination.content"
                  [nzTotal]="pagination.totalElements"
                  [nzShowTotal]="totalTemplate"
                  [(nzPageIndex)]="pagination.pageIndex"
                  [(nzPageSize)]="pagination.pageSize" [nzPageSizeOptions]="[20,50,100,200]"
                  (nzPageIndexChange)="pagination.refresh()"
                  (nzPageSizeChange)="pagination.refresh()">
            <ng-template #totalTemplate let-total let-range="range">
                显示第 {{range[0]}} 到第 {{range[1]}} 条记录，总共 {{total}} 条记录
            </ng-template>
            <thead nzSingleSort>
            <tr>
                <th [nzWidth]="'150px'" nzAlign="center" nzShowSort
                    (nzSortOrderChange)="pagination.setSort('ruleName', $event)">规则名称
                </th>
                <th [nzWidth]="'120px'" nzAlign="center" nzShowSort
                    (nzSortOrderChange)="pagination.setSort('freeMonth', $event)">免费期月数
                </th>
                <th [nzWidth]="'120px'" nzAlign="center" nzShowSort
                    (nzSortOrderChange)="pagination.setSort('graceMonth', $event)">宽限期月数
                </th>
                <th [nzWidth]="'120px'" nzAlign="center" nzShowSort
                    (nzSortOrderChange)="pagination.setSort('chargeMode', $event)">收费模式
                </th>
                <th [nzWidth]="'120px'" nzAlign="center" nzShowSort
                    (nzSortOrderChange)="pagination.setSort('unitPrice', $event)">单价
                </th>
                <th [nzWidth]="'120px'" nzAlign="center" nzShowSort
                    (nzSortOrderChange)="pagination.setSort('reportBufferDay', $event)">上报缓冲天数
                </th>
                <th [nzWidth]="'180px'" nzAlign="center" nzShowSort
                    (nzSortOrderChange)="pagination.setSort('createdDate', $event)">创建时间
                </th>
                <th nzAlign="center" nzRight nzWidth="110px">操作</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let data of basicTable['data']">
                <td nzAlign="center"><a (click)="viewDetail(data)">{{ data?.ruleName }}</a></td>
                <td nzAlign="center">{{ data?.freeMonth }}</td>
                <td nzAlign="center">{{ data?.graceMonth }}</td>
                <td nzAlign="center">{{ 'ChargeMode_' + data?.chargeMode | translate }}</td>
                <td nzAlign="center">{{ data?.unitPrice }}</td>
                <td nzAlign="center">{{ data?.reportBufferDay }}</td>
                <td nzAlign="center">{{ data?.createdDate }}</td>
                <td nzAlign="center" nzRight>
                    <ng-container *aclIf="'sys-serviceCharge-emServiceCharge-rule-edit'">
                        <a routerLink="edit" [queryParams]="{id:data.id}">
                            <span nz-icon nzType="edit" nzTheme="outline" nzTooltipTitle="编辑"
                                  nzTooltipPlacement="bottom" nz-tooltip></span>
                        </a>
                    </ng-container>
                    <ng-container *aclIf="'sys-serviceCharge-emServiceCharge-rule-delete'">
                        <nz-divider nzType="vertical"></nz-divider>
                        <a (click)="remove(data)">
                            <span nz-icon nzType="delete" nzTheme="outline" class="text-danger" nzTooltipTitle="删除"
                                  nzTooltipPlacement="bottom" nz-tooltip></span>
                        </a>
                    </ng-container>
                </td>
            </tr>
            </tbody>
        </nz-table>
    </nz-card>
</nz-spin>
