<!--页头查询部分-->

<app-query-card [pagination]="pagination"></app-query-card>


<nz-card class="table-card">
    <!--表格内容部分-->
    <nz-table #basicTable nzBordered nzShowPagination nzShowSizeChanger
              nzFrontPagination="false" nzSize="small"
              [nzScroll]="{ x: '100%',y:'calc(100vh - 310px)' }"
              [nzLoading]="pagination.loading"
              [nzData]="pagination.content"
              [nzTotal]="pagination.totalElements"
              [nzShowTotal]="totalTemplate"
              [(nzPageIndex)]="pagination.pageIndex"
              [(nzPageSize)]="pagination.pageSize" [nzPageSizeOptions]="[20,50,100,200]"
              (nzPageIndexChange)="pagination.refresh()"
              (nzPageSizeChange)="pagination.refresh()">
        <ng-template #totalTemplate let-total let-range="range">
            显示第 {{ range[0] }} 到第 {{ range[1] }} 条记录，总共 {{ total }} 条记录
        </ng-template>
        <thead>
        <tr>
            <th [nzWidth]="'110px'" nzAlign="center">公司名称</th>
            <th [nzWidth]="'100px'" nzAlign="center" nzShowSort
                (nzSortOrderChange)="pagination.setSort('chargeMode', $event)">收费模式
            </th>
            <th [nzWidth]="'100px'" nzAlign="center" nzShowSort
                (nzSortOrderChange)="pagination.setSort('chargePeriod', $event)">收费周期
            </th>
            <th [nzWidth]="'100px'" nzAlign="center" nzShowSort
                (nzSortOrderChange)="pagination.setSort('unitPrice', $event)">单价/费率
            </th>
            <th [nzWidth]="'120px'" nzAlign="center" nzShowSort
                (nzSortOrderChange)="pagination.setSort('amount', $event)">账单金额
            </th>
            <th [nzWidth]="'180px'" nzAlign="center" nzShowSort
                (nzSortOrderChange)="pagination.setSort('bill.amount', $event)">水电费账单金额
            </th>
            <th [nzWidth]="'200px'" nzAlign="center">水电费账单周期</th>
            <th [nzWidth]="'200px'" nzAlign="center">表号</th>
            <th [nzWidth]="'180px'" nzAlign="center" nzShowSort
                (nzSortOrderChange)="pagination.setSort('payTime', $event)">支付时间
            </th>
            <th [nzWidth]="'180px'" nzAlign="center">备注
            </th>
        </tr>
        </thead>
        <tbody>
        <ng-template #once>一次性收取</ng-template>
        <tr *ngFor="let data of basicTable['data']">
            <td nzLeft nzAlign="center" [nzTooltipTitle]="data.companyId" nzTooltipPlacement="bottom" nz-tooltip>
                <a (click)="viewDetail(data)">{{ data?.companyId }}</a>
            </td>
            <td nzAlign="center">{{ "ChargeMode_" + data?.chargeMode | translate }}</td>
            <td nzAlign="center">
                <ng-container *ngIf="data?.chargeMode === 'PERIOD'">
                    <ng-container *ngIf="data?.chargePeriod>0;else once">
                        {{ data?.chargePeriod }}个月
                    </ng-container>
                </ng-container>
                <ng-container *ngIf="data?.chargeMode === 'PERCENT' ">-</ng-container>
            </td>
            <td nzAlign="center">{{ data?.unitPrice }}</td>
            <td nzAlign="center">{{ data?.amount | amountFormatter }}</td>
            <td nzAlign="center">
                <ng-container *ngIf="data?.chargeMode === 'PERCENT'">{{ data?.bill?.amount }}</ng-container>
            </td>
            <td nzAlign="center">
                <ng-container *ngIf="data?.chargeMode === 'PERCENT'">{{ data?.bill.startDate }}~{{ data?.bill.endDate }}</ng-container>
            </td>
            <td nzAlign="center">
                <ng-container *ngIf="data?.chargeMode === 'PERCENT'">
                    <ng-container *ngIf="data?.bill?.waterMeter">水表:{{ data?.bill.waterMeter.code }}</ng-container>
                    <ng-container *ngIf="data?.bill?.electricMeter">电表:{{ data?.bill.electricMeter.code }}
                    </ng-container>
                </ng-container>
            </td>
            <td nzAlign="center">{{ data?.payTime }}</td>
            <td nzAlign="center">{{ data?.remark }}</td>
        </tr>
        </tbody>
    </nz-table>
</nz-card>

