import {Component, Inject, OnInit} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {NzMessageService} from "ng-zorro-antd/message";
import {NzModalService} from "ng-zorro-antd/modal";
import {NzDrawerService} from "ng-zorro-antd/drawer";
import {Observable} from "rxjs";
import {Operator, Order} from "@core";
import {AbstractPageComponent} from "../../../../../../components/AbstractPageComponent";
import {AuthService} from "../../../../../../auth/auth.service";
import {ConcentratorService} from "../../../../../../service/archive/concentrator.service";
import {BuildingUnitService} from "../../../../../../service/archive/building-unit.service";
import {WmConcentratorWaterMeterService} from '../../../../../../service/archive/wm-concentrator-water-meter.service';
import {ViewWmConcentratorWaterMeterComponent} from './view-wm-concentrator-water-meter/view-wm-concentrator-water-meter.component';
import {WmConcentratorWaterMeter} from '@lib';
import {ExcelDownloadService} from '../../../../../../service/common/excel-download.service';
import {UploadConcentratorWaterMeter} from './upload-concentrator-water-meter/upload-concentrator-water-meter';
import {WmWaterMeterService} from "../../../../../../service/archive/wm-water-meter.service";
import {WaterMeterChildrenComponent} from "../water-meter-children/water-meter-children.component";
import {SyncStatus, WaterTypes, wrap} from "../../../../../../components/EnumOptions";
import {ChangeWmConcentratorWaterMeterComponent} from './change-wm-concentrator-water-meter/change-wm-concentrator-water-meter.component';
import {WaterMeterChangeBatchComponent} from '../water-meter-change-batch/water-meter-change-batch.component';

@Component({
    selector: 'app-wm-concentrator-water-meter',
    templateUrl: './wm-concentrator-water-meter.component.html',
    styleUrls: ['./wm-concentrator-water-meter.component.scss']
})
export class WmConcentratorWaterMeterComponent extends AbstractPageComponent<any> implements OnInit {
    changeId: null;
    newMeterCode: string;
    boundParentWindow = false;
    boundMeterId = null;
    boundParentMeterId = null;
    parentMeterCode: string = '';
    waterMeterBoundDto: any;
    setRateWindow = false;
    setRateMeterId = null;
    rate = null;

    constructor(http: HttpClient, @Inject('apiUrl') public apiUrl: any, @Inject('exportUrl') public exportUrl: any,
                messageService: NzMessageService, modalService: NzModalService,
                public drawerService: NzDrawerService, public wmConcentratorWaterMeterService: WmConcentratorWaterMeterService,
                private authService: AuthService,
                private concentratorService: ConcentratorService, private buildingUnitService: BuildingUnitService,
                public excelDownloadService: ExcelDownloadService, private wmWaterMeterService: WmWaterMeterService) {
        super(http, apiUrl + 'auth/archive/meter/concentratorWater/page', messageService, modalService);
    }

    ngOnInit() {
        this.pagination.queryForm = this.buildQueryForm({
            code: [null, this.filter({
                label: '水表编号', field: 'code', maxlength: 14, operator: Operator.like
            })],
            concentrator: [null, this.filter({
                label: '集中器编号',
                field: 'concentrator.code',
                operator: Operator.like
            })],
            roomNumber: [null, this.filter({
                label: '房号', field: 'room.roomNumber', maxlength: 15, operator: Operator.like
            })],
            customerName: [null, this.filter({
                label: '住户名', field: 'room.customer.name', maxlength: 32, operator: Operator.like
            })],
            customerMobile: [null, this.filter({
                label: '住户手机号', field: 'room.customer.mobile', maxlength: 11, operator: Operator.like
            })],
            sync: [null, this.filter({
                label: '同步状态',
                field: 'sync',
                type: 'select',
                optionItems: wrap(SyncStatus),
                operator: Operator.eq
            })],
            valve: [null, this.filter({
                label: '是否有阀',
                field: 'valve',
                type: 'select',
                optionItems: wrap([{label: '有阀', value: true}, {label: '无阀', value: false}]),
                operator: Operator.eq
            })],
            waterType: [null, this.filter({
                label: '用水类型',
                field: 'waterType',
                type: 'select',
                optionItems: wrap(WaterTypes),
                operator: Operator.eq
            })]
        });
        this.pagination.setDefaultSort('createdDate', Order.desc);
        this.buildingUnitService.initCommunityFilter(this.pagination);
        this.pagination.reload();
    }

    /**
     * 单个删除
     * @param id
     */
    override removeData(id: any): Observable<void> {
        return this.wmConcentratorWaterMeterService.removeWmConcentratorWaterMeterById(id);
    }

    /**
     * 批量删除
     * @param ids
     */
    override batchRemoveData(ids: any[]): Observable<void> {
        return this.wmConcentratorWaterMeterService.batchRemoveWmConcentratorWaterMeterByIds(ids);
    }

    /**
     * 集中器水表详情
     * @param wmConcentratorWaterMeter
     */
    viewDetail(wmConcentratorWaterMeter: any) {
        const drawerRef = this.drawerService.create<ViewWmConcentratorWaterMeterComponent, { wmConcentratorWaterMeter: WmConcentratorWaterMeter }, string>({
            nzTitle: '集中器水表详情',
            nzWidth: 750,
            nzContent: ViewWmConcentratorWaterMeterComponent,
            nzContentParams: {
                wmConcentratorWaterMeter: wmConcentratorWaterMeter
            }
        });
        drawerRef.afterOpen.subscribe(() => {
        });
        drawerRef.afterClose.subscribe(() => {
        });
    }

    export() {
        if(this.pagination.content.length === 0){
            this.messageService.warning('当前无数据可导出');
            return;
        }
        this.pagination.doExport(this.apiUrl + 'auth/archive/meter/concentratorWater/export', this.exportUrl);
    }

    downloadExcelTemplate(name: string) {
        this.excelDownloadService.downloadExcelTemplate(name);
    }

    uploadConcentrator(type) {
        const modal = this.modalService.create({
            nzTitle: `上传导入${type === 'add' ? '(新增模式)' : '(编辑模式)'}`,
            nzContent: UploadConcentratorWaterMeter,
            nzMaskClosable: false,
            nzWidth: 480,
            nzComponentParams: {
                type: type
            },
            nzFooter: [
                {
                    label: '确定',
                    // @ts-ignore
                    type: 'primary',
                    onClick: component => {
                        if (component !== null) {
                            component.submit(() => {
                                this.messageService.create('success', '导入成功！');
                                this.pagination.refresh();
                                modal.destroy();
                            });
                        }
                    }
                },
                {
                    label: '取消',
                    // @ts-ignore
                    shape: 'default',
                    onClick: () => modal.destroy()
                }
            ]
        });
    }

    handleOk() {
        if (this.newMeterCode.length != 14) {
            return;
        }

        this.wmConcentratorWaterMeterService.changeMeterCode(this.changeId, this.newMeterCode).subscribe(() => {
            this.messageService.success("更换成功");
            this.changeId = null;
            this.pagination.refresh();
        });
    }

    showBoundParentMeterWindow(id, child) {
        this.boundParentWindow = true;
        this.boundMeterId = id;
        if (child) {
            // 弹窗时若水表有父表，需要查询并展示父表信息
            this.wmWaterMeterService.getParentMeterById(id).subscribe((waterMeterBoundDto): any => {
                this.waterMeterBoundDto = waterMeterBoundDto;
                this.boundParentMeterId = waterMeterBoundDto.id;
            });
        } else {
            this.waterMeterBoundDto = null;
        }
    }

    searchParentMeter() {
        if (!this.parentMeterCode) {
            this.messageService.warning("请输入父表表号");
            return;
        }
        this.wmWaterMeterService.getParentMeter(this.parentMeterCode).subscribe((waterMeterBoundDto): any => {
            this.waterMeterBoundDto = waterMeterBoundDto;
        });
    }

    handleBoundParentMeter() {
        if (this.boundParentMeterId) {
            if (!this.waterMeterBoundDto) {
                this.messageService.warning("未绑定父表，无需解绑");
                return;
            }
            this.wmConcentratorWaterMeterService.unboundParentMeter(this.boundMeterId).subscribe(() => {
                this.messageService.success("解绑成功");
                this.boundClose();
                this.pagination.refresh();
            });
        } else {
            if (!this.waterMeterBoundDto) {
                this.messageService.warning("请先查询父表后再绑定");
                return;
            }
            this.wmConcentratorWaterMeterService.boundParentMeter(this.boundMeterId, this.waterMeterBoundDto.id).subscribe(() => {
                this.messageService.success("绑定成功");
                this.boundClose();
                this.pagination.refresh();
            });
        }

    }

    boundClose() {
        this.boundParentWindow = false;
        this.parentMeterCode = '';
        this.boundMeterId = null;
        this.waterMeterBoundDto = null;
        this.boundParentMeterId = null;
    }

    viewChildren(id) {
        let modal = this.modalService.create({
            nzTitle: '子表',
            nzContent: WaterMeterChildrenComponent,
            nzMaskClosable: true,
            nzWidth: 1000,
            nzComponentParams: {
                id
            },
            nzCancelText: '关闭',
            nzOkText: null
        })
    }

    showSetRateWindow(id) {
        this.setRateWindow = true;
        this.setRateMeterId = id;
        this.wmWaterMeterService.getMeterRateById(id).subscribe((rate) => {
            this.rate = rate;
        });
    }

    handleSetRate() {
        this.wmWaterMeterService.configMeterRate(this.setRateMeterId, this.rate).subscribe(res => {
            this.messageService.success('设置成功');
            this.setRateWindow = false;
            this.rate = null;
        });
    }

    rateClose() {
        this.setRateWindow = false;
        this.rate = null;
    }

    meterChange(data) {
        let modal = this.modalService.create({
            nzTitle: '更换水表',
            nzContent: ChangeWmConcentratorWaterMeterComponent,
            nzMaskClosable: false,
            nzWidth: '1000px',
            nzFooter: null,
            nzComponentParams: {
                oldData: data
            },
            nzCancelText: '关闭',
            nzOkText: null
        });
        modal.componentInstance.close = (success)=>{
            modal.destroy();
            success && this.pagination.refresh();
        }

    }

    batchChange(){
        const modal = this.modalService.create({
            nzTitle: `批量换表`,
            nzContent: WaterMeterChangeBatchComponent,
            nzMaskClosable: false,
            nzWidth: 450,
            nzComponentParams: {
                meterType: 'CONCENTRATOR'
            },
            nzFooter: null
        });
        modal.componentInstance.close = (success)=>{
            modal.destroy();
            success && this.pagination.refresh();
        }
    }
}
