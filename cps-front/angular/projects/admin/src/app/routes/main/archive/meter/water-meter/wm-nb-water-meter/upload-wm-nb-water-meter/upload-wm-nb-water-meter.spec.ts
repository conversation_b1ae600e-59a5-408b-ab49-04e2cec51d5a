import { ComponentFixture, TestBed } from '@angular/core/testing';

import { UploadWmNbWaterMeter } from './upload-wm-nb-water-meter';

describe('UploadConcentratorComponent', () => {
  let component: UploadWmNbWaterMeter;
  let fixture: ComponentFixture<UploadWmNbWaterMeter>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ UploadWmNbWaterMeter ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(UploadWmNbWaterMeter);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
