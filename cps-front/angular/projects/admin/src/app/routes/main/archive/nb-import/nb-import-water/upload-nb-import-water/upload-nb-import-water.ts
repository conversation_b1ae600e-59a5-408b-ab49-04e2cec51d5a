import { Component, OnInit } from '@angular/core';
import { NzUploadFile, UploadFilter } from 'ng-zorro-antd/upload';
import { NzMessageService } from 'ng-zorro-antd/message';
import { WmMeterModel } from '@lib';
import { Observable, Observer } from 'rxjs';
import { WmNbWaterMeterService } from '../../../../../../service/archive/wm-nb-water-meter.service';
import { ExcelDownloadService } from '../../../../../../service/common/excel-download.service';
import { AuthService } from '../../../../../../auth/auth.service';
import { NbDevicePreImportService } from '../../../../../../service/archive/nb-device-pre-import.service';
import {CompanyService} from "../../../../../../service/sys/company.service";

@Component({
    selector: 'app-upload-concentrator',
    templateUrl: './upload-nb-import-water.html',
    styleUrls: ['./upload-nb-import-water.scss']
})
export class UploadNbImportWater implements OnInit {

    loading = false;
    fileList: NzUploadFile[] = [];
    meterModels: WmMeterModel[] = [];
    meterModel: WmMeterModel;
    treeData: any[] = [];
    node: null;
    bluePrepay: boolean = false;

    coursewareTypeFilters: any[] = [
        {
            key: 'XLS',
            filters: ['xls', 'xlsx', '']
        }
    ];

    filters: UploadFilter[] = [
        {
            name: 'type',
            fn: (fileList: NzUploadFile[]) => {
                const filterFiles = fileList.filter(w => {
                    const fr = this.coursewareTypeFilters[0].filters.filter(r => w.type.indexOf(r) >= 0);
                    return fr && fr.length > 0;
                });
                if (filterFiles.length !== fileList.length) {
                    this.messageService.error('包含文件格式不正确，只支持xls和xlsx格式');
                    return filterFiles;
                }
                return fileList;
            }
        },
        {
            name: 'async',
            fn: (fileList: NzUploadFile[]) => {
                return new Observable((observer: Observer<NzUploadFile[]>) => {
                    observer.next(fileList);
                    observer.complete();
                });
            }
        }
    ];
    beforeUpload = (file: NzUploadFile): boolean => {
        this.fileList = this.fileList.concat(file);
        return false;
    };

    constructor(public messageService: NzMessageService, public nbWaterMeterService: WmNbWaterMeterService,
                public excelDownloadService: ExcelDownloadService, public authService: AuthService,
                public nbDevicePreImportService: NbDevicePreImportService, private companyService: CompanyService) {
    }

    ngOnInit(): void {
        this.nbWaterMeterService.getNbWaterMeterModels().subscribe(result => {
            this.meterModels = result;
        });
        this.companyService.companyTree().subscribe(companies => {
            if (companies && companies.length > 0) {
                this.treeData.push(...companies);
            }
        });
    }

    submit(callback) {
        if (!this.fileList || this.fileList.length <= 0) {
            this.messageService.create('error', '请先上传文件！');
            return;
        }
        if (!this.meterModel) {
            this.messageService.create('error', '请选择水表型号！');
            return;
        }
        if (this.authService.isPlatformUser()) {
            if (!this.node) {
                this.messageService.create('error', '请选择公司！');
                return;
            }
        }
        this.loading = true;
        this.nbDevicePreImportService.uploadNbWaterMeter(this.fileList[0], this.node, this.meterModel.id, this.bluePrepay, (value) => {
            this.loading = false;
            callback();
        }, error => {
            this.loading = false;
            this.messageService.create('error', '导入失败，请检查文件内容！');
        });
    }

    downloadExcelTemplate(name: string) {
        this.excelDownloadService.downloadExcelTemplate(name);
    }

    treeSelectDisplayWith() {
        let fn = (node, full = '') => {
            if (full) {
                full = node.origin.title + '/' + full;
            } else {
                full = node.origin.title;
            }
            if (node.parentNode) {
                return fn(node.parentNode, full);
            }
            return full;
        };
        return fn;
    }
}
