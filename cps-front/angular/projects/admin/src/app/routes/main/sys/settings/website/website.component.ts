import { Component, Inject, OnInit } from '@angular/core';
import { FormComponent } from "@core";
import { NzMessageService } from "ng-zorro-antd/message";
import { Router } from "@angular/router";
import { Validators } from "@angular/forms";
import { WebsiteParams } from "@lib";
import { WebsiteService } from "../../../../../service/common/website.service";
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { AuthService } from '../../../../../auth/auth.service';

@Component({
    selector: 'app-website',
    templateUrl: './website.component.html',
    styleUrls: ['./website.component.scss']
})
export class WebsiteComponent extends FormComponent implements OnInit {

    title: string = '网站设置';
    spinning = false;

    loading: boolean = false;
    avatarUrl: string = '';
    oldImg: boolean = false;
    fileList: NzUploadFile[] = [];

    constructor(@Inject('apiUrl') public apiUrl, public messageService: NzMessageService,
                public router: Router, public webSiteService: WebsiteService,
                public authService: AuthService, @Inject('dataUrl') public dataUrl) {
        super();
    }

    beforeUpload =(file: NzUploadFile, _fileList: NzUploadFile[]) =>{
        this.loading = true;
        if(!file.type.startsWith('image')){
            this.loading = false;
            this.messageService.warning('请上传正确的图片格式!');
            return false;
        }

        const isLt2M = file.size! / 1024 / 1024 < 2;
        if (!isLt2M) {
            this.loading = false;
            this.messageService.warning('图标最大2MB!');
            return false;
        }

        this.fileList = [file];
        //@ts-ignore
        this.getBase64( file, (img: string) => {
            this.loading = false;
            this.avatarUrl = img;
        });
        return false;
    }

    handleRemove(event){
        this.avatarUrl ='';
        this.fileList = [];
        //阻止冒泡
        event.stopPropagation();
        return false
    }

    getBase64(img: File, callback: (img: string) => void): void {
        const reader = new FileReader();
        reader.addEventListener('load', () => callback(reader.result!.toString()));
        reader.readAsDataURL(img);
    }

    ngOnInit() {
        this.formGroup = this.buildForm({
            sysName: [null, [Validators.required, Validators.maxLength(32)], this.field({
                label: '网站标题', maxlength: 32, lineMode: 'single'
            })],
            servicerName: [null, [Validators.required, Validators.maxLength(32)], this.field({
                label: '公司名称', maxlength: 32, lineMode: 'single'
            })],
            copyright: [null, [Validators.required, Validators.maxLength(255)], this.field({
                label: '版权信息', maxlength: 255, lineMode: 'single', unspaced: false
            })],
            keywords: [null, [Validators.required, Validators.maxLength(255)], this.field({
                label: 'SEO关键字', maxlength: 255, lineMode: 'single', unspaced: false
            })],
            description: [null, [Validators.required, Validators.maxLength(255)], this.field({
                label: '网站描述', maxlength: 255, lineMode: 'single', unspaced: false
            })],
            author: [null, [Validators.required, Validators.maxLength(64)], this.field({
                label: '网站作者', maxlength: 64, lineMode: 'single'
            })]
        });
        if (this.authService.isCompanyUser()) {
            this.formFields.find(i => i.fieldName === 'servicerName').category = 'other';
            this.formGroup.controls['servicerName'].setValidators(null);
            this.formFields.find(i => i.fieldName === 'copyright').category = 'other';
            this.formGroup.controls['copyright'].setValidators(null);
            this.formFields.find(i => i.fieldName === 'keywords').category = 'other';
            this.formGroup.controls['keywords'].setValidators(null);
            this.formFields.find(i => i.fieldName === 'description').category = 'other';
            this.formGroup.controls['description'].setValidators(null);
            this.formFields.find(i => i.fieldName === 'author').category = 'other';
            this.formGroup.controls['author'].setValidators(null);
        }
        this.initFormData();
    }

    /**
     * 表单赋值
     */
    initFormData(): void {
        this.webSiteService.getWebsiteParams().subscribe((data) => {
            this.formGroup.patchValue(data);
            if (this.authService.isCompanyUser() && data.logoImg) {
                this.avatarUrl = this.dataUrl + data.logoImg;
                this.oldImg = true;
            }
        });
    }

    /**
     * 表单提交
     * @param formValue 表单数据
     */
    onValidSuccess(formValue: any): any {
        if (!this.submitted) {
            this.submitted = true;
            this.spinning = true;
            if (this.authService.isPlatformUser()) {
                const websiteParams: WebsiteParams = formValue;
                this.webSiteService.updateWebsiteParams(websiteParams).subscribe({
                    next: () => {
                        this.submitted = false;
                        this.spinning = false;
                        this.messageService.create('success', '保存成功，刷新页面后生效');
                    },
                    error: () => {
                        this.submitted = false;
                        this.spinning = false;
                    }
                });
            } else {
                if (!this.oldImg && (!this.fileList || this.fileList.length <= 0)) {
                    this.messageService.create('error', '请先上传文件！');
                    return;
                }
                let sysName = this.formGroup.value.sysName;
                this.webSiteService.uploadWebsiteParams(this.fileList[0], sysName, (value) => {
                    this.submitted = false;
                    this.spinning = false;
                    this.messageService.create('success', '保存成功，刷新页面后生效');
                }, error => {
                    this.submitted = false;
                    this.spinning = false;
                    this.messageService.create('error', '修改失败');
                });
            }
        }
    }
}
