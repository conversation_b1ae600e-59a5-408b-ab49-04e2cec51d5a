import {Component, Inject, OnInit} from '@angular/core';
import {Validators} from "@angular/forms";
import {NzMessageService} from "ng-zorro-antd/message";
import {ActivatedRoute, Params, Router} from "@angular/router";
import {User} from '@lib';
import {UserService} from '../../../../../../service/sys/user.service';
import {FormComponent} from '@core';
import {AuthService} from '../../../../../../auth/auth.service';
import {RoleService} from '../../../../../../service/sys/role.service';
import {RegionService} from '../../../../../../service/sys/region.service';
import {LoginService} from '../../../../../../service/common/login.service';
import {GenderEnums} from '../../../../../../components/EnumOptions';

@Component({
    selector: 'app-edit-user',
    templateUrl: './edit-user.component.html',
    styleUrls: ['./edit-user.component.scss']
})
export class EditUserComponent extends FormComponent implements OnInit {

    title: string = '编辑后台管理员';
    id: number;
    userType;
    user: User;

    constructor(public messageService: NzMessageService, @Inject('apiUrl') public apiUrl,
                public userService: UserService, public router: Router,
                public authService: AuthService, private loginService: LoginService,
                public roleService: RoleService, public regionService: RegionService,
                public activatedRoute: ActivatedRoute) {
        super();
        this.activatedRoute.queryParams.subscribe((params: Params) => {
            this.id = params['id'];
        });
    }

    ngOnInit(): void {
        const authentication: any = this.authService.getAuthentication();
        this.userType = authentication.userType;
        this.formGroup = this.buildForm({
            id: [this.id],
            name: [{value: null, disabled: true}, [Validators.required, Validators.maxLength(64)], this.field({
                label: '账号', maxlength: 64, lineMode: 'whole'
            })],
            roles: [null, [Validators.required], this.field({
                label: '用户角色', type: 'select', selectModeType: 'multiple', optionItems: []
            })],
            rolesText: [{value: '超级管理员角色', disabled: true}, [Validators.required], this.field({
                label: '用户角色', category: 'other'
            })],
            realName: [null, [Validators.maxLength(32)], this.field({
                label: '真实姓名', maxlength: 32
            })],
            mobile: [null, [Validators.pattern('^1[3-9]\\d{9}$')], this.field({
                label: '手机号码', maxlength: 11
            })],
            email: [null, [Validators.maxLength(1024), Validators.email], this.field({
                label: 'E-mail', maxlength: 1024
            })],
            gender: [null, [Validators.required], this.field({
                label: '性别', type: 'radio', optionItems: GenderEnums
            })],
            regions: [[], [], this.field({
                label: '管辖区域', type: 'select', selectModeType: 'multiple', optionItems: [], lineMode: 'whole',
                category: this.userType === 'COMPANY' ? 'default' : 'other'
            })],
            remark: [null, [Validators.maxLength(255)], this.field({
                label: '备注',
                type: 'textarea',
                lineMode: 'single',
                maxlength: 255,
                autoSize: {minRows: 2, maxRows: 3}
            })],
        });
        this.initFormData();
    }

    /**
     * 表单赋值
     */
    initFormData(): void {
        this.userService.getUserById(this.id).subscribe((data) => {
            this.user = data;
            this.formGroup.patchValue({
                name: this.user.name,
                realName: this.user.realName,
                mobile: this.user.mobile,
                email: this.user.email,
                gender: this.user.gender,
                remark: this.user.remark
            });
            this.initOtherData();
        });
    }

    /**
     * 加载其它数据
     */
    initOtherData() {
        // 回显管辖区域
        if (this.user.userType === 'COMPANY' && !this.user.companySuperUser) {
            // 只有非超级管理员的公司，才需要查询区域进行回显，否则直接禁用
            this.regionService.getRegions().subscribe((regions: any[]) => {
                if (regions && regions.length > 0) {
                    let items = this.setFieldOptionItems('regions', regions, 'name');
                    if (this.user.regions && this.user.regions.length > 0) {
                        const regionIds = this.user.regions.map(region => region.id);
                        let item = items.filter(i => regionIds.includes(i.value.id));
                        this.formGroup.patchValue({regions: item.map(i => i.value)});
                    }
                }
            });
        } else {
            const regionFiled = this.formFields.find(item => item.fieldName === 'regions');
            regionFiled.disabled = true;
        }
        // 回显角色
        this.roleService.roles().subscribe((roleArr: any[]) => {
            if (roleArr && roleArr.length > 0) {
                let items = this.setFieldOptionItems('roles', roleArr, 'name');
                if (this.user.roles && this.user.roles.length > 0) {
                    const roleIds = this.user.roles.map(role => role.id);
                    let item = items.filter(i => roleIds.includes(i.value.id));
                    this.formGroup.patchValue({roles: item.map(i => i.value)});
                }
            }
            // 如果是超级管理员，则不允许更改角色字段
            if (this.user.companySuperUser) {
                const roleFiled = this.formFields.find(item => item.fieldName === 'roles');
                roleFiled.category = 'other';
                // roles()方法未返回超级管理员角色，所以此处要将必填条件去掉
                this.updateValidators(this.formGroup, 'roles', []);
                const roleTextFiled = this.formFields.find(item => item.fieldName === 'rolesText');
                roleTextFiled.category = 'default';
            }
        });
    }

    onValidSuccess(formValue: any): any {
        if (!this.submitted) {
            this.submitted = true;
            const user: User = formValue;
            this.userService.updateUser(user).subscribe({
                next: () => {
                    this.messageService.create('success', '保存成功');
                    this.submitted = false;
                    this.goBack();
                },
                error: () => {
                    this.submitted = false;
                }
            });
        }
    }

    /**
     * 返回上个视图
     */
    goBack() {
        let curPath = this.router.url.split('?')[0];
        let pathArr = curPath.split('/');
        let parentPath = pathArr.splice(0, pathArr.length - 1).join('/');
        this.router.navigateByUrl(parentPath,{ replaceUrl: true })
    }
}
