import {Component, Inject, OnInit} from '@angular/core';
import {AbstractPageComponent} from "../../../../../components/AbstractPageComponent";
import {HttpClient} from "@angular/common/http";
import {NzMessageService} from "ng-zorro-antd/message";
import {NzModalService} from "ng-zorro-antd/modal";
import {NzDrawerService} from "ng-zorro-antd/drawer";
import {Router} from "@angular/router";
import {Operator, Order} from "@core";
import {Observable} from "rxjs";
import {BusinessUtils} from "../../../../../utils/BusinessUtils";
import {ViewAdapayConfigComponent} from "./view-adapay-config/view-adapay-config.component";
import {AdapayConfigService} from "../../../../../service/revenue/adapay-config.service";
import {AdapayConfig} from "@lib";

@Component({
    selector: 'app-adapay-config',
    templateUrl: './adapay-config.component.html',
    styleUrls: ['./adapay-config.component.scss']
})
export class AdapayConfigComponent extends AbstractPageComponent<AdapayConfig> implements OnInit {

    constructor(http: HttpClient, @Inject('apiUrl') apiUrl: any,
                messageService: NzMessageService, modalService: NzModalService,
                public drawerService: NzDrawerService, public adapayConfigService: AdapayConfigService,
                public router: Router) {
        super(http, apiUrl + 'auth/revenue/channel/adapayConfig/page', messageService, modalService);
    }

    ngOnInit() {
        this.pagination.queryForm = this.buildQueryForm({
            companyId: [null, this.filter({
                label: '公司ID',
                field: 'companyId',
                maxlength: 30,
                operator: Operator.eq
            })]
        });
        this.pagination.setDefaultSort('createdDate', Order.desc);
        this.pagination.reload();
    }

    /**
     * 单个删除
     * @param id
     */
    override removeData(id: any): Observable<void> {
        return this.adapayConfigService.removeAdapayConfigById(id);
    }

    /**
     * 批量删除
     * @param ids
     */
    override batchRemoveData(ids: any[]): Observable<void> {
        return this.adapayConfigService.batchRemoveAdapayConfigByIds(ids);
    }

    toAdd() {
        this.router.navigate(['/main/revenue/channel/adapayConfig/create']);
    }

    /**
     * 汇付天下支付配置详情
     * @param adapayConfig
     */
    viewDetail(adapayConfig: AdapayConfig) {
        const drawerRef = this.drawerService.create<ViewAdapayConfigComponent, { adapayConfig: AdapayConfig }, string>({
            nzTitle: '汇付天下支付配置详情',
            nzWidth: 850,
            nzContent: ViewAdapayConfigComponent,
            nzContentParams: {
                adapayConfig: adapayConfig
            }
        });
        drawerRef.afterOpen.subscribe(() => {
        });
        drawerRef.afterClose.subscribe(() => {
        });
    }

    /**
     * 查询数据前处理特殊逻辑
     */
    override beforeDataLoad() {
        // @ts-ignore
        BusinessUtils.handleCreatedDateRangeCondition(this.pagination, 'dateRange');
    }
}
