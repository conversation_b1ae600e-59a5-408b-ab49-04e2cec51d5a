<ng-template #titleRef>
    <app-page-header [title]="title" ></app-page-header>
</ng-template>

<nz-spin [nzSpinning]="loading" [nzSize]="'large'" [nzTip]="''">
    <nz-card [nzTitle]="titleRef" class="full-card head-sticky">
        <nz-form-item>
            <nz-form-label nzRequired>目标小区</nz-form-label>
            <nz-form-control>
                <nz-tree-select #treeSelect
                                style="width: auto; min-width: 350px"
                                [nzAllowClear]="false"
                                [nzPlaceHolder]="'请选择小区'" [nzShowSearch]="true" [nzHideUnMatched]="true"
                                [(ngModel)]="community"
                                [nzDropdownMatchSelectWidth]="false"
                                [nzDisplayWith]="treeSelectDisplayWith()"
                                [nzDropdownStyle]="{ 'max-height': '300px', 'min-width':'100%' }"
                                [nzNodes]="treeData"
                                [nzAsyncData]="true"
                                (nzExpandChange)="expendChange($event)"
                                (ngModelChange)="communityChange($event, treeSelect)"
                                [nzTreeTemplate]="nzTreeTemplate">
                    <ng-template #nzTreeTemplate let-node let-origin="origin">
                        <div class="node-wrap">
                            <i *ngIf="origin.type === 'community'" nz-icon [nzType]="'customer:archiveCommunity'"
                               class="text-processing"></i>
                            <i *ngIf="origin.type === 'company'" nz-icon [nzType]="'customer:company'"
                               class="text-magenta"></i>
                            <div class="name">{{ node.title }}</div>
                        </div>
                    </ng-template>
                </nz-tree-select>
            </nz-form-control>
        </nz-form-item>
        <nz-divider nzText="待更换小区的水表"></nz-divider>
        <nz-table #basicTable nzBordered
                  nzFrontPagination="false" nzSize="small"
                  [nzScroll]="{ x: '100%',y:'calc(100vh - 335px)' }"
                  [nzLoading]="loading"
                  [nzData]="meters">
            <thead >
            <tr>
                <th nzLeft [nzWidth]="'150px'" nzAlign="center" >水表编号
                </th>
                <th [nzWidth]="'150px'" nzAlign="center" >小区楼栋
                </th>
                <th [nzWidth]="'150px'" nzAlign="center" >用水类型
                </th>
                <th [nzWidth]="'180px'" nzAlign="center" >水表型号
                </th>
                <th [nzWidth]="'100px'" nzAlign="center">是否有阀
                </th>
                <th [nzWidth]="'150px'" nzAlign="center" >公司名称
                </th>
                <th [nzWidth]="'180px'" nzAlign="center">创建时间
                </th>
                <th nzAlign="center" nzRight nzWidth="90px">操作</th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let data of basicTable['data'];let index = index">
                <td nzLeft nzAlign="center" >
                    {{ data?.code }}
                </td>
                <td nzEllipsis
                    [nz-tooltip]="data?.community?.name + (data.buildingUnitNames ? '/' + data.buildingUnitNames : '')"
                    nzAlign="center">{{ data?.community?.name }}{{ data.buildingUnitNames ? '/' + data.buildingUnitNames : '' }}
                </td>
                <td nzEllipsis [nz-tooltip]="data?.waterType"
                    nzAlign="center">{{ "WaterType_" + data?.waterType | translate}}
                </td>
                <td nzEllipsis [nz-tooltip]="data?.model?.name" nzAlign="center">{{ data?.model?.name }}</td>
                <td nzAlign="center">{{ data?.valve ? '有阀' : '无阀' }}</td>
                <td nzEllipsis [nz-tooltip]="data?.companyId" nzAlign="center">{{ data?.companyId }}</td>
                <td nzAlign="center">{{ data?.createdDate }}</td>
                <td nzAlign="center" nzRight>
                    <a (click)="remove(data,index)" class="text-danger">移除</a>
                </td>
            </tr>
            </tbody>
        </nz-table>
        <ng-container *aclIf="'archive-meter-water-changeCommunity'">
            <button *aclIf="'archive-meter-water-changeCommunity'" (click)="batchChangeCommunity()" style="margin-left: 1px;" nz-button nzType="primary">确定</button>
            <button nz-button type="button" nzType="default" (click)="goBack()">
                <span nz-icon nzType="rollback" nzTheme="outline"></span>
                返回
            </button>
        </ng-container>
    </nz-card>
</nz-spin>
