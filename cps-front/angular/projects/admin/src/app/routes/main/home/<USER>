
.wrap {
  width: calc(100% + 10px);
  //height: 100vh;
  //min-height: 680px;
  min-width: 1300px;
  background: #f0f2f5;
  overflow: auto;
  display: flex;
  flex-direction: column;
  margin: -5px;

  .count {
    color: #2A82E4;
  }

  .unit {
    color: #808080;
    font-size: 0.6em;
    margin-left: 2px;
  }


  .row {
    display: flex;

    &.row1, &.row2 {
      height: 126px;
    }

    &.row1 {
      .col1 {
        width: 18%;

        .card {
          padding: 5px 10px;
          display: flex;
          flex-direction: column;
          background: linear-gradient(90deg, rgba(250, 252, 255, 1) 0%, rgba(78, 207, 198, 0.34) 100%);

          .title {
            color: #383838;
            font-size: 12px;
          }

          .content {
            display: flex;
            align-items: center;
            flex: 1;
            flex-wrap: wrap;

            .item {
              text-align: center;
              width: 50%;

              .count {
                font-size: 18px;
                line-height: 1.4;
              }

              .text {
                font-size: 12px;
                color: #808080;
                line-height: 1;
              }


            }
          }
        }
      }

      .col2, .col3 {
        width: 18%;

        .card {
          padding: 5px 10px;
          display: flex;
          flex-direction: column;
          background: linear-gradient(0deg, rgba(244, 237, 255, 0.64) 0%, rgba(197, 219, 252, 0.64) 70%, rgba(186, 214, 252, 0.64) 100%);

          .total-wrap {
            flex: 1;
            display: flex;
            align-items: center;

            .total {
              flex: 1;
              width: 50%;
              height: 100%;

              .title {
                font-size: 12px;
              }

              .count {
                font-size: 20px;
                text-align: center;
                height: calc(100% - 24px);
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }

            .icon {
              margin-right: 10px;
              width: 36px;
              height: 36px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #1c92ef;
              color: #ffffff;
              font-size: 28px;
              line-height: 1;
              border-radius: 4px;
            }
          }

          .item-wrap {
            display: flex;
            flex-wrap: wrap;

            .item:nth-child(2n) {
              border-left: #ffffff 1px solid;
            }
          }

          .item {
            width: 50%;
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: center;
            border-top: #ffffff 1px solid;
            font-size: 13px;
            line-height: 24px;

            .label {
              font-size: 12px;
              color: #383838;
            }

            .unit {
              font-size: 0.8em;
            }
          }
        }
      }

      .col3 {
        .card {
          background: linear-gradient(0deg, rgba(253, 250, 238, 0.64) 0%, rgba(255, 242, 195, 0.64) 70%, rgba(253, 235, 178, 0.64) 100%) !important;

        }

        .icon {
          background: #fca730 !important;
        }
      }

      .col4 {
        flex: 1;

        .card {
          display: flex;
          justify-content: space-around;
          align-items: center;

          .icon {
            font-size: 40px;
            color: #1cd30e;
          }

          .amount {
            text-align: center;
          }

          .val {
            font-size: 26px;
          }
        }
      }
    }

    &.row2 {
      .col1, .col2 {
        width: 20%;

        .card {
          position: relative;
        }

        .title {
          position: absolute;
          color: #383838;
          font-size: 12px;
        }
      }

      .col3 {
        width: 34%;

        .card {
          display: flex;

          .title {
            display: flex;
            justify-content: center;
            align-items: center;
            border-right: 1px solid #eeeeee;
            padding: 0 10px 0 5px;
          }

          .value-wrap {
            padding-right: 0;
            flex: 0 !important;
            font-size: 13px !important;
            transform: translateY(2px);

            .percent {
              margin-right: 8px;
              color: #1c92ef;
            }

            .count {
              color: #1c92ef;
            }

            .total {
              color: #666666;
            }
          }

          .content, .value-wrap {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-around;

            .item {
              display: flex;
              align-items: center;

              nz-progress {
                flex: 1;
                margin-right: -26px;
              }

              .label {
                font-size: 12px;
                line-height: 1;
                transform: translateY(1px);
                margin: 0 8px;
                width: 3.5em;
                text-align: right;
              }
            }
          }
        }
      }

      .col4 {
        flex: 1;

        .card {
          display: flex;
          justify-content: space-around;
          align-items: center;

          .icon {
            font-size: 40px;
            color: #ed4e14;
          }

          .amount {
            text-align: center;
          }

          .val {
            font-size: 26px;
          }
        }
      }
    }

    &.row3, &.row4 {
      .card {

        display: flex;
        flex-direction: column;

        .header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #eeeeee;
          height: 30px;
          flex-shrink: 0;

          .title {
            font-size: 13px;
            color: #383838;
          }
        }

        .body {
          flex: 1;
        }


      }

      .col1 {
        width: 360px;

        .body {
          display: flex;
          flex-wrap: wrap;
        }
      }

      .col2 {
        width: 40%;
        flex: 1;
      }

      .col3 {
        width: 520px;
      }

    }

    &.row3 {
      height: 250px;

      .col1 {
        .body > div {
          display: flex;
          justify-content: space-around;
          align-items: center;
          padding: 15px 10px 5px 10px;
          height: 50%;
          width: 100%;
          box-sizing: border-box;
        }

        .item {
          text-align: center;
          width: 23%;
          cursor: pointer;

          .icon {
            margin: 0 auto;
            border-radius: 8px;
            width: 50px;
            height: 50px;
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 34px;
            color: #ffffff;
            position: relative;

            .lock {
              position: absolute;
              top: -5px;
              right: -5px;
              font-size: 20px;
              color: #ed4e14;
            }

            &.icon1 {
              background: rgba(250, 155, 45, 0.93);
            }

            &.icon2 {
              background: rgb(226, 85, 248);
            }

            &.icon3 {
              background: rgb(106, 148, 253);
            }

            &.icon4 {
              background: rgba(160, 75, 241, 0.92);
            }

            &.icon5 {
              background: rgb(25, 221, 19);
            }

            &.icon6 {
              background: rgb(250, 177, 38);
            }

            &.icon7 {
              background: rgb(52, 170, 243);
            }

            &.icon8 {
              background: rgba(253, 103, 49, 0.95);
            }

            &:hover {
              transform: scale(1.1);
              box-shadow: 0 0 5px 0 #cccccc;
            }
          }

          .text {
            font-size: 13px;
            color: #555555;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }

      .col3 {
        .body {
          padding-right: 5px;
        }

        .rate-box {
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 12px;
          line-height: 18px;

          .split {
            color: #efefef;
            margin: 0 2px;
          }

          .total {
            color: #666666;
          }

          .fail {
            background: #FF5733;
            color: #ffffff;
            border-radius: 9px;
            padding: 0 5px;
            text-align: center;
            min-width: 30px;
            cursor: pointer;

            &.nofail {
              background: #ddd;
              cursor: unset;
            }

            :hover {
              text-decoration: underline;
            }
          }
        }
      }

    }

    &.row4 {
      min-height: calc(100vh - 615px);

      .col {
        width: 50%;

        .body {
          display: flex;
          flex-direction: column;
          padding-bottom: 10px;

          .sum {
            height: 60px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            font-size: 13px;

            .icon {
              font-size: 16px;
            }

            .val {
              color: #1c92ef;
              font-size: 18px;
            }
          }

          .calendar {
            height: 100%;
            flex: 1;
            display: flex;
            flex-direction: column;

            .months {
              & > div {
                width: 25%;

              }
            }

            .week {
              height: 20px;
              display: flex;
              text-align: center;
              font-size: 12px;
            }

            .months, .days {
              height: 100%;
              flex: 1;
              display: flex;
              flex-wrap: wrap;
              font-size: 14px;
              color: #aaaaaa;

              & > div {
                box-sizing: border-box;
                padding: 0 3px;

                .inner-box {
                  text-align: right;
                  height: 100%;
                  border-top: 1px solid #efefef;
                  display: flex;
                  flex-direction: column;

                  .day {
                    line-height: 18px;
                    font-size: 12px;
                    color: #666666;
                  }

                  .content {
                    flex: 1;
                    color: #1c92ef;
                    margin-top: -3px;
                  }
                }
              }
            }

            .week, .days {
              & > div {
                width: calc(100% / 7);

              }
            }
          }
        }
      }
    }

    .col {
      padding: 6px;
      box-sizing: border-box;
      background-clip: content-box;

      .card {
        height: 100%;
        width: 100%;
        box-sizing: border-box;
        border-radius: 5px;
        padding: 5px 10px;
        overflow: hidden;
        background: #ffffff;
      }


    }

  }

}

.home-table {
  th, td {
    padding: 2px 5px !important;
    text-align: center;
  }
}

.radio-box {
  border: 1px solid #1c92ef;
  border-radius: 5px;
  font-size: 13px;
  line-height: 20px;

  div {
    padding: 0 5px;
    min-width: 30px;
    text-align: center;
    display: inline-block;
    color: #1c92ef;
    cursor: pointer;
    transition: all 0.3s;

    &:first-child {
      border-right: 1px solid #1c92ef;
    }

    &.active {
      background: #1c92ef;
      color: #ffffff;
    }
  }
}



