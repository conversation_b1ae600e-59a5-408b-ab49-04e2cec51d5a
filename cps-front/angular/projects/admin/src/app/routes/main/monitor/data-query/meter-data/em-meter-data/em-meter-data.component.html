<nz-spin [nzTip]="tip" [nzSpinning]="spinning" style="height: 100%;">
	<!--页头查询部分-->
	<app-query-card [tab]="true" [pagination]="pagination" [customReset]="true" (customResetOn)="resetOn()"></app-query-card>
	<nz-card class="table-card">
		<!--表格内容部分-->
		<nz-table #basicTable nzBordered nzShowPagination nzShowSizeChanger
		          nzFrontPagination="false" nzSize="small"
		          [nzScroll]="{ x: '900px',y:'calc(100vh - 350px)' }"
		          [nzLoading]="pagination.loading"
		          [nzData]="pagination.content"
		          [nzTotal]="pagination.totalElements"
		          [nzShowTotal]="totalTemplate"
		          [(nzPageIndex)]="pagination.pageIndex"
		          [(nzPageSize)]="pagination.pageSize" [nzPageSizeOptions]="[20,50,100,200]"
		          (nzPageIndexChange)="pagination.refresh()"
		          (nzPageSizeChange)="pagination.refresh()">
			<ng-template #totalTemplate let-total let-range="range">
				显示第 {{range[0]}} 到第 {{range[1]}} 条记录，总共 {{total}} 条记录
			</ng-template>
			<thead nzSingleSort>
			<tr>
				<th [nzWidth]="'150px'" nzAlign="center" nzLeft nzShowSort (nzSortOrderChange)="pagination.setSort('meterCode', $event)">表号</th>
				<th [nzWidth]="'150px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('roomNumber', $event)">房号</th>
				<th [nzWidth]="'120px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('customerName', $event)">住户名称</th>
				<th [nzWidth]="'180px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('communityName', $event)">小区名称</th>
				<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
				    (nzSortOrderChange)="pagination.setSort('cmdType', $event)">抄读方式
				</th>
				<th *ngIf="cmdType === 'DEVICE_REPORT'" [nzWidth]="'120px'" nzAlign="center" nzShowSort
				    (nzSortOrderChange)="pagination.setSort('commandFn', $event)">上报命令字
				</th>
				<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
				    (nzSortOrderChange)="pagination.setSort('meteringTime', $event)">上报时间
				</th>
				<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
				    (nzSortOrderChange)="pagination.setSort('meterTime', $event)">电表时间
				</th>
				<ng-container *ngIf="cmdType === 'DEVICE_REPORT'">
                    <ng-container *ngIf="commandFn !== 'H0C-FN129' && commandFn !== 'H0C-FN131'">
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('switchStatus', $event)">电闸状态
					</th>
					<th [nzWidth]="'140px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('lastDayFrozenDate', $event)">日冻结时标
					</th>
                    </ng-container>
					<ng-container *ngIf="!commandFn || commandFn === 'H10-FN254' || commandFn === 'H0C-FN33'">
						<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('value', $event)">正向有功示值
						</th>
						<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('value1', $event)">正向有功(尖)
						</th>
						<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('value2', $event)">正向有功(峰)
						</th>
						<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('value3', $event)">正向有功(平)
						</th>
						<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('value4', $event)">正向有功(谷)
						</th>
					</ng-container>
					<ng-container *ngIf="!commandFn || commandFn === 'H0C-FN33'">
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentForwardReactiveValue', $event)">当前正向无功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentForwardReactiveValue1', $event)">当前费率1正向无功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentForwardReactiveValue2', $event)">当前费率2正向无功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentForwardReactiveValue3', $event)">当前费率3正向无功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentForwardReactiveValue4', $event)">当前费率4正向无功总电能示值
						</th>
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant1ReactiveValue', $event)">当前一象限无功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant1ReactiveValue1', $event)">当前一象限费率1无功电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant1ReactiveValue2', $event)">当前一象限费率2无功电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant1ReactiveValue3', $event)">当前一象限费率3无功电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant1ReactiveValue4', $event)">当前一象限费率4无功电能示值
						</th>
						
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant4ReactiveValue', $event)">当前四象限无功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant4ReactiveValue1', $event)">当前四象限费率1无功电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant4ReactiveValue2', $event)">当前四象限费率2无功电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant4ReactiveValue3', $event)">当前四象限费率3无功电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant4ReactiveValue4', $event)">当前四象限费率4无功电能示值
						</th>
					</ng-container>
					<ng-container *ngIf="!commandFn || commandFn === 'H0C-FN34'">
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentReverseActiveValue', $event)">当前反向有功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentReverseActiveValue1', $event)">当前费率1反向有功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentReverseActiveValue2', $event)">当前费率2反向有功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentReverseActiveValue3', $event)">当前费率3反向有功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentReverseActiveValue4', $event)">当前费率4反向有功总电能示值
						</th>
						
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentReverseReactiveValue', $event)">当前反向无功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentReverseReactiveValue1', $event)">当前费率1反向无功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentReverseReactiveValue2', $event)">当前费率2反向无功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentReverseReactiveValue3', $event)">当前费率3反向无功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentReverseReactiveValue4', $event)">当前费率4反向无功总电能示值
						</th>
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant2ReactiveValue', $event)">当前二象限无功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant2ReactiveValue1', $event)">当前二象限费率1无功电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant2ReactiveValue2', $event)">当前二象限费率2无功电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant2ReactiveValue3', $event)">当前二象限费率3无功电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant2ReactiveValue4', $event)">当前二象限费率4无功电能示值
						</th>
						
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant3ReactiveValue', $event)">当前三象限无功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant3ReactiveValue1', $event)">当前三象限费率1无功电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant3ReactiveValue2', $event)">当前三象限费率2无功电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant3ReactiveValue3', $event)">当前三象限费率3无功电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentQuadrant3ReactiveValue4', $event)">当前三象限费率4无功电能示值
						</th>
					</ng-container>
					<ng-container *ngIf="!commandFn || commandFn === 'H10-FN254' || commandFn === 'H10-FN253'">
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('lastForwardPower', $event)">日冻结正向有功总电能
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('lastForwardPower1', $event)">日冻结正向有功电能(尖)
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('lastForwardPower2', $event)">日冻结正向有功电能(峰)
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('lastForwardPower3', $event)">日冻结正向有功电能(平)
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('lastForwardPower4', $event)">日冻结正向有功电能(谷)
						</th>
					</ng-container>
					<ng-container *ngIf="!commandFn || commandFn === 'H10-FN254'">
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('lastFrozenTime', $event)">整点冻结时间
						</th>
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('lastWholeForwardPower', $event)">整点冻结总电能(正)
						</th>
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('lastWholeReversePower', $event)">整点冻结总电能(反)
						</th>
						
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('lastReversePower', $event)">日冻结反向有功总电能
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('lastReversePower1', $event)">日冻结反向有功电能(尖)
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('lastReversePower2', $event)">日冻结反向有功电能(峰)
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('lastReversePower3', $event)">日冻结反向有功电能(平)
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('lastReversePower4', $event)">日冻结反向有功电能(谷)
						</th>
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('lastActivePower', $event)">日冻结总有功功率
						</th>
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('lastActivePowerA', $event)">日冻结A相有功功率
						</th>
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('lastActivePowerB', $event)">日冻结B相有功功率
						</th>
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('lastActivePowerC', $event)">日冻结C相有功功率
						</th>
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('lastReactivePower', $event)">日冻结总无功功率
						</th>
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('lastReactivePowerA', $event)">日冻结A相无功功率
						</th>
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('lastReactivePowerB', $event)">日冻结B相无功功率
						</th>
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('lastReactivePowerC', $event)">日冻结C相无功功率
						<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('voltageA', $event)">A相电压
						</th>
						<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentA', $event)">A相电流
						</th>
						<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('voltageB', $event)">B相电压
						</th>
						<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentB', $event)">B相电流
						</th>
						<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('voltageC', $event)">C相电压
						</th>
						<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('currentC', $event)">C相电流
						</th>
						<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('instantPower', $event)">瞬时总有功功率
						</th>
						<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('instantPowerA', $event)">瞬时A相有功功率
						</th>
						<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('instantPowerB', $event)">瞬时B相有功功率
						</th>
						<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('instantPowerC', $event)">瞬时C相有功功率
						</th>
						<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('instantApparentPower', $event)">瞬时总视在功率
						</th>
						<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('instantApparentPowerA', $event)">瞬时A相视在功率
						</th>
						<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('instantApparentPowerB', $event)">瞬时B相视在功率
						</th>
						<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('instantApparentPowerC', $event)">瞬时C相视在功率
						</th>
						<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('powerFactor', $event)">总功率因数
						</th>
						<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('powerFactorA', $event)">A相功率因数
						</th>
						<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('powerFactorB', $event)">B相功率因数
						</th>
						<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('powerFactorC', $event)">C相功率因数
						</th>
						
						<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('rvalue', $event)">当前反向有功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('rvalue1', $event)">当前费率1反向有功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('rvalue2', $event)">当前费率2反向无功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('rvalue3', $event)">当前费率3反向无功总电能示值
						</th>
						<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
						    (nzSortOrderChange)="pagination.setSort('rvalue4', $event)">当前费率4反向无功总电能示值
						</th>
					</ng-container>
				</ng-container>
				<ng-container *ngIf="showNbSignal">
					<th [nzWidth]="'80px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('rsrp', $event)">RSRP
					</th>
					<th [nzWidth]="'80px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('sinr', $event)">SINR
					</th>
					<th [nzWidth]="'110px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('cellId', $event)">CELL_ID
					</th>
					<th [nzWidth]="'90px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('rssis', $event)">RSSIS
					</th>
					<th [nzWidth]="'450px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('version', $event)">版本信息
					</th>
					<th [nzWidth]="'270px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('apn', $event)">APN
					</th>
				</ng-container>
				
				<ng-container *ngIf="cmdType === 'READ_CUR_FORWARD_POWER' || (cmdType ==='DEVICE_REPORT' &&( !commandFn || commandFn === 'H0C-FN129'|| commandFn === 'H10-FN253'))">
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('value', $event)">正向有功示值
					</th>
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('value1', $event)">正向有功(尖)
					</th>
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('value2', $event)">正向有功(峰)
					</th>
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('value3', $event)">正向有功(平)
					</th>
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('value4', $event)">正向有功(谷)
					</th>
				</ng-container>

                <ng-container *ngIf="cmdType === 'READ_CUR_REVERSE_POWER'|| (cmdType ==='DEVICE_REPORT' &&( !commandFn || commandFn === 'H0C-FN131'))">
                    <th [nzWidth]="'120px'" nzAlign="center" nzShowSort
                        (nzSortOrderChange)="pagination.setSort('rvalue', $event)">反向有功示值
                    </th>
                    <th [nzWidth]="'120px'" nzAlign="center" nzShowSort
                        (nzSortOrderChange)="pagination.setSort('rvalue1', $event)">反向有功(尖)
                    </th>
                    <th [nzWidth]="'120px'" nzAlign="center" nzShowSort
                        (nzSortOrderChange)="pagination.setSort('rvalue2', $event)">反向有功(峰)
                    </th>
                    <th [nzWidth]="'120px'" nzAlign="center" nzShowSort
                        (nzSortOrderChange)="pagination.setSort('rvalue3', $event)">反向有功(平)
                    </th>
                    <th [nzWidth]="'120px'" nzAlign="center" nzShowSort
                        (nzSortOrderChange)="pagination.setSort('rvalue4', $event)">反向有功(谷)
                    </th>
                </ng-container>
				
				<ng-container *ngIf="cmdType === 'READ_DAY_FROZEN_FORWARD_POWER'">
					<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('lastForwardPower', $event)">日冻结正向有功总电能
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('lastForwardPower1', $event)">日冻结正向有功电能(尖)
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('lastForwardPower2', $event)">日冻结正向有功电能(峰)
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('lastForwardPower3', $event)">日冻结正向有功电能(平)
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('lastForwardPower4', $event)">日冻结正向有功电能(谷)
					</th>
                    <th [nzWidth]="'190px'" nzAlign="center" nzShowSort
                        (nzSortOrderChange)="pagination.setSort('lastDayFrozenDate', $event)">日冻结时标
                    </th>
				</ng-container>
				
				<ng-container *ngIf="cmdType === 'READ_DAY_FROZEN_REVERSE_POWER'">
					<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('lastReversePower', $event)">日冻结反向有功总电能
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('lastReversePower1', $event)">日冻结反向有功电能(尖)
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('lastReversePower2', $event)">日冻结反向有功电能(峰)
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('lastReversePower3', $event)">日冻结反向有功电能(平)
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('lastReversePower4', $event)">日冻结反向有功电能(谷)
					</th>
				</ng-container>
				
				<ng-container *ngIf="cmdType === 'READ_VOLTAGE_CURRENT_POWER' || (cmdType ==='DEVICE_REPORT' && (!commandFn || commandFn === 'H0C-FN25'))">
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('voltageA', $event)">A相电压
					</th>
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentA', $event)">A相电流
					</th>
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('voltageB', $event)">B相电压
					</th>
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentB', $event)">B相电流
					</th>
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('voltageC', $event)">C相电压
					</th>
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentC', $event)">C相电流
					</th>
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentZero', $event)">零序电流
					</th>
					<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('instantApparentPower', $event)">瞬时总视在功率
					</th>
					<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('instantApparentPowerA', $event)">瞬时A相视在功率
					</th>
					<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('instantApparentPowerB', $event)">瞬时B相视在功率
					</th>
					<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('instantApparentPowerC', $event)">瞬时C相视在功率
					</th>
					<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentActivePower', $event)">当前总有功功率
					</th>
					<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentActivePowerA', $event)">当前A相有功功率
					</th>
					<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentActivePowerB', $event)">当前B相有功功率
					</th>
					<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentActivePowerC', $event)">当前C相有功功率
					</th>
					<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentReactivePower', $event)">当前总无功功率
					</th>
					<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentReactivePowerA', $event)">当前A相无功功率
					</th>
					<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentReactivePowerB', $event)">当前B相无功功率
					</th>
					<th [nzWidth]="'150px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentReactivePowerC', $event)">当前C相无功功率
					</th>
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('powerFactor', $event)">总功率因数
					</th>
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('powerFactorA', $event)">A相功率因数
					</th>
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('powerFactorB', $event)">B相功率因数
					</th>
					<th [nzWidth]="'120px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('powerFactorC', $event)">C相功率因数
					</th>
				</ng-container>
				
				<ng-container *ngIf="cmdType === 'READ_ACTIVE_REACTIVE_VALUE'">
					<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('value', $event)">当前正向有功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('value1', $event)">当前费率1正向有功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('value2', $event)">当前费率2正向有功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('value3', $event)">当前费率3正向有功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('value4', $event)">当前费率4正向有功总电能示值
					</th>
					
					<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentForwardReactiveValue', $event)">当前正向无功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentForwardReactiveValue1', $event)">当前费率1正向无功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentForwardReactiveValue2', $event)">当前费率2正向无功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentForwardReactiveValue3', $event)">当前费率3正向无功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentForwardReactiveValue4', $event)">当前费率4正向无功总电能示值
					</th>
					
					<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant1ReactiveValue', $event)">当前一象限无功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant1ReactiveValue1', $event)">当前一象限费率1无功电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant1ReactiveValue2', $event)">当前一象限费率2无功电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant1ReactiveValue3', $event)">当前一象限费率3无功电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant1ReactiveValue4', $event)">当前一象限费率4无功电能示值
					</th>
					
					<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant4ReactiveValue', $event)">当前四象限无功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant4ReactiveValue1', $event)">当前四象限费率1无功电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant4ReactiveValue2', $event)">当前四象限费率2无功电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant4ReactiveValue3', $event)">当前四象限费率3无功电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant4ReactiveValue4', $event)">当前四象限费率4无功电能示值
					</th>
				</ng-container>
				
				<ng-container *ngIf="cmdType === 'READ_ACTIVE_REACTIVE_RVALUE'">
					<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('rvalue', $event)">当前反向有功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('rvalue1', $event)">当前费率1反向有功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('rvalue2', $event)">当前费率2反向无功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('rvalue3', $event)">当前费率3反向无功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('rvalue4', $event)">当前费率4反向无功总电能示值
					</th>
					
					<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentReverseReactiveValue', $event)">当前反向无功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentReverseReactiveValue1', $event)">当前费率1反向无功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentReverseReactiveValue2', $event)">当前费率2反向无功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentReverseReactiveValue3', $event)">当前费率3反向无功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentReverseReactiveValue4', $event)">当前费率4反向无功总电能示值
					</th>
					
					<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant2ReactiveValue', $event)">当前二象限无功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant2ReactiveValue1', $event)">当前二象限费率1无功电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant2ReactiveValue2', $event)">当前二象限费率2无功电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant2ReactiveValue3', $event)">当前二象限费率3无功电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant2ReactiveValue4', $event)">当前二象限费率4无功电能示值
					</th>
					
					<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant3ReactiveValue', $event)">当前三象限无功总电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant3ReactiveValue1', $event)">当前三象限费率1无功电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant3ReactiveValue2', $event)">当前三象限费率2无功电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant3ReactiveValue3', $event)">当前三象限费率3无功电能示值
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentQuadrant3ReactiveValue4', $event)">当前三象限费率4无功电能示值
					</th>
				</ng-container>
				
				<ng-container *ngIf="cmdType === 'READ_CURRENT_MONTH_FORWARD_MAX_DEMAND' || (cmdType ==='DEVICE_REPORT' && (!commandFn || commandFn === 'H0C-FN35'))">
					<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardActiveMaxDemand', $event)">当月正向有功总最大需量
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardActiveMaxDemand1', $event)">当月正向有功费率1最大需量
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardActiveMaxDemand2', $event)">当月正向有功费率2最大需量
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardActiveMaxDemand3', $event)">当月正向有功费率3最大需量
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardActiveMaxDemand4', $event)">当月正向有功费率4最大需量
					</th>
					
					<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardActiveMaxDemandTime', $event)">当月正向有功总最大需量发生时间
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardActiveMaxDemandTime1', $event)">当月正向有功费率1最大需量发生时间
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardActiveMaxDemandTime2', $event)">当月正向有功费率2最大需量发生时间
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardActiveMaxDemandTime3', $event)">当月正向有功费率3最大需量发生时间
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardActiveMaxDemandTime4', $event)">当月正向有功费率4最大需量发生时间
					</th>
					
					<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardReactiveMaxDemand', $event)">当月正向无功总最大需量
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardReactiveMaxDemand1', $event)">当月正向无功费率1最大需量
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardReactiveMaxDemand2', $event)">当月正向无功费率2最大需量
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardReactiveMaxDemand3', $event)">当月正向无功费率3最大需量
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardReactiveMaxDemand4', $event)">当月正向无功费率4最大需量
					</th>
					
					<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardReactiveMaxDemandTime', $event)">当月正向无功总最大需量发生时间
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardReactiveMaxDemandTime1', $event)">当月正向无功费率1最大需量发生时间
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardReactiveMaxDemandTime2', $event)">当月正向无功费率2最大需量发生时间
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardReactiveMaxDemandTime3', $event)">当月正向无功费率3最大需量发生时间
					</th>
					<th [nzWidth]="'190px'" nzAlign="center" nzShowSort
					    (nzSortOrderChange)="pagination.setSort('currentMonthForwardReactiveMaxDemandTime4', $event)">当月正向无功费率4最大需量发生时间
					</th>
				</ng-container>

				<ng-container *ngIf="cmdType === 'READ_CURRENT_TOTAL_ELECTRIC_POWER'">
					<th [nzWidth]="'180px'" nzAlign="center" nzShowSort
						(nzSortOrderChange)="pagination.setSort('value', $event)">当前组合有功总
					</th>
				</ng-container>
				
				<th [nzWidth]="'150px'" nzAlign="center" nzShowSort (nzSortOrderChange)="pagination.setSort('companyId', $event)">公司名称</th>
			</tr>
			</thead>
			<tbody>
			<tr *ngFor="let data of basicTable['data']">
				<td nzAlign="center" nzLeft [nzTooltipTitle]="data.meterCode" nzTooltipPlacement="bottom" nz-tooltip><a (click)="viewDetail(data)">{{ data?.meterCode }}</a></td>
				<td nzEllipsis nzTooltipPlacement="bottom" [nz-tooltip]="data?.roomNumber" nzAlign="center">{{ data?.roomNumber }}</td>
				<td nzEllipsis nzTooltipPlacement="bottom" [nz-tooltip]="data?.customerName" nzAlign="center">{{ data?.customerName }}</td>
				<td nzEllipsis nzTooltipPlacement="bottom" [nz-tooltip]="data?.communityName" nzAlign="center">{{ data?.communityName }}</td>
				<td nzAlign="center">
					<ng-container *ngIf="data?.cmdType;else deviceReport">
						{{ "EmMeterCmdType_" + data?.cmdType | translate}}
					</ng-container>
					<ng-template #deviceReport>设备上报</ng-template>
				</td>
				<td *ngIf="cmdType === 'DEVICE_REPORT'" nzAlign="center">{{ data?.commandFn }}</td>
				<td nzAlign="center">{{ data?.meteringTime }}</td>
				<td nzAlign="center">{{ data?.meterTime }}</td>
				
				<ng-container *ngIf="cmdType === 'DEVICE_REPORT'">
                    <ng-container *ngIf="commandFn !== 'H0C-FN129' && commandFn !== 'H0C-FN131'">
					<td nzAlign="center">
						<ng-container [ngSwitch]="data?.switchStatus">
							<nz-tag *ngSwitchCase="'ON'" [nzColor]="'#87d068'">合闸</nz-tag>
							<nz-tag *ngSwitchCase="'OFF'" [nzColor]="'#fa691c'">拉闸</nz-tag>
							<nz-tag *ngSwitchCase="'ALARM'" [nzColor]="'#fc0f0f'">异常</nz-tag>
						</ng-container>
					</td>
					<td nzAlign="center"> {{data.lastDayFrozenDate}} </td>
                    </ng-container>
					<ng-container *ngIf="!commandFn || commandFn === 'H10-FN254' || commandFn === 'H0C-FN33'">
						<td nzAlign="center">{{ data?.value || data?.value === 0 ? data?.value.toFixed(2) : '' }}</td>
						<td nzAlign="center">{{ data?.value1 || data?.value1 === 0 ? data?.value1.toFixed(2) : '' }}</td>
						<td nzAlign="center">{{ data?.value2 || data?.value2 === 0 ? data?.value2.toFixed(2) : '' }}</td>
						<td nzAlign="center">{{ data?.value3 || data?.value3 === 0 ? data?.value3.toFixed(2) : '' }}</td>
						<td nzAlign="center">{{ data?.value4 || data?.value4 === 0 ? data?.value4.toFixed(2) : '' }}</td>
					</ng-container>
					<ng-container *ngIf="!commandFn ||commandFn === 'H0C-FN33'">
						<td nzAlign="center">{{ data?.currentForwardReactiveValue }}</td>
						<td nzAlign="center">{{ data?.currentForwardReactiveValue1 }}</td>
						<td nzAlign="center">{{ data?.currentForwardReactiveValue2 }}</td>
						<td nzAlign="center">{{ data?.currentForwardReactiveValue3 }}</td>
						<td nzAlign="center">{{ data?.currentForwardReactiveValue4 }}</td>
						
						<td nzAlign="center">{{ data?.currentQuadrant1ReactiveValue }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant1ReactiveValue1 }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant1ReactiveValue2 }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant1ReactiveValue3 }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant1ReactiveValue4 }}</td>
						
						<td nzAlign="center">{{ data?.currentQuadrant4ReactiveValue }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant4ReactiveValue1 }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant4ReactiveValue2 }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant4ReactiveValue3 }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant4ReactiveValue4 }}</td>
					</ng-container>
					
					<ng-container *ngIf="!commandFn ||commandFn === 'H0C-FN34'">
						<td nzAlign="center">{{ data?.currentReverseActiveValue }}</td>
						<td nzAlign="center">{{ data?.currentReverseActiveValue1 }}</td>
						<td nzAlign="center">{{ data?.currentReverseActiveValue2 }}</td>
						<td nzAlign="center">{{ data?.currentReverseActiveValue3 }}</td>
						<td nzAlign="center">{{ data?.currentReverseActiveValue4 }}</td>
						
						<td nzAlign="center">{{ data?.currentReverseReactiveValue }}</td>
						<td nzAlign="center">{{ data?.currentReverseReactiveValue1 }}</td>
						<td nzAlign="center">{{ data?.currentReverseReactiveValue2 }}</td>
						<td nzAlign="center">{{ data?.currentReverseReactiveValue3 }}</td>
						<td nzAlign="center">{{ data?.currentReverseReactiveValue4 }}</td>
						
						<td nzAlign="center">{{ data?.currentQuadrant2ReactiveValue }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant2ReactiveValue1 }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant2ReactiveValue2 }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant2ReactiveValue3 }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant2ReactiveValue4 }}</td>
						
						<td nzAlign="center">{{ data?.currentQuadrant3ReactiveValue }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant3ReactiveValue1 }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant3ReactiveValue2 }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant3ReactiveValue3 }}</td>
						<td nzAlign="center">{{ data?.currentQuadrant3ReactiveValue4 }}</td>
					</ng-container>
					
					<ng-container *ngIf="!commandFn ||commandFn === 'H10-FN254' || commandFn === 'H10-FN253'">
						<td nzAlign="center">{{ data?.lastForwardPower }}</td>
						<td nzAlign="center">{{ data?.lastForwardPower1 }}</td>
						<td nzAlign="center">{{ data?.lastForwardPower2 }}</td>
						<td nzAlign="center">{{ data?.lastForwardPower3 }}</td>
						<td nzAlign="center">{{ data?.lastForwardPower4 }}</td>
					</ng-container>
					<ng-container *ngIf="!commandFn ||commandFn === 'H10-FN254'">
						<td nzAlign="center">{{ data?.lastFrozenTime | moment:'yyyy-MM-DD HH:mm' }}</td>
						<td nzAlign="center">{{ data?.lastWholeForwardPower }}</td>
						<td nzAlign="center">{{ data?.lastWholeReversePower }}</td>
						
						<td nzAlign="center">{{ data?.lastReversePower }}</td>
						<td nzAlign="center">{{ data?.lastReversePower1 }}</td>
						<td nzAlign="center">{{ data?.lastReversePower2}}</td>
						<td nzAlign="center">{{ data?.lastReversePower3 }}</td>
						<td nzAlign="center">{{ data?.lastReversePower4 }}</td>
						<td nzAlign="center">{{ data?.lastActivePower }}</td>
						<td nzAlign="center">{{ data?.lastActivePowerA }}</td>
						<td nzAlign="center">{{ data?.lastActivePowerB }}</td>
						<td nzAlign="center">{{ data?.lastActivePowerC }}</td>
						<td nzAlign="center">{{ data?.lastReactivePower }}</td>
						<td nzAlign="center">{{ data?.lastReactivePowerA }}</td>
						<td nzAlign="center">{{ data?.lastReactivePowerB }}</td>
						<td nzAlign="center">{{ data?.lastReactivePowerC }}</td>
						
						<td nzAlign="center">{{ data?.voltageA }}</td>
						<td nzAlign="center">{{ data?.currentA }}</td>
						<td nzAlign="center">{{ data?.voltageB }}</td>
						<td nzAlign="center">{{ data?.currentB }}</td>
						<td nzAlign="center">{{ data?.voltageC }}</td>
						<td nzAlign="center">{{ data?.currentC }}</td>
						<td nzAlign="center">{{ data?.instantPower }}</td>
						<td nzAlign="center">{{ data?.instantPowerA }}</td>
						<td nzAlign="center">{{ data?.instantPowerB }}</td>
						<td nzAlign="center">{{ data?.instantPowerC }}</td>
						<td nzAlign="center">{{ data?.instantApparentPower }}</td>
						<td nzAlign="center">{{ data?.instantApparentPowerA }}</td>
						<td nzAlign="center">{{ data?.instantApparentPowerB }}</td>
						<td nzAlign="center">{{ data?.instantApparentPowerC }}</td>
						<td nzAlign="center">{{ data?.powerFactor }}</td>
						<td nzAlign="center">{{ data?.powerFactorA }}</td>
						<td nzAlign="center">{{ data?.powerFactorB }}</td>
						<td nzAlign="center">{{ data?.powerFactorC }}</td>
						
						<td nzAlign="center">{{ data?.rvalue }}</td>
						<td nzAlign="center">{{ data?.rvalue1 }}</td>
						<td nzAlign="center">{{ data?.rvalue2 }}</td>
						<td nzAlign="center">{{ data?.rvalue3 }}</td>
						<td nzAlign="center">{{ data?.rvalue4 }}</td>
					</ng-container>
				</ng-container>
				
				<ng-container *ngIf="showNbSignal">
					<td nzAlign="center">{{ data?.rsrp }}</td>
					<td nzAlign="center">{{ data?.sinr }}</td>
					<td nzAlign="center">{{ data?.cellId }}</td>
					<td nzAlign="center">{{ data?.rssis }}</td>
					<td nzAlign="center">{{ data?.version }}</td>
					<td nzAlign="center">{{ data?.apn }}</td>
				</ng-container>
				
				<ng-container *ngIf="cmdType === 'READ_CUR_FORWARD_POWER' || (cmdType ==='DEVICE_REPORT' &&( !commandFn || commandFn === 'H0C-FN129'|| commandFn === 'H10-FN253'))">
					<td nzAlign="center">{{ data?.value || data?.value === 0 ? data?.value.toFixed(2) : '' }}</td>
					<td nzAlign="center">{{ data?.value1 || data?.value1 === 0 ? data?.value1.toFixed(2) : '' }}</td>
					<td nzAlign="center">{{ data?.value2 || data?.value2 === 0 ? data?.value2.toFixed(2) : '' }}</td>
					<td nzAlign="center">{{ data?.value3 || data?.value3 === 0 ? data?.value3.toFixed(2) : '' }}</td>
					<td nzAlign="center">{{ data?.value4 || data?.value4 === 0 ? data?.value4.toFixed(2) : '' }}</td>
				</ng-container>

                <ng-container *ngIf="cmdType === 'READ_CUR_REVERSE_POWER' || (cmdType ==='DEVICE_REPORT' &&( !commandFn || commandFn === 'H0C-FN131'))">
                    <td nzAlign="center">{{ data?.rvalue || data?.rvalue === 0 ? data?.rvalue.toFixed(2) : '' }}</td>
                    <td nzAlign="center">{{ data?.rvalue1 || data?.rvalue1 === 0 ? data?.rvalue1.toFixed(2) : '' }}</td>
                    <td nzAlign="center">{{ data?.rvalue2 || data?.rvalue2 === 0 ? data?.rvalue2.toFixed(2) : '' }}</td>
                    <td nzAlign="center">{{ data?.rvalue3 || data?.rvalue3 === 0 ? data?.rvalue3.toFixed(2) : '' }}</td>
                    <td nzAlign="center">{{ data?.rvalue4 || data?.rvalue4 === 0 ? data?.rvalue4.toFixed(2) : '' }}</td>
                </ng-container>
				
				<ng-container *ngIf="cmdType === 'READ_DAY_FROZEN_FORWARD_POWER'">
					<td nzAlign="center">{{ data?.lastForwardPower }}</td>
					<td nzAlign="center">{{ data?.lastForwardPower1 }}</td>
					<td nzAlign="center">{{ data?.lastForwardPower2 }}</td>
					<td nzAlign="center">{{ data?.lastForwardPower3 }}</td>
					<td nzAlign="center">{{ data?.lastForwardPower4 }}</td>
                    <td nzAlign="center">{{ data?.lastDayFrozenDate }}</td>
				</ng-container>
				
				<ng-container *ngIf="cmdType === 'READ_DAY_FROZEN_REVERSE_POWER'">
					<td nzAlign="center">{{ data?.lastReversePower }}</td>
					<td nzAlign="center">{{ data?.lastReversePower1 }}</td>
					<td nzAlign="center">{{ data?.lastReversePower2 }}</td>
					<td nzAlign="center">{{ data?.lastReversePower3 }}</td>
					<td nzAlign="center">{{ data?.lastReversePower4 }}</td>
				</ng-container>
				
				<ng-container *ngIf="cmdType === 'READ_VOLTAGE_CURRENT_POWER' || (cmdType ==='DEVICE_REPORT' && (!commandFn || commandFn === 'H0C-FN25'))">
					<td nzAlign="center">{{ data?.voltageA }}</td>
					<td nzAlign="center">{{ data?.currentA }}</td>
					<td nzAlign="center">{{ data?.voltageB }}</td>
					<td nzAlign="center">{{ data?.currentB }}</td>
					<td nzAlign="center">{{ data?.voltageC }}</td>
					<td nzAlign="center">{{ data?.currentC }}</td>
					<td nzAlign="center">{{ data?.currentZero }}</td>
					<td nzAlign="center">{{ data?.instantApparentPower }}</td>
					<td nzAlign="center">{{ data?.instantApparentPowerA }}</td>
					<td nzAlign="center">{{ data?.instantApparentPowerB }}</td>
					<td nzAlign="center">{{ data?.instantApparentPowerC }}</td>
					<td nzAlign="center">{{ data?.currentActivePower }}</td>
					<td nzAlign="center">{{ data?.currentActivePowerA }}</td>
					<td nzAlign="center">{{ data?.currentActivePowerB }}</td>
					<td nzAlign="center">{{ data?.currentActivePowerC }}</td>
					<td nzAlign="center">{{ data?.currentReactivePower }}</td>
					<td nzAlign="center">{{ data?.currentReactivePowerA }}</td>
					<td nzAlign="center">{{ data?.currentReactivePowerB }}</td>
					<td nzAlign="center">{{ data?.currentReactivePowerC }}</td>
					<td nzAlign="center">{{ data?.powerFactor }}</td>
					<td nzAlign="center">{{ data?.powerFactorA }}</td>
					<td nzAlign="center">{{ data?.powerFactorB }}</td>
					<td nzAlign="center">{{ data?.powerFactorC }}</td>
				</ng-container>
				
				<ng-container *ngIf="cmdType === 'READ_ACTIVE_REACTIVE_VALUE'">
					<td nzAlign="center">{{ data?.value }}</td>
					<td nzAlign="center">{{ data?.value1 }}</td>
					<td nzAlign="center">{{ data?.value2 }}</td>
					<td nzAlign="center">{{ data?.value3 }}</td>
					<td nzAlign="center">{{ data?.value4 }}</td>
					
					<td nzAlign="center">{{ data?.currentForwardReactiveValue }}</td>
					<td nzAlign="center">{{ data?.currentForwardReactiveValue1 }}</td>
					<td nzAlign="center">{{ data?.currentForwardReactiveValue2 }}</td>
					<td nzAlign="center">{{ data?.currentForwardReactiveValue3 }}</td>
					<td nzAlign="center">{{ data?.currentForwardReactiveValue4 }}</td>
					
					<td nzAlign="center">{{ data?.currentQuadrant1ReactiveValue }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant1ReactiveValue1 }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant1ReactiveValue2 }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant1ReactiveValue3 }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant1ReactiveValue4 }}</td>
					
					<td nzAlign="center">{{ data?.currentQuadrant4ReactiveValue }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant4ReactiveValue1 }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant4ReactiveValue2 }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant4ReactiveValue3 }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant4ReactiveValue4 }}</td>
				</ng-container>
				
				<ng-container *ngIf="cmdType === 'READ_ACTIVE_REACTIVE_RVALUE'">
					<td nzAlign="center">{{ data?.rvalue }}</td>
					<td nzAlign="center">{{ data?.rvalue1 }}</td>
					<td nzAlign="center">{{ data?.rvalue2 }}</td>
					<td nzAlign="center">{{ data?.rvalue3 }}</td>
					<td nzAlign="center">{{ data?.rvalue4 }}</td>
					
					<td nzAlign="center">{{ data?.currentReverseReactiveValue }}</td>
					<td nzAlign="center">{{ data?.currentReverseReactiveValue1 }}</td>
					<td nzAlign="center">{{ data?.currentReverseReactiveValue2 }}</td>
					<td nzAlign="center">{{ data?.currentReverseReactiveValue3 }}</td>
					<td nzAlign="center">{{ data?.currentReverseReactiveValue4 }}</td>
					
					<td nzAlign="center">{{ data?.currentQuadrant2ReactiveValue }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant2ReactiveValue1 }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant2ReactiveValue2 }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant2ReactiveValue3 }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant2ReactiveValue4 }}</td>
					
					<td nzAlign="center">{{ data?.currentQuadrant3ReactiveValue }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant3ReactiveValue1 }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant3ReactiveValue2 }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant3ReactiveValue3 }}</td>
					<td nzAlign="center">{{ data?.currentQuadrant3ReactiveValue4 }}</td>
				</ng-container>
				
				<ng-container *ngIf="cmdType === 'READ_CURRENT_MONTH_FORWARD_MAX_DEMAND'  || (cmdType ==='DEVICE_REPORT' && (!commandFn || commandFn === 'H0C-FN35'))">
					<td nzAlign="center">{{ data?.currentMonthForwardActiveMaxDemand }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardActiveMaxDemand1 }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardActiveMaxDemand2 }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardActiveMaxDemand3 }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardActiveMaxDemand4 }}</td>
					
					<td nzAlign="center">{{ data?.currentMonthForwardActiveMaxDemandTime }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardActiveMaxDemandTime1 }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardActiveMaxDemandTime2 }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardActiveMaxDemandTime3 }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardActiveMaxDemandTime4 }}</td>
					
					<td nzAlign="center">{{ data?.currentMonthForwardReactiveMaxDemand }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardReactiveMaxDemand1 }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardReactiveMaxDemand2 }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardReactiveMaxDemand3 }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardReactiveMaxDemand4 }}</td>
					
					<td nzAlign="center">{{ data?.currentMonthForwardReactiveMaxDemandTime }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardReactiveMaxDemandTime1 }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardReactiveMaxDemandTime2 }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardReactiveMaxDemandTime3 }}</td>
					<td nzAlign="center">{{ data?.currentMonthForwardReactiveMaxDemandTime4 }}</td>
				</ng-container>

				<ng-container *ngIf="cmdType === 'READ_CURRENT_TOTAL_ELECTRIC_POWER'">
					<td nzAlign="center">{{ data?.value || data?.value === 0 ? data?.value.toFixed(2) : '' }}</td>
				</ng-container>
				
				<td nzEllipsis [nz-tooltip]="data?.companyId" nzAlign="center">{{ data?.companyId }}</td>
			</tr>
			</tbody>
		</nz-table>
	</nz-card>
</nz-spin>
