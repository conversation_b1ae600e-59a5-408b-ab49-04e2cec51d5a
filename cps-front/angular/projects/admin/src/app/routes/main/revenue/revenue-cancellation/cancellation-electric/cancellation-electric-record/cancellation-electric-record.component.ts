import {Component, Inject, OnInit} from '@angular/core';
import {Operator, Order, PaginationComponent} from '@core';
import {HttpClient} from '@angular/common/http';
import {NzMessageService} from 'ng-zorro-antd/message';
import {NzModalService} from 'ng-zorro-antd/modal';
import {NzDrawerService} from 'ng-zorro-antd/drawer';
import {BuildingUnitService} from '../../../../../../service/archive/building-unit.service';
import {CancelHistoryDataComponent} from '../../cancel-history-data/cancel-history-data.component';
import {EmElectricMeter} from "@lib";
import { CancelRoomDeviceService } from '../../../../../../service/revenue/cancel-room-device.service';
import {CancellationType, wrap} from "../../../../../../components/EnumOptions";

@Component({
    selector: 'app-cancellation-electric-record',
    templateUrl: './cancellation-electric-record.component.html',
    styleUrls: ['./cancellation-electric-record.component.scss']
})
export class CancellationElectricRecordComponent extends PaginationComponent<any> implements OnInit {
    spinning = false;


    constructor(http: HttpClient, @Inject('apiUrl') apiUrl: any,
                private messageService: NzMessageService, private modalService: NzModalService,
                public drawerService: NzDrawerService, private buildingUnitService:BuildingUnitService,
                public cancelRoomDeviceService: CancelRoomDeviceService) {
        super(http, apiUrl + 'auth/revenue/cancellation/pageCancelElectricMeter');
    }

    ngOnInit(): void {
        this.pagination.queryForm = this.buildQueryForm({
            community: [null, this.filter({
                label: '小区楼栋',
                field: 'community-temp',
                type: 'treeSelect',
                operator: Operator.eq,
                expandChange: this.buildingUnitService.expandChange(),
                treeData: [],
                submitted: false
            })],
            code: [null, this.filter({
                label: '表号', field: 'code', type: 'text',
            })],
            cancellationType: [null, this.filter({
                label: '销户类型',
                field: 'cancellationType',
                type: 'select',
                optionItems: wrap(CancellationType),
                placeholder: '请选择销户类型',
                operator: Operator.eq
            })
            ]
        });
        this.pagination.setDefaultSort('cancellationTime', Order.desc);
        this.buildingUnitService.initCommunityFilter(this.pagination);
        this.pagination.reload();
    }


    queryData(data: EmElectricMeter) {
        this.modalService.create({
            nzTitle: '历史数据',
            nzWidth: '1300px',
            nzContent: CancelHistoryDataComponent,
            nzComponentParams: {
                deviceId: data.id,
                deviceType: 'electric'
            },
            nzOkText: null,
            nzCancelText: '关闭'
        })
    }

    delete(data: any) {
        this.modalService.confirm({
            nzTitle: '确认删除该销表电表吗？',
            nzOkDanger:true,
            nzOnOk: () => {
                this.cancelRoomDeviceService.deleteCancellationElectricMeter(data.id).subscribe(result => {
                    this.messageService.success('删除成功！');
                    this.pagination.reload();
                });
            }
        });
    }

    communitySelect(value: any) {
        this.pagination.addDefaultAndFilter('communityFilter', value);
        this.pagination.reload();
    }
}
