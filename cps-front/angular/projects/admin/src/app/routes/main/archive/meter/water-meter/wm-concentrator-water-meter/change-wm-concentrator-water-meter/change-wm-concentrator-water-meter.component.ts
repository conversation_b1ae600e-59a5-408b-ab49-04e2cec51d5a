import {Component, Inject, OnInit} from '@angular/core';
import {NzMessageService} from 'ng-zorro-antd/message';
import {FormComponent, FormField} from '@core';
import {Router} from '@angular/router';
import {WmConcentratorWaterMeterService} from '../../../../../../../service/archive/wm-concentrator-water-meter.service';
import {CommunityService} from '../../../../../../../service/archive/community.service';
import {Validators} from '@angular/forms';
import {WmMeterModel} from '@lib';
import {WmMeterModelService} from '../../../../../../../service/archive/wm-meter-model.service';
import {ConcentratorService} from '../../../../../../../service/archive/concentrator.service';
import {Port, Protocol} from '../../../../../../../components/EnumOptions';

@Component({
    selector: 'app-change-wm-concentrator-water-meter',
    templateUrl: './change-wm-concentrator-water-meter.component.html',
    styleUrls: ['./change-wm-concentrator-water-meter.component.scss']
})
export class ChangeWmConcentratorWaterMeterComponent extends FormComponent implements OnInit {
    oldData: any;

    constructor(@Inject('apiUrl') public apiUrl, public messageService: NzMessageService,
                public router: Router, public wmConcentratorWaterMeterService: WmConcentratorWaterMeterService,
                private wmMeterModelService: WmMeterModelService, private concentratorService: ConcentratorService) {
        super();
    }


    ngOnInit() {
        this.formGroup = this.buildForm({
            // 水表编号
            code: [null, [Validators.required, Validators.pattern(/\d{14}/)], this.field({
                label: '新表编号', type: 'text', maxlength: 14, required: true, pattern: '请输入14位数字'
            })],
            // 水表型号
            model: [null, [Validators.required], this.field({
                label: '新表型号', type: 'select', optionItems: [], showSearch: false, selectChange: (value) => this.modelChange(value)
            })],
            concentrator: [null, [Validators.required], this.field({
                label: '所属集中器',
                type: 'select',
                optionItems: [],
                selectChange: value => this.changeConcentrator(value),
                required: true
            })],

            protocol: [null, [], this.field({
                label: '协议类型', type: 'select', optionItems: Protocol, category: "other", required: true
            })],
            port: [null, [], this.field({
                label: '端口', type: 'select', optionItems: Port, category: "other", required: true
            })],
            // 口径
            diameter: [null, [Validators.min(15), Validators.max(999999)], this.field({
                label: '口径', type: 'number', min: 15, max: 999999
            })],
            valve: [null, [Validators.required], this.field({
                label: '是否有阀', type: 'radio', optionItems: [{label: '有阀', value: true}, {label: '无阀', value: false}]
            })],
            startValue: [null, [Validators.required, Validators.min(0)], this.field({
                label: '新表启用读数', type: 'number', min: 0
            })],
            lastValue: [null, [Validators.required, Validators.min(0)], this.field({
                label: '旧表最后读数', type: 'number', min: 0
            })]
        });
        this.initOtherData();
    }

    close(success: boolean) {
    }

    /**
     * 加载其它数据
     */
    initOtherData() {
        this.wmMeterModelService.getMeterModels("CONCENTRATOR").subscribe((meterModel: WmMeterModel[]) => {
            if (meterModel && meterModel.length > 0) {
                this.setFieldOptionItems('model', meterModel, 'name');
            }
        });

        // 根据小区id，加载集中器
        this.concentratorService.getConcentratorByCommunity(this.oldData.community.id).subscribe((concentrators: any[]) => {
            concentrators.forEach(concentrator => {
                concentrator.name = concentrator.code + this.concentratorTypeToContent(concentrator.concentratorType);

            });
            this.setFieldOptionItems('concentrator', concentrators, 'name');
        });
    }

    /**
     * 表单提交
     * @param formValue 表单数据
     */
    onValidSuccess(formValue: any): any {
        if (!this.submitted) {
            this.submitted = true;
            this.wmConcentratorWaterMeterService.changeMeter({oldId:this.oldData.id, lastValue: formValue.lastValue}, formValue).subscribe({
                next: () => {
                    this.messageService.create('success', '成功调用换表，请刷新页面关注换表结果');
                    this.submitted = false;
                    this.close(true);
                },
                error: () => {
                    this.submitted = false;
                }
            });

        }
    }

    sub() {
        if (this.formValid()) {
            this.formGroup.value.oldId = this.oldData.id;
            this.onValidSuccess(this.formGroup.value);
        }
    }

    formValid() {
        return this.formFields.every((formField: FormField) => {
            // @ts-ignore
            if (this.formGroup.controls.hasOwnProperty(formField.fieldName) && formField.category === 'default') {
                const control = this.formGroup.controls[formField.fieldName];
                control.markAsDirty();
                control.updateValueAndValidity();
                return control.valid;
            } else {
                return true;
            }
        })
    }

    /**
     * 集中器类型格式化
     * @param concentratorType
     */
    concentratorTypeToContent(concentratorType) {
        switch (concentratorType) {
            case 'WIRELESS':
                return '(无线)';
            case 'WIRED':
                return '(有线)';
            case 'WIRED_MULTI_FUNCTION':
                return '(有线多功能)';
            default:
                return '';
        }
    }

    changeConcentrator(concentrator: any) {
        // 有线多功能集中器
        if (concentrator.concentratorType === 'WIRED_MULTI_FUNCTION') {
            this.formFields[3].category = 'default';
            this.formFields[4].category = 'default';

        } else {
            this.formFields[3].category = 'other';
            this.formFields[4].category = 'other';
        }
    }

    modelChange(value: any) {
        if (value.communicationMechanism === 'THIRD_PARTY_ACCESS') {
            this.formFields[0].pattern = "请输入6-14位数字或字母";
            this.updateValidators(this.formGroup, 'code', [Validators.pattern(/^[a-zA-Z0-9]{6,14}$/), Validators.required]);
        } else {
            this.formFields[0].pattern = "请输入14位数字";
            this.updateValidators(this.formGroup, 'code', [Validators.pattern(/\d{14}$/), Validators.required]);
        }
    }
}
