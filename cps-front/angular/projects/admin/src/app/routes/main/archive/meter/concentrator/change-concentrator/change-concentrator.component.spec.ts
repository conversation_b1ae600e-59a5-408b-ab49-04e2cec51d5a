import {ComponentFixture, TestBed} from '@angular/core/testing';

import {ChangeConcentratorComponent} from './change-concentrator.component';

describe('ChangeConcentratorComponent', () => {
    let component: ChangeConcentratorComponent;
    let fixture: ComponentFixture<ChangeConcentratorComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [ChangeConcentratorComponent]
        })
            .compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(ChangeConcentratorComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
});