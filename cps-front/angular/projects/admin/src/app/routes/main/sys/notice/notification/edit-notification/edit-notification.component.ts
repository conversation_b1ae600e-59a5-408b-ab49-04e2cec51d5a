import {Component, Inject, OnInit} from '@angular/core';
import {Validators} from "@angular/forms";
import {NzMessageService} from "ng-zorro-antd/message";
import {ActivatedRoute, Params, Router} from "@angular/router";
import {FormComponent} from '@core';
import {Notification} from '@lib';
import {NotificationService} from "../../../../../../service/sys/notification.service";

@Component({
    selector: 'app-edit-notification',
    templateUrl: './edit-notification.component.html',
    styleUrls: ['./edit-notification.component.scss']
})
export class EditNotificationComponent extends FormComponent implements OnInit {

    title: string = '编辑系统通知';
    id: number;
    notification: Notification;

    constructor(@Inject('apiUrl') public apiUrl, public messageService: NzMessageService,
                public activatedRoute: ActivatedRoute, public router: Router,
                public notificationService: NotificationService) {
        super();
        this.activatedRoute.queryParams.subscribe((params: Params) => {
            this.id = params['id'];
        });
    }

    ngOnInit() {
        this.formGroup = this.buildForm({
            id: [this.id],
            subject: [null, [Validators.required, Validators.maxLength(64)], this.field({
                label: '标题', maxlength: 64
            })],
            content: [null, [Validators.maxLength(4096)], this.field({
                label: '内容',
                type: 'textarea',
                lineMode: 'single',
                maxlength: 4096,
                autoSize: {minRows: 2, maxRows: 3}
            })]
        });
        this.initFormData();
    }

    /**
     * 表单赋值
     */
    initFormData(): void {
        this.notificationService.getNotificationById(this.id).subscribe((data) => {
            this.notification = data;
            this.formGroup.patchValue(data);
        });
    }

    onValidSuccess(formValue: any): any {
        if (!this.submitted) {
            this.submitted = true;
            this.notificationService.updateNotification(formValue).subscribe({
                next: () => {
                    this.messageService.create('success', '保存成功');
                    this.submitted = false;
                    this.goBack();
                },
                error: () => {
                    this.submitted = false;
                }
            });
        }
    }

    /**
     * 返回上个视图
     */
    goBack() {
        let curPath = this.router.url.split('?')[0];
        let pathArr = curPath.split('/');
        let parentPath = pathArr.splice(0, pathArr.length - 1).join('/');
        this.router.navigateByUrl(parentPath,{ replaceUrl: true })
    }
}
