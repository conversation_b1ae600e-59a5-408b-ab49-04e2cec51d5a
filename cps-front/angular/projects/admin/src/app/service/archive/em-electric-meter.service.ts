import {Inject, Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {Observable} from "rxjs";
import {HttpService} from '@core';
import {EmElectricMeter, WmWaterMeter} from '@lib';

/**
 * 电表(WmElectricMeter)ts表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-30 14:13:48
 */
@Injectable({
    providedIn: 'root'
})
export class EmElectricMeterService extends HttpService {

    constructor(http: HttpClient, @Inject('apiUrl') apiUrl) {
        super(http, apiUrl);
    }

    getElectricMetersByRoomId(roomId: number): Observable<EmElectricMeter[]> {
        return this.get('auth/archive/meter/electric/getElectricMetersByRoomId', {roomId: roomId});
    }

    getParentMeterById(id: number): Observable<any> {
        return this.get('auth/archive/meter/electric/getParentMeterById', {id: id});
    }

    getParentMeter(meterCode: string): Observable<any> {
        return this.get('auth/archive/meter/electric/getParentMeter', {meterCode: meterCode});
    }

    findChildrenMetersById(id: any) {
        return this.get('auth/archive/meter/electric/findChildrenMetersById', {meterId: id});
    }

    batchChangeCommunity(ids: any[], communityId: number, buildingUnitId: number ): Observable<any> {
        let paramMap = {'ids[]': ids, 'communityId': communityId}
        if (buildingUnitId) {
            paramMap['buildingUnitId'] = buildingUnitId;
        }
        return this.postBodyAndParams('auth/archive/meter/electric/batchChangeCommunity', null, paramMap);
    }
}
