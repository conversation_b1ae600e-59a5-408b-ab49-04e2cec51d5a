import {Inject, Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {Observable} from "rxjs";
import {HttpService} from '@core';
import {WmWaterMeter} from '@lib';

/**
 * 水表(WmWaterMeter)ts表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-30 14:13:48
 */
@Injectable({
    providedIn: 'root'
})
export class WmWaterMeterService extends HttpService {

    constructor(http: HttpClient, @Inject('apiUrl') apiUrl) {
        super(http, apiUrl);
    }

    getWaterMetersByRoomId(roomId: number): Observable<WmWaterMeter[]> {
        return this.get('auth/archive/meter/water/getWaterMetersByRoomId', {roomId: roomId});
    }

    getParentMeter(meterCode: string): Observable<any> {
        return this.get('auth/archive/meter/water/getParentMeter', {meterCode: meterCode});
    }

    getParentMeterById(id: number): Observable<any> {
        return this.get('auth/archive/meter/water/getParentMeterById', {id: id});
    }

    findChildrenMetersById(meterId: number): Observable<any> {
        return this.get('auth/archive/meter/water/findChildrenMetersById', {meterId: meterId});
    }

    configMeterRate(id: number, rate: number): Observable<any> {
        return this.put('auth/archive/meter/water/configMeterRate', null, {id: id, rate: rate});
    }

    getMeterRateById(id: number): Observable<number> {
        return this.get('auth/archive/meter/water/getMeterRateById', {id: id});
    }
}
