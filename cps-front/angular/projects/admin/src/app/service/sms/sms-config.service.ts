import {Inject, Injectable} from '@angular/core';
import {HttpClient, HttpParams} from "@angular/common/http";
import {Observable} from "rxjs";
import {HttpService} from '@core';
import {SmsConfig} from '@lib';

/**
 * 短信配置表(SmsConfig)ts表服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-30 14:14:28
 */
@Injectable({
    providedIn: 'root'
})
export class SmsConfigService extends HttpService {

    constructor(http: HttpClient, @Inject('apiUrl') apiUrl) {
        super(http, apiUrl);
    }

    /**
     * 根据ID查询短信配置表
     */
    getSmsConfigById(id): Observable<SmsConfig> {
        const httpParams = new HttpParams().set('id', id);
        return this.get('auth/sys/notice/smsConfig/getSmsConfigById', httpParams);
    }

    /**
     * 添加短信配置表
     */
    addSmsConfig(smsConfig): Observable<SmsConfig> {
        return this.post('auth/sys/notice/smsConfig/add', smsConfig);
    }

    /**
     * 删除短信配置表
     */
    removeSmsConfigById(id): Observable<any> {
        const httpParams = new HttpParams().set('id', id);
        return this.delete('auth/sys/notice/smsConfig/delete', httpParams);
    }

    /**
     * 批量删除短信配置表
     */
    batchRemoveSmsConfigByIds(ids): Observable<any> {
        return this.delete('auth/sys/notice/smsConfig/batchDel', {'ids[]': ids});
    }

    /**
     * 修改短信配置表
     */
    updateSmsConfig(smsConfig: SmsConfig): Observable<SmsConfig> {
        return this.post('auth/sys/notice/smsConfig/edit', smsConfig);
    }
}
