import {Inject, Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {Observable} from "rxjs";
import {HttpService} from '@core';
import {UserModel} from '@lib';
import {AuthService} from '../../auth/auth.service';
import * as moment from 'moment';
import {CheckFileUploadComponent} from '../../components/check-file-upload/check-file-upload.component';
import {NzModalService} from 'ng-zorro-antd/modal';

@Injectable({
    providedIn: 'root'
})
export class LoginService extends HttpService {

    checkingSysExpired: boolean = false;

    constructor(http: HttpClient, @Inject('apiUrl') apiUrl: any, public authService: AuthService,
                private modalService: NzModalService) {
        super(http, apiUrl);
    }

    /**
     * 自动登录
     */
    autoLogin(successCallback: Function, errorCallback: Function) {
        const authToken = this.authService.getToken();
        this.loginToken(authToken).subscribe(userModel => {
            successCallback(userModel);
        }, error => {
            errorCallback(error);
        });
    }

    /**
     * 查询网站设置
     */
    getWebsiteParams(): Observable<any> {
        return this.get('green/getWebsiteParams');
    }

    /**
     * 获取加密公钥
     */
    getPublicKey(): Observable<any> {
        return this.get('green/getPublicKey');
    }

    /**
     * 用户名密码登录
     */
    login(username: string, password: any, keyId: string, captcha: string): Observable<UserModel> {
        return this.post('green/login', {username: username, password: password, keyId: keyId, code: captcha});
    }

    /**
     * 短信验证码登录
     */
    loginByMobile(mobile: string, smsCode: string): Observable<UserModel> {
        return this.post('green/loginByMobile', {mobile: mobile, smsCode: smsCode});
    }

    /**
     * 自动登录
     */
    loginToken(token: string): Observable<UserModel> {
        return this.post('green/loginToken', {token: token});
    }

    /**
     * 发送短信验证码
     */
    code(mobile: any): Observable<any> {
        return this.get('green/smsCode', {mobile: mobile});
    }

    /**
     * 发送找回密码短信验证码
     */
    forgetPassCode(mobile: any): Observable<any> {
        return this.get('green/forgetPassCode', {mobile: mobile});
    }

    /**
     * 通过手机号修改个人密码
     */
    updatePasswordBySmsCode(mobile: string, newPassword: any, smsCode: string,): Observable<any> {
        return this.put('green/updatePasswordBySmsCode', null, {mobile: mobile, newPassword: newPassword, smsCode: smsCode});
    }

    /**
     * 注册公司
     */
    registerCompany(companyRegister: any): Observable<any> {
        return this.post('green/registerCompany', companyRegister);
    }

    getSysExpiredInfo(): Observable<any> {
        return this.get('green/getSysExpiredInfo');
    }

    getPromotionDetail(): Observable<any> {
        return this.get('green/getPromotionDetail');
    }

    getCompanyServiceChargeDtoByCompanyId(companyId): Observable<any> {
        return this.get('green/getCompanyServiceChargeDtoByCompanyId', {companyId});
    }

    /**
     * 服务费充值
     */
    serviceChargeWxPay(companyId, amount, remark): Observable<any> {
        return this.postBodyAndParams('green/serviceChargeWxPay', null, {companyId, amount, remark});
    }

    checkSysExpired() {
        if (this.checkingSysExpired) {
            return
        }
        this.checkingSysExpired = true;
        this.getSysExpiredInfo().subscribe(res => {
            let expired = !!res.expiredDate, title = '', type = 'error',isBefore = false;
            if (expired) {
                isBefore = moment(res.expiredDate).isBefore(moment());
                if (isBefore) {
                    title = '授权已过期';
                } else if (moment(res.expiredDate).diff(moment(), 'days') <= 5) {
                    title = '授权即将过期';
                    type = 'warning';
                }else{
                    return
                }
            } else {
                title = '系统未授权';
            }
            let modal = this.modalService[type]({
                nzWidth: expired? '510px': '400px',
                nzTitle: title,
                nzContent: CheckFileUploadComponent,
                nzFooter: null,
                nzClosable: false,
                nzOkText: null,
                nzMaskClosable: false,
                nzComponentParams: {
                    sysExpiredInfoDto: res,
                    before: isBefore
                }
            });
            modal.componentInstance.close = (res) => {
                modal.destroy();
                this.checkingSysExpired = false;
            };
        })
    }
}
