import {Inject, Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {Observable} from "rxjs";
import {HttpService} from '@core';

@Injectable({
    providedIn: 'root'
})
export class MinePasswordService extends HttpService {

    constructor(http: HttpClient, @Inject('apiUrl') apiUrl) {
        super(http, apiUrl);
    }

    /**
     * 发送修改密码短信验证码
     */
    smsCode(): Observable<any> {
        return this.get('auth/settings/password/smsCode');
    }

    /**
     * 通过旧密码修改个人密码
     */
    updatePasswordByOldPassword(oldPassword, newPassword): Observable<any> {
        return this.put('auth/settings/password/updatePasswordByOldPassword', null, {oldPassword: oldPassword, newPassword: newPassword});
    }

    /**
     * 通过短信验证码修改个人密码
     */
    updatePasswordBySmsCode(smsCode, newPassword): Observable<any> {
        return this.put('auth/settings/password/updatePasswordBySmsCode', null, {smsCode: smsCode, newPassword: newPassword});
    }
}
