import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'meterBaud'
})
export class MeterBaudPipe implements PipeTransform {

    constructor() {
    }

    transform(meterBaud, dockingType) {
      if (dockingType === 'JASON_CONCENTRATOR_OLD') {
        switch (meterBaud) {
          case 1:
            return '1200bps';
          case 2:
            return '2400bps';
          case 3:
            return '4800bps';
          case 4:
            return '9600bps';
          case 5:
            return '19200bps';
          default:
            return '--';
        }
      } else {
        switch (meterBaud) {
          case 1:
            return '1200bps';
          case 2:
            return '2400bps';
          case 4:
            return '4800bps';
          case 9:
            return '9600bps';
          default:
            return '2400bps';
        }
      }
    }
}
