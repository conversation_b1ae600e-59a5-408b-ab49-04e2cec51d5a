import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SafeHtmlPipe } from './safe-html.pipe';
import { ValveStatusPipe } from './valve-status.pipe';
import {ConcentratorProtocolPipe} from "./concentrator-protocol.pipe";
import {MeterBaudPipe} from "./meter-baud.pipe";
import {ConcentratorPortPipe} from "./concentrator-port.pipe";
import {NumberTranslatorPipe} from "./number-translator.pipe";
import {MomentPipe} from './moment.pipe';
import {SignalFormatterPipe} from "./signal-formatter.pipe";
import { AmountFormatterPipe } from './amount-formatter.pipe';
import {PriceDetailFormatterPipe} from "./price-detail-formatter.pipe";
import {CmdValueFormatterPipe} from "./cmd-value-formatter.pipe";
import {ForceCloseValveFormatterPipe} from "./force-close-valve-formatter.pipe";
import {FailReasonFormatterPipe} from "./fail-reason-formatter.pipe";

@NgModule({
    declarations: [
        SafeHtmlPipe, ValveStatusPipe, ConcentratorProtocolPipe, MeterBaudPipe, ConcentratorPortPipe, NumberTranslatorPipe,MomentPipe, AmountFormatterPipe, SignalFormatterPipe, PriceDetailFormatterPipe, CmdValueFormatterPipe, ForceCloseValveFormatterPipe, FailReasonFormatterPipe
    ],
    exports: [
        SafeHtmlPipe, ValveStatusPipe, ConcentratorProtocolPipe, MeterBaudPipe, ConcentratorPortPipe, NumberTranslatorPipe,MomentPipe, AmountFormatterPipe, SignalFormatterPipe, PriceDetailFormatterPipe, CmdValueFormatterPipe, ForceCloseValveFormatterPipe, FailReasonFormatterPipe
    ],
    providers: [
        // 管道的提供者
        AmountFormatterPipe,
        PriceDetailFormatterPipe
    ],
    imports: [
        CommonModule
    ]
})
export class PipesModule {
}
