<div [class]="tab ? 'tab' : 'wrap'">
    <form style="margin-bottom: -10px;" nz-form [nzLayout]="'inline'" [formGroup]="pagination.queryForm"
          class="search__form query-form">
        <div style="display: flex;width: 100%;" [ngStyle]="{flexWrap:showBatch? 'wrap': 'nowrap'}">
            <at-pagination-filter-renderer *ngIf="!showBatch" [formGroup]="pagination.queryForm"

                                           [filterFields]="defaultFields"></at-pagination-filter-renderer>
            <ng-container *ngIf="showBatch">
                <textarea
                    style="margin-bottom: 10px;width: 100%;border-bottom-left-radius: 4px!important;border-top-left-radius: 4px!important;"
                    nz-input [(ngModel)]="codes" [ngModelOptions]="{standalone: true}"
                    [placeholder]="'请输入多个'+ (batchType==='meterCodes'? '表号' : 'IMEI') +'以逗号、空格或换行符分隔'"
                    [nzAutosize]="{ minRows: 2, maxRows: 6 }"
                ></textarea>

                <nz-form-item>
                    <nz-form-label>搜索类型</nz-form-label>
                    <nz-form-control>
                        <nz-select [(ngModel)]="batchType" [ngModelOptions]="{standalone: true}">
                            <nz-option nzLabel="表号" nzValue="meterCodes"></nz-option>
                            <nz-option *ngIf="batch === 'wm'" nzLabel="IMEI" nzValue="imeis"></nz-option>
                        </nz-select>
                    </nz-form-control>
                </nz-form-item>

            </ng-container>

            <div class="form-button">
                <nz-spin style="display: inline-block" [nzSpinning]="pagination.loading">
                    <button nz-button type="button" style="margin: 0" (click)="queryOn()" [nzType]="'primary'">查询
                    </button>
                </nz-spin>
                <ng-container *ngIf="!showBatch">
                    <nz-spin style="display: inline-block" [nzSpinning]="pagination.loading">
                        <button class="ant-btn-success" nz-button type="button" [nzType]="'primary'"
                                (click)="pagination.refresh()">刷新
                        </button>
                    </nz-spin>
                    <button nz-button type="button" (click)="resetOn()" [disabled]="pagination.loading">清除</button>
                    <button nz-button type="button" *ngIf="showExpandFields"
                            (click)="expandForm = !expandForm">
                        <i nz-icon nzType="double-right" nzTheme="outline" [nzRotate]="expandForm ? 270 : 90"></i>
                    </button>
                </ng-container>

                <button nz-button type="button" *ngIf="batch" (click)="showBatch = !showBatch;">
                    <i nz-icon [nzType]="showBatch? 'rollback':'customer:search'" nzTheme="outline"></i>
                </button>
            </div>
        </div>
        <nz-collapse nzGhost *ngIf="showExpandFields && !showBatch">
            <nz-collapse-panel [nzHeader]="" [nzActive]="expandForm" [nzShowArrow]="false">
                <at-pagination-filter-renderer [formGroup]="pagination.queryForm"
                                               [filterFields]="expandFields"></at-pagination-filter-renderer>
            </nz-collapse-panel>
        </nz-collapse>
    </form>
</div>
