import {FormGroup} from '@angular/forms';

/**
 * Created by hsq on 21/4/26.
 */
export class StringUtils {

    static phoneEeg = /^1\d{10}$/;
    static emailEeg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
    static accountEeg = /^(?![0-9]*$)[a-zA-Z0-9]{6,32}$/;
    static nameEeg = /^[a-zA-Z\u4e00-\u9fa5]+$/;
    static specialCharacters = /^[\u4E00-\u9FA5A-Za-z0-9]+$/;
    static numberAndEnglishEeg = /^[0-9a-zA-Z]+$/;
    static meterCodeEeg = /\d{12,14}$/;

    constructor() {

    }

    /**
     * 获取地址栏参数
     * @param name
     * @returns {any}
     */
    static getQueryString(name: any): string {
        const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
        let r = window.location.search.substr(1).match(reg);
        if (r != null) {
            return r[2];
        }
        return '';
    }

    static isBlank(str: string) {
        return str === undefined || str === null || '' === str.trim();
    }

    static isString(str: any) {
        return (typeof str === 'string') && str.constructor === String;
    }

    static trim(o: any) {
        if (StringUtils.isEmpty(o)) {
            return '';
        }
        return o.trim();
    }

    static allTrim(value: any) {
        if (!value) {
            return '';
        }
        return value.replace(/\s/g, '');
    }

    static formatPhoneStr(value: any) {
        if (!value) {
            return '';
        }
        const result = value.replace(/\D/g, '');
        return result.length > 11 ? result.substr(result.length - 11, 11) : result;
    }

    static commonFormTrim(commonForm: FormGroup, keys: string[]) {
        if (commonForm != null) {
            if (keys != null) {
                keys.forEach(key => {
                    commonForm.value[key] = StringUtils.trim(commonForm.value[key]);
                });
            }
        }
        return commonForm;
    }

    static objectFieldTrim(obj: any, keys: string[]) {
        if (obj != null) {
            if (keys != null) {
                keys.forEach(key => {
                    obj[key] = StringUtils.trim(obj[key]);
                });
            }
        }
        return obj;
    }

    static commonFormAllTrim(formGroup: FormGroup) {
        if (formGroup != null) {
            for (const key in formGroup.value) {
                const val = formGroup.value[key];
                const type = typeof (val);
                if (type === 'string') {
                    formGroup.controls[key].setValue(val.trim());
                }
            }
        }
        return formGroup;
    }

    static isEmpty(o: any) {
        if (o === null || o === 'null' || o === undefined || o === 'undefined' || o === '') {
            return true;
        } else {
            return false;
        }
    }

    static checkEmail(email: any) {
        return this.emailEeg.test(email);
    }

    static checkPhone(phone: any) {
        return this.phoneEeg.test(phone);
    }

    static checkAccount(account: any) {
        return this.accountEeg.test(account);
    }

    static checkMeterCode(meterCode: any) {
        return this.meterCodeEeg.test(meterCode);
    }

    static isRepeat(arr: any[]) {
        const hash: any = {};
        for (const i in arr) {
            if (hash[arr[i]]) {
                return true;
            }
            hash[arr[i]] = true;
        }
        return false;
    }

    static checkName(name: any) {
        return this.nameEeg.test(name);
    }

    static checkNumberAndEnglish(name: any) {
        return this.numberAndEnglishEeg.test(name);
    }

    static checkSpecialCharacters(character: any) {
        return this.specialCharacters.test(character);
    }

    static fillLeft(value: any, fill: any, length: any) {
        if (value.length >= length) {
            return value;
        }
        let count = length - value.length;
        for (let i = 0; i < count; i++) {
            value = fill + value;
        }
        return value;
    }

    static defaultIfNull(value: any, defaultValue: any) {
        if (!value) {
            return defaultValue;
        }
        return value;
    }
}
