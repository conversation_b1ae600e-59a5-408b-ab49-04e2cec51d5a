import {HttpClient, HttpHeaders, HttpRequest, HttpResponse} from '@angular/common/http';
import {Observable} from 'rxjs';
import {filter} from 'rxjs/operators';

/**
 * http接口服务类
 */
export class HttpService {

    constructor(public http: HttpClient, public apiUrl: string) {
    }

    /**
     * 通用GET请求
     * @ param {string} url
     * @ param paramMap
     * @ returns {Promise<any>}
     */
    protected get(url: string, paramMap: any = null): Observable<any> {
        const options = {
            params: paramMap,
        };
        return this.http.get(this.apiUrl + url, options);
    }

    /**
     * 以json形式post提交
     * @ param {string} url
     * @ param body
     * @ param {boolean} showLoading
     * @ returns {Promise<any>}
     */
    protected post(url: string, body: any = null): Observable<any> {
        const options = {
            headers: new HttpHeaders({
                'Content-Type': 'application/json; charset=UTF-8',
            }),
        };
        if (typeof body === 'string') {
            body = JSON.stringify(body);
        }
        return this.http.post(this.apiUrl + url, body, options);
    }

    /**
     * 以表单方式提交
     * @ param {string} url
     * @ param body
     * @ returns {Promise<any>}
     */
    protected postForm(url: string, body: any = null): Observable<any> {
        const options = {
            headers: new HttpHeaders({
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            }),
        };
        return this.http.post(this.apiUrl + url, body, options);
    }

    /**
     * 以post-json提交，且支持提供查询参数
     * @param url
     * @param body
     * @param paramMap
     */
    protected postBodyAndParams(url: string, body: any = null, paramMap: any = null): Observable<any> {
        const options = {
            params: paramMap,
            headers: new HttpHeaders({
                'Content-Type': 'application/json; charset=UTF-8',
            }),
        };
        return this.http.post(this.apiUrl + url, body, options);
    }

    /**
     * 以put类型提交json
     * @param url
     * @param body
     * @param paramMap
     */
    protected put(url: string, body: any = null, paramMap: any = null): Observable<any> {
        const options = {
            params: paramMap,
            headers: new HttpHeaders({
                'Content-Type': 'application/json; charset=UTF-8',
            }),
        };
        return this.http.put(this.apiUrl + url, body, options);
    }

    /**
     * 以delete类型提交
     * @param url
     * @param paramMap
     */
    protected delete(url: string, paramMap: any = null): Observable<any> {
        const options = {
            params: paramMap,
        };
        return this.http.delete(this.apiUrl + url, options);
    }


    /**
     * 以post-json提交，且支持提供查询参数
     * @param url
     * @param formData
     */
    protected postFile(url: string, formData: any = null): Observable<any> {
        const req = new HttpRequest('POST', this.apiUrl + url, formData);
        return this.http
            .request(req)
            .pipe(filter(e => e instanceof HttpResponse));
    }
}
