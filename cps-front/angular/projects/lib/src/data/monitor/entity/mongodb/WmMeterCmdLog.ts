import {MongoDeviceAuditingEntity} from '../../../base/entity/mongodb/MongoDeviceAuditingEntity';

/**
 * 水表命令日志
 */
export class WmMeterCmdLog extends MongoDeviceAuditingEntity {

    /**
     * 命令类型
     */
    cmdType: string;

    /**
     * 命令ID，NB平台返回
     */
    commandId: string;

    /**
     * 命令详细
     */
    details: string;

    /**
     * 命令参数
     */
    params: string;

    /**
     * 强制阀门控制持续时间
     */
    forceValveTime: string;

    /**
     * 强制阀门动作类型
     */
    forceValveType: string;

    /**
     * 日流量阈值限制（2-9）
     */
    dailyFlow: number;

    /**
     * 命令状态
     */
    commandState: string;
}
