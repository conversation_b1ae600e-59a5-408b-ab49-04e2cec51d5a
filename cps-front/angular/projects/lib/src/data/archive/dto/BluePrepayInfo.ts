import {DeviceGroup} from "../entity/DeviceGroup";
import {BluePrepayDevicePrice} from "./BluePrepayDevicePrice";

export class BluePrepayInfo {

    /**
     * 销售公司
     */
    salesCompanyId: string;

    /**
     * 销售公司ID
     */
    salesCompanyIdVal: string;

    /**
     * 销售时间
     */
    salesTime: string;

    /**
     * 设备分组
     */
    deviceGroup: DeviceGroup;

    /**
     * 资产码
     */
    assetNo: string;

    /**
     * 预付费水表价格方案
     */
    bluePrepayDevicePrice: BluePrepayDevicePrice;

    /**
     * 预付费水表价格方案拓展
     */
    bluePrepayDevicePriceExtend: BluePrepayDevicePrice;

    /**
     * 当前生效价格方案
     */
    currentBluePrepayDevicePrice: BluePrepayDevicePrice;


    /**
     * 预付费充值流水号
     */
    serialNumber: number;

    /**
     * 单笔充值上限
     */
    singleRechargeLimit: number;

    /**
     * 预付费开户状态
     */
    openAccountStatus: string;
}
