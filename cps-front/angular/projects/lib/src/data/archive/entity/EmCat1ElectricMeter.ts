import { EmElectricMeter } from "./EmElectricMeter";

/**
 * 4G电表(EmCat1ElectricMeter)实体类
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
export class EmCat1ElectricMeter extends EmElectricMeter {
    
    /**
     * 通讯地址，与cat1模块的通讯地址，1376.1协议所需地址
     */
    uid: string;
    
    /**
     * 电表小类
     */
    meterCategory: string;
    
    /**
     * 电表协议
     */
    protocol: string;
    
    /**
     * 通信速率
     */
    communicationRate: number;
    
    /**
     * 通信端口
     */
    communicationPort: number;
    
    /**
     * 是否在线
     */
    online: boolean;
    
    /**
     * 参考信号接收功率
     */
    rsrp: number;
    
    /**
     * 信号与干扰加噪声比
     */
    sinr: number;
    
    /**
     * 小区位置信息
     */
    cellId: number;

    /**
     * IMEI
     */
    imei: string;

    /**
     * IMSI
     */
    imsi: string;

    /**
     * ICCID
     */
    iccid: string;
}
