import {Community} from "./Community";
import { Room } from './Room';
import { BaseAuditingCommunityEntity } from '../../base/entity/BaseAuditingCommunityEntity';
import {BuildingUnit} from "./BuildingUnit";
import {WxUser} from '../../revenue/entity/WxUser';

/**
 * 住户(Customer)实体类
 *
 * <AUTHOR>
 * @since 2022-08-30 14:13:59
 */
export class Customer extends BaseAuditingCommunityEntity {

    /**
     * 联系地址
     */
    contactAddress: string;

    /**
     * 性别
     */
    gender: 'MALE' | 'FEMALE' | 'UNKNOWN';

    /**
     * 身份证
     */
    idCard: string;

    /**
     * 手机号
     */
    mobile: string;

    /**
     * 住户名称
     */
    name: string;

    /**
     * 小区id
     */
    community: Community;

    /**
     * 楼栋单元
     */
    buildingUnit: BuildingUnit;

    /**
     * 前端显示的楼栋单元名称
     */
    buildingUnitNames: string;

    /**
     * 绑定的微信用户
     */
    wxUser: WxUser;

    /**
     * 绑定的微信小程序用户
     */
    wxMaUser: WxUser;

    /**
     * 最新咨询时间
     */
    lastAdvisoryTime: string;

    /**
     * 咨询是否已回复
     */
    advisoryHandle: boolean;

    /**
     * 最新咨询内容
     */
    latestContent: string;

    /**
     * 临时字段：要绑定的房间
     */
    rooms: Room[];
}
