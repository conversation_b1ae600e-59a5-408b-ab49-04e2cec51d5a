package com.acpiot.production.data.production.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.production.data.common.entity.Customer;
import com.acpiot.production.data.common.entity.Customer_;
import com.acpiot.production.data.production.entity.WaterMeter;
import com.acpiot.production.data.production.entity.WaterMeter_;
import com.acpiot.production.data.production.entity.WmTestBatch;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import pers.mx.jupiter.jpa.Filter;
import pers.mx.jupiter.jpa.QueryParams;
import pers.mx.jupiter.jpa.repository.BaseRepository;

import java.util.List;
import java.util.Optional;

/**
 * Created by moxin on 2020-11-25-0025
 *
 * <AUTHOR> Email: <EMAIL>
 */
public interface WaterMeterRepository extends JpaRepository<WaterMeter, Long>, JpaSpecificationExecutor<WaterMeter>, BaseRepository<WaterMeter> {

    @EntityGraph(attributePaths = "testBatch")
    Optional<WaterMeter> findByImei(String imei);

    @Query("select wm from WaterMeter wm left join wm.testBatch tb where (wm.testResult is null or wm.testResult =" +
            " com.acpiot.production.data.production.entity.TestResult.SUCCESS) and tb.customer = ?1 and wm.meterCode = ?2")
    Optional<WaterMeter> findByMeterCode(Customer customer, String meterCode);

    List<WaterMeter> findByImeiIn(List<String> imeis);

    List<WaterMeter> findByAssetNoIn(List<String> assetNos);

    boolean existsByTestBatchId(long testBatchId);

    boolean existsByPackBoxId(long boxId);

    List<WaterMeter> findByTestBatchId(long batchId);

    long countByTestBatch(WmTestBatch testBatch);

    long countByPackBoxId(long boxId);

    default List<WaterMeter> findByIdIn(List<Long> ids) {
        QueryParams<WaterMeter> params = QueryParams.newInstance();
        CollUtil.split(ids, 1000)
                .forEach(splitIds -> params.or(Filter.in(WaterMeter_.ID, splitIds)));
        return findAll(params);
    }

    List<WaterMeter> findByTestBatchAndIdIn(WmTestBatch testBatch, long[] ids);

    @Modifying
    @Query("update WaterMeter wm set wm.customer = :customer where wm.testBatch = :testBatch")
    void changeCustomer(@Param("testBatch") WmTestBatch testBatch, @Param("customer") Customer customer);

    default Optional<WaterMeter> findByDeviceNo(WmTestBatch testBatch, String deviceNo) {
        final int len = deviceNo.length();
        if (len > 15) {
            throw new IllegalArgumentException("错误的设备号");
        }

        Filter batchFilter = Filter.eq(WaterMeter_.TEST_BATCH, testBatch);

        if (len == 15) {
            // IMEI号查询
            return findOne(new Filter[]{
                    batchFilter,
                    Filter.eq(WaterMeter_.IMEI, deviceNo)
            });
        }

        if (len == 10) {
            // 资产号查询
            Optional<WaterMeter> optional = findOne(new Filter[]{
                    batchFilter,
                    Filter.eq(WaterMeter_.ASSET_NO, deviceNo)
            });
            if (optional.isPresent()) {
                return optional;
            }
        }

        // 水表号查询
        String meterCode = StrUtil.padPre(deviceNo, 14, "0");
        return findOne(new Filter[]{
                batchFilter,
                Filter.eq(WaterMeter_.METER_CODE, meterCode)
        });
    }

    default Optional<WaterMeter> findByDeviceNo(String deviceNo) {
        int len = deviceNo.length();
        if (len > 15) {
            throw new IllegalArgumentException("错误的设备号");
        }

        if (len == 15) {
            // IMEI号查询
            return findOne(new Filter[]{Filter.eq(WaterMeter_.IMEI, deviceNo)});
        }

        if (len == 10) {
            // 资产号查询
            Optional<WaterMeter> optional = findOne(new Filter[]{Filter.eq(WaterMeter_.ASSET_NO, deviceNo)});
            if (optional.isPresent()) {
                return optional;
            }
        }

        // 水表号查询
        String meterCode = StrUtil.padPre(deviceNo, 14, "0");
        QueryParams<WaterMeter> params = QueryParams.newInstance();
        params.or(filterByCode(meterCode));
        return findOne(params);
    }

    default Filter[] filterByCode(String deviceNo) {
        return new Filter[]{
                Filter.eq(WaterMeter_.EXPECT_CODE, deviceNo),
                Filter.eq(WaterMeter_.METER_CODE, deviceNo)
        };
    }

    default Optional<WaterMeter> findByDeviceNo(long customerId, String deviceNo) {
        int len = deviceNo.length();
        if (len > 15) {
            throw new IllegalArgumentException("错误的设备号");
        }

        Filter customerFilter = Filter.eq(StrUtil.format("{}.{}", WaterMeter_.CUSTOMER, Customer_.ID), customerId);

        if (len == 15) {
            // IMEI号查询
            return findOne(new Filter[]{
                    customerFilter,
                    Filter.eq(WaterMeter_.IMEI, deviceNo)
            });
        }

        if (len == 10) {
            // 资产号查询
            Optional<WaterMeter> optional = findOne(new Filter[]{
                    customerFilter,
                    Filter.eq(WaterMeter_.ASSET_NO, deviceNo)
            });
            if (optional.isPresent()) {
                return optional;
            }
        }

        // 水表号查询
        String meterCode = StrUtil.padPre(deviceNo, 14, "0");
        QueryParams<WaterMeter> params = QueryParams.of(customerFilter);
        params.or(filterByCode(meterCode));
        return findOne(params);
    }
}
