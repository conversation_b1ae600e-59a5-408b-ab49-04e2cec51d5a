package com.acpiot.production.data.production.entity;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.acpiot.production.data.common.config.jackson.serializer.TestCurrentSerializer;
import com.acpiot.production.data.common.entity.BaseNbDevice;
import com.acpiot.production.data.common.entity.Customer;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.Hibernate;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;

import static com.acpiot.production.data.common.ProductionConstant.INVALID_CODE;

/**
 * Created by moxin on 2020-11-25-0025
 *
 * <AUTHOR> Email: <EMAIL>
 */
@Getter
@Setter
@NoArgsConstructor
@Entity
@DynamicInsert
@DynamicUpdate
public class WaterMeter extends BaseNbDevice {

    /**
     * 所属客户
     */
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    private Customer customer;

    /**
     * 所属测试批次
     */
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    private WmTestBatch testBatch;

    /**
     * 水表/采集器设备运行的eDRX周期
     */
    @Enumerated
    @Column(nullable = false)
    private EdrxPeriod edrxPeriod = EdrxPeriod.TWENTY_MINUTES;

    /**
     * 资产编号
     */
    @Column(nullable = false, unique = true, length = 15)
    private String assetNo;

    /**
     * 关联水表号
     */
    @Column(length = 14, columnDefinition = "char(14)")
    private String meterCode;

    /**
     * 期望的水表表号
     */
    @Column(length = 14, columnDefinition = "char(14)")
    private String expectCode;

    /**
     * 转换因子 小数点位数, 取值范围0-4
     */
    @Column(nullable = false, columnDefinition = "tinyint")
    private int factor;

    /**
     * 基表底数
     */
    @Column(columnDefinition = "char(8)")
    private String baseValue;

    /**
     * 模块当前所处产测模式
     */
    @Enumerated
    @Column(nullable = false, columnDefinition = "tinyint default 0")
    private ProductionMode productionMode = ProductionMode.NORMAL;

    /**
     * 设置的产测超时时间
     */
    @Column(columnDefinition = "char(8)")
    private String productionTimeout;

    /**
     * 成表产测信息
     */
    @Embedded
    private WmTestInfo testInfo;

    /**
     * 最终测试结果
     */
    @Enumerated
    @Column(columnDefinition = "tinyint")
    private TestResult testResult;

    /**
     * 最新水表数据
     */
    @Embedded
    private MeterData meterData;

    /**
     * 归属打包箱
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private WmPackBox packBox;

    /**
     * 是否执行了上电关阀
     */
    @Column(nullable = false)
    private boolean firstCloseValve;

    /**
     * 是否蓝牙测试通过
     */
    @Column(nullable = false)
    private boolean bleTestPass;

    /**
     * 蓝牙MAC地址
     */
    @Column(length = 17, columnDefinition = "char(17)")
    private String bleMac;

    /**
     * 所属参数模板
     */
    @ManyToOne(fetch = FetchType.LAZY)
    private ParamTemplate paramTemplate;

    /**
     * 测试电流
     */
    @JsonSerialize(using = TestCurrentSerializer.class)
    @Column(scale = 1)
    private Double testCurrent;

    /**
     * 是否校表
     */
    @Transient
    private boolean proofread;

    /**
     * Instantiates a new Water meter.
     *
     * @param imei    the imei
     * @param assetNo the asset no
     * @param factor  the factor
     */
    public WaterMeter(String imei, String assetNo, int factor) {
        super(imei);
        this.assetNo = assetNo;
        this.factor = factor;
    }

    /**
     * 获取格式化显示的基表底数
     *
     * @return formative base value
     */
    public BigDecimal getFormativeBaseValue() {
        if (StrUtil.isBlank(baseValue)) {
            return null;
        }
        return new BigDecimal(new BigInteger(baseValue), factor);
    }

    /**
     * 是否版本匹配
     *
     * @return boolean
     */
    public boolean isVersionMatch() {
        if (!Hibernate.isInitialized(testBatch)
                || meterData == null) {
            return false;
        }
        return testBatch.validVersion(meterData.getMeterVersion());
    }

    /**
     * 是否可以实时通信
     *
     * @return boolean
     */
    public boolean isRealtime() {
        if (StrUtil.isBlank(productionTimeout)) {
            return false;
        }
        DateTime timeoutTime = DateUtil.parse(productionTimeout, "yyMMddHH");
        return productionMode == ProductionMode.METER_TEST && DateUtil.date().isBefore(timeoutTime);
    }

    /**
     * 格式化显示的水表读数
     *
     * @return formatted value
     */
    public BigDecimal getFormattedValue() {
        if (meterData == null) {
            return null;
        }
        BigDecimal value = meterData.getValue();
        if (value == null) {
            return null;
        }
        return value.setScale(factor, RoundingMode.HALF_UP);
    }

    /**
     * 是否最后一步测试步骤
     *
     * @return boolean
     */
    @JsonIgnore
    public boolean isFinishStep() {
        if (testInfo == null) {
            return false;
        }
        TestPlan testPlan = testBatch.getTestPlan();
        return testPlan.isFinishStep(testInfo.getProductionStep());
    }

    /**
     * 设备号
     *
     * @return device no
     */
    public String getDeviceNo() {
        return StrUtil.format("{} {} {}", getImei(), assetNo, meterCode == null ? "" : meterCode);
    }

    /**
     * Sets test info.
     *
     * @param testInfo the test info
     */
    public void setTestInfo(WmTestInfo testInfo) {
        if (testInfo.getOperator() == null) {
            testInfo.setOperator(this.testInfo.getOperator());
        }
        if (testInfo.getAddTestTime() == null) {
            testInfo.setAddTestTime(this.testInfo.getAddTestTime());
        }
        this.testInfo = testInfo;
    }

    /**
     * Check code boolean.
     *
     * @return the boolean
     */
    public boolean checkCode() {
        return (StrUtil.isNotBlank(meterCode) && !StrUtil.equalsIgnoreCase(meterCode, INVALID_CODE))
                || (StrUtil.isNotBlank(expectCode) && !StrUtil.equalsIgnoreCase(expectCode, INVALID_CODE));
    }

    /**
     * Gets valid code.
     *
     * @return the valid code
     */
    @JsonIgnore
    public String getValidCode() {
        if (StrUtil.isNotBlank(expectCode)
                && !StrUtil.equalsIgnoreCase(expectCode, INVALID_CODE)) {
            return expectCode;
        }
        if (StrUtil.isNotBlank(meterCode)
                && !StrUtil.equalsIgnoreCase(meterCode, INVALID_CODE)) {
            return meterCode;
        }
        return null;
    }

}
